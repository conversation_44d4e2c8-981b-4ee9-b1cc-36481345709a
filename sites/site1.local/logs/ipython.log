2025-05-20 21:24:02,604 INFO ipython === bench console session ===
2025-05-20 21:24:02,605 INFO ipython frappe.get_installed_apps()
2025-05-20 21:24:02,605 INFO ipython frappe.get_all("DocType", filters={"module": "WhatsApp Integration"})
2025-05-20 21:24:02,605 INFO ipython === session end ===
2025-05-20 22:34:19,438 INFO ipython === bench console session ===
2025-05-20 22:34:19,444 INFO ipython import frappe
2025-05-20 22:34:19,445 INFO ipython frappe.db.sql("DELETE FROM `tabModule Def` WHERE name='WhatsApp Integration'")
2025-05-20 22:34:19,445 INFO ipython frappe.db.commit()
2025-05-20 22:34:19,445 INFO ipython === session end ===
2025-05-25 16:05:04,322 INFO ipython === bench console session ===
2025-05-25 16:05:04,330 INFO ipython === session end ===
2025-05-31 14:09:11,939 INFO ipython === bench console session ===
2025-05-31 14:09:12,152 INFO ipython doc = frappe.get_doc({"doctype": "Download Backup", "enable_auto_backup": 0, "backup_count": 7, "backup_format": "SQL"})
2025-05-31 14:09:12,153 INFO ipython doc.insert(ignore_permissions=True)
2025-05-31 14:09:12,153 INFO ipython === session end ===
2025-06-28 07:45:35,478 INFO ipython === bench console session ===
2025-06-28 07:45:35,488 INFO ipython import frappe
2025-06-28 07:45:35,488 INFO ipython result = frappe.db.sql("SELECT name FROM tabDocType WHERE name LIKE '%Car%' OR name LIKE '%Record%'")
2025-06-28 07:45:35,489 INFO ipython print(result)
2025-06-28 07:45:35,490 INFO ipython === session end ===
2025-06-28 07:45:53,604 INFO ipython === bench console session ===
2025-06-28 07:45:53,606 INFO ipython import frappe
2025-06-28 07:45:53,606 INFO ipython doc = frappe.get_doc('DocType', 'Car Record')
2025-06-28 07:45:53,608 INFO ipython print(f"Module: {doc.module}")
2025-06-28 07:45:53,608 INFO ipython print(f"Custom: {doc.custom}")
2025-06-28 07:45:53,608 INFO ipython print(f"Istable: {doc.istable}")
2025-06-28 07:45:53,608 INFO ipython print(f"Issingle: {doc.issingle}")
2025-06-28 07:45:53,612 INFO ipython print(f"App: {doc.app}")
2025-06-28 07:45:53,612 INFO ipython === session end ===
2025-06-28 07:46:09,418 INFO ipython === bench console session ===
2025-06-28 07:46:09,418 INFO ipython import frappe
2025-06-28 07:46:09,423 INFO ipython doc = frappe.get_doc('DocType', 'Car Record')
2025-06-28 07:46:09,423 INFO ipython print("Fields in Car Record:")
2025-06-28 07:46:09,425 INFO ipython === session end ===
2025-06-28 07:47:37,305 INFO ipython === bench console session ===
2025-06-28 07:47:37,306 INFO ipython import frappe
2025-06-28 07:47:37,306 INFO ipython doc = frappe.get_doc('DocType', 'Car Record')
2025-06-28 07:47:37,306 INFO ipython print('Car Record DocType exists!')
2025-06-28 07:47:37,306 INFO ipython print('Module:', doc.module)
2025-06-28 07:47:37,307 INFO ipython print('Custom:', doc.custom)
2025-06-28 07:47:37,308 INFO ipython print('Fields:')
2025-06-28 07:47:37,308 INFO ipython for field in doc.fields:
    if field.fieldname:
        print('  -', field.fieldname + ':', field.label, '(' + field.fieldtype + ')')
2025-06-28 07:47:37,309 INFO ipython === session end ===
2025-06-28 07:47:55,342 INFO ipython === bench console session ===
2025-06-28 07:47:55,342 INFO ipython import frappe
2025-06-28 07:47:55,343 INFO ipython doc = frappe.get_doc('DocType', 'Car Record')
2025-06-28 07:47:55,343 INFO ipython print('Car Record DocType Settings:')
2025-06-28 07:47:55,346 INFO ipython print('- Name:', doc.name)
2025-06-28 07:47:55,357 INFO ipython print('- Module:', doc.module)
2025-06-28 07:47:55,358 INFO ipython print('- Is Custom:', doc.custom)
2025-06-28 07:47:55,358 INFO ipython print('- Has Permissions:', bool(doc.permissions))
2025-06-28 07:47:55,358 INFO ipython print('- Autoname:', doc.autoname)
2025-06-28 07:47:55,359 INFO ipython print('- Search Fields:', doc.search_fields)
2025-06-28 07:47:55,359 INFO ipython print('- Title Field:', doc.title_field)
2025-06-28 07:47:55,359 INFO ipython print('- Sort Field:', doc.sort_field)
2025-06-28 07:47:55,363 INFO ipython print('- Sort Order:', doc.sort_order)
2025-06-28 07:47:55,364 INFO ipython === session end ===
2025-06-28 07:59:26,588 INFO ipython === bench console session ===
2025-06-28 07:59:26,591 INFO ipython import frappe
2025-06-28 07:59:26,591 INFO ipython import json
2025-06-28 07:59:26,591 INFO ipython doc = frappe.get_doc('DocType', 'Car Record')
2025-06-28 07:59:26,591 INFO ipython print('DocType JSON Path would be in:')
2025-06-28 07:59:26,592 INFO ipython print('/home/<USER>/frappe-bench/apps/customsmart/customsmart/customsmart/doctype/car_record/')
2025-06-28 07:59:26,592 INFO ipython print()
2025-06-28 07:59:26,592 INFO ipython print('But since this is a Custom DocType, it might not have physical files.')
2025-06-28 07:59:26,592 INFO ipython print('Custom DocTypes are stored in database.')
2025-06-28 07:59:26,592 INFO ipython print()
2025-06-28 07:59:26,592 INFO ipython print('To export it to JSON:')
2025-06-28 07:59:26,592 INFO ipython export_doc = doc.as_dict()
2025-06-28 07:59:26,592 INFO ipython export_doc.pop('creation', None)
2025-06-28 07:59:26,593 INFO ipython export_doc.pop('modified', None)
2025-06-28 07:59:26,593 INFO ipython export_doc.pop('modified_by', None)
2025-06-28 07:59:26,593 INFO ipython export_doc.pop('owner', None)
2025-06-28 07:59:26,593 INFO ipython print('JSON content:')
2025-06-28 07:59:26,593 INFO ipython print(json.dumps(export_doc, indent=2)[:500] + '...')
2025-06-28 07:59:26,593 INFO ipython === session end ===
2025-06-28 08:04:01,603 INFO ipython === bench console session ===
2025-06-28 08:04:01,604 INFO ipython import frappe
2025-06-28 08:04:01,605 INFO ipython doctypes = frappe.db.sql("SELECT name FROM tabDocType WHERE module='customsmart' OR app='customsmart'", as_dict=True)
2025-06-28 08:04:01,605 INFO ipython print('DocTypes in customsmart module:')
2025-06-28 08:04:01,608 INFO ipython for dt in doctypes:
    print('- ' + dt.name)
2025-06-28 08:04:01,609 INFO ipython === session end ===
2025-06-28 08:42:33,157 INFO ipython === bench console session ===
2025-06-28 08:42:33,157 INFO ipython import frappe
2025-06-28 08:42:33,158 INFO ipython try:
    workspace = frappe.get_doc('Workspace', 'Car Showroom')
    print('Workspace exists!')
    print('Name:', workspace.name)
    print('Label:', workspace.label)
    print('Hidden:', workspace.is_hidden)
    print('Icon:', workspace.icon)
except frappe.DoesNotExistError:
    print('Workspace does not exist in database')
2025-06-28 08:42:33,159 INFO ipython === session end ===
2025-06-28 08:42:51,108 INFO ipython === bench console session ===
2025-06-28 08:42:51,109 INFO ipython import frappe
2025-06-28 08:42:51,109 INFO ipython workspaces = frappe.get_all('Workspace', fields=['name', 'label', 'is_hidden', 'icon', 'module'])
2025-06-28 08:42:51,110 INFO ipython print('All Workspaces:')
2025-06-28 08:42:51,111 INFO ipython for ws in workspaces:
    if 'car' in ws.name.lower() or 'showroom' in ws.name.lower():
        print(f'- {ws.name}: {ws.label} (Hidden: {ws.is_hidden}, Module: {ws.module})')
2025-06-28 08:42:51,111 INFO ipython === session end ===
2025-06-28 08:43:11,075 INFO ipython === bench console session ===
2025-06-28 08:43:11,076 INFO ipython import frappe
2025-06-28 08:43:11,076 INFO ipython # Check user permissions
2025-06-28 08:43:11,076 INFO ipython user = frappe.session.user
2025-06-28 08:43:11,077 INFO ipython print(f'Current user: {user}')
2025-06-28 08:43:11,078 INFO ipython # Get workspace permissions 
2025-06-28 08:43:11,078 INFO ipython workspace = frappe.get_doc('Workspace', 'Car Showroom')
2025-06-28 08:43:11,078 INFO ipython print(f'For User: {workspace.for_user}')
2025-06-28 08:43:11,079 INFO ipython print(f'Custom: {workspace.custom}')
2025-06-28 08:43:11,079 INFO ipython print(f'Module: {workspace.module}')
2025-06-28 08:43:11,079 INFO ipython # Check if user has access to customsmart module
2025-06-28 08:43:11,079 INFO ipython has_access = frappe.has_permission('Workspace', 'read', doc=workspace)
2025-06-28 08:43:11,080 INFO ipython print(f'User has access: {has_access}')
2025-06-28 08:43:11,080 INFO ipython === session end ===
2025-06-28 08:47:53,924 INFO ipython === bench console session ===
2025-06-28 08:47:53,930 INFO ipython import frappe
2025-06-28 08:47:53,931 INFO ipython # Clear cache to reload workspaces
2025-06-28 08:47:53,931 INFO ipython frappe.clear_cache()
2025-06-28 08:47:53,932 INFO ipython # Check workspace status
2025-06-28 08:47:53,933 INFO ipython workspace = frappe.get_doc('Workspace', 'Car Showroom')
2025-06-28 08:47:53,933 INFO ipython print('Workspace Status:')
2025-06-28 08:47:53,933 INFO ipython print('- Name:', workspace.name)
2025-06-28 08:47:53,933 INFO ipython print('- Label:', workspace.label)
2025-06-28 08:47:53,933 INFO ipython print('- Hidden:', workspace.is_hidden)
2025-06-28 08:47:53,934 INFO ipython print('- Module:', workspace.module)
2025-06-28 08:47:53,934 INFO ipython print('- Icon:', workspace.icon)
2025-06-28 08:47:53,934 INFO ipython === session end ===
2025-06-28 09:46:24,809 INFO ipython === bench console session ===
2025-06-28 09:46:24,813 INFO ipython import frappe
2025-06-28 09:46:24,814 INFO ipython import json
2025-06-28 09:46:24,817 INFO ipython # قراءة ملف Print Format
2025-06-28 09:46:24,818 INFO ipython file_path = '/home/<USER>/frappe-bench/apps/customsmart/customsmart/customsmart/print_format/multilingual_car_invoice/multilingual_car_invoice.json'
2025-06-28 09:46:24,818 INFO ipython with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
2025-06-28 09:46:24,819 INFO ipython # التحقق من وجود Print Format
2025-06-28 09:46:24,820 INFO ipython try:
    existing = frappe.get_doc('Print Format', data['name'])
    print(f'Print Format {data["name"]} موجود - سيتم التحديث')
    for key, value in data.items():
        if key not in ['name', 'creation', 'modified', 'modified_by', 'owner']:
            setattr(existing, key, value)
    existing.save()
    print('تم التحديث بنجاح')
except frappe.DoesNotExistError:
    print(f'إنشاء Print Format جديد: {data["name"]}')
    doc = frappe.get_doc(data)
    doc.insert()
    print('تم الإنشاء بنجاح')
2025-06-28 09:46:24,820 INFO ipython frappe.db.commit()
2025-06-28 09:46:24,827 INFO ipython print('✅ تم حفظ Print Format متعدد اللغات!')
2025-06-28 09:46:24,828 INFO ipython === session end ===
2025-06-28 09:52:30,017 INFO ipython === bench console session ===
2025-06-28 09:52:30,023 INFO ipython # التحقق من Print Format
2025-06-28 09:52:30,023 INFO ipython print_format = frappe.get_doc('Print Format', 'Multilingual Car Invoice')
2025-06-28 09:52:30,024 INFO ipython print('✅ Print Format موجود:', print_format.name)
2025-06-28 09:52:30,024 INFO ipython print('�� DocType:', print_format.doc_type)
2025-06-28 09:52:30,024 INFO ipython print('🌐 اللغة الافتراضية:', print_format.default_print_language)
2025-06-28 09:52:30,025 INFO ipython # التحقق من فواتير المبيعات
2025-06-28 09:52:30,025 INFO ipython invoices = frappe.get_all('Sales Invoice', filters={'docstatus': 1}, limit=3, fields=['name', 'customer_name'])
2025-06-28 09:52:30,025 INFO ipython print('💰 فواتير المبيعات المتاحة:', len(invoices))
2025-06-28 09:52:30,025 INFO ipython for inv in invoices:
    print(f'  - {inv.name}: {inv.customer_name}')
2025-06-28 09:52:30,025 INFO ipython print('🎉 نظام الطباعة متعدد اللغات جاهز!')
2025-06-28 09:52:30,025 INFO ipython === session end ===
2025-06-28 16:23:33,317 INFO ipython === bench console session ===
2025-06-28 16:23:33,325 INFO ipython frappe.get_all("Workspace", fields=["name", "label", "module"])
2025-06-28 16:23:33,332 INFO ipython === session end ===
2025-06-28 16:23:57,781 INFO ipython === bench console session ===
2025-06-28 16:23:57,783 INFO ipython doc = frappe.get_doc("Workspace", "Car Showroom"); print(f"Name: {doc.name}"); print(f"Label: {doc.label}"); print(f"Module: {doc.module}"); print(f"Public: {doc.public}"); print(f"Hidden: {doc.is_hidden}"); print(f"Icon: {doc.icon}")
2025-06-28 16:23:57,784 INFO ipython === session end ===
2025-06-30 10:57:22,810 INFO ipython === bench console session ===
2025-06-30 10:57:22,818 INFO ipython # إصلاح مشكلة Workspaces
2025-06-30 10:57:22,819 INFO ipython import frappe
2025-06-30 10:57:22,823 INFO ipython # تحديث جميع workspaces لتكون عامة
2025-06-30 10:57:22,823 INFO ipython frappe.db.sql("""UPDATE `tabWorkspace` SET public = 1 WHERE name IN ('Home', 'Accounting', 'Selling', 'Buying', 'Stock', 'Assets', 'Projects', 'CRM', 'Support', 'HR', 'Manufacturing', 'Website', 'Tools', 'Settings')""")
2025-06-30 10:57:22,823 INFO ipython # حذف تخصيصات المستخدمين
2025-06-30 10:57:22,823 INFO ipython frappe.db.sql("DELETE FROM `tabUser Settings` WHERE doctype = 'Workspace'")
2025-06-30 10:57:22,823 INFO ipython # مسح cache
2025-06-30 10:57:22,823 INFO ipython frappe.clear_cache()
2025-06-30 10:57:22,824 INFO ipython # commit التغييرات
2025-06-30 10:57:22,825 INFO ipython frappe.db.commit()
2025-06-30 10:57:22,825 INFO ipython print("تم إصلاح مشكلة Workspaces")
2025-06-30 10:57:22,825 INFO ipython === session end ===
2025-07-01 14:39:44,947 INFO ipython === bench console session ===
2025-07-01 14:39:44,956 INFO ipython import frappe
2025-07-01 14:39:44,956 INFO ipython frappe.init(site='site1.local')
2025-07-01 14:39:44,957 INFO ipython frappe.connect()
2025-07-01 14:39:44,957 INFO ipython from frappe.installer import install_app
2025-07-01 14:39:44,960 INFO ipython install_app('erp_optimizer', verbose=True)
2025-07-01 14:39:44,965 INFO ipython print('App installed successfully!')
2025-07-01 14:39:44,966 INFO ipython === session end ===
2025-07-01 14:58:17,222 INFO ipython === bench console session ===
2025-07-01 14:58:17,234 INFO ipython import frappe
2025-07-01 14:58:17,234 INFO ipython frappe.init(site='site1.local')
2025-07-01 14:58:17,235 INFO ipython frappe.connect()
2025-07-01 14:58:17,236 INFO ipython from frappe.installer import install_app
2025-07-01 14:58:17,240 INFO ipython install_app('erp_optimizer_ui', verbose=True)
2025-07-01 14:58:17,242 INFO ipython print('App installed successfully!')
2025-07-01 14:58:17,246 INFO ipython === session end ===
2025-07-01 15:12:26,948 INFO ipython === bench console session ===
2025-07-01 15:12:26,966 INFO ipython import frappe
2025-07-01 15:12:26,974 INFO ipython frappe.init(site='site1.local')
2025-07-01 15:12:26,975 INFO ipython frappe.connect()
2025-07-01 15:12:26,975 INFO ipython print('Installed apps:', frappe.get_installed_apps())
2025-07-01 15:12:26,975 INFO ipython === session end ===
2025-07-01 15:41:45,649 INFO ipython === bench console session ===
2025-07-01 15:41:45,664 INFO ipython import frappe
2025-07-01 15:41:45,664 INFO ipython frappe.init(site='site1.local')
2025-07-01 15:41:45,664 INFO ipython frappe.connect()
2025-07-01 15:41:45,665 INFO ipython # Create custom field for Sales Invoice
2025-07-01 15:41:45,665 INFO ipython if not frappe.db.exists('Custom Field', {'dt': 'Sales Invoice', 'fieldname': 'is_cash'}):
    custom_field = frappe.get_doc({
        'doctype': 'Custom Field',
        'dt': 'Sales Invoice',
        'fieldname': 'is_cash',
        'label': 'فاتورة نقدية؟',
        'fieldtype': 'Check',
        'insert_after': 'due_date'
    })
    custom_field.insert()
    print('Custom field created for Sales Invoice')
else:
    print('Custom field already exists for Sales Invoice')
2025-07-01 15:41:45,666 INFO ipython frappe.db.commit()
2025-07-01 15:41:45,666 INFO ipython print('Done!')
2025-07-01 15:41:45,667 INFO ipython === session end ===
2025-07-01 15:42:39,726 INFO ipython === bench console session ===
2025-07-01 15:42:39,727 INFO ipython import frappe
2025-07-01 15:42:39,728 INFO ipython frappe.init(site='site1.local')
2025-07-01 15:42:39,730 INFO ipython frappe.connect()
2025-07-01 15:42:39,738 INFO ipython # Create property setters
2025-07-01 15:42:39,739 INFO ipython property_setters = [
    {
        'doctype': 'Property Setter',
        'doctype_or_field': 'DocField',
        'doc_type': 'Sales Invoice',
        'field_name': 'due_date',
        'property': 'depends_on',
        'property_type': 'Data',
        'value': 'eval:doc.is_pos == 0'
    },
    {
        'doctype': 'Property Setter',
        'doctype_or_field': 'DocField',
        'doc_type': 'Sales Invoice',
        'field_name': 'pos_profile',
        'property': 'hidden',
        'property_type': 'Check',
        'value': '1'
    }
]
2025-07-01 15:42:39,739 INFO ipython for ps_data in property_setters:
    if not frappe.db.exists('Property Setter', {
        'doc_type': ps_data['doc_type'],
        'field_name': ps_data['field_name'],
        'property': ps_data['property']
    }):
        ps = frappe.get_doc(ps_data)
        ps.insert()
        print(f'Property setter created for {ps_data["field_name"]}')
    else:
        print(f'Property setter already exists for {ps_data["field_name"]}')
2025-07-01 15:42:39,741 INFO ipython frappe.db.commit()
2025-07-01 15:42:39,744 INFO ipython print('Property setters applied!')
2025-07-01 15:42:39,745 INFO ipython === session end ===
2025-07-01 15:43:18,749 INFO ipython === bench console session ===
2025-07-01 15:43:18,750 INFO ipython import frappe
2025-07-01 15:43:18,750 INFO ipython frappe.init(site='site1.local')
2025-07-01 15:43:18,751 INFO ipython frappe.connect()
2025-07-01 15:43:18,751 INFO ipython # Create Print Formats
2025-07-01 15:43:18,752 INFO ipython print_formats = [
    {
        'name': 'Sales Invoice Cash',
        'doc_type': 'Sales Invoice',
        'html': "<div style='direction: rtl;'>فاتورة نقدية - مختصرة وسريعة</div>",
        'custom_format': 1
    },
    {
        'name': 'Sales Invoice Credit',
        'doc_type': 'Sales Invoice', 
        'html': "<div style='direction: rtl;'>فاتورة آجلة - تفصيلية</div>",
        'custom_format': 1
    },
    {
        'name': 'Purchase Invoice Cash',
        'doc_type': 'Purchase Invoice',
        'html': "<div style='direction: rtl;'>فاتورة شراء نقدية</div>",
        'custom_format': 1
    },
    {
        'name': 'Purchase Invoice Credit',
        'doc_type': 'Purchase Invoice',
        'html': "<div style='direction: rtl;'>فاتورة شراء آجلة</div>",
        'custom_format': 1
    }
]
2025-07-01 15:43:18,752 INFO ipython for pf_data in print_formats:
    if not frappe.db.exists('Print Format', pf_data['name']):
        pf = frappe.get_doc({
            'doctype': 'Print Format',
            **pf_data
        })
        pf.insert()
        print(f'Print Format created: {pf_data["name"]}')
    else:
        print(f'Print Format already exists: {pf_data["name"]}')
2025-07-01 15:43:18,753 INFO ipython frappe.db.commit()
2025-07-01 15:43:18,754 INFO ipython print('Print Formats created!')
2025-07-01 15:43:18,755 INFO ipython === session end ===
2025-07-01 15:44:20,933 INFO ipython === bench console session ===
2025-07-01 15:44:20,934 INFO ipython import frappe
2025-07-01 15:44:20,934 INFO ipython frappe.init(site='site1.local')
2025-07-01 15:44:20,934 INFO ipython frappe.connect()
2025-07-01 15:44:20,935 INFO ipython # Create Workspaces
2025-07-01 15:44:20,935 INFO ipython workspaces = [
    {'name': 'المبيعات', 'label': 'المبيعات', 'module': 'Selling'},
    {'name': 'الشراء', 'label': 'الشراء', 'module': 'Buying'},
    {'name': 'المخزون', 'label': 'المخزون', 'module': 'Stock'},
    {'name': 'الحسابات', 'label': 'الحسابات', 'module': 'Accounts'},
    {'name': 'الموارد البشرية', 'label': 'الموارد البشرية', 'module': 'HR'},
    {'name': 'إدارة العملاء', 'label': 'إدارة العملاء', 'module': 'CRM'},
    {'name': 'التصنيع', 'label': 'التصنيع', 'module': 'Manufacturing'},
    {'name': 'الأصول', 'label': 'الأصول', 'module': 'Assets'}
]
2025-07-01 15:44:20,936 INFO ipython for ws_data in workspaces:
    if not frappe.db.exists('Workspace', ws_data['name']):
        ws = frappe.get_doc({
            'doctype': 'Workspace',
            'title': ws_data['label'],
            'module': ws_data['module'],
            'is_standard': 0,
            'public': 1
        })
        ws.insert()
        print(f'Workspace created: {ws_data["name"]}')
    else:
        print(f'Workspace already exists: {ws_data["name"]}')
2025-07-01 15:44:20,936 INFO ipython frappe.db.commit()
2025-07-01 15:44:20,939 INFO ipython print('Workspaces created!')
2025-07-01 15:44:20,940 INFO ipython === session end ===
2025-07-01 15:48:06,162 INFO ipython === bench console session ===
2025-07-01 15:48:06,167 INFO ipython import frappe
2025-07-01 15:48:06,168 INFO ipython frappe.init(site='site1.local')
2025-07-01 15:48:06,170 INFO ipython frappe.connect()
2025-07-01 15:48:06,174 INFO ipython # Check if custom field exists
2025-07-01 15:48:06,178 INFO ipython custom_field = frappe.db.get_value('Custom Field', {'dt': 'Sales Invoice', 'fieldname': 'is_cash'}, 'name')
2025-07-01 15:48:06,186 INFO ipython if custom_field:
    print(f'✅ Custom Field exists: {custom_field}')
else:
    print('❌ Custom Field not found')
2025-07-01 15:48:06,192 INFO ipython # Check if print formats exist  
2025-07-01 15:48:06,192 INFO ipython print_formats = frappe.db.get_list('Print Format', {'custom_format': 1}, 'name')
2025-07-01 15:48:06,207 INFO ipython print(f'✅ Custom Print Formats: {len(print_formats)} found')
2025-07-01 15:48:06,208 INFO ipython for pf in print_formats:
    print(f'  - {pf.name}')
2025-07-01 15:48:06,211 INFO ipython # Check if property setters exist
2025-07-01 15:48:06,212 INFO ipython property_setters = frappe.db.get_list('Property Setter', {'doc_type': 'Sales Invoice'}, 'name')
2025-07-01 15:48:06,215 INFO ipython print(f'✅ Property Setters: {len(property_setters)} found')
2025-07-01 15:48:06,217 INFO ipython print('\n🎯 All customizations have been applied!')
2025-07-01 15:48:06,218 INFO ipython === session end ===
2025-07-15 14:31:16,094 INFO ipython === bench console session ===
2025-07-15 14:31:16,170 INFO ipython exec(open('/home/<USER>/frappe-bench/frappevue_adminlte/setup_basic_data.py').read())
2025-07-15 14:31:16,170 INFO ipython # إنشاء Customer Groups
2025-07-15 14:31:16,171 INFO ipython print("=== إنشاء Customer Groups ===")
2025-07-15 14:31:16,171 INFO ipython # All Customer Groups (Parent)
2025-07-15 14:31:16,171 INFO ipython if not frappe.db.exists("Customer Group", "All Customer Groups"):
        all_customer_groups = frappe.get_doc({
                "doctype": "Customer Group",
                        "customer_group_name": "All Customer Groups",
                                "is_group": 1
                                    })
                                        all_customer_groups.insert()
2025-07-15 14:31:16,171 INFO ipython     print("✓ All Customer Groups created")
2025-07-15 14:31:16,172 INFO ipython else:
        print("✓ All Customer Groups already exists")
2025-07-15 14:31:16,172 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,174 INFO ipython # إنشاء Individual Customer Group
2025-07-15 14:31:16,175 INFO ipython if not frappe.db.exists("Customer Group", "Individual"):
        individual_group = frappe.get_doc({
                "doctype": "Customer Group",
                        "customer_group_name": "Individual",
                                "parent_customer_group": "All Customer Groups"
                                    })
                                        individual_group.insert()
2025-07-15 14:31:16,175 INFO ipython     print("✓ Individual Customer Group created")
2025-07-15 14:31:16,177 INFO ipython else:
        print("✓ Individual Customer Group already exists")
2025-07-15 14:31:16,177 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,185 INFO ipython # إنشاء Company Customer Group
2025-07-15 14:31:16,188 INFO ipython company_group = frappe.get_doc({
    "doctype": "Customer Group",
        "customer_group_name": "Company",
            "parent_customer_group": "All Customer Groups"
            })
2025-07-15 14:31:16,188 INFO ipython company_group.insert(ignore_if_duplicate=True)
2025-07-15 14:31:16,189 INFO ipython print("✓ Company Customer Group created")
2025-07-15 14:31:16,189 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,189 INFO ipython # إنشاء Territories
2025-07-15 14:31:16,190 INFO ipython print("=== إنشاء Territories ===")
2025-07-15 14:31:16,190 INFO ipython # All Territories (Parent)
2025-07-15 14:31:16,190 INFO ipython all_territories = frappe.get_doc({
    "doctype": "Territory",
        "territory_name": "All Territories",
            "is_group": 1
            })
2025-07-15 14:31:16,190 INFO ipython all_territories.insert(ignore_if_duplicate=True)
2025-07-15 14:31:16,191 INFO ipython print("✓ All Territories created")
2025-07-15 14:31:16,191 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,191 INFO ipython # إنشاء Saudi Arabia Territory
2025-07-15 14:31:16,192 INFO ipython saudi_territory = frappe.get_doc({
    "doctype": "Territory",
        "territory_name": "Saudi Arabia",
            "parent_territory": "All Territories"
            })
2025-07-15 14:31:16,193 INFO ipython saudi_territory.insert(ignore_if_duplicate=True)
2025-07-15 14:31:16,193 INFO ipython print("✓ Saudi Arabia Territory created")
2025-07-15 14:31:16,201 INFO ipython # إنشاء Rest Of The World Territory
2025-07-15 14:31:16,201 INFO ipython rest_world = frappe.get_doc({
    "doctype": "Territory",
        "territory_name": "Rest Of The World",
            "parent_territory": "All Territories"
            })
2025-07-15 14:31:16,203 INFO ipython rest_world.insert(ignore_if_duplicate=True)
2025-07-15 14:31:16,203 INFO ipython print("✓ Rest Of The World Territory created")
2025-07-15 14:31:16,204 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,204 INFO ipython # اختبار إنشاء عميل مع البيانات الجديدة
2025-07-15 14:31:16,204 INFO ipython print("=== اختبار إنشاء عميل ===")
2025-07-15 14:31:16,204 INFO ipython customer_data = {
    "doctype": "Customer",
        "customer_name": "Test Customer Final",
            "customer_type": "Individual",
                "customer_group": "Individual",
                    "territory": "Saudi Arabia"
                    }
2025-07-15 14:31:16,205 INFO ipython customer = frappe.get_doc(customer_data)
2025-07-15 14:31:16,205 INFO ipython customer.insert()
2025-07-15 14:31:16,205 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,205 INFO ipython print(f"✅ Customer created successfully: {customer.name}")
2025-07-15 14:31:16,205 INFO ipython # حذف العميل التجريبي
2025-07-15 14:31:16,205 INFO ipython frappe.delete_doc("Customer", customer.name)
2025-07-15 14:31:16,206 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,206 INFO ipython print("✓ Test customer deleted")
2025-07-15 14:31:16,206 INFO ipython # التحقق من البيانات الموجودة
2025-07-15 14:31:16,206 INFO ipython print("=== التحقق من البيانات الموجودة ===")
2025-07-15 14:31:16,206 INFO ipython customer_groups = frappe.get_list("Customer Group", fields=["name", "is_group"])
2025-07-15 14:31:16,206 INFO ipython print(f"Customer Groups ({len(customer_groups)}):")
2025-07-15 14:31:16,207 INFO ipython for cg in customer_groups:
        print(f"  - {cg.name} (is_group: {cg.is_group})")
        
2025-07-15 14:31:16,207 INFO ipython territories = frappe.get_list("Territory", fields=["name", "is_group"])
2025-07-15 14:31:16,207 INFO ipython print(f"\nTerritories ({len(territories)}):")
2025-07-15 14:31:16,207 INFO ipython for t in territories:
        print(f"  - {t.name} (is_group: {t.is_group})")
        for t in territories:
                print(f"  - {t.name} (is_group: {t.is_group})")
                
2025-07-15 14:31:16,207 INFO ipython # اختبار إنشاء عميل مع البيانات الموجودة
2025-07-15 14:31:16,207 INFO ipython print("\n=== اختبار إنشاء عميل مع البيانات الموجودة ===")
2025-07-15 14:31:16,207 INFO ipython customer_data = {
    "doctype": "Customer",
        "customer_name": "Test Customer Arabic",
            "customer_type": "Individual",
                "customer_group": "فرد",  # استخدام القيمة العربية الموجودة
                    "territory": "Saudi Arabia"
                    }
2025-07-15 14:31:16,212 INFO ipython customer = frappe.get_doc(customer_data)
2025-07-15 14:31:16,212 INFO ipython customer.insert()
2025-07-15 14:31:16,213 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,213 INFO ipython print(f"✅ Customer created successfully: {customer.name}")
2025-07-15 14:31:16,213 INFO ipython # حذف العميل التجريبي
2025-07-15 14:31:16,215 INFO ipython frappe.delete_doc("Customer", customer.name)
2025-07-15 14:31:16,216 INFO ipython frappe.db.commit()
2025-07-15 14:31:16,217 INFO ipython print("✓ Test customer deleted")
2025-07-15 14:31:16,218 INFO ipython === session end ===
