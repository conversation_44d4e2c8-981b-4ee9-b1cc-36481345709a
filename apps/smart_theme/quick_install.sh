#!/bin/bash

# Smart Theme Quick Installation Script
# This script installs and configures Smart Theme for ERPNext

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
PAINT="🎨"
GEAR="⚙️"
CLEAN="🧹"

print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    SMART THEME INSTALLER                     ║"
    echo "║              Advanced ERPNext Theme v2.0.0                  ║"
    echo "║                   by newsmart.tech                          ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${BLUE}${ROCKET} $1${NC}"
}

print_success() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_error() {
    echo -e "${RED}${CROSS} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check if we're in a Frappe bench
    if [ ! -d "sites" ]; then
        print_error "Not in a Frappe bench directory"
        echo "Please run this script from your Frappe bench root directory"
        exit 1
    fi
    
    # Check if ERPNext is installed
    if [ ! -d "apps/erpnext" ]; then
        print_error "ERPNext not found"
        echo "Please install ERPNext first"
        exit 1
    fi
    
    # Check if smart_theme exists
    if [ ! -d "apps/smart_theme" ]; then
        print_error "Smart Theme app not found"
        echo "Please ensure smart_theme is in the apps directory"
        exit 1
    fi
    
    # Check if bench command exists
    if ! command -v bench &> /dev/null; then
        print_error "Bench command not found"
        echo "Please ensure Frappe bench is properly installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

get_site_name() {
    # List available sites
    sites=($(ls sites/ | grep -v common_site_config.json | grep -v assets))
    
    if [ ${#sites[@]} -eq 0 ]; then
        print_error "No sites found"
        exit 1
    fi
    
    if [ ${#sites[@]} -eq 1 ]; then
        SITE_NAME=${sites[0]}
        print_success "Found site: $SITE_NAME"
        return
    fi
    
    echo -e "${CYAN}📋 Available sites:${NC}"
    for i in "${!sites[@]}"; do
        echo "  $((i+1)). ${sites[$i]}"
    done
    
    while true; do
        read -p "Select site (1-${#sites[@]}): " choice
        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "${#sites[@]}" ]; then
            SITE_NAME=${sites[$((choice-1))]}
            break
        else
            print_error "Invalid choice. Please try again."
        fi
    done
}

install_theme() {
    print_step "Installing Smart Theme on site: $SITE_NAME"
    
    # Install app
    print_step "Installing Smart Theme app..."
    if bench --site "$SITE_NAME" install-app smart_theme; then
        print_success "Smart Theme app installed"
    else
        print_error "Failed to install Smart Theme app"
        exit 1
    fi
    
    # Run migrations
    print_step "Running database migrations..."
    if bench --site "$SITE_NAME" migrate; then
        print_success "Database migrations completed"
    else
        print_warning "Migrations completed with warnings"
    fi
    
    # Build assets
    print_step "Building theme assets..."
    if bench --site "$SITE_NAME" build; then
        print_success "Assets built successfully"
    else
        print_error "Failed to build assets"
        exit 1
    fi
    
    # Restart services
    print_step "Restarting services..."
    if bench restart; then
        print_success "Services restarted"
    else
        print_warning "Services restart completed with warnings"
    fi
}

verify_installation() {
    print_step "Verifying installation..."
    
    # Check if app is listed
    if bench --site "$SITE_NAME" list-apps | grep -q "smart_theme"; then
        print_success "Smart Theme app is installed"
    else
        print_error "Smart Theme app not found in installed apps"
        return 1
    fi
    
    # Check if assets are built
    if [ -d "sites/assets/smart_theme" ]; then
        print_success "Theme assets are built"
    else
        print_error "Theme assets not found"
        return 1
    fi
    
    return 0
}

show_success_message() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║${NC}${PAINT}           SMART THEME INSTALLATION COMPLETED!           ${PAINT}${GREEN}║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${CYAN}${CHECK} Smart Theme has been installed on site: ${YELLOW}$SITE_NAME${NC}"
    echo ""
    echo -e "${PURPLE}🎨 FEATURES AVAILABLE:${NC}"
    echo "• 6 Beautiful themes (Light, Dark, Ocean Blue, Forest Green, Royal Purple, Sunset Orange)"
    echo "• Advanced animations and micro-interactions"
    echo "• Responsive design for all devices"
    echo "• Custom color picker"
    echo "• Dark mode support"
    echo "• Performance optimized"
    echo ""
    echo -e "${BLUE}${ROCKET} NEXT STEPS:${NC}"
    echo "1. Open your ERPNext site in a web browser"
    echo "2. Look for the theme toggle button in the navbar"
    echo "3. Click to explore different themes"
    echo "4. Use the color picker to customize colors"
    echo "5. Enjoy your new beautiful interface!"
    echo ""
    echo -e "${YELLOW}⌨️  KEYBOARD SHORTCUTS:${NC}"
    echo "• Ctrl/Cmd + Shift + D: Toggle dark mode"
    echo "• Ctrl/Cmd + Shift + T: Open theme selector"
    echo "• Ctrl/Cmd + K: Quick search"
    echo "• Esc: Close dropdowns/modals"
    echo ""
    echo -e "${GREEN}📚 DOCUMENTATION:${NC}"
    echo "• README.md: Quick start guide"
    echo "• SMART_THEME_GUIDE.md: Comprehensive documentation"
    echo ""
    echo -e "${CYAN}🆘 SUPPORT:${NC}"
    echo "• GitHub Issues: Report bugs and request features"
    echo "• ERPNext Community: Get help from the community"
    echo ""
    echo -e "${PURPLE}Developed with ❤️  by newsmart.tech${NC}"
    echo ""
}

show_error_help() {
    echo ""
    echo -e "${RED}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${RED}║${NC}                    INSTALLATION FAILED                      ${RED}║${NC}"
    echo -e "${RED}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${YELLOW}🔧 TROUBLESHOOTING STEPS:${NC}"
    echo ""
    echo "1. Check if you have proper permissions:"
    echo "   sudo chown -R \$(whoami): ."
    echo ""
    echo "2. Try manual installation:"
    echo "   bench --site $SITE_NAME install-app smart_theme"
    echo "   bench --site $SITE_NAME migrate"
    echo "   bench --site $SITE_NAME build"
    echo "   bench restart"
    echo ""
    echo "3. Check logs for detailed errors:"
    echo "   tail -f logs/bench.log"
    echo ""
    echo "4. Clear cache and try again:"
    echo "   bench --site $SITE_NAME clear-cache"
    echo "   bench --site $SITE_NAME build --force"
    echo ""
    echo -e "${CYAN}If problems persist, please report the issue with:${NC}"
    echo "• Your ERPNext version"
    echo "• Error messages from logs"
    echo "• Steps to reproduce the issue"
    echo ""
}

main() {
    print_header
    
    # Check prerequisites
    check_prerequisites
    
    # Get site name
    get_site_name
    
    # Confirm installation
    echo ""
    echo -e "${CYAN}📋 Installation Summary:${NC}"
    echo "   Site: $SITE_NAME"
    echo "   Theme: Smart Theme v2.0.0"
    echo "   Features: 6 themes, animations, responsive design"
    echo ""
    
    read -p "❓ Proceed with installation? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Installation cancelled"
        exit 0
    fi
    
    # Install theme
    if install_theme; then
        echo ""
        if verify_installation; then
            show_success_message
        else
            print_warning "Installation completed but verification failed"
            echo "Please check manually and report any issues"
        fi
    else
        show_error_help
        exit 1
    fi
}

# Handle Ctrl+C
trap 'echo -e "\n${RED}Installation cancelled by user${NC}"; exit 1' INT

# Run main function
main "$@"
