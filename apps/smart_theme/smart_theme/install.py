"""
Smart Theme Installation Hooks
Functions to run before and after theme installation
"""

import frappe
from frappe import _
import os
import json

def before_install():
    """Run before Smart Theme installation"""
    print("🎨 Preparing Smart Theme installation...")
    
    # Check ERPNext version compatibility
    check_erpnext_compatibility()
    
    # Create necessary directories
    create_theme_directories()
    
    # Backup existing theme settings
    backup_existing_settings()
    
    print("✅ Pre-installation checks completed")

def after_install():
    """Run after Smart Theme installation"""
    print("🚀 Finalizing Smart Theme installation...")
    
    # Set default theme settings
    set_default_theme_settings()
    
    # Create sample data if needed
    create_sample_data()
    
    # Setup user permissions
    setup_permissions()
    
    # Create custom fields if needed
    create_custom_fields()
    
    # Clear cache and rebuild
    clear_cache_and_rebuild()
    
    print("✅ Smart Theme installation completed successfully!")
    print_success_message()

def check_erpnext_compatibility():
    """Check if ERPNext version is compatible"""
    try:
        import erpnext
        version = erpnext.__version__
        
        # Extract major version
        major_version = int(version.split('.')[0])
        
        if major_version < 13:
            frappe.throw(
                _("Smart Theme requires ERPNext v13 or higher. Current version: {0}").format(version),
                title=_("Incompatible Version")
            )
        
        print(f"✅ ERPNext version {version} is compatible")
        
    except ImportError:
        frappe.throw(
            _("ERPNext not found. Please install ERPNext first."),
            title=_("ERPNext Required")
        )

def create_theme_directories():
    """Create necessary directories for theme assets"""
    directories = [
        "public/files/smart_theme",
        "public/files/smart_theme/custom_themes",
        "public/files/smart_theme/user_uploads"
    ]
    
    for directory in directories:
        dir_path = frappe.get_site_path(directory)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"📁 Created directory: {directory}")

def backup_existing_settings():
    """Backup existing theme settings"""
    try:
        # Backup website settings
        website_settings = frappe.get_single("Website Settings")
        backup_data = {
            "website_theme": getattr(website_settings, "website_theme", ""),
            "brand_html": getattr(website_settings, "brand_html", ""),
            "favicon": getattr(website_settings, "favicon", ""),
            "banner_html": getattr(website_settings, "banner_html", "")
        }
        
        # Save backup
        backup_file = frappe.get_site_path("public/files/smart_theme/settings_backup.json")
        with open(backup_file, 'w') as f:
            json.dump(backup_data, f, indent=2)
        
        print("💾 Existing settings backed up")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not backup settings: {str(e)}")

def set_default_theme_settings():
    """Set default theme settings"""
    try:
        # Update Website Settings
        website_settings = frappe.get_single("Website Settings")
        
        # Set Smart Theme as default
        website_settings.website_theme = "smart_theme"
        
        # Set brand HTML with Smart Theme logo
        if not website_settings.brand_html:
            website_settings.brand_html = '''
            <img class="navbar-brand-img" 
                 src="/assets/smart_theme/logo/logoRect.png" 
                 alt="Smart Theme" 
                 style="max-height: 40px;">
            '''
        
        # Set favicon
        if not website_settings.favicon:
            website_settings.favicon = "/assets/smart_theme/favicon.ico"
        
        website_settings.save()
        
        print("⚙️  Default theme settings applied")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not set default settings: {str(e)}")

def create_sample_data():
    """Create sample data for demonstration"""
    try:
        # Create sample workspace if it doesn't exist
        if not frappe.db.exists("Workspace", "Smart Theme Demo"):
            workspace = frappe.get_doc({
                "doctype": "Workspace",
                "title": "Smart Theme Demo",
                "module": "Smart Theme",
                "icon": "palette",
                "is_standard": 0,
                "public": 1,
                "content": json.dumps([
                    {
                        "type": "header",
                        "data": {
                            "text": "Welcome to Smart Theme",
                            "level": 2
                        }
                    },
                    {
                        "type": "paragraph",
                        "data": {
                            "text": "Experience the beautiful and modern interface of Smart Theme for ERPNext."
                        }
                    },
                    {
                        "type": "card",
                        "data": {
                            "card_name": "Theme Features",
                            "cards": [
                                {
                                    "name": "Multiple Themes",
                                    "description": "6 beautiful pre-built themes"
                                },
                                {
                                    "name": "Custom Colors", 
                                    "description": "Personalize with your brand colors"
                                },
                                {
                                    "name": "Responsive Design",
                                    "description": "Works perfectly on all devices"
                                },
                                {
                                    "name": "Advanced Animations",
                                    "description": "Smooth and beautiful interactions"
                                }
                            ]
                        }
                    }
                ])
            })
            workspace.insert()
            print("📋 Sample workspace created")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not create sample data: {str(e)}")

def setup_permissions():
    """Setup permissions for Smart Theme"""
    try:
        # Ensure all users can access theme features
        roles = ["System Manager", "Administrator", "All"]
        
        for role in roles:
            if frappe.db.exists("Role", role):
                # Add any specific permissions here
                pass
        
        print("🔐 Permissions configured")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not setup permissions: {str(e)}")

def create_custom_fields():
    """Create custom fields for theme functionality"""
    try:
        # Add theme preference field to User doctype
        if not frappe.db.exists("Custom Field", {"dt": "User", "fieldname": "theme_preference"}):
            custom_field = frappe.get_doc({
                "doctype": "Custom Field",
                "dt": "User",
                "label": "Theme Preference",
                "fieldname": "theme_preference",
                "fieldtype": "Select",
                "options": "\nlight\ndark\nblue\ngreen\npurple\norange",
                "default": "light",
                "insert_after": "language",
                "description": "Choose your preferred Smart Theme"
            })
            custom_field.insert()
            print("🔧 Custom fields created")
        
        # Add custom colors field
        if not frappe.db.exists("Custom Field", {"dt": "User", "fieldname": "custom_theme_colors"}):
            custom_field = frappe.get_doc({
                "doctype": "Custom Field", 
                "dt": "User",
                "label": "Custom Theme Colors",
                "fieldname": "custom_theme_colors",
                "fieldtype": "JSON",
                "insert_after": "theme_preference",
                "description": "Custom color configuration for Smart Theme"
            })
            custom_field.insert()
            print("🎨 Custom color field created")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not create custom fields: {str(e)}")

def clear_cache_and_rebuild():
    """Clear cache and rebuild assets"""
    try:
        # Clear cache
        frappe.clear_cache()
        
        # Clear website cache
        from frappe.website.utils import clear_cache
        clear_cache()
        
        print("🧹 Cache cleared")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not clear cache: {str(e)}")

def print_success_message():
    """Print success message with instructions"""
    message = """
    
🎉 SMART THEME INSTALLATION SUCCESSFUL!

🎨 Features Available:
• 6 Beautiful themes (Light, Dark, Ocean Blue, Forest Green, Royal Purple, Sunset Orange)
• Advanced animations and micro-interactions  
• Responsive design for all devices
• Custom color picker
• Dark mode support
• Performance optimized

🚀 Next Steps:
1. Refresh your browser
2. Look for the theme toggle button in the navbar
3. Explore different themes and customize colors
4. Check out the Smart Theme Demo workspace

⌨️  Keyboard Shortcuts:
• Ctrl/Cmd + Shift + D: Toggle dark mode
• Ctrl/Cmd + Shift + T: Open theme selector

📚 Documentation:
• README.md: Quick start guide
• SMART_THEME_GUIDE.md: Comprehensive documentation

Enjoy your beautiful new interface! 🎨✨
    """
    print(message)

def before_uninstall():
    """Run before Smart Theme uninstallation"""
    print("🗑️  Preparing Smart Theme uninstallation...")
    
    # Backup current settings
    backup_current_settings()
    
    # Restore original settings
    restore_original_settings()

def after_uninstall():
    """Run after Smart Theme uninstallation"""
    print("🧹 Cleaning up Smart Theme...")
    
    # Remove custom fields
    remove_custom_fields()
    
    # Clean up files
    cleanup_files()
    
    # Clear cache
    frappe.clear_cache()
    
    print("✅ Smart Theme uninstalled successfully")

def backup_current_settings():
    """Backup current theme settings before uninstall"""
    try:
        # Get current user theme preferences
        users_with_themes = frappe.db.sql("""
            SELECT name, theme_preference, custom_theme_colors 
            FROM tabUser 
            WHERE theme_preference IS NOT NULL 
            OR custom_theme_colors IS NOT NULL
        """, as_dict=True)
        
        if users_with_themes:
            backup_file = frappe.get_site_path("public/files/smart_theme/user_themes_backup.json")
            with open(backup_file, 'w') as f:
                json.dump(users_with_themes, f, indent=2, default=str)
            
            print("💾 User theme preferences backed up")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not backup user settings: {str(e)}")

def restore_original_settings():
    """Restore original settings from backup"""
    try:
        backup_file = frappe.get_site_path("public/files/smart_theme/settings_backup.json")
        
        if os.path.exists(backup_file):
            with open(backup_file, 'r') as f:
                backup_data = json.load(f)
            
            # Restore Website Settings
            website_settings = frappe.get_single("Website Settings")
            for key, value in backup_data.items():
                if hasattr(website_settings, key):
                    setattr(website_settings, key, value)
            
            website_settings.save()
            print("🔄 Original settings restored")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not restore settings: {str(e)}")

def remove_custom_fields():
    """Remove custom fields created by Smart Theme"""
    try:
        custom_fields = [
            {"dt": "User", "fieldname": "theme_preference"},
            {"dt": "User", "fieldname": "custom_theme_colors"}
        ]
        
        for field in custom_fields:
            if frappe.db.exists("Custom Field", field):
                frappe.delete_doc("Custom Field", field)
        
        print("🔧 Custom fields removed")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not remove custom fields: {str(e)}")

def cleanup_files():
    """Clean up theme files"""
    try:
        import shutil
        
        theme_dir = frappe.get_site_path("public/files/smart_theme")
        if os.path.exists(theme_dir):
            shutil.rmtree(theme_dir)
            print("📁 Theme files cleaned up")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not clean up files: {str(e)}")
