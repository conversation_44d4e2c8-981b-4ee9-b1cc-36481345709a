"""
Smart Theme Utilities
Helper functions and Jinja methods for Smart Theme
"""

import frappe
from frappe import _
import json
import os

def jinja_methods():
    """Return Jinja methods for Smart Theme"""
    return {
        "get_theme_config": get_theme_config,
        "get_user_theme": get_user_theme,
        "get_theme_colors": get_theme_colors,
        "is_mobile": is_mobile,
        "get_app_version": get_app_version,
        "get_smart_theme_assets": get_smart_theme_assets
    }

def jinja_filters():
    """Return Jinja filters for Smart Theme"""
    return {
        "theme_color": theme_color_filter,
        "smart_format": smart_format_filter,
        "responsive_image": responsive_image_filter
    }

def get_theme_config():
    """Get theme configuration"""
    return {
        "version": "2.0.0",
        "themes": [
            {
                "id": "light",
                "name": _("Light"),
                "icon": "fa-sun",
                "primary": "#3b82f6",
                "secondary": "#6366f1",
                "accent": "#f59e0b"
            },
            {
                "id": "dark", 
                "name": _("Dark"),
                "icon": "fa-moon",
                "primary": "#3b82f6",
                "secondary": "#6366f1", 
                "accent": "#f59e0b"
            },
            {
                "id": "blue",
                "name": _("Ocean Blue"),
                "icon": "fa-water",
                "primary": "#0ea5e9",
                "secondary": "#0284c7",
                "accent": "#06b6d4"
            },
            {
                "id": "green",
                "name": _("Forest Green"), 
                "icon": "fa-leaf",
                "primary": "#059669",
                "secondary": "#047857",
                "accent": "#10b981"
            },
            {
                "id": "purple",
                "name": _("Royal Purple"),
                "icon": "fa-crown", 
                "primary": "#7c3aed",
                "secondary": "#6d28d9",
                "accent": "#8b5cf6"
            },
            {
                "id": "orange",
                "name": _("Sunset Orange"),
                "icon": "fa-fire",
                "primary": "#ea580c", 
                "secondary": "#dc2626",
                "accent": "#f97316"
            }
        ],
        "features": {
            "animations": True,
            "dark_mode": True,
            "custom_colors": True,
            "responsive": True,
            "accessibility": True
        }
    }

def get_user_theme():
    """Get current user's theme preference"""
    if frappe.session.user == "Guest":
        return "light"
    
    user_settings = frappe.get_doc("User", frappe.session.user)
    return getattr(user_settings, "theme_preference", "light")

def get_theme_colors(theme_id="light"):
    """Get colors for a specific theme"""
    themes = get_theme_config()["themes"]
    theme = next((t for t in themes if t["id"] == theme_id), themes[0])
    
    return {
        "primary": theme["primary"],
        "secondary": theme["secondary"], 
        "accent": theme["accent"]
    }

def is_mobile():
    """Check if the request is from a mobile device"""
    user_agent = frappe.request.headers.get("User-Agent", "").lower()
    mobile_keywords = ["mobile", "android", "iphone", "ipad", "tablet"]
    return any(keyword in user_agent for keyword in mobile_keywords)

def get_app_version():
    """Get Smart Theme app version"""
    try:
        app_path = frappe.get_app_path("smart_theme")
        hooks_path = os.path.join(app_path, "hooks.py")
        
        if os.path.exists(hooks_path):
            with open(hooks_path, 'r') as f:
                content = f.read()
                # Extract version from hooks.py
                for line in content.split('\n'):
                    if 'app_version' in line and '=' in line:
                        version = line.split('=')[1].strip().strip('"\'')
                        return version
    except Exception:
        pass
    
    return "2.0.0"

def get_smart_theme_assets():
    """Get list of Smart Theme assets"""
    return {
        "css": [
            "/assets/smart_theme/css/smart_theme.css",
            "/assets/smart_theme/css/animations.css", 
            "/assets/smart_theme/css/components.css",
            "/assets/smart_theme/css/responsive.css"
        ],
        "js": [
            "/assets/smart_theme/js/smart_theme.js",
            "/assets/smart_theme/js/animations.js",
            "/assets/smart_theme/js/theme_switcher.js"
        ],
        "icons": "/assets/smart_theme/icons.svg"
    }

def theme_color_filter(color_name, theme_id=None):
    """Jinja filter to get theme color"""
    if not theme_id:
        theme_id = get_user_theme()
    
    colors = get_theme_colors(theme_id)
    return colors.get(color_name, "#3b82f6")

def smart_format_filter(value, format_type="default"):
    """Smart formatting filter"""
    if format_type == "currency":
        return frappe.format_value(value, {"fieldtype": "Currency"})
    elif format_type == "date":
        return frappe.format_value(value, {"fieldtype": "Date"})
    elif format_type == "datetime":
        return frappe.format_value(value, {"fieldtype": "Datetime"})
    elif format_type == "number":
        return frappe.format_value(value, {"fieldtype": "Float", "precision": 2})
    else:
        return str(value)

def responsive_image_filter(image_url, sizes="100vw"):
    """Generate responsive image HTML"""
    if not image_url:
        return ""
    
    # Generate different sizes
    base_url = image_url.rsplit('.', 1)[0]
    ext = image_url.rsplit('.', 1)[1] if '.' in image_url else 'jpg'
    
    srcset = []
    for width in [320, 640, 768, 1024, 1280, 1920]:
        srcset.append(f"{base_url}-{width}w.{ext} {width}w")
    
    return f'<img src="{image_url}" srcset="{", ".join(srcset)}" sizes="{sizes}" loading="lazy" alt="">'

@frappe.whitelist()
def get_theme_settings():
    """API endpoint to get theme settings"""
    return {
        "config": get_theme_config(),
        "user_theme": get_user_theme(),
        "is_mobile": is_mobile(),
        "version": get_app_version()
    }

@frappe.whitelist()
def save_user_theme(theme_id):
    """API endpoint to save user theme preference"""
    if frappe.session.user == "Guest":
        return {"success": False, "message": "Please login to save theme preference"}
    
    try:
        user_doc = frappe.get_doc("User", frappe.session.user)
        user_doc.theme_preference = theme_id
        user_doc.save(ignore_permissions=True)
        
        return {"success": True, "message": "Theme preference saved"}
    except Exception as e:
        frappe.log_error(f"Error saving theme preference: {str(e)}")
        return {"success": False, "message": "Failed to save theme preference"}

@frappe.whitelist()
def get_dashboard_stats():
    """Get dashboard statistics for Smart Theme"""
    if not frappe.has_permission("Dashboard"):
        return {}
    
    try:
        stats = {}
        
        # Get basic counts
        if frappe.has_permission("Customer", "read"):
            stats["customers"] = frappe.db.count("Customer")
        
        if frappe.has_permission("Sales Invoice", "read"):
            stats["invoices"] = frappe.db.count("Sales Invoice")
            stats["revenue"] = frappe.db.sql("""
                SELECT SUM(grand_total) 
                FROM `tabSales Invoice` 
                WHERE docstatus = 1 
                AND posting_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            """)[0][0] or 0
        
        if frappe.has_permission("Item", "read"):
            stats["items"] = frappe.db.count("Item")
        
        return stats
    except Exception as e:
        frappe.log_error(f"Error getting dashboard stats: {str(e)}")
        return {}

def get_system_health():
    """Get system health information"""
    try:
        import psutil
        
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent
        }
    except ImportError:
        return {}
    except Exception as e:
        frappe.log_error(f"Error getting system health: {str(e)}")
        return {}

def optimize_theme_assets():
    """Optimize theme assets for better performance"""
    try:
        # This could include:
        # - Minifying CSS/JS files
        # - Compressing images
        # - Generating critical CSS
        # - Creating service worker cache manifest
        
        # For now, just return success
        return {"success": True, "message": "Assets optimized"}
    except Exception as e:
        frappe.log_error(f"Error optimizing assets: {str(e)}")
        return {"success": False, "message": "Failed to optimize assets"}

def validate_theme_config(config):
    """Validate theme configuration"""
    required_fields = ["id", "name", "primary", "secondary", "accent"]
    
    if not isinstance(config, dict):
        return False, "Config must be a dictionary"
    
    for field in required_fields:
        if field not in config:
            return False, f"Missing required field: {field}"
    
    # Validate color format
    color_fields = ["primary", "secondary", "accent"]
    for field in color_fields:
        color = config[field]
        if not isinstance(color, str) or not color.startswith("#") or len(color) != 7:
            return False, f"Invalid color format for {field}: {color}"
    
    return True, "Valid configuration"

def generate_theme_css(theme_config):
    """Generate CSS for a custom theme"""
    try:
        css_template = """
        :root[data-theme="{theme_id}"] {{
            --primary-color: {primary};
            --secondary-color: {secondary};
            --accent-color: {accent};
        }}
        """
        
        return css_template.format(
            theme_id=theme_config["id"],
            primary=theme_config["primary"],
            secondary=theme_config["secondary"],
            accent=theme_config["accent"]
        )
    except Exception as e:
        frappe.log_error(f"Error generating theme CSS: {str(e)}")
        return ""

@frappe.whitelist()
def create_custom_theme(theme_config):
    """Create a custom theme"""
    try:
        # Validate configuration
        is_valid, message = validate_theme_config(theme_config)
        if not is_valid:
            return {"success": False, "message": message}
        
        # Generate CSS
        css = generate_theme_css(theme_config)
        if not css:
            return {"success": False, "message": "Failed to generate CSS"}
        
        # Save to user settings or custom theme file
        # This would typically save to a custom themes table or file
        
        return {
            "success": True, 
            "message": "Custom theme created successfully",
            "css": css
        }
    except Exception as e:
        frappe.log_error(f"Error creating custom theme: {str(e)}")
        return {"success": False, "message": "Failed to create custom theme"}
