/*
 * Smart ERPNext Theme - Website Styles
 * Beautiful website styling for ERPNext portal
 * Version: 2.0.0
 */

/* ===== WEBSITE LAYOUT ===== */
.website-layout {
  font-family: var(--font-family-primary);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
}

/* ===== HERO SECTIONS ===== */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-white);
  padding: 120px 0 80px;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>') no-repeat bottom;
  background-size: cover;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-extrabold);
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.5rem);
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
  font-weight: var(--font-normal);
}

.hero-cta {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.hero-cta .btn {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  border-radius: var(--radius-xl);
  text-decoration: none;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
}

.hero-cta .btn-primary {
  background: var(--text-white);
  color: var(--primary-color);
}

.hero-cta .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: var(--primary-dark);
}

.hero-cta .btn-outline {
  background: transparent;
  color: var(--text-white);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-cta .btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--text-white);
}

/* ===== FEATURE SECTIONS ===== */
.features-section {
  padding: 100px 0;
  background: var(--bg-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.feature-card {
  background: var(--bg-card);
  padding: var(--spacing-xl);
  border-radius: var(--radius-2xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  font-size: 2rem;
  color: var(--text-white);
  box-shadow: var(--shadow-lg);
}

.feature-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.7;
}

/* ===== STATS SECTION ===== */
.stats-section {
  padding: 80px 0;
  background: var(--primary-gradient);
  color: var(--text-white);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.stat-number {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: var(--font-extrabold);
  display: block;
  margin-bottom: var(--spacing-sm);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: var(--text-lg);
  opacity: 0.9;
  font-weight: var(--font-medium);
}

/* ===== TESTIMONIALS ===== */
.testimonials-section {
  padding: 100px 0;
  background: var(--bg-primary);
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section-title {
  text-align: center;
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2xl);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.testimonial-card {
  background: var(--bg-card);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  position: relative;
  transition: all var(--transition-normal);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.testimonial-quote {
  font-size: var(--text-lg);
  line-height: 1.7;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  font-style: italic;
  position: relative;
}

.testimonial-quote::before {
  content: '"';
  font-size: 4rem;
  color: var(--primary-color);
  position: absolute;
  top: -20px;
  left: -10px;
  font-family: serif;
  opacity: 0.3;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
}

.author-info h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.author-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* ===== CTA SECTION ===== */
.cta-section {
  padding: 100px 0;
  background: var(--bg-secondary);
  text-align: center;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.cta-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.cta-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.7;
}

.cta-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* ===== FOOTER ===== */
.website-footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: 60px 0 30px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
  color: var(--text-white);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--spacing-md);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
  color: var(--text-white);
}

.footer-bottom {
  border-top: 1px solid var(--gray-700);
  padding-top: var(--spacing-lg);
  text-align: center;
  color: var(--gray-400);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .hero-section {
    padding: 80px 0 60px;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-cta .btn {
    width: 100%;
    max-width: 300px;
  }
  
  .features-section,
  .testimonials-section,
  .cta-section {
    padding: 60px 0;
  }
  
  .stats-section {
    padding: 60px 0;
  }
  
  .features-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .feature-card,
  .testimonial-card {
    padding: var(--spacing-lg);
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .testimonials-grid {
    gap: var(--spacing-lg);
  }
  
  .testimonial-card {
    padding: var(--spacing-md);
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation classes */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

.stagger-animation > * {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }
