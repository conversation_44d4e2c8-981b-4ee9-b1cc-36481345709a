/*
 * Smart ERPNext Theme - Main Stylesheet
 * Advanced comprehensive theme with modern design
 * Version: 2.0.0
 * Author: newsmart.tech
 */

/* ===== CSS VARIABLES ===== */
:root {
  /* Primary Colors */
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #93c5fd;
  --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  
  /* Secondary Colors */
  --secondary-color: #6366f1;
  --secondary-dark: #4338ca;
  --secondary-light: #a5b4fc;
  
  /* Accent Colors */
  --accent-color: #f59e0b;
  --accent-dark: #d97706;
  --accent-light: #fbbf24;
  
  /* Success/Error/Warning */
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #06b6d4;
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-dark: #0f172a;
  --bg-card: #ffffff;
  --bg-sidebar: #1e293b;
  
  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-white: #ffffff;
  --text-dark: #111827;
  
  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;
  
  /* Shadow Variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Typography */
  --font-family-primary: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --bg-sidebar: #0f172a;
  
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  
  --border-light: #334155;
  --border-medium: #475569;
  --border-dark: #64748b;
}

/* ===== FONT IMPORTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== LAYOUT IMPROVEMENTS ===== */
.layout-main {
  background: var(--bg-secondary);
  min-height: 100vh;
  transition: all var(--transition-normal);
}

.container {
  max-width: 100%;
  padding: 0 var(--spacing-md);
  margin: 0 auto;
}

/* ===== NAVBAR ENHANCEMENTS ===== */
.navbar {
  background: var(--primary-gradient) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.navbar-brand {
  font-weight: var(--font-bold);
  font-size: var(--text-xl);
  color: var(--text-white) !important;
  transition: all var(--transition-fast);
}

.navbar-brand:hover {
  transform: scale(1.05);
  color: var(--accent-light) !important;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: var(--font-medium);
  padding: var(--spacing-sm) var(--spacing-md) !important;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.navbar-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white) !important;
  transform: translateY(-1px);
}

.navbar-nav .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.navbar-nav .nav-link:hover::before {
  left: 100%;
}

/* ===== SIDEBAR IMPROVEMENTS ===== */
.layout-side-section {
  background: var(--bg-sidebar);
  border-right: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.sidebar-item {
  margin: var(--spacing-xs) 0;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.sidebar-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
}

.sidebar-item-label {
  font-family: var(--font-family-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
}

.sidebar-item:hover .sidebar-item-label {
  color: var(--primary-color);
  font-weight: var(--font-semibold);
}

.sidebar-item.selected {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-md);
}

.sidebar-item.selected .sidebar-item-label {
  color: var(--text-white);
  font-weight: var(--font-semibold);
}

/* ===== MAIN CONTENT AREA ===== */
.layout-main-section {
  background: var(--bg-primary);
  border-radius: var(--radius-xl) 0 0 0;
  box-shadow: var(--shadow-lg);
  min-height: calc(100vh - 60px);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.layout-main-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  z-index: 1;
}

.page-content {
  padding: var(--spacing-xl);
  position: relative;
  z-index: 2;
}

/* ===== TYPOGRAPHY ENHANCEMENTS ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  line-height: 1.3;
  transition: color var(--transition-normal);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-extrabold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  position: relative;
  display: inline-block;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
}

.section-head {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: var(--spacing-xl) 0 var(--spacing-md) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

.section-head::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--primary-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .layout-side-section {
    transform: translateX(-100%);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--z-modal);
    transition: transform var(--transition-normal);
  }
  
  .layout-side-section.show {
    transform: translateX(0);
  }
  
  .layout-main-section {
    border-radius: 0;
    margin-left: 0;
  }
  
  .page-content {
    padding: var(--spacing-md);
  }
  
  .page-title {
    font-size: var(--text-2xl);
  }
}

@media (max-width: 480px) {
  .page-content {
    padding: var(--spacing-sm);
  }
  
  .page-title {
    font-size: var(--text-xl);
  }
  
  .navbar-brand {
    font-size: var(--text-lg);
  }
}
