/*
 * Smart ERPNext Theme - Responsive Design
 * Mobile-first responsive design system
 * Version: 2.0.0
 */

/* ===== BREAKPOINTS ===== */
/* 
  xs: 0px - 575px (Mobile)
  sm: 576px - 767px (Mobile Large)
  md: 768px - 991px (Tablet)
  lg: 992px - 1199px (Desktop)
  xl: 1200px - 1399px (Large Desktop)
  xxl: 1400px+ (Extra Large Desktop)
*/

/* ===== BASE RESPONSIVE UTILITIES ===== */
/* إعادة تعيين الحاويات للحفاظ على العرض الأصلي */
.container-fluid {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  max-width: none !important;
}

.container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* ===== GRID SYSTEM ===== */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
  flex: 1;
  padding: 0 var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

/* Column Sizes */
.col-1 {
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.col-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.col-11 {
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

/* ===== MOBILE FIRST (XS - 0px+) ===== */
/* Base styles are mobile-first */

/* ===== SMALL DEVICES (SM - 576px+) ===== */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }

  /* Small device columns */
  .col-sm-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-sm-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-sm-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-sm-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-sm-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-sm-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-sm-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-sm-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Visibility utilities */
  .d-sm-none {
    display: none !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-flex {
    display: flex !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }
}

/* ===== MEDIUM DEVICES (MD - 768px+) ===== */
@media (min-width: 768px) {
  .container {
    max-width: none !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Tablet adjustments - الحفاظ على العرض الأصلي */
  .layout-side-section {
    /* لا نغير عرض الشريط الجانبي الأصلي */
  }

  .layout-main-section {
    /* لا نضيف margin-left إضافي */
    margin-left: 0 !important;
    border-radius: 0 !important;
    width: 100% !important;
  }

  /* Medium device columns */
  .col-md-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-md-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-md-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-md-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-md-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Card grid for tablets */
  .card-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }

  /* Form layouts */
  .form-row {
    display: flex;
    gap: var(--spacing-md);
  }

  .form-row .form-group {
    flex: 1;
  }

  /* Visibility utilities */
  .d-md-none {
    display: none !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-flex {
    display: flex !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }
}

/* ===== LARGE DEVICES (LG - 992px+) ===== */
@media (min-width: 992px) {
  .container {
    max-width: none !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Desktop optimizations - الحفاظ على التخطيط الأصلي */
  .layout-side-section {
    /* لا نغير عرض الشريط الجانبي الأصلي */
  }

  .layout-main-section {
    /* لا نضيف margin إضافي */
    margin-left: 0 !important;
    width: 100% !important;
  }

  /* Large device columns */
  .col-lg-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-lg-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-lg-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-lg-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-lg-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-lg-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Card grid for desktop */
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  /* Dashboard layouts */
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
  }

  /* Visibility utilities */
  .d-lg-none {
    display: none !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-flex {
    display: flex !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }
}

/* ===== EXTRA LARGE DEVICES (XL - 1200px+) ===== */
@media (min-width: 1200px) {
  .container {
    max-width: none !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Extra large device columns */
  .col-xl-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-xl-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-xl-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-xl-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-xl-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-xl-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-xl-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-xl-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Large desktop optimizations */
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .dashboard-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  /* Visibility utilities */
  .d-xl-none {
    display: none !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-flex {
    display: flex !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }
}

/* ===== EXTRA EXTRA LARGE DEVICES (XXL - 1400px+) ===== */
@media (min-width: 1400px) {
  .container {
    max-width: none !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Ultra-wide optimizations - الحفاظ على التخطيط الأصلي */
  .layout-side-section {
    /* لا نغير عرض الشريط الجانبي الأصلي */
  }

  .layout-main-section {
    /* لا نضيف margin إضافي */
    margin-left: 0 !important;
    width: 100% !important;
  }

  .card-grid {
    grid-template-columns: repeat(5, 1fr);
  }

  .dashboard-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

/* ===== MOBILE SPECIFIC STYLES ===== */
@media (max-width: 767px) {

  /* Mobile navigation */
  .mobile-nav-toggle {
    display: block;
    position: fixed;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: var(--z-fixed);
    background: var(--primary-color);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
  }

  /* Mobile sidebar overlay */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  /* Mobile forms */
  .form-row {
    flex-direction: column;
  }

  /* Mobile tables */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table {
    min-width: 600px;
  }

  /* Mobile cards */
  .card {
    margin-bottom: var(--spacing-md);
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--spacing-md);
  }

  /* Mobile buttons */
  .btn {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }

  .btn-group .btn {
    width: auto;
    margin-bottom: 0;
  }

  /* Mobile modals */
  .modal {
    width: 95%;
    margin: var(--spacing-md);
  }

  /* Mobile typography */
  .page-title {
    font-size: var(--text-xl);
    margin-bottom: var(--spacing-md);
  }

  .section-head {
    font-size: var(--text-lg);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .layout-side-section,
  .navbar,
  .btn,
  .no-print {
    display: none !important;
  }

  .layout-main-section {
    margin-left: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }

  .page-content {
    padding: 0 !important;
  }

  .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
    page-break-inside: avoid;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    page-break-after: avoid;
  }

  .table {
    border-collapse: collapse !important;
  }

  .table th,
  .table td {
    border: 1px solid #ddd !important;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-medium: #000000;
    --border-dark: #000000;
  }
}

/* Dark mode preference */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-sidebar: #0f172a;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    --border-light: #334155;
    --border-medium: #475569;
    --border-dark: #64748b;
  }
}