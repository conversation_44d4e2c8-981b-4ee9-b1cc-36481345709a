/*
 * Smart ERPNext Theme - Components
 * Enhanced UI components and widgets
 * Version: 2.0.0
 */

/* ===== BUTTONS ===== */
.btn {
  font-family: var(--font-family-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  box-shadow: var(--shadow-sm);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-white);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  color: var(--text-white);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-white);
}

.btn-secondary:hover {
  background: var(--secondary-dark);
  color: var(--text-white);
}

.btn-success {
  background: var(--success-color);
  color: var(--text-white);
}

.btn-warning {
  background: var(--warning-color);
  color: var(--text-white);
}

.btn-danger {
  background: var(--error-color);
  color: var(--text-white);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--text-white);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  box-shadow: none;
}

.btn-ghost:hover {
  background: var(--gray-100);
  color: var(--text-primary);
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--text-lg);
}

.btn-xl {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--text-xl);
}

/* ===== CARDS ===== */
.card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
  position: relative;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Card Variants */
.card-gradient {
  background: var(--primary-gradient);
  color: var(--text-white);
  border: none;
}

.card-gradient .card-header,
.card-gradient .card-footer {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.card-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== FORMS ===== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: var(--text-sm);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-family: var(--font-family-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: var(--border-medium);
}

.form-control.is-invalid {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* ===== TABLES ===== */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.table th {
  background: var(--primary-gradient);
  color: var(--text-white);
  font-weight: var(--font-semibold);
  padding: var(--spacing-md);
  text-align: left;
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.table tbody tr {
  transition: all var(--transition-fast);
}

.table tbody tr:hover {
  background: var(--bg-secondary);
  transform: scale(1.01);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ===== BADGES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-lg);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: var(--primary-color);
  color: var(--text-white);
}

.badge-secondary {
  background: var(--secondary-color);
  color: var(--text-white);
}

.badge-success {
  background: var(--success-color);
  color: var(--text-white);
}

.badge-warning {
  background: var(--warning-color);
  color: var(--text-white);
}

.badge-danger {
  background: var(--error-color);
  color: var(--text-white);
}

.badge-light {
  background: var(--gray-200);
  color: var(--text-primary);
}

/* ===== ALERTS ===== */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  margin-bottom: var(--spacing-md);
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: currentColor;
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--success-color);
  color: var(--success-color);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: var(--warning-color);
  color: var(--warning-color);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--error-color);
  color: var(--error-color);
}

.alert-info {
  background: rgba(6, 182, 212, 0.1);
  border-color: var(--info-color);
  color: var(--info-color);
}

/* ===== PROGRESS BARS ===== */
.progress {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: var(--radius-lg);
  transition: width var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

/* ===== TOOLTIPS ===== */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background: var(--gray-800);
  color: var(--text-white);
  text-align: center;
  border-radius: var(--radius-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-xs);
  position: absolute;
  z-index: var(--z-tooltip);
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.tooltip .tooltip-text::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--gray-800) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* ===== MODALS ===== */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform var(--transition-normal);
}

.modal-backdrop.show .modal {
  transform: scale(1);
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* ===== DROPDOWNS ===== */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: var(--bg-card);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: all var(--transition-fast);
}

.dropdown-toggle:hover {
  border-color: var(--primary-color);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
  margin-top: var(--spacing-xs);
}

.dropdown.show .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.dropdown-item:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-light);
  margin: var(--spacing-xs) 0;
}

/* ===== TABS ===== */
.nav-tabs {
  display: flex;
  border-bottom: 2px solid var(--border-light);
  margin-bottom: var(--spacing-lg);
}

.nav-tab {
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-weight: var(--font-medium);
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast);
  position: relative;
}

.nav-tab:hover {
  color: var(--primary-color);
}

.nav-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

/* ===== PAGINATION ===== */
.pagination {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  justify-content: center;
  margin: var(--spacing-lg) 0;
}

.page-link {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  background: var(--bg-card);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: var(--font-medium);
}

.page-link:hover {
  background: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.page-link.active {
  background: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.page-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}