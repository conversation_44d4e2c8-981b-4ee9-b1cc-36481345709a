/**
 * Smart ERPNext Theme - Website JavaScript
 * Enhanced website interactions and animations
 * Version: 2.0.0
 */

(function() {
    'use strict';

    const SmartWebsite = {
        // Initialize website features
        init: function() {
            this.setupScrollAnimations();
            this.setupCounterAnimations();
            this.setupSmoothScrolling();
            this.setupParallaxEffects();
            this.setupFormEnhancements();
            this.setupNavigationEffects();
            this.setupTestimonialSlider();
            this.setupLazyLoading();
            this.setupContactForm();
            this.setupSearchEnhancements();
        },

        // Setup scroll-triggered animations
        setupScrollAnimations: function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const scrollObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animated');
                        
                        // Stagger animations for children
                        if (entry.target.classList.contains('stagger-animation')) {
                            const children = entry.target.children;
                            Array.from(children).forEach((child, index) => {
                                setTimeout(() => {
                                    child.style.opacity = '1';
                                    child.style.transform = 'translateY(0)';
                                }, index * 100);
                            });
                        }
                        
                        scrollObserver.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.animate-on-scroll, .stagger-animation').forEach(el => {
                scrollObserver.observe(el);
            });

            // Auto-add animation classes to common elements
            document.querySelectorAll('.feature-card, .testimonial-card, .stat-item').forEach(el => {
                if (!el.classList.contains('animate-on-scroll')) {
                    el.classList.add('animate-on-scroll');
                    scrollObserver.observe(el);
                }
            });
        },

        // Setup counter animations
        setupCounterAnimations: function() {
            const counters = document.querySelectorAll('.stat-number[data-count]');
            
            const counterObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateCounter(entry.target);
                        counterObserver.unobserve(entry.target);
                    }
                });
            });

            counters.forEach(counter => {
                counterObserver.observe(counter);
            });
        },

        // Animate counter
        animateCounter: function(element) {
            const target = parseInt(element.dataset.count);
            const duration = parseInt(element.dataset.duration) || 2000;
            const suffix = element.dataset.suffix || '';
            const prefix = element.dataset.prefix || '';
            
            let current = 0;
            const increment = target / (duration / 16);
            
            const timer = setInterval(() => {
                current += increment;
                const value = Math.floor(current);
                element.textContent = prefix + value.toLocaleString() + suffix;
                
                if (current >= target) {
                    element.textContent = prefix + target.toLocaleString() + suffix;
                    clearInterval(timer);
                }
            }, 16);
        },

        // Setup smooth scrolling
        setupSmoothScrolling: function() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    
                    if (target) {
                        const headerOffset = 80;
                        const elementPosition = target.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        },

        // Setup parallax effects
        setupParallaxEffects: function() {
            const parallaxElements = document.querySelectorAll('.parallax, [data-parallax]');
            
            if (parallaxElements.length === 0) return;

            let ticking = false;

            const updateParallax = () => {
                const scrolled = window.pageYOffset;
                
                parallaxElements.forEach(element => {
                    const rate = element.dataset.parallax || 0.5;
                    const yPos = -(scrolled * rate);
                    element.style.transform = `translateY(${yPos}px)`;
                });
                
                ticking = false;
            };

            window.addEventListener('scroll', () => {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                }
            });
        },

        // Setup form enhancements
        setupFormEnhancements: function() {
            // Floating labels
            document.querySelectorAll('.form-floating input, .form-floating textarea').forEach(input => {
                const updateLabel = () => {
                    const label = input.nextElementSibling;
                    if (label && label.tagName === 'LABEL') {
                        if (input.value || input === document.activeElement) {
                            label.classList.add('floating');
                        } else {
                            label.classList.remove('floating');
                        }
                    }
                };

                input.addEventListener('focus', updateLabel);
                input.addEventListener('blur', updateLabel);
                input.addEventListener('input', updateLabel);
                
                // Initial check
                updateLabel();
            });

            // Form validation
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', (e) => {
                    if (!this.validateForm(form)) {
                        e.preventDefault();
                    }
                });
            });

            // Real-time validation
            document.querySelectorAll('input[required], textarea[required]').forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        },

        // Validate form
        validateForm: function(form) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');
            
            requiredFields.forEach(field => {
                if (!this.validateField(field)) {
                    isValid = false;
                }
            });
            
            return isValid;
        },

        // Validate field
        validateField: function(field) {
            const value = field.value.trim();
            let isValid = true;
            let message = '';

            // Required validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                message = 'This field is required';
            }

            // Email validation
            if (field.type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    message = 'Please enter a valid email address';
                }
            }

            // Phone validation
            if (field.type === 'tel' && value) {
                const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                if (!phoneRegex.test(value.replace(/\s/g, ''))) {
                    isValid = false;
                    message = 'Please enter a valid phone number';
                }
            }

            // Update field appearance
            if (isValid) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
                this.hideFieldError(field);
            } else {
                field.classList.remove('is-valid');
                field.classList.add('is-invalid');
                this.showFieldError(field, message);
            }

            return isValid;
        },

        // Show field error
        showFieldError: function(field, message) {
            let errorElement = field.parentNode.querySelector('.field-error');
            
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'field-error';
                errorElement.style.cssText = `
                    color: var(--error-color);
                    font-size: 0.875rem;
                    margin-top: 0.25rem;
                    display: block;
                `;
                field.parentNode.appendChild(errorElement);
            }
            
            errorElement.textContent = message;
        },

        // Hide field error
        hideFieldError: function(field) {
            const errorElement = field.parentNode.querySelector('.field-error');
            if (errorElement) {
                errorElement.remove();
            }
        },

        // Setup navigation effects
        setupNavigationEffects: function() {
            const navbar = document.querySelector('.navbar, .website-header');
            
            if (!navbar) return;

            let lastScrollY = window.scrollY;
            let ticking = false;

            const updateNavbar = () => {
                const scrollY = window.scrollY;
                
                if (scrollY > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }

                // Hide/show navbar on scroll
                if (scrollY > lastScrollY && scrollY > 200) {
                    navbar.classList.add('nav-hidden');
                } else {
                    navbar.classList.remove('nav-hidden');
                }

                lastScrollY = scrollY;
                ticking = false;
            };

            window.addEventListener('scroll', () => {
                if (!ticking) {
                    requestAnimationFrame(updateNavbar);
                    ticking = true;
                }
            });

            // Mobile menu toggle
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileToggle && mobileMenu) {
                mobileToggle.addEventListener('click', () => {
                    mobileMenu.classList.toggle('show');
                    mobileToggle.classList.toggle('active');
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!mobileMenu.contains(e.target) && !mobileToggle.contains(e.target)) {
                        mobileMenu.classList.remove('show');
                        mobileToggle.classList.remove('active');
                    }
                });
            }
        },

        // Setup testimonial slider
        setupTestimonialSlider: function() {
            const slider = document.querySelector('.testimonial-slider');
            if (!slider) return;

            const slides = slider.querySelectorAll('.testimonial-slide');
            const prevBtn = slider.querySelector('.slider-prev');
            const nextBtn = slider.querySelector('.slider-next');
            const indicators = slider.querySelector('.slider-indicators');

            if (slides.length === 0) return;

            let currentSlide = 0;
            const totalSlides = slides.length;

            // Create indicators
            if (indicators) {
                for (let i = 0; i < totalSlides; i++) {
                    const indicator = document.createElement('button');
                    indicator.className = i === 0 ? 'active' : '';
                    indicator.addEventListener('click', () => this.goToSlide(i));
                    indicators.appendChild(indicator);
                }
            }

            // Navigation functions
            const goToSlide = (index) => {
                slides[currentSlide].classList.remove('active');
                currentSlide = index;
                slides[currentSlide].classList.add('active');

                // Update indicators
                if (indicators) {
                    indicators.querySelectorAll('button').forEach((btn, i) => {
                        btn.classList.toggle('active', i === currentSlide);
                    });
                }
            };

            const nextSlide = () => {
                goToSlide((currentSlide + 1) % totalSlides);
            };

            const prevSlide = () => {
                goToSlide((currentSlide - 1 + totalSlides) % totalSlides);
            };

            // Event listeners
            if (nextBtn) nextBtn.addEventListener('click', nextSlide);
            if (prevBtn) prevBtn.addEventListener('click', prevSlide);

            // Auto-play
            setInterval(nextSlide, 5000);

            // Expose functions
            this.goToSlide = goToSlide;
        },

        // Setup lazy loading
        setupLazyLoading: function() {
            const lazyImages = document.querySelectorAll('img[data-src]');
            
            if (lazyImages.length === 0) return;

            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            lazyImages.forEach(img => {
                img.classList.add('lazy');
                imageObserver.observe(img);
            });
        },

        // Setup contact form
        setupContactForm: function() {
            const contactForm = document.querySelector('#contact-form, .contact-form');
            
            if (!contactForm) return;

            contactForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const submitBtn = contactForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                
                // Show loading state
                submitBtn.textContent = 'Sending...';
                submitBtn.disabled = true;
                
                try {
                    const formData = new FormData(contactForm);
                    const data = Object.fromEntries(formData);
                    
                    // Here you would typically send the data to your server
                    // For now, we'll simulate a successful submission
                    await this.simulateFormSubmission(data);
                    
                    // Show success message
                    this.showFormMessage('Thank you! Your message has been sent successfully.', 'success');
                    contactForm.reset();
                    
                } catch (error) {
                    // Show error message
                    this.showFormMessage('Sorry, there was an error sending your message. Please try again.', 'error');
                } finally {
                    // Reset button
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            });
        },

        // Simulate form submission
        simulateFormSubmission: function(data) {
            return new Promise((resolve) => {
                setTimeout(resolve, 1500); // Simulate network delay
            });
        },

        // Show form message
        showFormMessage: function(message, type) {
            const messageElement = document.createElement('div');
            messageElement.className = `form-message ${type}`;
            messageElement.textContent = message;
            messageElement.style.cssText = `
                padding: 1rem;
                margin: 1rem 0;
                border-radius: 0.5rem;
                font-weight: 500;
                ${type === 'success' ? 
                    'background: rgba(16, 185, 129, 0.1); color: #059669; border: 1px solid #10b981;' :
                    'background: rgba(239, 68, 68, 0.1); color: #dc2626; border: 1px solid #ef4444;'
                }
            `;

            const form = document.querySelector('#contact-form, .contact-form');
            form.insertBefore(messageElement, form.firstChild);

            // Remove message after 5 seconds
            setTimeout(() => {
                messageElement.remove();
            }, 5000);
        },

        // Setup search enhancements
        setupSearchEnhancements: function() {
            const searchInputs = document.querySelectorAll('.search-input, input[type="search"]');
            
            searchInputs.forEach(input => {
                let searchTimeout;
                
                input.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        this.performSearch(e.target.value);
                    }, 300);
                });
            });
        },

        // Perform search
        performSearch: function(query) {
            if (query.length < 2) return;
            
            // Here you would typically implement your search logic
            console.log('Searching for:', query);
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => SmartWebsite.init());
    } else {
        SmartWebsite.init();
    }

    // Expose SmartWebsite globally
    window.SmartWebsite = SmartWebsite;

})();
