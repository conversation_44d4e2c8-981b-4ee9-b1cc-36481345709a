/**
 * Smart ERPNext Theme - Main JavaScript
 * Enhanced interactions and functionality
 * Version: 2.0.0
 */

(function() {
    'use strict';

    // Theme configuration
    const SmartTheme = {
        version: '2.0.0',
        config: {
            animationDuration: 300,
            enableAnimations: true,
            enableSounds: false,
            autoSave: true,
            theme: 'auto' // 'light', 'dark', 'auto'
        },
        
        // Initialize theme
        init: function() {
            console.log('Smart Theme v' + this.version + ' initializing...');
            
            this.setupEventListeners();
            this.initializeAnimations();
            this.setupThemeToggle();
            this.enhanceNavigation();
            this.setupMobileMenu();
            this.initializeTooltips();
            this.setupFormEnhancements();
            this.initializeNotifications();
            this.setupKeyboardShortcuts();
            
            // Mark as initialized
            document.body.classList.add('smart-theme-loaded');
            
            console.log('Smart Theme initialized successfully!');
        },

        // Setup event listeners
        setupEventListeners: function() {
            // Page load animations
            document.addEventListener('DOMContentLoaded', () => {
                this.animatePageLoad();
            });

            // Window resize handler
            window.addEventListener('resize', this.debounce(() => {
                this.handleResize();
            }, 250));

            // Scroll handler for navbar effects
            window.addEventListener('scroll', this.throttle(() => {
                this.handleScroll();
            }, 16));

            // Click outside handler for dropdowns
            document.addEventListener('click', (e) => {
                this.handleClickOutside(e);
            });
        },

        // Initialize animations
        initializeAnimations: function() {
            if (!this.config.enableAnimations) return;

            // Intersection Observer for scroll animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in-up');
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.card, .form-group, .table, .alert').forEach(el => {
                observer.observe(el);
            });
        },

        // Setup theme toggle functionality
        setupThemeToggle: function() {
            // Create theme toggle button if it doesn't exist
            if (!document.querySelector('.theme-toggle')) {
                const themeToggle = document.createElement('button');
                themeToggle.className = 'theme-toggle btn btn-ghost';
                themeToggle.innerHTML = '<i class="fa fa-moon"></i>';
                themeToggle.title = 'Toggle Dark Mode';
                
                // Add to navbar
                const navbar = document.querySelector('.navbar-nav');
                if (navbar) {
                    const li = document.createElement('li');
                    li.className = 'nav-item';
                    li.appendChild(themeToggle);
                    navbar.appendChild(li);
                }
            }

            // Theme toggle event
            document.addEventListener('click', (e) => {
                if (e.target.closest('.theme-toggle')) {
                    this.toggleTheme();
                }
            });

            // Initialize theme based on preference
            this.initializeTheme();
        },

        // Initialize theme based on user preference
        initializeTheme: function() {
            const savedTheme = localStorage.getItem('smart-theme-mode');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            let theme = savedTheme || (prefersDark ? 'dark' : 'light');
            
            if (this.config.theme === 'auto') {
                theme = prefersDark ? 'dark' : 'light';
            } else {
                theme = this.config.theme;
            }
            
            this.setTheme(theme);
        },

        // Toggle between light and dark theme
        toggleTheme: function() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            this.setTheme(newTheme);
        },

        // Set theme
        setTheme: function(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('smart-theme-mode', theme);
            
            // Update theme toggle icon
            const themeToggle = document.querySelector('.theme-toggle i');
            if (themeToggle) {
                themeToggle.className = theme === 'dark' ? 'fa fa-sun' : 'fa fa-moon';
            }
            
            // Animate theme transition
            document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
        },

        // Enhance navigation
        enhanceNavigation: function() {
            // Add hover effects to sidebar items
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.classList.add('hover-lift');
                });
                
                item.addEventListener('mouseleave', function() {
                    this.classList.remove('hover-lift');
                });
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        },

        // Setup mobile menu
        setupMobileMenu: function() {
            // Create mobile menu toggle if it doesn't exist
            if (!document.querySelector('.mobile-nav-toggle')) {
                const mobileToggle = document.createElement('button');
                mobileToggle.className = 'mobile-nav-toggle d-md-none';
                mobileToggle.innerHTML = '<i class="fa fa-bars"></i>';
                mobileToggle.setAttribute('aria-label', 'Toggle Navigation');
                
                document.body.appendChild(mobileToggle);
            }

            // Mobile menu toggle event
            document.addEventListener('click', (e) => {
                if (e.target.closest('.mobile-nav-toggle')) {
                    this.toggleMobileMenu();
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                const sidebar = document.querySelector('.layout-side-section');
                const toggle = document.querySelector('.mobile-nav-toggle');
                
                if (sidebar && sidebar.classList.contains('show') && 
                    !sidebar.contains(e.target) && !toggle.contains(e.target)) {
                    this.closeMobileMenu();
                }
            });
        },

        // Toggle mobile menu
        toggleMobileMenu: function() {
            const sidebar = document.querySelector('.layout-side-section');
            const overlay = document.querySelector('.sidebar-overlay') || this.createSidebarOverlay();
            
            if (sidebar) {
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
                document.body.classList.toggle('mobile-menu-open');
            }
        },

        // Close mobile menu
        closeMobileMenu: function() {
            const sidebar = document.querySelector('.layout-side-section');
            const overlay = document.querySelector('.sidebar-overlay');
            
            if (sidebar) {
                sidebar.classList.remove('show');
                if (overlay) overlay.classList.remove('show');
                document.body.classList.remove('mobile-menu-open');
            }
        },

        // Create sidebar overlay for mobile
        createSidebarOverlay: function() {
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            overlay.addEventListener('click', () => this.closeMobileMenu());
            document.body.appendChild(overlay);
            return overlay;
        },

        // Initialize tooltips
        initializeTooltips: function() {
            // Simple tooltip implementation
            document.querySelectorAll('[data-tooltip]').forEach(element => {
                element.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'tooltip-popup';
                    tooltip.textContent = this.getAttribute('data-tooltip');
                    tooltip.style.cssText = `
                        position: absolute;
                        background: var(--gray-800);
                        color: white;
                        padding: 8px 12px;
                        border-radius: 6px;
                        font-size: 12px;
                        z-index: 1000;
                        pointer-events: none;
                        opacity: 0;
                        transition: opacity 0.2s;
                    `;
                    
                    document.body.appendChild(tooltip);
                    
                    const rect = this.getBoundingClientRect();
                    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
                    
                    setTimeout(() => tooltip.style.opacity = '1', 10);
                    
                    this._tooltip = tooltip;
                });
                
                element.addEventListener('mouseleave', function() {
                    if (this._tooltip) {
                        this._tooltip.remove();
                        this._tooltip = null;
                    }
                });
            });
        },

        // Setup form enhancements
        setupFormEnhancements: function() {
            // Auto-resize textareas
            document.querySelectorAll('textarea').forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });

            // Form validation styling
            document.querySelectorAll('input, select, textarea').forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.checkValidity()) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                });
            });
        },

        // Initialize notifications
        initializeNotifications: function() {
            // Create notification container
            if (!document.querySelector('.notification-container')) {
                const container = document.createElement('div');
                container.className = 'notification-container';
                container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1060;
                    max-width: 400px;
                `;
                document.body.appendChild(container);
            }
        },

        // Show notification
        showNotification: function(message, type = 'info', duration = 5000) {
            const container = document.querySelector('.notification-container');
            if (!container) return;

            const notification = document.createElement('div');
            notification.className = `alert alert-${type} animate-slide-in-right`;
            notification.style.cssText = `
                margin-bottom: 10px;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            `;
            notification.innerHTML = `
                <span>${message}</span>
                <button type="button" class="btn-close" style="position: absolute; top: 8px; right: 8px; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
            `;

            // Auto-remove after duration
            const timer = setTimeout(() => {
                this.removeNotification(notification);
            }, duration);

            // Manual close
            notification.querySelector('.btn-close').addEventListener('click', () => {
                clearTimeout(timer);
                this.removeNotification(notification);
            });

            container.appendChild(notification);
        },

        // Remove notification
        removeNotification: function(notification) {
            notification.classList.add('animate-slide-out-right');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        },

        // Setup keyboard shortcuts
        setupKeyboardShortcuts: function() {
            document.addEventListener('keydown', (e) => {
                // Ctrl/Cmd + K for search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    this.focusSearch();
                }
                
                // Ctrl/Cmd + Shift + D for dark mode toggle
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                    e.preventDefault();
                    this.toggleTheme();
                }
                
                // Escape to close modals/dropdowns
                if (e.key === 'Escape') {
                    this.closeAllDropdowns();
                    this.closeMobileMenu();
                }
            });
        },

        // Focus search input
        focusSearch: function() {
            const searchInput = document.querySelector('input[type="search"], .search-input, #navbar-search');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        },

        // Close all dropdowns
        closeAllDropdowns: function() {
            document.querySelectorAll('.dropdown.show').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        },

        // Handle page load animation
        animatePageLoad: function() {
            document.body.classList.add('page-loaded');
            
            // Stagger animation for main elements
            const elements = document.querySelectorAll('.layout-main-section > *');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.classList.add('animate-fade-in-up');
                }, index * 100);
            });
        },

        // Handle window resize
        handleResize: function() {
            // Close mobile menu on resize to desktop
            if (window.innerWidth >= 768) {
                this.closeMobileMenu();
            }
        },

        // Handle scroll events
        handleScroll: function() {
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            }
        },

        // Handle click outside
        handleClickOutside: function(e) {
            // Close dropdowns when clicking outside
            if (!e.target.closest('.dropdown')) {
                this.closeAllDropdowns();
            }
        },

        // Utility: Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Utility: Throttle function
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => SmartTheme.init());
    } else {
        SmartTheme.init();
    }

    // Expose SmartTheme globally
    window.SmartTheme = SmartTheme;

})();
