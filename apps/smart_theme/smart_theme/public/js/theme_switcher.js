/**
 * Smart ERPNext Theme - Theme Switcher
 * Advanced theme switching with multiple themes and customization
 * Version: 2.0.0
 */

(function() {
    'use strict';

    const ThemeSwitcher = {
        // Available themes
        themes: {
            light: {
                name: 'Light',
                icon: 'fa-sun',
                colors: {
                    primary: '#3b82f6',
                    secondary: '#6366f1',
                    accent: '#f59e0b',
                    background: '#ffffff',
                    surface: '#f8fafc',
                    text: '#1f2937'
                }
            },
            dark: {
                name: 'Dark',
                icon: 'fa-moon',
                colors: {
                    primary: '#3b82f6',
                    secondary: '#6366f1',
                    accent: '#f59e0b',
                    background: '#0f172a',
                    surface: '#1e293b',
                    text: '#f8fafc'
                }
            },
            blue: {
                name: 'Ocean Blue',
                icon: 'fa-water',
                colors: {
                    primary: '#0ea5e9',
                    secondary: '#0284c7',
                    accent: '#06b6d4',
                    background: '#f0f9ff',
                    surface: '#e0f2fe',
                    text: '#0c4a6e'
                }
            },
            green: {
                name: '<PERSON> Green',
                icon: 'fa-leaf',
                colors: {
                    primary: '#059669',
                    secondary: '#047857',
                    accent: '#10b981',
                    background: '#f0fdf4',
                    surface: '#dcfce7',
                    text: '#064e3b'
                }
            },
            purple: {
                name: 'Royal Purple',
                icon: 'fa-crown',
                colors: {
                    primary: '#7c3aed',
                    secondary: '#6d28d9',
                    accent: '#8b5cf6',
                    background: '#faf5ff',
                    surface: '#f3e8ff',
                    text: '#581c87'
                }
            },
            orange: {
                name: 'Sunset Orange',
                icon: 'fa-fire',
                colors: {
                    primary: '#ea580c',
                    secondary: '#dc2626',
                    accent: '#f97316',
                    background: '#fff7ed',
                    surface: '#fed7aa',
                    text: '#9a3412'
                }
            }
        },

        // Current theme
        currentTheme: 'light',

        // Custom colors
        customColors: {},

        // Initialize theme switcher
        init: function() {
            this.loadSavedTheme();
            this.createThemeSwitcher();
            this.setupEventListeners();
            this.applyTheme(this.currentTheme);
        },

        // Load saved theme from localStorage
        loadSavedTheme: function() {
            const saved = localStorage.getItem('smart-theme-current');
            const customColors = localStorage.getItem('smart-theme-custom-colors');
            
            if (saved && this.themes[saved]) {
                this.currentTheme = saved;
            }
            
            if (customColors) {
                try {
                    this.customColors = JSON.parse(customColors);
                } catch (e) {
                    console.warn('Failed to parse custom colors:', e);
                }
            }
        },

        // Create theme switcher UI
        createThemeSwitcher: function() {
            // Create theme switcher container
            const switcher = document.createElement('div');
            switcher.className = 'theme-switcher-container';
            switcher.innerHTML = `
                <div class="theme-switcher">
                    <button class="theme-toggle-btn" title="Change Theme">
                        <i class="fa ${this.themes[this.currentTheme].icon}"></i>
                    </button>
                    <div class="theme-dropdown">
                        <div class="theme-dropdown-header">
                            <h4>Choose Theme</h4>
                            <button class="theme-close-btn">&times;</button>
                        </div>
                        <div class="theme-options">
                            ${this.generateThemeOptions()}
                        </div>
                        <div class="theme-customizer">
                            <h5>Custom Colors</h5>
                            <div class="color-inputs">
                                <div class="color-input-group">
                                    <label>Primary</label>
                                    <input type="color" id="primary-color" value="${this.getCurrentColor('primary')}">
                                </div>
                                <div class="color-input-group">
                                    <label>Secondary</label>
                                    <input type="color" id="secondary-color" value="${this.getCurrentColor('secondary')}">
                                </div>
                                <div class="color-input-group">
                                    <label>Accent</label>
                                    <input type="color" id="accent-color" value="${this.getCurrentColor('accent')}">
                                </div>
                            </div>
                            <div class="theme-actions">
                                <button class="btn btn-sm btn-primary" id="apply-custom">Apply Custom</button>
                                <button class="btn btn-sm btn-secondary" id="reset-theme">Reset</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add styles
            this.addThemeSwitcherStyles();

            // Add to navbar
            const navbar = document.querySelector('.navbar-nav');
            if (navbar) {
                const li = document.createElement('li');
                li.className = 'nav-item';
                li.appendChild(switcher);
                navbar.appendChild(li);
            } else {
                // Fallback: add to body
                document.body.appendChild(switcher);
            }
        },

        // Generate theme options HTML
        generateThemeOptions: function() {
            return Object.keys(this.themes).map(themeKey => {
                const theme = this.themes[themeKey];
                const isActive = themeKey === this.currentTheme ? 'active' : '';
                
                return `
                    <div class="theme-option ${isActive}" data-theme="${themeKey}">
                        <div class="theme-preview" style="background: ${theme.colors.primary};">
                            <i class="fa ${theme.icon}"></i>
                        </div>
                        <span class="theme-name">${theme.name}</span>
                    </div>
                `;
            }).join('');
        },

        // Add theme switcher styles
        addThemeSwitcherStyles: function() {
            if (document.querySelector('#theme-switcher-styles')) return;

            const style = document.createElement('style');
            style.id = 'theme-switcher-styles';
            style.textContent = `
                .theme-switcher-container {
                    position: relative;
                }
                
                .theme-toggle-btn {
                    background: none;
                    border: none;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 1.1em;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                
                .theme-toggle-btn:hover {
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                }
                
                .theme-dropdown {
                    position: absolute;
                    top: 100%;
                    right: 0;
                    width: 320px;
                    background: var(--bg-card);
                    border: 1px solid var(--border-light);
                    border-radius: 12px;
                    box-shadow: var(--shadow-xl);
                    z-index: 1000;
                    opacity: 0;
                    visibility: hidden;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                    margin-top: 8px;
                }
                
                .theme-dropdown.show {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(0);
                }
                
                .theme-dropdown-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 16px 20px;
                    border-bottom: 1px solid var(--border-light);
                }
                
                .theme-dropdown-header h4 {
                    margin: 0;
                    font-size: 1.1em;
                    color: var(--text-primary);
                }
                
                .theme-close-btn {
                    background: none;
                    border: none;
                    font-size: 1.5em;
                    color: var(--text-secondary);
                    cursor: pointer;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }
                
                .theme-close-btn:hover {
                    background: var(--bg-secondary);
                    color: var(--text-primary);
                }
                
                .theme-options {
                    padding: 16px 20px;
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 12px;
                }
                
                .theme-option {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;
                    padding: 12px 8px;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    border: 2px solid transparent;
                }
                
                .theme-option:hover {
                    background: var(--bg-secondary);
                }
                
                .theme-option.active {
                    border-color: var(--primary-color);
                    background: rgba(59, 130, 246, 0.1);
                }
                
                .theme-preview {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.2em;
                }
                
                .theme-name {
                    font-size: 0.85em;
                    color: var(--text-secondary);
                    text-align: center;
                    font-weight: 500;
                }
                
                .theme-customizer {
                    padding: 16px 20px;
                    border-top: 1px solid var(--border-light);
                }
                
                .theme-customizer h5 {
                    margin: 0 0 12px 0;
                    font-size: 1em;
                    color: var(--text-primary);
                }
                
                .color-inputs {
                    display: flex;
                    gap: 12px;
                    margin-bottom: 16px;
                }
                
                .color-input-group {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }
                
                .color-input-group label {
                    font-size: 0.8em;
                    color: var(--text-secondary);
                    font-weight: 500;
                }
                
                .color-input-group input[type="color"] {
                    width: 100%;
                    height: 32px;
                    border: 1px solid var(--border-light);
                    border-radius: 6px;
                    cursor: pointer;
                    background: none;
                }
                
                .theme-actions {
                    display: flex;
                    gap: 8px;
                }
                
                .theme-actions .btn {
                    flex: 1;
                    padding: 8px 12px;
                    font-size: 0.85em;
                }
                
                @media (max-width: 768px) {
                    .theme-dropdown {
                        width: 280px;
                        right: -20px;
                    }
                    
                    .theme-options {
                        grid-template-columns: repeat(2, 1fr);
                    }
                    
                    .color-inputs {
                        flex-direction: column;
                    }
                }
            `;
            document.head.appendChild(style);
        },

        // Setup event listeners
        setupEventListeners: function() {
            // Toggle dropdown
            document.addEventListener('click', (e) => {
                const toggleBtn = e.target.closest('.theme-toggle-btn');
                const dropdown = document.querySelector('.theme-dropdown');
                
                if (toggleBtn) {
                    e.stopPropagation();
                    dropdown.classList.toggle('show');
                } else if (!e.target.closest('.theme-dropdown')) {
                    dropdown.classList.remove('show');
                }
            });

            // Close button
            document.addEventListener('click', (e) => {
                if (e.target.closest('.theme-close-btn')) {
                    document.querySelector('.theme-dropdown').classList.remove('show');
                }
            });

            // Theme selection
            document.addEventListener('click', (e) => {
                const themeOption = e.target.closest('.theme-option');
                if (themeOption) {
                    const theme = themeOption.dataset.theme;
                    this.switchTheme(theme);
                }
            });

            // Custom color inputs
            document.addEventListener('input', (e) => {
                if (e.target.type === 'color') {
                    this.updateCustomColor(e.target.id, e.target.value);
                }
            });

            // Apply custom theme
            document.addEventListener('click', (e) => {
                if (e.target.id === 'apply-custom') {
                    this.applyCustomTheme();
                }
            });

            // Reset theme
            document.addEventListener('click', (e) => {
                if (e.target.id === 'reset-theme') {
                    this.resetTheme();
                }
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                // Ctrl/Cmd + Shift + T for theme switcher
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                    e.preventDefault();
                    document.querySelector('.theme-dropdown').classList.toggle('show');
                }
            });
        },

        // Switch theme
        switchTheme: function(themeKey) {
            if (!this.themes[themeKey]) return;

            this.currentTheme = themeKey;
            this.applyTheme(themeKey);
            this.updateUI();
            this.saveTheme();
        },

        // Apply theme
        applyTheme: function(themeKey) {
            const theme = this.themes[themeKey];
            if (!theme) return;

            const root = document.documentElement;
            
            // Apply theme colors
            Object.keys(theme.colors).forEach(colorKey => {
                const cssVar = this.getCSSVariableName(colorKey);
                root.style.setProperty(cssVar, theme.colors[colorKey]);
            });

            // Set theme attribute
            root.setAttribute('data-theme', themeKey);

            // Apply custom colors if any
            if (Object.keys(this.customColors).length > 0) {
                Object.keys(this.customColors).forEach(colorKey => {
                    const cssVar = this.getCSSVariableName(colorKey);
                    root.style.setProperty(cssVar, this.customColors[colorKey]);
                });
            }

            // Trigger theme change event
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: themeKey, colors: theme.colors }
            }));
        },

        // Update UI after theme change
        updateUI: function() {
            // Update toggle button icon
            const toggleBtn = document.querySelector('.theme-toggle-btn i');
            if (toggleBtn) {
                toggleBtn.className = `fa ${this.themes[this.currentTheme].icon}`;
            }

            // Update active theme option
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
                if (option.dataset.theme === this.currentTheme) {
                    option.classList.add('active');
                }
            });

            // Update color inputs
            this.updateColorInputs();
        },

        // Update color inputs
        updateColorInputs: function() {
            const primaryInput = document.querySelector('#primary-color');
            const secondaryInput = document.querySelector('#secondary-color');
            const accentInput = document.querySelector('#accent-color');

            if (primaryInput) primaryInput.value = this.getCurrentColor('primary');
            if (secondaryInput) secondaryInput.value = this.getCurrentColor('secondary');
            if (accentInput) accentInput.value = this.getCurrentColor('accent');
        },

        // Get current color
        getCurrentColor: function(colorKey) {
            return this.customColors[colorKey] || this.themes[this.currentTheme].colors[colorKey];
        },

        // Update custom color
        updateCustomColor: function(inputId, color) {
            const colorKey = inputId.replace('-color', '');
            this.customColors[colorKey] = color;
            
            // Apply immediately
            const cssVar = this.getCSSVariableName(colorKey);
            document.documentElement.style.setProperty(cssVar, color);
        },

        // Apply custom theme
        applyCustomTheme: function() {
            this.saveCustomColors();
            this.applyTheme(this.currentTheme);
            
            // Show success message
            if (window.SmartTheme && window.SmartTheme.showNotification) {
                window.SmartTheme.showNotification('Custom theme applied successfully!', 'success');
            }
        },

        // Reset theme
        resetTheme: function() {
            this.customColors = {};
            localStorage.removeItem('smart-theme-custom-colors');
            this.applyTheme(this.currentTheme);
            this.updateColorInputs();
            
            // Show success message
            if (window.SmartTheme && window.SmartTheme.showNotification) {
                window.SmartTheme.showNotification('Theme reset to default!', 'info');
            }
        },

        // Get CSS variable name
        getCSSVariableName: function(colorKey) {
            const mapping = {
                primary: '--primary-color',
                secondary: '--secondary-color',
                accent: '--accent-color',
                background: '--bg-primary',
                surface: '--bg-secondary',
                text: '--text-primary'
            };
            return mapping[colorKey] || `--${colorKey}-color`;
        },

        // Save theme to localStorage
        saveTheme: function() {
            localStorage.setItem('smart-theme-current', this.currentTheme);
        },

        // Save custom colors
        saveCustomColors: function() {
            localStorage.setItem('smart-theme-custom-colors', JSON.stringify(this.customColors));
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => ThemeSwitcher.init());
    } else {
        ThemeSwitcher.init();
    }

    // Expose ThemeSwitcher globally
    window.ThemeSwitcher = ThemeSwitcher;

})();
