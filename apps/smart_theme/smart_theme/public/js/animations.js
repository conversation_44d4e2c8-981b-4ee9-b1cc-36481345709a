/**
 * Smart ERPNext Theme - Advanced Animations
 * Beautiful animations and micro-interactions
 * Version: 2.0.0
 */

(function() {
    'use strict';

    const SmartAnimations = {
        // Animation configuration
        config: {
            duration: {
                fast: 200,
                normal: 300,
                slow: 500
            },
            easing: {
                ease: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
                easeIn: 'cubic-bezier(0.42, 0, 1, 1)',
                easeOut: 'cubic-bezier(0, 0, 0.58, 1)',
                easeInOut: 'cubic-bezier(0.42, 0, 0.58, 1)',
                bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
            }
        },

        // Initialize animations
        init: function() {
            this.setupScrollAnimations();
            this.setupHoverEffects();
            this.setupLoadingAnimations();
            this.setupFormAnimations();
            this.setupButtonAnimations();
            this.setupCardAnimations();
            this.setupModalAnimations();
            this.setupCounterAnimations();
            this.setupParallaxEffects();
        },

        // Setup scroll-triggered animations
        setupScrollAnimations: function() {
            const observerOptions = {
                threshold: [0, 0.25, 0.5, 0.75, 1],
                rootMargin: '0px 0px -100px 0px'
            };

            const scrollObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const element = entry.target;
                    const animationType = element.dataset.scrollAnimation || 'fadeInUp';
                    const delay = parseInt(element.dataset.animationDelay) || 0;

                    if (entry.isIntersecting && entry.intersectionRatio > 0.25) {
                        setTimeout(() => {
                            element.classList.add('animate-' + animationType);
                            element.classList.add('animation-visible');
                        }, delay);
                        scrollObserver.unobserve(element);
                    }
                });
            }, observerOptions);

            // Observe elements with scroll animations
            document.querySelectorAll('[data-scroll-animation]').forEach(el => {
                scrollObserver.observe(el);
            });

            // Auto-detect elements for animation
            document.querySelectorAll('.card, .alert, .form-section, .stats-card').forEach(el => {
                if (!el.dataset.scrollAnimation) {
                    el.dataset.scrollAnimation = 'fadeInUp';
                    scrollObserver.observe(el);
                }
            });
        },

        // Setup hover effects
        setupHoverEffects: function() {
            // Magnetic effect for buttons
            document.querySelectorAll('.btn-magnetic').forEach(btn => {
                btn.addEventListener('mousemove', (e) => {
                    const rect = btn.getBoundingClientRect();
                    const x = e.clientX - rect.left - rect.width / 2;
                    const y = e.clientY - rect.top - rect.height / 2;
                    
                    btn.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
                });

                btn.addEventListener('mouseleave', () => {
                    btn.style.transform = 'translate(0, 0)';
                });
            });

            // Tilt effect for cards
            document.querySelectorAll('.card-tilt').forEach(card => {
                card.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;
                    
                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`;
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)';
                });
            });

            // Ripple effect
            document.querySelectorAll('.btn-ripple, .ripple').forEach(element => {
                element.addEventListener('click', this.createRipple);
            });
        },

        // Create ripple effect
        createRipple: function(e) {
            const button = e.currentTarget;
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            const ripple = document.createElement('span');
            ripple.className = 'ripple-effect';
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple-animation 0.6s ease-out;
                pointer-events: none;
            `;

            // Add ripple styles if not exists
            if (!document.querySelector('#ripple-styles')) {
                const style = document.createElement('style');
                style.id = 'ripple-styles';
                style.textContent = `
                    @keyframes ripple-animation {
                        to {
                            transform: scale(2);
                            opacity: 0;
                        }
                    }
                    .btn-ripple, .ripple {
                        position: relative;
                        overflow: hidden;
                    }
                `;
                document.head.appendChild(style);
            }

            button.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        },

        // Setup loading animations
        setupLoadingAnimations: function() {
            // Skeleton loading
            this.createSkeletonLoader();
            
            // Progress bar animations
            document.querySelectorAll('.progress-bar').forEach(bar => {
                const width = bar.style.width || bar.getAttribute('data-width') || '0%';
                bar.style.width = '0%';
                
                setTimeout(() => {
                    bar.style.transition = 'width 1s ease-out';
                    bar.style.width = width;
                }, 100);
            });

            // Loading dots
            this.createLoadingDots();
        },

        // Create skeleton loader
        createSkeletonLoader: function() {
            const style = document.createElement('style');
            style.textContent = `
                .skeleton {
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-loading 1.5s infinite;
                }
                
                @keyframes skeleton-loading {
                    0% { background-position: 200% 0; }
                    100% { background-position: -200% 0; }
                }
                
                .skeleton-text {
                    height: 1em;
                    margin-bottom: 0.5em;
                    border-radius: 4px;
                }
                
                .skeleton-avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                }
                
                .skeleton-button {
                    height: 40px;
                    width: 120px;
                    border-radius: 6px;
                }
            `;
            document.head.appendChild(style);
        },

        // Create loading dots
        createLoadingDots: function() {
            const style = document.createElement('style');
            style.textContent = `
                .loading-dots {
                    display: inline-block;
                    position: relative;
                    width: 80px;
                    height: 80px;
                }
                
                .loading-dots div {
                    position: absolute;
                    top: 33px;
                    width: 13px;
                    height: 13px;
                    border-radius: 50%;
                    background: var(--primary-color);
                    animation-timing-function: cubic-bezier(0, 1, 1, 0);
                }
                
                .loading-dots div:nth-child(1) {
                    left: 8px;
                    animation: loading-dots1 0.6s infinite;
                }
                
                .loading-dots div:nth-child(2) {
                    left: 8px;
                    animation: loading-dots2 0.6s infinite;
                }
                
                .loading-dots div:nth-child(3) {
                    left: 32px;
                    animation: loading-dots2 0.6s infinite;
                }
                
                .loading-dots div:nth-child(4) {
                    left: 56px;
                    animation: loading-dots3 0.6s infinite;
                }
                
                @keyframes loading-dots1 {
                    0% { transform: scale(0); }
                    100% { transform: scale(1); }
                }
                
                @keyframes loading-dots3 {
                    0% { transform: scale(1); }
                    100% { transform: scale(0); }
                }
                
                @keyframes loading-dots2 {
                    0% { transform: translate(0, 0); }
                    100% { transform: translate(24px, 0); }
                }
            `;
            document.head.appendChild(style);
        },

        // Setup form animations
        setupFormAnimations: function() {
            // Floating labels
            document.querySelectorAll('.form-floating input, .form-floating textarea').forEach(input => {
                const updateLabel = () => {
                    const label = input.nextElementSibling;
                    if (label && label.tagName === 'LABEL') {
                        if (input.value || input === document.activeElement) {
                            label.classList.add('floating');
                        } else {
                            label.classList.remove('floating');
                        }
                    }
                };

                input.addEventListener('focus', updateLabel);
                input.addEventListener('blur', updateLabel);
                input.addEventListener('input', updateLabel);
                
                // Initial check
                updateLabel();
            });

            // Form validation animations
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', (e) => {
                    const invalidInputs = form.querySelectorAll(':invalid');
                    invalidInputs.forEach(input => {
                        input.classList.add('animate-shake');
                        setTimeout(() => {
                            input.classList.remove('animate-shake');
                        }, 500);
                    });
                });
            });
        },

        // Setup button animations
        setupButtonAnimations: function() {
            // Loading state for buttons
            document.querySelectorAll('[data-loading-text]').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.classList.contains('loading')) return;
                    
                    const originalText = this.innerHTML;
                    const loadingText = this.dataset.loadingText;
                    
                    this.classList.add('loading');
                    this.innerHTML = `<span class="spinner"></span> ${loadingText}`;
                    this.disabled = true;
                    
                    // Auto-reset after 3 seconds (or handle manually)
                    setTimeout(() => {
                        this.classList.remove('loading');
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 3000);
                });
            });

            // Success animation for buttons
            this.setupButtonSuccessAnimation();
        },

        // Setup button success animation
        setupButtonSuccessAnimation: function() {
            const style = document.createElement('style');
            style.textContent = `
                .btn-success-animation {
                    position: relative;
                    overflow: hidden;
                }
                
                .btn-success-animation.success::after {
                    content: '✓';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) scale(0);
                    color: white;
                    font-size: 1.2em;
                    animation: success-pop 0.6s ease-out forwards;
                }
                
                @keyframes success-pop {
                    0% {
                        transform: translate(-50%, -50%) scale(0);
                        opacity: 0;
                    }
                    50% {
                        transform: translate(-50%, -50%) scale(1.2);
                        opacity: 1;
                    }
                    100% {
                        transform: translate(-50%, -50%) scale(1);
                        opacity: 1;
                    }
                }
                
                .spinner {
                    display: inline-block;
                    width: 1em;
                    height: 1em;
                    border: 2px solid transparent;
                    border-top: 2px solid currentColor;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        },

        // Setup card animations
        setupCardAnimations: function() {
            // Stagger animation for card grids
            document.querySelectorAll('.card-grid, .dashboard-grid').forEach(grid => {
                const cards = grid.querySelectorAll('.card');
                cards.forEach((card, index) => {
                    card.style.animationDelay = `${index * 100}ms`;
                    card.classList.add('animate-fade-in-up');
                });
            });

            // Card flip animation
            document.querySelectorAll('.card-flip').forEach(card => {
                card.addEventListener('click', function() {
                    this.classList.toggle('flipped');
                });
            });
        },

        // Setup modal animations
        setupModalAnimations: function() {
            // Enhanced modal animations
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('show', function() {
                    this.classList.add('animate-scale-in');
                });

                modal.addEventListener('hide', function() {
                    this.classList.add('animate-scale-out');
                });
            });
        },

        // Setup counter animations
        setupCounterAnimations: function() {
            const counters = document.querySelectorAll('[data-counter]');
            
            const counterObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateCounter(entry.target);
                        counterObserver.unobserve(entry.target);
                    }
                });
            });

            counters.forEach(counter => {
                counterObserver.observe(counter);
            });
        },

        // Animate counter
        animateCounter: function(element) {
            const target = parseInt(element.dataset.counter);
            const duration = parseInt(element.dataset.duration) || 2000;
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                element.textContent = Math.floor(current);

                if (current >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                }
            }, 16);
        },

        // Setup parallax effects
        setupParallaxEffects: function() {
            const parallaxElements = document.querySelectorAll('[data-parallax]');
            
            if (parallaxElements.length === 0) return;

            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                
                parallaxElements.forEach(element => {
                    const rate = scrolled * (element.dataset.parallax || 0.5);
                    element.style.transform = `translateY(${rate}px)`;
                });
            });
        },

        // Utility: Animate element
        animate: function(element, animation, duration = 300) {
            return new Promise((resolve) => {
                element.style.animationDuration = duration + 'ms';
                element.classList.add('animate-' + animation);
                
                element.addEventListener('animationend', function handler() {
                    element.removeEventListener('animationend', handler);
                    element.classList.remove('animate-' + animation);
                    resolve();
                });
            });
        },

        // Utility: Stagger animations
        stagger: function(elements, animation, delay = 100) {
            elements.forEach((element, index) => {
                setTimeout(() => {
                    this.animate(element, animation);
                }, index * delay);
            });
        }
    };

    // Initialize animations when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => SmartAnimations.init());
    } else {
        SmartAnimations.init();
    }

    // Expose SmartAnimations globally
    window.SmartAnimations = SmartAnimations;

})();
