# Smart ERPNext Theme 🎨

A comprehensive, modern, and feature-rich theme for ERPNext with advanced animations, multiple color schemes, and responsive design.

## ✨ Features

### 🎨 **Multiple Themes**
- **Light Theme** - Clean and professional
- **Dark Theme** - Easy on the eyes
- **Ocean Blue** - Calm and focused
- **Forest Green** - Natural and fresh
- **Royal Purple** - Elegant and sophisticated
- **Sunset Orange** - Warm and energetic

### 🚀 **Advanced Features**
- **Dynamic Color Customization** - Create your own color schemes
- **Smooth Animations** - Beautiful micro-interactions
- **Responsive Design** - Works perfectly on all devices
- **Dark Mode Support** - Automatic and manual switching
- **Performance Optimized** - Fast loading and smooth performance
- **Accessibility Ready** - WCAG compliant design

### 💫 **Animations & Effects**
- Scroll-triggered animations
- Hover effects and transitions
- Loading animations
- Parallax effects
- Ripple effects on buttons
- Stagger animations for lists

### 📱 **Responsive Design**
- Mobile-first approach
- Tablet optimization
- Desktop enhancements
- Touch-friendly interface
- Adaptive layouts

## 🛠️ Installation

### Prerequisites
- ERPNext v13+ or v14+
- Frappe Framework
- Modern web browser

### Quick Install
```bash
# Navigate to your bench directory
cd /path/to/your/bench

# Install the app
bench get-app smart_theme

# Install on your site
bench --site your-site-name install-app smart_theme

# Build assets
bench --site your-site-name build

# Restart
bench restart
```

## 🎯 Usage

### Theme Switching
1. Look for the theme toggle button in the navbar
2. Click to open the theme selector
3. Choose from 6 pre-built themes
4. Customize colors using the color picker
5. Apply and save your preferences

### Keyboard Shortcuts
- `Ctrl/Cmd + Shift + D` - Toggle dark mode
- `Ctrl/Cmd + Shift + T` - Open theme selector
- `Ctrl/Cmd + K` - Quick search
- `Esc` - Close dropdowns/modals

## 🎨 Customization

### CSS Variables
The theme uses CSS custom properties for easy customization:

```css
:root {
  --primary-color: #3b82f6;
  --secondary-color: #6366f1;
  --accent-color: #f59e0b;
  --bg-primary: #ffffff;
  --text-primary: #1f2937;
}
```

### Adding Custom Animations
```css
.my-element {
  animation: fadeInUp 0.6s ease-out;
}
```

### Custom Components
```css
.my-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}
```

## 📁 File Structure

```
smart_theme/
├── smart_theme/
│   ├── public/
│   │   ├── css/
│   │   │   ├── smart_theme.css      # Main styles
│   │   │   ├── animations.css       # Animations
│   │   │   ├── components.css       # UI components
│   │   │   ├── responsive.css       # Responsive design
│   │   │   └── website.css          # Website styles
│   │   ├── js/
│   │   │   ├── smart_theme.js       # Main functionality
│   │   │   ├── animations.js        # Animation engine
│   │   │   ├── theme_switcher.js    # Theme switching
│   │   │   └── website.js           # Website interactions
│   │   ├── icons.svg                # Icon library
│   │   └── logo/                    # Brand assets
│   ├── hooks.py                     # App configuration
│   └── ...
├── README.md
└── SMART_THEME_GUIDE.md            # Detailed guide
```

## 🔧 Configuration

### Hooks Configuration
The theme is configured through `hooks.py`:

```python
app_include_css = [
    "/assets/smart_theme/css/smart_theme.css",
    "/assets/smart_theme/css/animations.css",
    "/assets/smart_theme/css/components.css",
    "/assets/smart_theme/css/responsive.css"
]

app_include_js = [
    "/assets/smart_theme/js/smart_theme.js",
    "/assets/smart_theme/js/animations.js",
    "/assets/smart_theme/js/theme_switcher.js"
]
```

## 🐛 Troubleshooting

### Theme Not Loading
```bash
# Check if app is installed
bench --site your-site list-apps

# Rebuild assets
bench --site your-site build --force

# Clear cache
bench --site your-site clear-cache

# Restart
bench restart
```

### Animations Not Working
- Check browser settings for reduced motion
- Ensure JavaScript is enabled
- Check browser console for errors
- Verify animation files are loaded

### Custom Colors Not Saving
- Clear browser cache
- Check localStorage in browser dev tools
- Verify theme switcher is loaded

## 📊 Performance

### Optimization Features
- CSS variables for efficient styling
- GPU-accelerated animations
- Lazy loading for images
- Minimal JavaScript footprint
- Compressed assets

### Performance Tips
- Use animations sparingly
- Enable lazy loading for images
- Use CSS variables instead of hardcoded values
- Minimize DOM queries in JavaScript

## 🤝 Contributing

We welcome contributions! Please:

1. Fork the repository
2. Create a feature branch
3. Follow coding standards
4. Test on multiple devices
5. Submit a pull request

### Coding Standards
- Use CSS variables for customizable values
- Follow BEM methodology for CSS classes
- Use ES6+ JavaScript features
- Write clear comments for complex code

## 📄 License

MIT License

## 🆘 Support

- **Documentation**: See SMART_THEME_GUIDE.md
- **Issues**: GitHub Issues
- **Community**: ERPNext Community Forums

## 🏆 Credits

Developed by **newsmart.tech** with ❤️

## 📈 Changelog

### v2.0.0 (Current)
- Multiple theme support
- Advanced animations
- Responsive design improvements
- Performance optimizations
- Custom color picker
- Enhanced accessibility

### Roadmap
- **v2.1**: Additional themes and color schemes
- **v2.2**: Performance improvements
- **v2.3**: More customization options
- **v3.0**: Complete redesign with new features

---

**Made with ❤️ for the ERPNext Community**