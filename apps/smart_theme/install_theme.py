#!/usr/bin/env python3
"""
Smart Theme Installation Script
Installs and configures Smart Theme for ERPNext
"""

import os
import subprocess
import sys

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    print(f"\n🔄 {description}")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(f"✅ {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False

def check_prerequisites():
    """Check if ERPNext and Frappe are installed"""
    print("🔍 Checking prerequisites...")
    
    # Check if we're in a Frappe bench
    if not os.path.exists("sites"):
        print("❌ Error: Not in a Frappe bench directory")
        print("Please run this script from your Frappe bench root directory")
        return False
    
    # Check if ERPNext is installed
    if not os.path.exists("apps/erpnext"):
        print("❌ Error: ERPNext not found")
        print("Please install ERPNext first")
        return False
    
    # Check if smart_theme exists
    if not os.path.exists("apps/smart_theme"):
        print("❌ Error: Smart Theme app not found")
        print("Please ensure smart_theme is in the apps directory")
        return False
    
    print("✅ Prerequisites check passed")
    return True

def get_site_name():
    """Get the site name from user input or detect automatically"""
    sites = []
    
    # List available sites
    if os.path.exists("sites"):
        for item in os.listdir("sites"):
            site_path = os.path.join("sites", item)
            if os.path.isdir(site_path) and item not in ["common_site_config.json", "assets"]:
                sites.append(item)
    
    if not sites:
        print("❌ No sites found")
        return None
    
    if len(sites) == 1:
        print(f"🎯 Found site: {sites[0]}")
        return sites[0]
    
    print("\n📋 Available sites:")
    for i, site in enumerate(sites, 1):
        print(f"  {i}. {site}")
    
    while True:
        try:
            choice = input(f"\nSelect site (1-{len(sites)}): ").strip()
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(sites):
                    return sites[index]
            print("❌ Invalid choice. Please try again.")
        except KeyboardInterrupt:
            print("\n❌ Installation cancelled")
            return None

def install_theme(site_name):
    """Install Smart Theme on the specified site"""
    print(f"\n🚀 Installing Smart Theme on site: {site_name}")
    
    steps = [
        {
            "command": f"bench --site {site_name} install-app smart_theme",
            "description": "Installing Smart Theme app"
        },
        {
            "command": f"bench --site {site_name} migrate",
            "description": "Running database migrations"
        },
        {
            "command": f"bench --site {site_name} build",
            "description": "Building assets"
        },
        {
            "command": "bench restart",
            "description": "Restarting services"
        }
    ]
    
    for step in steps:
        if not run_command(step["command"], step["description"]):
            print(f"❌ Installation failed at: {step['description']}")
            return False
    
    return True

def verify_installation(site_name):
    """Verify that the theme is installed correctly"""
    print(f"\n🔍 Verifying installation on site: {site_name}")
    
    # Check if app is listed
    result = subprocess.run(
        f"bench --site {site_name} list-apps",
        shell=True, capture_output=True, text=True
    )
    
    if "smart_theme" in result.stdout:
        print("✅ Smart Theme app is installed")
    else:
        print("❌ Smart Theme app not found in installed apps")
        return False
    
    # Check if assets are built
    assets_path = f"sites/assets/smart_theme"
    if os.path.exists(assets_path):
        print("✅ Theme assets are built")
    else:
        print("❌ Theme assets not found")
        return False
    
    return True

def show_success_message(site_name):
    """Show success message with next steps"""
    print("\n" + "="*60)
    print("🎉 SMART THEME INSTALLATION COMPLETED SUCCESSFULLY!")
    print("="*60)
    print(f"""
✅ Smart Theme has been installed on site: {site_name}

🎨 FEATURES AVAILABLE:
• 6 Beautiful themes (Light, Dark, Ocean Blue, Forest Green, Royal Purple, Sunset Orange)
• Advanced animations and micro-interactions
• Responsive design for all devices
• Custom color picker
• Dark mode support
• Performance optimized

🚀 NEXT STEPS:
1. Open your ERPNext site in a web browser
2. Look for the theme toggle button in the navbar
3. Click to explore different themes
4. Use the color picker to customize colors
5. Enjoy your new beautiful interface!

⌨️  KEYBOARD SHORTCUTS:
• Ctrl/Cmd + Shift + D: Toggle dark mode
• Ctrl/Cmd + Shift + T: Open theme selector
• Ctrl/Cmd + K: Quick search
• Esc: Close dropdowns/modals

📚 DOCUMENTATION:
• README.md: Quick start guide
• SMART_THEME_GUIDE.md: Comprehensive documentation

🆘 SUPPORT:
• GitHub Issues: Report bugs and request features
• ERPNext Community: Get help from the community

Developed with ❤️  by newsmart.tech
""")

def main():
    """Main installation function"""
    print("🎨 Smart ERPNext Theme Installer")
    print("=" * 40)
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Get site name
    site_name = get_site_name()
    if not site_name:
        sys.exit(1)
    
    # Confirm installation
    print(f"\n📋 Installation Summary:")
    print(f"   Site: {site_name}")
    print(f"   Theme: Smart Theme v2.0.0")
    
    try:
        confirm = input("\n❓ Proceed with installation? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ Installation cancelled")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n❌ Installation cancelled")
        sys.exit(0)
    
    # Install theme
    if not install_theme(site_name):
        print("\n❌ Installation failed")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation(site_name):
        print("\n⚠️  Installation completed but verification failed")
        print("Please check manually and report any issues")
    
    # Show success message
    show_success_message(site_name)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Installation cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
