# -*- coding: utf-8 -*-
# API functions for Car Record
from __future__ import unicode_literals
import frappe
from frappe.utils import flt, cstr
from frappe import _

@frappe.whitelist()
def get_available_cars(filters=None):
	"""الحصول على السيارات المتاحة للبيع"""
	conditions = ["status = 'Available'"]
	values = []
	
	if filters:
		filters = frappe.parse_json(filters) if isinstance(filters, str) else filters
		
		if filters.get("brand_filter"):
			conditions.append("brand = %s")
			values.append(filters.get("brand_filter"))
			
		if filters.get("year_from"):
			conditions.append("year >= %s")
			values.append(filters.get("year_from"))
			
		if filters.get("year_to"):
			conditions.append("year <= %s")
			values.append(filters.get("year_to"))
			
		if filters.get("max_price"):
			conditions.append("selling_price <= %s")
			values.append(filters.get("max_price"))
			
		if filters.get("car_search"):
			search_term = "%" + cstr(filters.get("car_search")) + "%"
			conditions.append("(car_name LIKE %s OR brand LIKE %s OR model LIKE %s)")
			values.extend([search_term, search_term, search_term])
	
	query = f"""
		SELECT name, car_name, brand, model, year, color, selling_price, mileage, status
		FROM `tabCar Record`
		WHERE {" AND ".join(conditions)}
		ORDER BY creation DESC
		LIMIT 20
	"""
	
	return frappe.db.sql(query, values, as_dict=True)

@frappe.whitelist()
def get_car_dashboard_data():
	"""بيانات لوحة المعلومات الخاصة بالسيارات"""
	
	# إحصائيات السيارات
	total_cars = frappe.db.count("Car Record")
	available_cars = frappe.db.count("Car Record", {"status": "Available"})
	sold_cars = frappe.db.count("Car Record", {"status": "Sold"})
	reserved_cars = frappe.db.count("Car Record", {"status": "Reserved"})
	
	# أعلى الماركات مبيعاً
	top_brands = frappe.db.sql("""
		SELECT brand, COUNT(*) as count
		FROM `tabCar Record`
		WHERE status = 'Sold'
		GROUP BY brand
		ORDER BY count DESC
		LIMIT 5
	""", as_dict=True)
	
	# المبيعات الشهرية
	monthly_sales = frappe.db.sql("""
		SELECT 
			MONTHNAME(sale_date) as month,
			COUNT(*) as sales_count,
			SUM(selling_price) as total_amount
		FROM `tabCar Record`
		WHERE status = 'Sold' 
		AND sale_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
		GROUP BY MONTH(sale_date), YEAR(sale_date)
		ORDER BY sale_date DESC
		LIMIT 12
	""", as_dict=True)
	
	return {
		"stats": {
			"total_cars": total_cars,
			"available_cars": available_cars,
			"sold_cars": sold_cars,
			"reserved_cars": reserved_cars
		},
		"top_brands": top_brands,
		"monthly_sales": monthly_sales
	}

@frappe.whitelist()
def create_car_quotation(car_name, customer, additional_items=None):
	"""إنشاء عرض سعر للسيارة"""
	car = frappe.get_doc("Car Record", car_name)
	
	# التحقق من توفر السيارة
	if car.status != "Available":
		frappe.throw(_("Car is not available for sale"))
	
	# إنشاء عرض السعر
	quotation = frappe.new_doc("Quotation")
	quotation.party_name = customer
	quotation.quotation_to = "Customer"
	quotation.car_record = car_name
	
	# إضافة السيارة كصنف
	quotation.append("items", {
		"item_code": car.item_code or car.name,
		"item_name": car.car_name,
		"description": f"{car.brand} {car.model} {car.year} - {car.color or ''}",
		"qty": 1,
		"rate": car.selling_price,
		"amount": car.selling_price
	})
	
	# إضافة أصناف إضافية إذا وجدت
	if additional_items:
		additional_items = frappe.parse_json(additional_items) if isinstance(additional_items, str) else additional_items
		for item in additional_items:
			quotation.append("items", item)
	
	quotation.insert()
	return quotation.name

@frappe.whitelist()
def reserve_car(car_name, customer, deposit_amount=0):
	"""حجز السيارة للعميل"""
	car = frappe.get_doc("Car Record", car_name)
	
	if car.status != "Available":
		frappe.throw(_("Car is not available for reservation"))
	
	# تحديث حالة السيارة
	car.status = "Reserved"
	car.customer = customer
	car.deposit_amount = flt(deposit_amount)
	car.save()
	
	frappe.msgprint(_("Car has been reserved successfully"))
	return True

@frappe.whitelist()
def get_car_features_template():
	"""الحصول على قوالب المزايا الشائعة للسيارات"""
	
	luxury_features = [
		{"feature_name": "Leather Seats", "feature_category": "Interior Comfort", "is_premium": 1},
		{"feature_name": "Sunroof", "feature_category": "Windows & Sunroof", "is_premium": 1},
		{"feature_name": "Navigation System", "feature_category": "Advanced Technology", "is_premium": 0},
		{"feature_name": "Bluetooth Connectivity", "feature_category": "Advanced Technology", "is_premium": 0},
		{"feature_name": "Premium Sound System", "feature_category": "Audio System", "is_premium": 1},
		{"feature_name": "Heated Seats", "feature_category": "Interior Comfort", "is_premium": 1},
		{"feature_name": "Automatic Climate Control", "feature_category": "Climate Control", "is_premium": 0},
		{"feature_name": "LED Headlights", "feature_category": "Lighting", "is_premium": 0}
	]
	
	safety_features = [
		{"safety_feature_name": "Anti-lock Braking System (ABS)", "safety_category": "Advanced Braking", "is_standard": 1},
		{"safety_feature_name": "Airbags", "safety_category": "Airbags", "is_standard": 1},
		{"safety_feature_name": "Electronic Stability Control", "safety_category": "Driver Assistance", "is_standard": 1},
		{"safety_feature_name": "Backup Camera", "safety_category": "Vision Technology", "is_standard": 0},
		{"safety_feature_name": "Blind Spot Monitoring", "safety_category": "Driver Assistance", "is_standard": 0},
		{"safety_feature_name": "Lane Departure Warning", "safety_category": "Alerts & Warnings", "is_standard": 0},
		{"safety_feature_name": "Collision Avoidance System", "safety_category": "Driver Assistance", "is_standard": 0},
		{"safety_feature_name": "Child Safety Locks", "safety_category": "Child Safety", "is_standard": 1}
	]
	
	return {
		"luxury_features": luxury_features,
		"safety_features": safety_features
	}