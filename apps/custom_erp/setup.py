from setuptools import setup, find_packages

with open("requirements.txt") as f:
	install_requires = f.read().strip().split("\n")

# get version from __version__ variable in custom_erp/__init__.py
from custom_erp import __version__ as version

setup(
	name="custom_erp",
	version=version,
	description="Car Showroom Management System for ERPNext",
	author="moneer",
	author_email="<EMAIL>",
	packages=find_packages(),
	zip_safe=False,
	include_package_data=True,
	install_requires=install_requires
)