# نظام إدارة معرض السيارات 🚗

نظام شامل لإدارة معارض السيارات مبني على ERPNext، يوفر جميع الأدوات اللازمة لإدارة المخزون والمبيعات والعملاء.

## المميزات الرئيسية ✨

### 🚘 إدارة السيارات
- تسجيل تفاصيل شاملة لكل سيارة
- إدارة الصور والمستندات
- تتبع الحالة (متاحة، محجوزة، مباعة، قيد الصيانة)
- تسجيل تاريخ الخدمة والصيانة

### 🏷️ إدارة الماركات
- تصنيف الماركات (فاخرة/عادية)
- معلومات تفصيلية لكل ماركة
- إحصائيات المبيعات حسب الماركة

### 💰 إدارة العروض والتسعير
- إنشاء عروض خاصة
- تحديد الحد الأدنى للأسعار
- حساب هامش الربح تلقائياً
- تتبع تاريخ انتهاء العروض

### 📊 التقارير والتحليلات
- تقرير تحليل المبيعات
- تقرير مخزون السيارات
- تقرير السيارات الفاخرة
- لوحة معلومات تفاعلية

### 🔄 التكامل مع ERPNext
- ربط تلقائي مع فواتير المبيعات
- تكامل مع إدارة العملاء
- تحديث حالة المخزون تلقائياً

## التثبيت 🔧

### المتطلبات
- ERPNext v15.0+
- Frappe Framework v15.0+
- Python 3.10+

### خطوات التثبيت

1. **تحميل التطبيق**
```bash
cd /path/to/frappe-bench
bench get-app custom_erp /path/to/custom_erp
```

2. **تثبيت التطبيق**
```bash
bench --site your-site install-app custom_erp
```

3. **تشغيل التحديثات**
```bash
bench --site your-site migrate
```

4. **بناء الموارد**
```bash
bench build
```

## الاستخدام 📖

### إعداد السيارات الجديدة

1. انتقل إلى **معرض السيارات > سجل السيارة**
2. أنشئ سجل جديد وأدخل التفاصيل:
   - المعلومات الأساسية (الماركة، الموديل، السنة)
   - المعلومات المالية (سعر الشراء، سعر البيع)
   - الحالة والموقع
   - الصور والمستندات

### إنشاء عرض سعر

1. من قائمة السيارات، اختر السيارة المطلوبة
2. انقر على **إنشاء عرض سعر**
3. اختر العميل وأدخل التفاصيل
4. احفظ العرض وأرسله للعميل

### متابعة المبيعات

1. عند إنشاء فاتورة مبيعات للسيارة
2. سيتم تحديث حالة السيارة تلقائياً إلى "مباعة"
3. سيتم حساب الربح الفعلي وهامش الربح

## الهيكل التقني 🏗️

### DocTypes الرئيسية

- **Car Record**: سجل السيارة الرئيسي
- **Car Brand**: ماركات السيارات
- **Car Promotion**: العروض والخصومات
- **Car Service History**: تاريخ الخدمة والصيانة

### Child DocTypes

- **Car Image**: صور السيارة
- **Car Document**: مستندات السيارة
- **Car Luxury Feature**: الميزات الفاخرة
- **Car Safety Feature**: ميزات الأمان

### التقارير

- **Car Sales Analysis**: تحليل المبيعات
- **Car Inventory Report**: تقرير المخزون
- **Luxury Cars Report**: تقرير السيارات الفاخرة

## المهام المجدولة ⏰

- **يومياً**: إرسال تنبيهات الصيانة
- **أسبوعياً**: تحديث تقادم المخزون
- **شهرياً**: إنشاء التقارير الشهرية

## التخصيص والتطوير 🛠️

### إضافة ميزات جديدة

1. **إنشاء DocType جديد**
```bash
bench --site your-site make-doctype "New DocType"
```

2. **إضافة تقرير جديد**
```bash
bench --site your-site make-report "New Report"
```

3. **إضافة صفحة ويب**
```bash
bench --site your-site make-web-page "new-page"
```

### ملفات مهمة للتطوير

- `hooks.py`: إعدادات التطبيق
- `utils.py`: دوال مساعدة
- `tasks.py`: المهام المجدولة
- `install.py`: إعدادات التثبيت

## الأمان والصلاحيات 🔐

### الأدوار المقترحة

- **Car Showroom Manager**: صلاحيات كاملة
- **Sales Person**: عرض وإنشاء العروض
- **Inventory Manager**: إدارة المخزون
- **Accountant**: عرض التقارير المالية

### الصلاحيات الافتراضية

- عرض السيارات: جميع المستخدمين
- إنشاء/تعديل السيارات: المديرين فقط
- تحديث الأسعار: المديرين ومدراء المبيعات
- عرض التقارير: حسب الدور

## الدعم والمساعدة 🆘

### المشاكل الشائعة

**Q: السيارة لا تظهر في القائمة**
A: تأكد من أن حالة السيارة مناسبة وأن المستخدم لديه صلاحيات كافية

**Q: خطأ في حساب الربح**
A: تأكد من إدخال سعر الشراء وسعر البيع بشكل صحيح

**Q: الصور لا تظهر**
A: تأكد من رفع الصور بالصيغ المدعومة (JPG, PNG, GIF)

### التواصل

- البريد الإلكتروني: <EMAIL>
- إنشاء Issue على GitHub للمشاكل التقنية

## المساهمة 🤝

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. إنشاء Pull Request

### معايير الكود

- استخدام اللغة العربية في التسميات
- اتباع معايير Frappe/ERPNext
- إضافة التعليقات والوثائق
- اختبار الكود قبل الإرسال

## الترخيص 📄

MIT License - انظر ملف [LICENSE](license.txt) للتفاصيل

## التحديثات المستقبلية 🚀

### المميزات القادمة

- [ ] تطبيق موبايل
- [ ] تكامل مع منصات التسويق
- [ ] نظام المواعيد والحجوزات
- [ ] تقارير متقدمة بـ Power BI
- [ ] نظام تقييم العملاء
- [ ] إدارة قطع الغيار

---

**تم تطوير هذا النظام بـ ❤️ لمجتمع ERPNext العربي**