# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe import _

def get_context(context):
	context.title = _("Car Showroom Dashboard")
	context.page_name = "car-dashboard"
	
	# التحقق من الصلاحيات (مع تساهل في التطوير)
	try:
		if not frappe.has_permission("Car Record", "read"):
			# في حالة عدم وجود صلاحيات، نعرض رسالة ولكن لا نتوقف
			context.no_permission = True
	except Exception:
		# في حالة عدم وجود DocType، نكمل بدون توقف
		context.no_permission = True
	
	# إحضار البيانات
	context.stats = get_dashboard_stats()
	context.charts_data = get_charts_data()
	
	# للتصحيح - إضافة معلومات إضافية
	context.debug_info = {
		'stats_available': bool(context.stats),
		'charts_available': bool(context.charts_data),
		'has_car_record': frappe.db.exists("DocType", "Car Record")
	}
	
	return context

def get_dashboard_stats():
	"""إحصائيات الداشبورد"""
	try:
		from custom_erp.car_showroom.dashboard.car_dashboard import get_car_stats
		return get_car_stats()
	except Exception as e:
		frappe.log_error(f"خطأ في جلب إحصائيات الداشبورد: {str(e)}")
		return {
			'total_cars': 0,
			'available_cars': 0,
			'sold_cars': 0,
			'reserved_cars': 0,
			'maintenance_cars': 0,
			'inventory_value': 0,
			'average_price': 0
		}

def get_charts_data():
	"""بيانات الرسوم البيانية"""
	try:
		from custom_erp.car_showroom.dashboard.car_dashboard import get_sales_by_brand, get_monthly_sales
		
		sales_by_brand = get_sales_by_brand() or []
		monthly_sales = get_monthly_sales() or []
		
		# التأكد من أن البيانات في تنسيق صحيح
		if not isinstance(sales_by_brand, list):
			sales_by_brand = []
		if not isinstance(monthly_sales, list):
			monthly_sales = []
			
		return {
			'sales_by_brand': sales_by_brand,
			'monthly_sales': monthly_sales
		}
	except Exception as e:
		frappe.log_error(f"خطأ في جلب بيانات الرسوم البيانية: {str(e)}")
		return {
			'sales_by_brand': [],
			'monthly_sales': []
		}