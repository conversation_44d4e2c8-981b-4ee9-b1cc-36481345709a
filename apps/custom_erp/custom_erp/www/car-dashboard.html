{% extends "templates/web.html" %}

{% block title %}{{ _("Car Showroom Dashboard") }}{% endblock %}

{% block head_include %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .car-dashboard { direction: rtl; text-align: right; }
        .car-stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .car-stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }
        .car-stat-number { font-size: 2.5rem; font-weight: bold; margin-bottom: 10px; }
        .car-stat-label { font-size: 1.1rem; color: #6c757d; }
        .stat-available .car-stat-number { color: #28a745; }
        .stat-sold .car-stat-number { color: #dc3545; }
        .stat-reserved .car-stat-number { color: #ffc107; }
        .stat-maintenance .car-stat-number { color: #17a2b8; }
        .car-chart-container { background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .chart-title { text-align: center; margin-bottom: 20px; color: #495057; }
        .car-action-buttons { display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; }
        .btn-car-primary, .btn-car-secondary, .btn-car-success { padding: 10px 20px; border-radius: 5px; text-decoration: none; font-weight: bold; }
        .btn-car-primary { background: #007bff; color: white; }
        .btn-car-secondary { background: #6c757d; color: white; }
        .btn-car-success { background: #28a745; color: white; }
        .dashboard-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
        .dashboard-title { color: #495057; margin: 0; }
        .dashboard-actions { display: flex; gap: 10px; }
    </style>
{% endblock %}

{% block page_content %}
<div class="car-dashboard arabic-content">
    <div class="dashboard-header">
        <h1 class="dashboard-title">لوحة معلومات معرض السيارات</h1>
        <div class="dashboard-actions">
            <a href="/app/car-record" class="btn btn-primary">إدارة السيارات</a>
            <a href="/app/query-report/Car Sales Analysis" class="btn btn-secondary">تقرير المبيعات</a>
        </div>
    </div>

    {% if no_permission %}
    <div class="alert alert-warning">
        <strong>تنبيه:</strong> لا توجد صلاحيات كافية لعرض بيانات السيارات أو أن DocTypes غير مثبتة بعد.
    </div>
    {% endif %}

    <!-- معلومات التصحيح (يمكن إزالتها لاحقاً) -->
    {% if debug_info %}
    <div class="alert alert-info" style="font-size: 12px;">
        <strong>معلومات التصحيح:</strong>
        البيانات متوفرة: {{ debug_info.stats_available }}، 
        الرسوم البيانية متوفرة: {{ debug_info.charts_available }}،
        Car Record موجود: {{ debug_info.has_car_record }}
    </div>
    {% endif %}

    <!-- إحصائيات سريعة -->
    <div class="car-stats-grid">
        <div class="car-stat-card stat-available">
            <div class="car-stat-number">{{ (stats.available_cars if stats else 0) or 0 }}</div>
            <div class="car-stat-label">سيارات متاحة</div>
        </div>
        <div class="car-stat-card stat-sold">
            <div class="car-stat-number">{{ (stats.sold_cars if stats else 0) or 0 }}</div>
            <div class="car-stat-label">سيارات مباعة</div>
        </div>
        <div class="car-stat-card stat-reserved">
            <div class="car-stat-number">{{ (stats.reserved_cars if stats else 0) or 0 }}</div>
            <div class="car-stat-label">سيارات محجوزة</div>
        </div>
        <div class="car-stat-card stat-maintenance">
            <div class="car-stat-number">{{ (stats.maintenance_cars if stats else 0) or 0 }}</div>
            <div class="car-stat-label">قيد الصيانة</div>
        </div>
    </div>

    <!-- معلومات مالية -->
    <div class="row">
        <div class="col-md-6">
            <div class="car-stat-card">
                <h4>قيمة المخزون</h4>
                <div class="car-stat-number" style="color: #007bff;">
                    {{ "{:,.0f}".format((stats.inventory_value if stats else 0) or 0) }} ريال
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="car-stat-card">
                <h4>متوسط سعر السيارة</h4>
                <div class="car-stat-number" style="color: #28a745;">
                    {{ "{:,.0f}".format((stats.average_price if stats else 0) or 0) }} ريال
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="car-chart-container">
                <h3 class="chart-title">المبيعات حسب الماركة</h3>
                <canvas id="brandSalesChart" width="400" height="200" 
                    data-chart-data="{{ (charts_data.sales_by_brand if charts_data and charts_data.sales_by_brand else []) | tojson | safe }}"></canvas>
            </div>
        </div>
        <div class="col-md-6">
            <div class="car-chart-container">
                <h3 class="chart-title">المبيعات الشهرية</h3>
                <canvas id="monthlySalesChart" width="400" height="200"
                    data-chart-data="{{ (charts_data.monthly_sales if charts_data and charts_data.monthly_sales else []) | tojson | safe }}"></canvas>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="car-stat-card">
                <h4>الإجراءات السريعة</h4>
                <div class="car-action-buttons">
                    <a href="/app/car-record/new" class="btn-car-primary">إضافة سيارة جديدة</a>
                    <a href="/app/car-promotion/new" class="btn-car-secondary">إنشاء عرض</a>
                    <a href="/app/query-report/Car Inventory Report" class="btn-car-success">تقرير المخزون</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// قراءة بيانات الرسوم البيانية من data attributes
function getChartData(elementId) {
    try {
        const element = document.getElementById(elementId);
        const dataAttr = element.getAttribute('data-chart-data');
        
        if (!dataAttr || dataAttr === '[]') {
            return [];
        }
        
        const data = JSON.parse(dataAttr);
        return Array.isArray(data) ? data : [];
    } catch(e) {
        console.log('خطأ في قراءة بيانات الرسم البياني:', e);
        return [];
    }
}

// تحميل البيانات
const brandSalesData = getChartData('brandSalesChart');
const monthlySalesData = getChartData('monthlySalesChart');

// رسم بياني للمبيعات حسب الماركة
function createBrandSalesChart() {
    if (brandSalesData && brandSalesData.length > 0) {
        const ctx1 = document.getElementById('brandSalesChart').getContext('2d');
        new Chart(ctx1, {
            type: 'pie',
            data: {
                labels: brandSalesData.map(item => item.brand),
                datasets: [{
                    data: brandSalesData.map(item => item.count),
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    } else {
        // رسالة بديلة عند عدم وجود بيانات
        document.getElementById('brandSalesChart').style.display = 'none';
        const noDataMsg1 = document.createElement('div');
        noDataMsg1.innerHTML = '<p style="text-align: center; color: #666; padding: 50px;">لا توجد بيانات مبيعات متاحة لعرض الرسم البياني</p>';
        document.getElementById('brandSalesChart').parentNode.appendChild(noDataMsg1);
    }
}

// رسم بياني للمبيعات الشهرية
function createMonthlySalesChart() {
    if (monthlySalesData && monthlySalesData.length > 0) {
        const ctx2 = document.getElementById('monthlySalesChart').getContext('2d');
        new Chart(ctx2, {
            type: 'line',
            data: {
                labels: monthlySalesData.map(item => item.month),
                datasets: [{
                    label: 'عدد السيارات المباعة',
                    data: monthlySalesData.map(item => item.cars_sold),
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    } else {
        // رسالة بديلة عند عدم وجود بيانات
        document.getElementById('monthlySalesChart').style.display = 'none';
        const noDataMsg2 = document.createElement('div');
        noDataMsg2.innerHTML = '<p style="text-align: center; color: #666; padding: 50px;">لا توجد بيانات مبيعات شهرية متاحة لعرض الرسم البياني</p>';
        document.getElementById('monthlySalesChart').parentNode.appendChild(noDataMsg2);
    }
}

// تنفيذ إنشاء الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    createBrandSalesChart();
    createMonthlySalesChart();
});
</script>
{% endblock %}