[{"chart_name": "Car Sales by Brand", "chart_type": "Donut", "creation": "2024-01-01 00:00:00.000000", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Car Record", "dynamic_filters_json": "[]", "filters_json": "[{\"fieldname\": \"status\", \"operator\": \"=\", \"value\": \"مباعة\"}]", "group_by_based_on": "brand", "group_by_type": "Count", "idx": 0, "is_public": 1, "is_standard": 1, "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Car Sales by Brand", "number_of_groups": 5, "owner": "Administrator", "roles": [], "source": "List", "timeseries": 0, "type": "Group By"}, {"chart_name": "Monthly Car Sales", "chart_type": "Line", "creation": "2024-01-01 00:00:00.000000", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Car Record", "dynamic_filters_json": "[]", "filters_json": "[{\"fieldname\": \"status\", \"operator\": \"=\", \"value\": \"مباعة\"}]", "time_series_based_on": "sale_date", "idx": 0, "is_public": 1, "is_standard": 1, "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Monthly Car Sales", "number_of_groups": 0, "owner": "Administrator", "roles": [], "source": "List", "time_interval": "Monthly", "timeseries": 1, "type": "Count", "value_based_on": ""}]