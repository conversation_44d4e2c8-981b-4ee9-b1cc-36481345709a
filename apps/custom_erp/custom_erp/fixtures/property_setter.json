[{"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "company", "is_system_generated": 0, "modified": "2025-06-25 22:56:33.611434", "module": null, "name": "Purchase Invoice-company-in_list_view", "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "company", "is_system_generated": 0, "modified": "2025-06-25 22:56:33.520225", "module": null, "name": "Purchase Invoice-company-bold", "property": "bold", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Supplier", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "is_system_generated": 0, "modified": "2024-03-06 12:39:26.704444", "module": null, "name": "Supplier-naming_series-reqd", "property": "reqd", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Supplier", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "is_system_generated": 0, "modified": "2024-03-06 12:39:27.079985", "module": null, "name": "Supplier-naming_series-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Customer", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.083224", "module": null, "name": "Customer-naming_series-reqd", "property": "reqd", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Customer", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.554789", "module": null, "name": "Customer-naming_series-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "tax_id", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.627384", "module": null, "name": "Sales Order-tax_id-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "tax_id", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.644306", "module": null, "name": "Sales Order-tax_id-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "tax_id", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.662900", "module": null, "name": "Sales Invoice-tax_id-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "tax_id", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.674979", "module": null, "name": "Sales Invoice-tax_id-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "tax_id", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.691615", "module": null, "name": "Delivery Note-tax_id-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "tax_id", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.711000", "module": null, "name": "Delivery Note-tax_id-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Packed Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rate", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.725816", "module": null, "name": "Packed Item-rate-read_only", "property": "read_only", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Invoice Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "discount_account", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.742535", "module": null, "name": "Sales Invoice Item-discount_account-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Invoice Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "discount_account", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.755278", "module": null, "name": "Sales Invoice Item-discount_account-mandatory_depends_on", "property": "mandatory_depends_on", "property_type": "Code", "row_name": null, "value": ""}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "additional_discount_account", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.768188", "module": null, "name": "Sales Invoice-additional_discount_account-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "additional_discount_account", "is_system_generated": 0, "modified": "2024-03-06 12:39:28.780377", "module": null, "name": "Sales Invoice-additional_discount_account-mandatory_depends_on", "property": "mandatory_depends_on", "property_type": "Code", "row_name": null, "value": ""}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:30.491699", "module": null, "name": "Quotation-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.087460", "module": null, "name": "Quotation-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.109989", "module": null, "name": "Quotation-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.123639", "module": null, "name": "Quotation-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.137038", "module": null, "name": "Quotation-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.151223", "module": null, "name": "Sales Order-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.165449", "module": null, "name": "Sales Order-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.180925", "module": null, "name": "Sales Order-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.198686", "module": null, "name": "Sales Order-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.217332", "module": null, "name": "Sales Order-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.231266", "module": null, "name": "Sales Invoice-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.255191", "module": null, "name": "Sales Invoice-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.269109", "module": null, "name": "Sales Invoice-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.287713", "module": null, "name": "Sales Invoice-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.304611", "module": null, "name": "Sales Invoice-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.327430", "module": null, "name": "Delivery Note-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.342249", "module": null, "name": "Delivery Note-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.357574", "module": null, "name": "Delivery Note-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.372116", "module": null, "name": "Delivery Note-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.386680", "module": null, "name": "Delivery Note-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Supplier Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.405361", "module": null, "name": "Supplier Quotation-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Supplier Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.421377", "module": null, "name": "Supplier Quotation-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Supplier Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.438640", "module": null, "name": "Supplier Quotation-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Supplier Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.453918", "module": null, "name": "Supplier Quotation-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Supplier Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.468637", "module": null, "name": "Supplier Quotation-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.482585", "module": null, "name": "Purchase Order-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.498347", "module": null, "name": "Purchase Order-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.513528", "module": null, "name": "Purchase Order-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.528632", "module": null, "name": "Purchase Order-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.543066", "module": null, "name": "Purchase Order-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.559847", "module": null, "name": "Purchase Invoice-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.587277", "module": null, "name": "Purchase Invoice-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.608018", "module": null, "name": "Purchase Invoice-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.622603", "module": null, "name": "Purchase Invoice-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.636864", "module": null, "name": "Purchase Invoice-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.656403", "module": null, "name": "Purchase Receipt-base_rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.672082", "module": null, "name": "Purchase Receipt-base_rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.684470", "module": null, "name": "Purchase Receipt-rounded_total-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.696617", "module": null, "name": "Purchase Receipt-rounded_total-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.708862", "module": null, "name": "Purchase Receipt-disable_rounded_total-default", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.721209", "module": null, "name": "Quotation-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.733344", "module": null, "name": "Quotation-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.745508", "module": null, "name": "Sales Order-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.757598", "module": null, "name": "Sales Order-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.769616", "module": null, "name": "Sales Invoice-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.782009", "module": null, "name": "Sales Invoice-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.795781", "module": null, "name": "Delivery Note-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.808955", "module": null, "name": "Delivery Note-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Supplier Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.821199", "module": null, "name": "Supplier Quotation-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Supplier Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.832662", "module": null, "name": "Supplier Quotation-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.844076", "module": null, "name": "Purchase Order-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.855601", "module": null, "name": "Purchase Order-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.866888", "module": null, "name": "Purchase Invoice-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.878074", "module": null, "name": "Purchase Invoice-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.889100", "module": null, "name": "Purchase Receipt-in_words-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "is_system_generated": 0, "modified": "2024-03-06 12:39:31.901278", "module": null, "name": "Purchase Receipt-in_words-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "<PERSON><PERSON>", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "is_system_generated": 0, "modified": "2024-03-06 12:39:34.566709", "module": null, "name": "Item-naming_series-reqd", "property": "reqd", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "<PERSON><PERSON>", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "is_system_generated": 0, "modified": "2024-03-06 12:39:34.820506", "module": null, "name": "Item-naming_series-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "<PERSON><PERSON>", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "item_code", "is_system_generated": 0, "modified": "2024-03-06 12:39:34.830992", "module": null, "name": "Item-item_code-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "<PERSON><PERSON>", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "item_code", "is_system_generated": 0, "modified": "2024-03-06 12:39:34.841147", "module": null, "name": "Item-item_code-reqd", "property": "reqd", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Stock Entry Detail", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.852311", "module": null, "name": "Stock Entry Detail-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Item Barcode", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.863804", "module": null, "name": "Item Barcode-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Job Card", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.875207", "module": null, "name": "Job Card-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.885933", "module": null, "name": "Delivery Note Item-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "POS Invoice Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.897434", "module": null, "name": "POS Invoice Item-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.911535", "module": null, "name": "Sales Invoice Item-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.923064", "module": null, "name": "Purchase Receipt Item-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Stock Reconciliation Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.934579", "module": null, "name": "Stock Reconciliation Item-barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "<PERSON><PERSON>", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "barcodes", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.947129", "module": null, "name": "Item-barcodes-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.957773", "module": null, "name": "Purchase Order-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.967695", "module": null, "name": "Sales Invoice-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.977347", "module": null, "name": "Sales Order-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.987095", "module": null, "name": "Purchase Invoice-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:34.996758", "module": null, "name": "Purchase Receipt-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "POS Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:35.006778", "module": null, "name": "POS Invoice-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Pick List", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:35.017449", "module": null, "name": "Pick List-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Material Request", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:35.027260", "module": null, "name": "Material Request-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Stock Reconciliation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:35.036876", "module": null, "name": "Stock Reconciliation-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Quotation", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:35.046986", "module": null, "name": "Quotation-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Stock Entry", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:35.056523", "module": null, "name": "Stock Entry-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Delivery Note", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "is_system_generated": 1, "modified": "2024-03-06 12:39:35.066277", "module": null, "name": "Delivery Note-scan_barcode-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "target_warehouse", "is_system_generated": 0, "modified": "2024-03-06 12:39:35.087270", "module": null, "name": "Sales Invoice Item-target_warehouse-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Delivery Note Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "target_warehouse", "is_system_generated": 0, "modified": "2024-03-06 12:39:35.098925", "module": null, "name": "Delivery Note Item-target_warehouse-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Invoice Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "from_warehouse", "is_system_generated": 0, "modified": "2024-03-06 12:39:35.109312", "module": null, "name": "Purchase Invoice Item-from_warehouse-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Receipt Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "from_warehouse", "is_system_generated": 0, "modified": "2024-03-06 12:39:35.120131", "module": null, "name": "Purchase Receipt Item-from_warehouse-hidden", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "due_date", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.454791", "module": null, "name": "Sales Order-due_date-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "payment_schedule", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.460162", "module": null, "name": "Sales Order-payment_schedule-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "due_date", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.465305", "module": null, "name": "Sales Invoice-due_date-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "payment_schedule", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.470450", "module": null, "name": "Sales Invoice-payment_schedule-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "due_date", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.475560", "module": null, "name": "Purchase Order-due_date-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "payment_schedule", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.480708", "module": null, "name": "Purchase Order-payment_schedule-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "due_date", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.488721", "module": null, "name": "Purchase Invoice-due_date-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "payment_schedule", "is_system_generated": 0, "modified": "2024-03-06 15:05:26.494019", "module": null, "name": "Purchase Invoice-payment_schedule-print_hide", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Sales Invoice Item", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "DocType", "field_name": null, "is_system_generated": 0, "modified": "2025-02-23 17:05:36.253422", "module": null, "name": "Sales Invoice Item-main-field_order", "property": "field_order", "property_type": "Data", "row_name": null, "value": "[\"barcode\", \"has_item_scanned\", \"item_code\", \"col_break1\", \"item_name\", \"customer_item_code\", \"description_section\", \"description\", \"item_group\", \"brand\", \"image_section\", \"image\", \"image_view\", \"quantity_and_rate\", \"qty\", \"stock_uom\", \"col_break2\", \"uom\", \"conversion_factor\", \"stock_qty\", \"section_break_17\", \"price_list_rate\", \"base_price_list_rate\", \"discount_and_margin\", \"margin_type\", \"margin_rate_or_amount\", \"rate_with_margin\", \"column_break_19\", \"discount_percentage\", \"discount_amount\", \"base_rate_with_margin\", \"section_break1\", \"rate\", \"amount\", \"item_tax_template\", \"col_break3\", \"base_rate\", \"base_amount\", \"pricing_rules\", \"stock_uom_rate\", \"is_free_item\", \"grant_commission\", \"section_break_21\", \"net_rate\", \"net_amount\", \"column_break_24\", \"base_net_rate\", \"base_net_amount\", \"drop_ship\", \"delivered_by_supplier\", \"accounting\", \"income_account\", \"is_fixed_asset\", \"asset\", \"finance_book\", \"col_break4\", \"expense_account\", \"discount_account\", \"deferred_revenue\", \"deferred_revenue_account\", \"service_stop_date\", \"enable_deferred_revenue\", \"column_break_50\", \"service_start_date\", \"service_end_date\", \"section_break_18\", \"weight_per_unit\", \"total_weight\", \"column_break_21\", \"weight_uom\", \"warehouse_and_reference\", \"warehouse\", \"target_warehouse\", \"quality_inspection\", \"pick_serial_and_batch\", \"serial_and_batch_bundle\", \"use_serial_batch_fields\", \"col_break5\", \"allow_zero_valuation_rate\", \"incoming_rate\", \"item_tax_rate\", \"actual_batch_qty\", \"actual_qty\", \"section_break_eoec\", \"serial_no\", \"column_break_ytgd\", \"batch_no\", \"edit_references\", \"sales_order\", \"so_detail\", \"sales_invoice_item\", \"column_break_74\", \"delivery_note\", \"dn_detail\", \"delivered_qty\", \"internal_transfer_section\", \"purchase_order\", \"column_break_92\", \"purchase_order_item\", \"accounting_dimensions_section\", \"cost_center\", \"dimension_col_break\", \"project\", \"section_break_54\", \"page_break\", \"custom_valuation_rate\"]"}]