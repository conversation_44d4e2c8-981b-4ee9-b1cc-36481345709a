[{"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "accept_payment", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payments", "is_system_generated": 1, "is_virtual": 0, "label": "Accept Payment", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:13.513168", "module": null, "name": "Web Form-accept_payment", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "car_record", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 1, "in_preview": 0, "in_standard_filter": 0, "insert_after": "item_code", "is_system_generated": 1, "is_virtual": 0, "label": "Car Record / سجل السيارة", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:14.747430", "module": null, "name": "Sales Invoice Item-car_record", "no_copy": 0, "non_negative": 0, "options": "Car Record", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": "car_record.chassis_no", "fetch_if_empty": 0, "fieldname": "chassis_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 1, "in_preview": 0, "in_standard_filter": 0, "insert_after": "car_record", "is_system_generated": 1, "is_virtual": 0, "label": "<PERSON><PERSON><PERSON> / رقم الهيكل", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:15.242864", "module": null, "name": "Sales Invoice Item-chassis_number", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": "car_record.engine_no", "fetch_if_empty": 0, "fieldname": "engine_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "chassis_number", "is_system_generated": 1, "is_virtual": 0, "label": "Engine No / رقم المحرك", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:15.504208", "module": null, "name": "Sales Invoice Item-engine_number", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "1", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Print Settings", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "compact_item_print", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "with_letterhead", "is_system_generated": 1, "is_virtual": 0, "label": "Compact Item Print", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 15:05:27.455066", "module": null, "name": "Print Settings-compact_item_print", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Print Settings", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "print_uom_after_quantity", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "compact_item_print", "is_system_generated": 1, "is_virtual": 0, "label": "Print UOM after Quantity", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 15:05:27.495393", "module": null, "name": "Print Settings-print_uom_after_quantity", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Address", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "tax_category", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "fax", "is_system_generated": 0, "is_virtual": 0, "label": "Tax Category", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2018-12-28 22:29:21.828090", "module": null, "name": "Address-tax_category", "no_copy": 0, "non_negative": 0, "options": "Tax Category", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Print Settings", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "print_taxes_with_zero_amount", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "allow_print_for_cancelled", "is_system_generated": 1, "is_virtual": 0, "label": "Print taxes with zero amount", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 15:05:27.536758", "module": null, "name": "Print Settings-print_taxes_with_zero_amount", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Address", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_your_company_address", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "linked_with", "is_system_generated": 0, "is_virtual": 0, "label": "Is Your Company Address", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2020-10-14 17:41:40.878179", "module": null, "name": "Address-is_your_company_address", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Contact", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_billing_contact", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_primary_contact", "is_system_generated": 0, "is_virtual": 0, "label": "Is Billing Contact", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2019-12-02 11:00:03.432994", "module": null, "name": "Contact-is_billing_contact", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payments_tab", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_css", "is_system_generated": 1, "is_virtual": 0, "label": "Payments", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:13.239241", "module": null, "name": "Web Form-payments_tab", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Buy Now", "depends_on": "accept_payment", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payment_button_label", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payment_gateway", "is_system_generated": 1, "is_virtual": 0, "label": "Button Label", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:13.737818", "module": null, "name": "Web Form-payment_button_label", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payments_cb", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payment_button_help", "is_system_generated": 1, "is_virtual": 0, "label": null, "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:13.982517", "module": null, "name": "Web Form-payments_cb", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.accept_payment && doc.amount_based_on_field", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "amount_field", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "amount_based_on_field", "is_system_generated": 1, "is_virtual": 0, "label": "Amount Field", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:14.516778", "module": null, "name": "Web Form-amount_field", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.accept_payment && !doc.amount_based_on_field", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "amount_field", "is_system_generated": 1, "is_virtual": 0, "label": "Amount", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:14.794937", "module": null, "name": "Web Form-amount", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "accept_payment", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "currency", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "amount", "is_system_generated": 1, "is_virtual": 0, "label": "<PERSON><PERSON><PERSON><PERSON>", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:14.984753", "module": null, "name": "Web Form-currency", "no_copy": 0, "non_negative": 0, "options": "<PERSON><PERSON><PERSON><PERSON>", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": "accept_payment", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "amount_based_on_field", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payments_cb", "is_system_generated": 1, "is_virtual": 0, "label": "Amount Based On Field", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:14.101954", "module": null, "name": "Web Form-amount_based_on_field", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "accept_payment", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payment_button_help", "fieldtype": "Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payment_button_label", "is_system_generated": 1, "is_virtual": 0, "label": "Button Help", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:13.857594", "module": null, "name": "Web Form-payment_button_help", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "accept_payment", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Web Form", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payment_gateway", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "accept_payment", "is_system_generated": 1, "is_virtual": 0, "label": "Payment Gateway", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-03-06 14:50:13.624763", "module": null, "name": "Web Form-payment_gateway", "no_copy": 0, "non_negative": 0, "options": "Payment Gateway", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 1, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": "", "fetch_if_empty": 0, "fieldname": "custom_valuation_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "page_break", "is_system_generated": 0, "is_virtual": 0, "label": "Valuation Rate", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-02-23 17:03:43.304953", "module": null, "name": "Sales Invoice Item-custom_valuation_rate", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": "eval:doc.chassis_no || doc.engine_no", "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "car_details_section", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "customer_address", "is_system_generated": 1, "is_virtual": 0, "label": "Car Details / تفاصيل السيارة", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:03.656468", "module": null, "name": "Sales Invoice-car_details_section", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 1, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "chassis_no", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "car_details_section", "is_system_generated": 1, "is_virtual": 0, "label": "Chassis Number / رقم الهيكل", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:09.640247", "module": null, "name": "Sales Invoice-chassis_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 1, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "engine_no", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "chassis_no", "is_system_generated": 1, "is_virtual": 0, "label": "Engine Number / رقم المحرك", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:10.407380", "module": null, "name": "Sales Invoice-engine_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "col_break_car_1", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "engine_no", "is_system_generated": 1, "is_virtual": 0, "label": null, "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:11.259926", "module": null, "name": "Sales Invoice-col_break_car_1", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "car_color", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "col_break_car_1", "is_system_generated": 1, "is_virtual": 0, "label": "Car Color / لون السيارة", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:11.907769", "module": null, "name": "Sales Invoice-car_color", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "car_model", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "car_color", "is_system_generated": 1, "is_virtual": 0, "label": "Model / الموديل", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:12.452062", "module": null, "name": "Sales Invoice-car_model", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "col_break_car_2", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "car_model", "is_system_generated": 1, "is_virtual": 0, "label": null, "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:12.912190", "module": null, "name": "Sales Invoice-col_break_car_2", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "manufacture_year", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "col_break_car_2", "is_system_generated": 1, "is_virtual": 0, "label": "Manufacture Year / سنة الصنع", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:13.511545", "module": null, "name": "Sales Invoice-manufacture_year", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "showroom_name", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "manufacture_year", "is_system_generated": 1, "is_virtual": 0, "label": "Showroom Name / اسم المعرض", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-06-27 17:45:14.086721", "module": null, "name": "Sales Invoice-showroom_name", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}]