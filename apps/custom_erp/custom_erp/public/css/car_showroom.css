/* Car Showroom CSS Styles */

/* تنسيقات عامة للنظام */
.rtl-layout {
    direction: rtl;
    text-align: right;
}

.rtl-layout .container-fluid {
    direction: rtl;
}

/* تنسيقات معرض الصور */
.car-image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.car-image-item {
    position: relative;
    width: 150px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.car-image-item:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.car-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: white;
    padding: 8px;
    font-size: 12px;
    text-align: center;
}

/* تنسيقات كروت السيارات */
.car-card {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    margin: 10px 0;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.car-card:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.car-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.car-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.car-status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.car-status.available {
    background-color: #d4edda;
    color: #155724;
}

.car-status.reserved {
    background-color: #fff3cd;
    color: #856404;
}

.car-status.sold {
    background-color: #f8d7da;
    color: #721c24;
}

.car-status.maintenance {
    background-color: #cce5ff;
    color: #004085;
}

.car-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.car-detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.car-detail-label {
    font-weight: bold;
    color: #666;
    min-width: 80px;
}

.car-detail-value {
    color: #333;
}

.car-price-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: center;
}

.car-price-main {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.car-price-details {
    font-size: 14px;
    opacity: 0.9;
}

/* تنسيقات الفلاتر المخصصة */
.custom-filters {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-item {
    min-width: 200px;
}

/* تنسيقات الأزرار المخصصة */
.car-action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: flex-end;
}

.btn-car-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-car-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-car-secondary {
    background: #6c757d;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-car-success {
    background: #28a745;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* تنسيقات الإحصائيات */
.car-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.car-stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.car-stat-card:hover {
    transform: translateY(-5px);
}

.car-stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
}

.car-stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-available .car-stat-number {
    color: #28a745;
}

.stat-sold .car-stat-number {
    color: #dc3545;
}

.stat-reserved .car-stat-number {
    color: #ffc107;
}

.stat-maintenance .car-stat-number {
    color: #17a2b8;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .car-details {
        grid-template-columns: 1fr;
    }
    
    .car-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .car-action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .car-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .car-image-gallery {
        flex-direction: column;
        align-items: center;
    }
    
    .car-image-item {
        width: 100%;
        max-width: 300px;
    }
}