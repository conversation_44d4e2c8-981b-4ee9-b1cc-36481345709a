// Car Showroom JavaScript Functions
frappe.provide('car_showroom');

// تهيئة النظام عند تحميل الصفحة
$(document).ready(function() {
    car_showroom.init();
});

car_showroom.init = function() {
    // إضافة ستايل للصفحات العربية
    if (frappe.boot.lang === 'ar') {
        $('body').addClass('rtl-layout');
    }
    
    // تحسين عرض السيارات
    car_showroom.setup_car_display();
};

car_showroom.setup_car_display = function() {
    // إضافة فلاتر مخصصة للسيارات
    if (cur_page && cur_page.page_name === 'List/Car Record') {
        car_showroom.add_custom_filters();
    }
};

car_showroom.add_custom_filters = function() {
    // إضافة فلتر سريع للحالة
    if (cur_list) {
        cur_list.page.add_field({
            fieldtype: 'Select',
            fieldname: 'quick_status_filter',
            label: 'فلتر سريع للحالة',
            options: '\nمتاحة\nمحجوزة\nمباعة\nقيد الصيانة\nقيد التقييم',
            change: function() {
                let status = this.get_value();
                if (status) {
                    cur_list.filter_area.add([['Car Record', 'status', '=', status]]);
                } else {
                    cur_list.filter_area.clear();
                }
                cur_list.refresh();
            }
        });
        
        // إضافة زر إنشاء عرض سعر سريع
        cur_list.page.add_button('إنشاء عرض سعر', function() {
            car_showroom.create_quick_quotation();
        }, 'primary');
    }
};

car_showroom.create_quick_quotation = function() {
    let selected_cars = cur_list.get_checked_items();
    
    if (selected_cars.length === 0) {
        frappe.msgprint('يرجى اختيار سيارة واحدة على الأقل');
        return;
    }
    
    if (selected_cars.length > 1) {
        frappe.msgprint('يمكن إنشاء عرض سعر لسيارة واحدة فقط في المرة الواحدة');
        return;
    }
    
    let car = selected_cars[0];
    
    // طلب بيانات العميل
    frappe.prompt([
        {
            'fieldname': 'customer',
            'fieldtype': 'Link',
            'options': 'Customer',
            'label': 'العميل',
            'reqd': 1
        },
        {
            'fieldname': 'valid_till',
            'fieldtype': 'Date',
            'label': 'صالح حتى',
            'default': frappe.datetime.add_days(frappe.datetime.now_date(), 30)
        }
    ], function(values) {
        frappe.call({
            method: 'custom_erp.car_showroom.doctype.car_record.car_record.create_quotation',
            args: {
                'car_record': car.name,
                'customer': values.customer,
                'valid_till': values.valid_till
            },
            callback: function(r) {
                if (r.message) {
                    frappe.msgprint('تم إنشاء عرض السعر بنجاح');
                    frappe.set_route('Form', 'Quotation', r.message);
                }
            }
        });
    }, 'إنشاء عرض سعر', 'إنشاء');
};

// دوال مساعدة للسيارات
car_showroom.format_car_price = function(price) {
    if (!price) return 'غير محدد';
    return format_currency(price) + ' ريال';
};

car_showroom.get_car_age = function(year) {
    let current_year = new Date().getFullYear();
    return current_year - year;
};

car_showroom.calculate_depreciation = function(purchase_price, year) {
    let age = car_showroom.get_car_age(year);
    let depreciation_rate = 0.15; // 15% سنوياً
    return purchase_price * Math.pow((1 - depreciation_rate), age);
};

// تحسينات خاصة بصفحة السيارة
frappe.ui.form.on('Car Record', {
    refresh: function(frm) {
        car_showroom.setup_car_form(frm);
    },
    
    purchase_price: function(frm) {
        car_showroom.update_profit_margin(frm);
    },
    
    selling_price: function(frm) {
        car_showroom.update_profit_margin(frm);
    },
    
    year: function(frm) {
        if (frm.doc.year && frm.doc.purchase_price) {
            let estimated_value = car_showroom.calculate_depreciation(frm.doc.purchase_price, frm.doc.year);
            frm.set_value('minimum_price', estimated_value * 0.9); // 90% من القيمة المقدرة
        }
    }
});

car_showroom.setup_car_form = function(frm) {
    // إضافة أزرار مخصصة
    if (frm.doc.docstatus === 0) {
        frm.add_custom_button('إنشاء عرض سعر', function() {
            car_showroom.create_quotation_from_form(frm);
        }, 'الإجراءات');
        
        frm.add_custom_button('تحديد كمباعة', function() {
            car_showroom.mark_as_sold(frm);
        }, 'الإجراءات');
        
        frm.add_custom_button('تحديث حالة المخزون', function() {
            car_showroom.update_stock_status(frm);
        }, 'الإجراءات');
    }
    
    // تحسين عرض الصور
    if (frm.doc.car_images && frm.doc.car_images.length > 0) {
        car_showroom.setup_image_gallery(frm);
    }
    
    // إضافة معلومات إضافية
    car_showroom.add_car_insights(frm);
};

car_showroom.update_profit_margin = function(frm) {
    if (frm.doc.purchase_price && frm.doc.selling_price) {
        let profit = frm.doc.selling_price - frm.doc.purchase_price;
        let margin = (profit / frm.doc.purchase_price) * 100;
        frm.set_value('profit_margin', margin);
        
        // تنبيه إذا كان الهامش منخفض أو سالب
        if (margin < 0) {
            frm.dashboard.add_indicator('خسارة: ' + format_currency(Math.abs(profit)), 'red');
        } else if (margin < 10) {
            frm.dashboard.add_indicator('هامش ربح منخفض: ' + margin.toFixed(2) + '%', 'orange');
        } else {
            frm.dashboard.add_indicator('هامش ربح جيد: ' + margin.toFixed(2) + '%', 'green');
        }
    }
};

car_showroom.create_quotation_from_form = function(frm) {
    if (!frm.doc.name) {
        frappe.msgprint('يرجى حفظ السيارة أولاً');
        return;
    }
    
    frappe.prompt([
        {
            'fieldname': 'customer',
            'fieldtype': 'Link',
            'options': 'Customer',
            'label': 'العميل',
            'reqd': 1
        }
    ], function(values) {
        frappe.call({
            method: 'create_quotation',
            doc: frm.doc,
            args: {
                'customer': values.customer
            },
            callback: function(r) {
                if (r.message) {
                    frappe.msgprint('تم إنشاء عرض السعر بنجاح');
                    frappe.set_route('Form', 'Quotation', r.message.name);
                }
            }
        });
    }, 'إنشاء عرض سعر', 'إنشاء');
};

car_showroom.mark_as_sold = function(frm) {
    frappe.prompt([
        {
            'fieldname': 'customer',
            'fieldtype': 'Link',
            'options': 'Customer',
            'label': 'العميل',
            'reqd': 1
        },
        {
            'fieldname': 'sales_invoice',
            'fieldtype': 'Link',
            'options': 'Sales Invoice',
            'label': 'فاتورة البيع'
        }
    ], function(values) {
        frappe.call({
            method: 'mark_as_sold',
            doc: frm.doc,
            args: values,
            callback: function(r) {
                if (r.message) {
                    frm.reload_doc();
                }
            }
        });
    }, 'تحديد كمباعة', 'تحديث');
};

car_showroom.setup_image_gallery = function(frm) {
    // إنشاء معرض صور تفاعلي
    let images_html = '<div class="car-image-gallery">';
    
    frm.doc.car_images.forEach(function(img) {
        if (img.image_file) {
            images_html += `
                <div class="car-image-item" data-type="${img.image_type || ''}">
                    <img src="${img.image_file}" alt="${img.image_title || ''}" 
                         class="car-thumbnail" onclick="car_showroom.show_image('${img.image_file}')">
                    <div class="image-caption">${img.image_title || img.image_type || 'صورة'}</div>
                </div>
            `;
        }
    });
    
    images_html += '</div>';
    
    // إضافة المعرض للفورم
    $(frm.fields_dict.car_images.wrapper).after(images_html);
};

car_showroom.show_image = function(image_url) {
    let dialog = new frappe.ui.Dialog({
        title: 'عرض الصورة',
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'image_preview',
                options: `<img src="${image_url}" style="width: 100%; max-width: 500px; height: auto;">`
            }
        ]
    });
    dialog.show();
};

car_showroom.add_car_insights = function(frm) {
    if (frm.doc.year) {
        let age = car_showroom.get_car_age(frm.doc.year);
        frm.dashboard.add_indicator(`عمر السيارة: ${age} سنة`, age <= 3 ? 'green' : age <= 7 ? 'orange' : 'red');
    }
    
    if (frm.doc.mileage) {
        let mileage_status = frm.doc.mileage < 50000 ? 'ممتاز' : 
                           frm.doc.mileage < 100000 ? 'جيد' : 
                           frm.doc.mileage < 200000 ? 'متوسط' : 'عالي';
        let indicator_color = frm.doc.mileage < 50000 ? 'green' : 
                             frm.doc.mileage < 100000 ? 'blue' : 
                             frm.doc.mileage < 200000 ? 'orange' : 'red';
        frm.dashboard.add_indicator(`الكيلومترات: ${mileage_status}`, indicator_color);
    }
};

// تحسينات للداشبورد
frappe.provide('frappe.dashboards.chart_sources');

frappe.dashboards.chart_sources["Car Sales by Brand"] = {
	method: "custom_erp.car_showroom.dashboard.car_dashboard.get_sales_by_brand",
	filters: [
		{
			fieldname: "from_date",
			label: __("From Date"),
			fieldtype: "Date",
			default: frappe.datetime.add_months(frappe.datetime.get_today(), -1)
		},
		{
			fieldname: "to_date", 
			label: __("To Date"),
			fieldtype: "Date",
			default: frappe.datetime.get_today()
		}
	]
};