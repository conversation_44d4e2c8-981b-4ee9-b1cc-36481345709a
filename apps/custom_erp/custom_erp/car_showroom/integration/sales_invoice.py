# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import flt, nowdate

def validate_car_sale(doc, method):
	"""التحقق من صحة بيع السيارة"""
	for item in doc.items:
		# التحقق من وجود سيارة مرتبطة بالصنف
		car_record = frappe.db.get_value("Car Record", {"item_code": item.item_code}, "name")
		if car_record:
			car_doc = frappe.get_doc("Car Record", car_record)
			
			# التحقق من أن السيارة متاحة للبيع
			if car_doc.status != "متاحة":
				frappe.throw(f"السيارة {car_doc.car_name} غير متاحة للبيع. الحالة الحالية: {car_doc.status}")
			
			# التحقق من أن السعر لا يقل عن الحد الأدنى
			if car_doc.minimum_price and flt(item.rate) < flt(car_doc.minimum_price):
				frappe.throw(f"سعر السيارة {car_doc.car_name} أقل من الحد الأدنى المسموح {car_doc.minimum_price}")
			
			# ربط السيارة بالفاتورة
			item.car_record = car_record

def update_car_status(doc, method):
	"""تحديث حالة السيارة عند إرسال الفاتورة"""
	for item in doc.items:
		if item.get("car_record"):
			car_doc = frappe.get_doc("Car Record", item.car_record)
			
			# تحديث حالة السيارة
			car_doc.status = "مباعة"
			car_doc.customer = doc.customer
			car_doc.sale_date = nowdate()
			car_doc.sales_invoice = doc.name
			
			# إضافة ملاحظة إذا كان هناك خصم
			if flt(item.rate) < flt(car_doc.selling_price):
				discount = flt(car_doc.selling_price) - flt(item.rate)
				car_doc.internal_notes = (car_doc.internal_notes or "") + f"\nتم بيع السيارة بخصم {discount} في الفاتورة {doc.name}"
			
			car_doc.save(ignore_permissions=True)
			
			frappe.msgprint(f"تم تحديث حالة السيارة {car_doc.car_name} إلى مباعة", alert=True)