# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe

def validate_car_item(doc, method):
	"""التحقق من صحة أصناف السيارات"""
	# التحقق من وجود مجموعة أصناف السيارات
	if not frappe.db.exists("Item Group", "سيارات"):
		car_group = frappe.new_doc("Item Group")
		car_group.item_group_name = "سيارات"
		car_group.parent_item_group = "All Item Groups"
		car_group.is_group = 0
		car_group.save(ignore_permissions=True)
		frappe.msgprint("تم إنشاء مجموعة أصناف السيارات", alert=True)
	
	# إذا كان الصنف مرتبط بسيارة
	car_record = frappe.db.get_value("Car Record", {"item_code": doc.item_code}, "name")
	if car_record:
		car_doc = frappe.get_doc("Car Record", car_record)
		
		# تحديث معلومات الصنف من السيارة
		doc.item_group = "سيارات"
		doc.stock_uom = "Nos"
		doc.is_stock_item = 1
		doc.is_sales_item = 1
		doc.has_serial_no = 1
		
		# تحديث السعر من السيارة
		if car_doc.selling_price:
			doc.standard_rate = car_doc.selling_price
		
		# تحديث الوصف
		if not doc.description or "سيارة" not in doc.description:
			doc.description = f"""
			<div style="direction: rtl; text-align: right;">
				<h3>{car_doc.car_name}</h3>
				<p><strong>الماركة:</strong> {car_doc.brand}</p>
				<p><strong>الموديل:</strong> {car_doc.model}</p>
				<p><strong>سنة الصنع:</strong> {car_doc.year}</p>
				<p><strong>اللون:</strong> {car_doc.color or 'غير محدد'}</p>
				<p><strong>الكيلومترات:</strong> {car_doc.mileage or 0} كم</p>
				<p><strong>نوع الوقود:</strong> {car_doc.fuel_type or 'غير محدد'}</p>
				<p><strong>ناقل الحركة:</strong> {car_doc.transmission or 'غير محدد'}</p>
			</div>
			"""