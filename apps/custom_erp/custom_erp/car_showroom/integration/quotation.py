# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import flt

def validate_car_quotation(doc, method):
	"""التحقق من صحة عرض أسعار السيارات"""
	for item in doc.items:
		# البحث عن السيارة المرتبطة بالصنف
		car_record = frappe.db.get_value("Car Record", {"item_code": item.item_code}, "name")
		if car_record:
			car_doc = frappe.get_doc("Car Record", car_record)
			
			# التحقق من توفر السيارة
			if car_doc.status not in ["متاحة", "محجوزة"]:
				frappe.throw(f"السيارة {car_doc.car_name} غير متاحة. الحالة: {car_doc.status}")
			
			# التأكد من أن السعر معقول
			if car_doc.minimum_price and flt(item.rate) < flt(car_doc.minimum_price):
				frappe.msgprint(f"تحذير: سعر السيارة {car_doc.car_name} في العرض أقل من الحد الأدنى المسموح", alert=True)
			
			# ربط السيارة بعرض السعر
			item.car_record = car_record
			
			# إضافة وصف مفصل للسيارة
			if not item.description or len(item.description) < 50:
				item.description = f"""
				{car_doc.car_name} - {car_doc.brand} {car_doc.model} {car_doc.year}
				اللون: {car_doc.color or 'غير محدد'}
				الكيلومترات: {car_doc.mileage or 0} كم
				نوع الوقود: {car_doc.fuel_type or 'غير محدد'}
				ناقل الحركة: {car_doc.transmission or 'غير محدد'}
				الحالة الخارجية: {car_doc.exterior_condition or 'غير محدد'}
				الحالة الداخلية: {car_doc.interior_condition or 'غير محدد'}
				"""
			
			# تحديث حالة السيارة إلى محجوزة إذا كان العرض صالح
			if doc.docstatus == 1 and car_doc.status == "متاحة":  
				car_doc.status = "محجوزة"
				car_doc.save(ignore_permissions=True)
				frappe.msgprint(f"تم حجز السيارة {car_doc.car_name} مؤقتاً", alert=True)