# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe import _

def after_install():
	"""تنفيذ الإعدادات بعد تثبيت التطبيق"""
	setup_car_item_group()
	setup_default_brands()
	setup_workspace()
	create_sample_data()
	setup_additional_settings()

def setup_car_item_group():
	"""إنشاء مجموعة أصناف السيارات"""
	if not frappe.db.exists("Item Group", "سيارات"):
		item_group = frappe.new_doc("Item Group")
		item_group.item_group_name = "سيارات"
		item_group.parent_item_group = "All Item Groups"
		item_group.is_group = 0
		item_group.save(ignore_permissions=True)
		frappe.db.commit()
		print("تم إنشاء مجموعة أصناف السيارات")

def setup_default_brands():
	"""إنشاء الماركات الافتراضية"""
	default_brands = [
		{"name": "تويوتا", "brand_name_english": "Toyota", "is_luxury_brand": 0},
		{"name": "هوندا", "brand_name_english": "Honda", "is_luxury_brand": 0},
		{"name": "نيسان", "brand_name_english": "Nissan", "is_luxury_brand": 0},
		{"name": "هيونداي", "brand_name_english": "Hyundai", "is_luxury_brand": 0},
		{"name": "كيا", "brand_name_english": "Kia", "is_luxury_brand": 0},
		{"name": "مرسيدس", "brand_name_english": "Mercedes-Benz", "is_luxury_brand": 1},
		{"name": "بي ام دبليو", "brand_name_english": "BMW", "is_luxury_brand": 1},
		{"name": "أودي", "brand_name_english": "Audi", "is_luxury_brand": 1},
		{"name": "لكزس", "brand_name_english": "Lexus", "is_luxury_brand": 1},
	]
	
	for brand_data in default_brands:
		if not frappe.db.exists("Car Brand", brand_data["name"]):
			brand = frappe.new_doc("Car Brand")
			brand.brand_name = brand_data["name"]
			brand.brand_name_english = brand_data["brand_name_english"]
			brand.is_luxury_brand = brand_data["is_luxury_brand"]
			brand.save(ignore_permissions=True)
	
	frappe.db.commit()
	print("تم إنشاء الماركات الافتراضية")

def setup_workspace():
	"""إنشاء مساحة العمل"""
	workspace_data = {
		"name": "Car Showroom",
		"title": "معرض السيارات",
		"icon": "car",
		"indicator_color": "blue",
		"is_standard": 1,
		"module": "Car Showroom",
		"links": [
			{
				"type": "DocType",
				"label": "سجل السيارة",
				"name": "Car Record",
				"description": "إدارة سجلات السيارات"
			},
			{
				"type": "DocType", 
				"label": "ماركات السيارات",
				"name": "Car Brand",
				"description": "إدارة ماركات السيارات"
			},
			{
				"type": "DocType",
				"label": "العروض",
				"name": "Car Promotion", 
				"description": "إدارة عروض السيارات"
			},
			{
				"type": "Report",
				"label": "تحليل المبيعات",
				"name": "Car Sales Analysis",
				"description": "تقرير تحليل مبيعات السيارات"
			},
			{
				"type": "Report",
				"label": "تقرير المخزون",
				"name": "Car Inventory Report",
				"description": "تقرير مخزون السيارات"
			},
			{
				"type": "Page",
				"label": "لوحة المعلومات",
				"name": "car-dashboard",
				"description": "لوحة معلومات معرض السيارات"
			}
		]
	}
	
	# إنشاء workspace إذا لم يكن موجود
	if not frappe.db.exists("Workspace", "Car Showroom"):
		workspace = frappe.new_doc("Workspace")
		workspace.update(workspace_data)
		workspace.save(ignore_permissions=True)
		frappe.db.commit()
		print("تم إنشاء مساحة العمل")

def create_sample_data():
	"""إنشاء بيانات تجريبية"""
	# إنشاء عميل تجريبي
	if not frappe.db.exists("Customer", "عميل تجريبي"):
		customer = frappe.new_doc("Customer")
		customer.customer_name = "عميل تجريبي"
		customer.customer_type = "Individual"
		customer.customer_group = "All Customer Groups"
		customer.territory = "All Territories"
		customer.save(ignore_permissions=True)
	
	# إنشاء مورد تجريبي
	if not frappe.db.exists("Supplier", "مورد تجريبي"):
		supplier = frappe.new_doc("Supplier")
		supplier.supplier_name = "مورد تجريبي"
		supplier.supplier_type = "Company"
		supplier.supplier_group = "All Supplier Groups"
		supplier.save(ignore_permissions=True)
	
	frappe.db.commit()
	print("تم إنشاء البيانات التجريبية")

def setup_additional_settings():
	"""إعدادات إضافية"""
	try:
		from custom_erp.car_showroom.setup import (
			create_car_item_groups, create_customer_groups, 
			create_sales_person_tree, create_print_formats
		)
		
		create_car_item_groups()
		create_customer_groups()
		create_sales_person_tree()
		create_print_formats()
		frappe.db.commit()
		print("تم إنشاء الإعدادات الإضافية بنجاح")
	except Exception as e:
		print(f"تحذير: خطأ في الإعدادات الإضافية: {str(e)}")
		frappe.log_error(f"خطأ في الإعدادات الإضافية: {str(e)}")

def setup_permissions():
	"""إعداد الصلاحيات"""
	# سيتم إعداد الصلاحيات من خلال ملفات JSON
	pass