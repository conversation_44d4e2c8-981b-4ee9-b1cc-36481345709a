# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import unittest
import frappe
from frappe.utils import nowdate, add_days

class TestCarShowroom(unittest.TestCase):
	
	def setUp(self):
		"""إعداد البيانات للاختبار"""
		self.test_brand = self.create_test_brand()
		self.test_car = self.create_test_car()
		
	def create_test_brand(self):
		"""إنشاء ماركة للاختبار"""
		if not frappe.db.exists("Car Brand", "تويوتا للاختبار"):
			brand = frappe.new_doc("Car Brand")
			brand.brand_name = "تويوتا للاختبار"
			brand.brand_name_english = "Toyota Test"
			brand.is_luxury_brand = 0
			brand.insert(ignore_permissions=True)
			return brand.name
		return "تويوتا للاختبار"
	
	def create_test_car(self):
		"""إنشاء سيارة للاختبار"""
		car = frappe.new_doc("Car Record")
		car.car_name = "كامري للاختبار"
		car.brand = self.test_brand
		car.model = "كامري"
		car.year = 2020
		car.color = "أبيض"
		car.mileage = 50000
		car.fuel_type = "بنزين"
		car.transmission = "أوتوماتيك"
		car.status = "متاحة"
		car.purchase_price = 80000
		car.selling_price = 95000
		car.minimum_price = 85000
		car.acquisition_date = nowdate()
		car.insert(ignore_permissions=True)
		return car
	
	def test_car_creation(self):
		"""اختبار إنشاء سيارة"""
		self.assertTrue(frappe.db.exists("Car Record", self.test_car.name))
		self.assertEqual(self.test_car.status, "متاحة")
		self.assertEqual(self.test_car.brand, self.test_brand)
	
	def test_profit_calculation(self):
		"""اختبار حساب الربح"""
		expected_profit = self.test_car.selling_price - self.test_car.purchase_price
		expected_margin = (expected_profit / self.test_car.purchase_price) * 100
		
		# تحديث السيارة لحساب الربح
		self.test_car.save()
		
		self.assertEqual(self.test_car.profit_margin, expected_margin)
	
	def test_car_status_update(self):
		"""اختبار تحديث حالة السيارة"""
		# تغيير الحالة إلى محجوزة
		self.test_car.status = "محجوزة"
		self.test_car.save()
		
		# التحقق من التحديث
		updated_car = frappe.get_doc("Car Record", self.test_car.name)
		self.assertEqual(updated_car.status, "محجوزة")
	
	def test_car_validation(self):
		"""اختبار التحقق من صحة بيانات السيارة"""
		# اختبار سعر بيع أقل من الحد الأدنى
		car = frappe.new_doc("Car Record")
		car.car_name = "سيارة اختبار فشل"
		car.brand = self.test_brand
		car.model = "اختبار"
		car.year = 2020
		car.purchase_price = 50000
		car.selling_price = 40000  # أقل من سعر الشراء
		car.minimum_price = 45000
		
		with self.assertRaises(frappe.ValidationError):
			car.insert()
	
	def test_dashboard_functions(self):
		"""اختبار دوال لوحة المعلومات"""
		from custom_erp.car_showroom.dashboard.car_dashboard import get_car_stats
		
		stats = get_car_stats()
		
		self.assertIsInstance(stats, dict)
		self.assertIn('total_cars', stats)
		self.assertIn('available_cars', stats)
		self.assertIn('sold_cars', stats)
	
	def test_utils_functions(self):
		"""اختبار الدوال المساعدة"""
		from custom_erp.car_showroom.utils import calculate_car_value, validate_vin_number
		
		# اختبار حساب قيمة السيارة
		estimated_value = calculate_car_value(100000, 2020, 50000)
		self.assertGreater(estimated_value, 0)
		self.assertLess(estimated_value, 100000)  # يجب أن تكون أقل من سعر الشراء
		
		# اختبار التحقق من رقم الهيكل
		valid_vin = validate_vin_number("1HGBH41JXMN109186")
		self.assertTrue(valid_vin["valid"])
		
		invalid_vin = validate_vin_number("123")  # قصير جداً
		self.assertFalse(invalid_vin["valid"])
	
	def tearDown(self):
		"""تنظيف البيانات بعد الاختبار"""
		# حذف السيارة التجريبية
		if frappe.db.exists("Car Record", self.test_car.name):
			frappe.delete_doc("Car Record", self.test_car.name, force=True)
		
		# حذف الماركة التجريبية
		if frappe.db.exists("Car Brand", self.test_brand):
			frappe.delete_doc("Car Brand", self.test_brand, force=True)

if __name__ == '__main__':
	unittest.main()