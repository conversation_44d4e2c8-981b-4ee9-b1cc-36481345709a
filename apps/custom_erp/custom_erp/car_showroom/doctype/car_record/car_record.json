{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["basic_info_section", "naming_series", "car_name", "vin_number", "column_break_5", "brand", "model", "year", "section_break_9", "body_type", "color", "engine_type", "column_break_13", "transmission", "fuel_type", "mileage", "pricing_section", "purchase_price", "selling_price", "column_break_20", "minimum_price", "profit_margin", "status_section", "status", "location", "column_break_26", "acquisition_date", "sale_date", "features_section", "luxury_features", "safety_features", "section_break_32", "interior_color", "exterior_condition", "column_break_35", "interior_condition", "service_history", "images_section", "car_images", "documents_section", "ownership_documents", "insurance_info", "section_break_42", "registration_number", "registration_expiry", "column_break_45", "insurance_expiry", "last_service_date", "customer_info_section", "customer", "sales_person", "column_break_51", "deposit_amount", "payment_terms", "notes_section", "notes", "internal_notes"], "fields": [{"fieldname": "basic_info_section", "fieldtype": "Section Break", "label": "المعلومات الأساسية"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "سلسلة الترقيم", "options": "CAR-.YYYY.-\nLUX-.YYYY.-\nPRM-.YYYY.-", "reqd": 1}, {"fieldname": "car_name", "fieldtype": "Data", "in_list_view": 1, "label": "اسم السيارة", "reqd": 1}, {"fieldname": "vin_number", "fieldtype": "Data", "label": "رقم الهيكل (VIN)", "unique": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "brand", "fieldtype": "Link", "in_list_view": 1, "label": "الماركة", "options": "Car Brand", "reqd": 1}, {"fieldname": "model", "fieldtype": "Data", "in_list_view": 1, "label": "الموديل", "reqd": 1}, {"fieldname": "year", "fieldtype": "Int", "in_list_view": 1, "label": "سنة الصنع", "reqd": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "المواصفات التقنية"}, {"fieldname": "body_type", "fieldtype": "Select", "label": "نوع الهيكل", "options": "سيدان\nSUV\nهاتشباك\nكوبيه\nكونفرتبل\nبيك آب\nفان\nسبورت"}, {"fieldname": "color", "fieldtype": "Data", "label": "اللون الخارجي"}, {"fieldname": "engine_type", "fieldtype": "Data", "label": "نوع المحرك"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"fieldname": "transmission", "fieldtype": "Select", "label": "ناقل الحركة", "options": "أوتوماتيك\nيدوي\nCVT\nهجين"}, {"fieldname": "fuel_type", "fieldtype": "Select", "label": "نوع الوقود", "options": "بنزين\nديزل\nهجين\nكهربائي\nغاز"}, {"fieldname": "mileage", "fieldtype": "Float", "label": "الكيلومترات المقطوعة"}, {"fieldname": "pricing_section", "fieldtype": "Section Break", "label": "معلومات السعر"}, {"fieldname": "purchase_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "سعر الشراء", "precision": "2"}, {"fieldname": "selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "سعر البيع", "precision": "2", "reqd": 1}, {"fieldname": "column_break_20", "fieldtype": "Column Break"}, {"fieldname": "minimum_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "أقل سعر مقبول", "precision": "2"}, {"fieldname": "profit_margin", "fieldtype": "Percent", "label": "ها<PERSON><PERSON> الربح"}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "الحالة والموقع"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "الحالة", "options": "متاحة\nمحجوزة\nمباعة\nقيد الصيانة\nقيد التقييم", "reqd": 1}, {"fieldname": "location", "fieldtype": "Data", "label": "الموقع في المعرض"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"fieldname": "acquisition_date", "fieldtype": "Date", "label": "تاريخ الاستحواذ"}, {"fieldname": "sale_date", "fieldtype": "Date", "label": "تاريخ البيع"}, {"fieldname": "features_section", "fieldtype": "Section Break", "label": "الميزات والإضافات"}, {"fieldname": "luxury_features", "fieldtype": "Table", "label": "الميزات الفخمة", "options": "Car Luxury Feature"}, {"fieldname": "safety_features", "fieldtype": "Table", "label": "ميزات الأمان", "options": "Car Safety Feature"}, {"fieldname": "section_break_32", "fieldtype": "Section Break"}, {"fieldname": "interior_color", "fieldtype": "Data", "label": "لون الداخلية"}, {"fieldname": "exterior_condition", "fieldtype": "Select", "label": "حالة الهيكل الخارجي", "options": "ممتازة\nجيدة جداً\nجيدة\nمقبولة\nتحتاج إصلاح"}, {"fieldname": "column_break_35", "fieldtype": "Column Break"}, {"fieldname": "interior_condition", "fieldtype": "Select", "label": "حالة الداخلية", "options": "ممتازة\nجيدة جداً\nجيدة\nمقبولة\nتحتاج إصلاح"}, {"fieldname": "service_history", "fieldtype": "Table", "label": "تاريخ الصيانة", "options": "Car Service History"}, {"fieldname": "images_section", "fieldtype": "Section Break", "label": "صور السيارة"}, {"fieldname": "car_images", "fieldtype": "Table", "label": "الصور", "options": "Car Image"}, {"fieldname": "documents_section", "fieldtype": "Section Break", "label": "الوثائق والتأمين"}, {"fieldname": "ownership_documents", "fieldtype": "Table", "label": "وثائق الملكية", "options": "Car Document"}, {"fieldname": "insurance_info", "fieldtype": "Link", "label": "شركة التأمين", "options": "Supplier"}, {"fieldname": "section_break_42", "fieldtype": "Section Break"}, {"fieldname": "registration_number", "fieldtype": "Data", "label": "رقم اللوحة"}, {"fieldname": "registration_expiry", "fieldtype": "Date", "label": "انتهاء الترخيص"}, {"fieldname": "column_break_45", "fieldtype": "Column Break"}, {"fieldname": "insurance_expiry", "fieldtype": "Date", "label": "انتهاء التأمين"}, {"fieldname": "last_service_date", "fieldtype": "Date", "label": "آخر صيانة"}, {"fieldname": "customer_info_section", "fieldtype": "Section Break", "label": "معلومات العميل والبيع"}, {"fieldname": "customer", "fieldtype": "Link", "label": "العميل", "options": "Customer"}, {"fieldname": "sales_person", "fieldtype": "Link", "label": "موظف المبيعات", "options": "Sales Person"}, {"fieldname": "column_break_51", "fieldtype": "Column Break"}, {"fieldname": "deposit_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "مب<PERSON>غ العربون", "precision": "2"}, {"fieldname": "payment_terms", "fieldtype": "Link", "label": "شروط الدفع", "options": "Payment Terms Template"}, {"fieldname": "notes_section", "fieldtype": "Section Break", "label": "الملاحظات"}, {"fieldname": "notes", "fieldtype": "Text Editor", "label": "ملاحظات عامة"}, {"fieldname": "internal_notes", "fieldtype": "Text Editor", "label": "ملاحظات داخلية"}], "index_web_pages_for_search": 1, "links": [{"link_doctype": "Sales Invoice", "link_fieldname": "car_record"}, {"link_doctype": "Quotation", "link_fieldname": "car_record"}], "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Car Record", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "car_name", "track_changes": 1, "track_seen": 1, "track_views": 1}