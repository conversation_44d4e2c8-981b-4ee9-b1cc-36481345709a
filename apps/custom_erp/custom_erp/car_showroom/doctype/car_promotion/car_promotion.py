# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe.utils import nowdate, getdate, add_days

class CarPromotion(Document):
	def validate(self):
		self.validate_dates()
		self.validate_items()
		
	def validate_dates(self):
		"""التحقق من صحة التواريخ"""
		if self.end_date and self.start_date:
			if getdate(self.end_date) < getdate(self.start_date):
				frappe.throw("تاريخ انتهاء العرض لا يمكن أن يكون قبل تاريخ البداية")
		
		if self.start_date and getdate(self.start_date) < getdate(nowdate()):
			frappe.msgprint("تحذير: تاريخ بداية العرض في الماضي", alert=True)
	
	def validate_items(self):
		"""التحقق من صحة أصناف العرض"""
		if not self.promotion_items:
			frappe.throw("يجب إضافة أصناف للعرض")
		
		# التأكد من عدم تكرار الأصناف
		seen_items = []
		for item in self.promotion_items:
			if item.car_record in seen_items:
				frappe.throw(f"السيارة {item.car_record} مكررة في قائمة العرض")
			seen_items.append(item.car_record)
	
	def on_submit(self):
		"""عند تأكيد العرض"""
		self.update_car_prices()
		
	def on_cancel(self):
		"""عند إلغاء العرض"""
		self.reset_car_prices()
	
	def update_car_prices(self):
		"""تحديث أسعار السيارات في العرض"""
		for item in self.promotion_items:
			if item.car_record and item.promotional_price:
				car_doc = frappe.get_doc("Car Record", item.car_record)
				
				# حفظ السعر الأصلي
				item.original_price = car_doc.selling_price
				
				# تحديث السعر
				car_doc.selling_price = item.promotional_price
				car_doc.add_comment("Comment", f"تم تطبيق عرض {self.name}: السعر من {item.original_price} إلى {item.promotional_price}")
				car_doc.save(ignore_permissions=True)
				
	def reset_car_prices(self):
		"""إعادة أسعار السيارات للسعر الأصلي"""
		for item in self.promotion_items:
			if item.car_record and item.original_price:
				car_doc = frappe.get_doc("Car Record", item.car_record)
				car_doc.selling_price = item.original_price
				car_doc.add_comment("Comment", f"تم إلغاء عرض {self.name}: إعادة السعر إلى {item.original_price}")
				car_doc.save(ignore_permissions=True)