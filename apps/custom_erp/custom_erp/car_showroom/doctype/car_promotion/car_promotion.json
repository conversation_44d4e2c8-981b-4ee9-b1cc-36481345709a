{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["basic_section", "naming_series", "promotion_title", "promotion_type", "column_break_5", "start_date", "end_date", "is_active", "details_section", "promotion_description", "terms_conditions", "section_break_11", "applicable_cars", "discount_section", "discount_type", "discount_amount", "column_break_16", "maximum_discount", "minimum_purchase_amount", "marketing_section", "promotion_banner", "social_media_post", "column_break_22", "email_template", "whatsapp_message"], "fields": [{"fieldname": "basic_section", "fieldtype": "Section Break", "label": "معلومات العرض"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "سلسلة الترقيم", "options": "PROMO-.YYYY.-\nSALE-.YYYY.-\nSPCL-.YYYY.-", "reqd": 1}, {"fieldname": "promotion_title", "fieldtype": "Data", "in_list_view": 1, "label": "عنوان العرض", "reqd": 1}, {"fieldname": "promotion_type", "fieldtype": "Select", "in_list_view": 1, "label": "نوع العرض", "options": "خصم نقدي\nخصم نسبي\nعرض تبديل\nتمويل بدون فوائد\nهدايا مجانية\nصيانة مجانية\nتأمين مجاني", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "start_date", "fieldtype": "Date", "in_list_view": 1, "label": "تاريخ البداية", "reqd": 1}, {"fieldname": "end_date", "fieldtype": "Date", "in_list_view": 1, "label": "تاريخ النهاية", "reqd": 1}, {"fieldname": "is_active", "fieldtype": "Check", "label": "فعال"}, {"fieldname": "details_section", "fieldtype": "Section Break", "label": "تفاصيل العرض"}, {"fieldname": "promotion_description", "fieldtype": "Text Editor", "label": "وصف العرض"}, {"fieldname": "terms_conditions", "fieldtype": "Text Editor", "label": "الشروط والأحكام"}, {"fieldname": "section_break_11", "fieldtype": "Section Break"}, {"fieldname": "applicable_cars", "fieldtype": "Table", "label": "السيارات المشمولة", "options": "Car Promotion Item"}, {"fieldname": "discount_section", "fieldtype": "Section Break", "label": "تفاصيل الخصم"}, {"fieldname": "discount_type", "fieldtype": "Select", "label": "نوع الخصم", "options": "مبلغ ثابت\nنسبة مئوية"}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "قيمة الخصم", "precision": "2"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "maximum_discount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON> خصم", "precision": "2"}, {"fieldname": "minimum_purchase_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "أقل مبلغ شراء", "precision": "2"}, {"fieldname": "marketing_section", "fieldtype": "Section Break", "label": "المواد التسويقية"}, {"fieldname": "promotion_banner", "fieldtype": "Attach Image", "label": "بانر العرض"}, {"fieldname": "social_media_post", "fieldtype": "Text Editor", "label": "منشور وسائل التواصل"}, {"fieldname": "column_break_22", "fieldtype": "Column Break"}, {"fieldname": "email_template", "fieldtype": "Link", "label": "قالب الإيميل", "options": "<PERSON>ail Te<PERSON>late"}, {"fieldname": "whatsapp_message", "fieldtype": "Text", "label": "رسالة واتساب"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Car Promotion", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "promotion_title", "track_changes": 1}