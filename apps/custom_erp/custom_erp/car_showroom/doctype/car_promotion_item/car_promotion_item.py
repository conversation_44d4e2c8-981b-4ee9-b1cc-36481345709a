# Copyright (c) 2024, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class CarPromotionItem(Document):
	"""Car Promotion Item doctype for handling individual car promotions"""
	
	def before_save(self):
		"""Validate and set data before saving"""
		if self.car_record:
			# Fetch car brand from car record if not already set
			if not self.car_brand:
				car_doc = frappe.get_doc("Car Record", self.car_record)
				if car_doc.brand:
					self.car_brand = car_doc.brand
	
	def validate(self):
		"""Validate promotion item data"""
		if self.special_discount and self.special_discount < 0:
			frappe.throw("الخصم الخاص لا يمكن أن يكون قيمة سالبة")
		
		# Ensure car_record exists
		if self.car_record and not frappe.db.exists("Car Record", self.car_record):
			frappe.throw(f"السيارة {self.car_record} غير موجودة")