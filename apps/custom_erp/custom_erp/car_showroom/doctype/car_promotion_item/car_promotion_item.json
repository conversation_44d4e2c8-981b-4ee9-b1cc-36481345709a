{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["car_record", "car_brand", "column_break_3", "special_discount", "promotion_notes"], "fields": [{"fieldname": "car_record", "fieldtype": "Link", "in_list_view": 1, "label": "السيارة", "options": "Car Record", "reqd": 1}, {"fieldname": "car_brand", "fieldtype": "Link", "in_list_view": 1, "label": "الماركة", "options": "Car Brand"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "special_discount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "خصم خاص", "precision": "2"}, {"fieldname": "promotion_notes", "fieldtype": "Text", "label": "ملاحظات العرض"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Car Promotion Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}