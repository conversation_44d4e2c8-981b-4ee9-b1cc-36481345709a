# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
import os

class CarImage(Document):
	def validate(self):
		self.validate_image()
		
	def validate_image(self):
		"""التحقق من صحة الصورة"""
		if self.image_file:
			# التحقق من نوع الملف
			allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
			file_extension = os.path.splitext(self.image_file)[1].lower()
			
			if file_extension not in allowed_extensions:
				frappe.throw(f"نوع الملف غير مدعوم. الأنواع المدعومة: {', '.join(allowed_extensions)}")
			
			# تعيين عنوان افتراضي إذا لم يتم تحديده
			if not self.image_title:
				self.image_title = f"صورة {self.image_type or 'عامة'}"
	
	def before_save(self):
		"""قبل الحفظ"""
		# ترتيب الصور
		if not self.sort_order:
			# احضار أعلى ترتيب موجود
			max_order = frappe.db.sql("""
				SELECT IFNULL(MAX(sort_order), 0) as max_order
				FROM `tabCar Image`
				WHERE parent = %s
			""", [self.parent])
			
			self.sort_order = (max_order[0][0] if max_order else 0) + 1