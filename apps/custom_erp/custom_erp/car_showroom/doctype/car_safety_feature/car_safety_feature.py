# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document

class CarSafetyFeature(Document):
	def validate(self):
		self.validate_feature()
		
	def validate_feature(self):
		"""التحقق من صحة ميزة الأمان"""
		if self.feature_name:
			self.feature_name = self.feature_name.strip()
		
		# إضافة وصف افتراضي إذا لم يتم تحديده
		if not self.feature_description and self.feature_name:
			self.feature_description = f"ميزة أمان: {self.feature_name}"