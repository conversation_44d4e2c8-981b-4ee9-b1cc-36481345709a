# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe.utils import nowdate, getdate, flt

class CarServiceHistory(Document):
	def validate(self):
		self.calculate_total_cost()
		self.validate_dates()
		
	def calculate_total_cost(self):
		"""حساب التكلفة الإجمالية"""
		total = 0
		if self.parts_cost:
			total += flt(self.parts_cost)
		if self.labor_cost:
			total += flt(self.labor_cost)
		if self.other_costs:
			total += flt(self.other_costs)
		
		self.total_cost = total
	
	def validate_dates(self):
		"""التحقق من صحة التواريخ"""
		if self.service_date and getdate(self.service_date) > getdate(nowdate()):
			frappe.throw("تاريخ الخدمة لا يمكن أن يكون في المستقبل")
		
		if self.next_service_date and self.service_date:
			if getdate(self.next_service_date) <= getdate(self.service_date):
				frappe.throw("تاريخ الخدمة القادمة يجب أن يكون بعد تاريخ الخدمة الحالية")
	
	def after_insert(self):
		"""بعد إدراج سجل الخدمة"""
		self.update_car_service_info()
	
	def update_car_service_info(self):
		"""تحديث معلومات الخدمة في سجل السيارة"""
		if self.car_record:
			car_doc = frappe.get_doc("Car Record", self.car_record)
			
			# تحديث تاريخ آخر خدمة
			car_doc.last_service_date = self.service_date
			
			# تحديث تاريخ الخدمة القادمة
			if self.next_service_date:
				car_doc.next_service_date = self.next_service_date
			
			# إضافة ملاحظة
			car_doc.add_comment("Comment", f"تم إجراء خدمة: {self.service_type} - التكلفة: {self.total_cost}")
			
			car_doc.save(ignore_permissions=True)