{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["service_date", "service_type", "column_break_3", "service_provider", "mileage_at_service", "section_break_6", "service_description", "parts_replaced", "column_break_9", "service_cost", "warranty_period", "next_service_due"], "fields": [{"fieldname": "service_date", "fieldtype": "Date", "in_list_view": 1, "label": "تاريخ الصيانة", "reqd": 1}, {"fieldname": "service_type", "fieldtype": "Select", "in_list_view": 1, "label": "نوع الصيانة", "options": "صيانة دورية\nإصلاح\nتغيير زيت\nفحص شامل\nإصلاح حادث\nتركيب قطع\nغسيل وتنظيف\nأخرى", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "service_provider", "fieldtype": "Data", "label": "مقدم الخدمة"}, {"fieldname": "mileage_at_service", "fieldtype": "Float", "label": "الكيلومترات وقت الصيانة"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "service_description", "fieldtype": "Text", "label": "وصف الصيانة"}, {"fieldname": "parts_replaced", "fieldtype": "Text", "label": "القطع المستبدلة"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "service_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "تكلفة الصيانة", "precision": "2"}, {"fieldname": "warranty_period", "fieldtype": "Int", "label": "فترة الضمان (أيام)"}, {"fieldname": "next_service_due", "fieldtype": "Date", "label": "موعد الصيانة التالية"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Car Service History", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}