{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["feature_name", "feature_category", "column_break_3", "is_premium", "additional_value"], "fields": [{"fieldname": "feature_name", "fieldtype": "Data", "in_list_view": 1, "label": "اسم الميزة", "reqd": 1}, {"fieldname": "feature_category", "fieldtype": "Select", "in_list_view": 1, "label": "فئة الميزة", "options": "كماليات داخلية\nتقنيات متقدمة\nنظام صوتي\nمقاعد فاخرة\nتحكم مناخي\nإضاءة\nنوافذ وأسقف\nتقنيات القيادة"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "is_premium", "fieldtype": "Check", "label": "ميزة مميزة"}, {"fieldname": "additional_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "القيمة المضافة", "precision": "2"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Car Luxury Feature", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}