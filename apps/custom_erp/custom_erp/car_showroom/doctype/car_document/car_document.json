{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["document_type", "document_number", "column_break_3", "document_file", "expiry_date", "section_break_6", "issued_by", "issued_date", "column_break_9", "notes"], "fields": [{"fieldname": "document_type", "fieldtype": "Select", "in_list_view": 1, "label": "نوع الوثيقة", "options": "استمارة ملكية\nترخيص مرور\nشهادة فحص\nوثيقة تأمين\nفاتورة شراء\nسند قبض\nعقد بيع\nضمان\nأخرى", "reqd": 1}, {"fieldname": "document_number", "fieldtype": "Data", "in_list_view": 1, "label": "رقم الوثيقة"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "document_file", "fieldtype": "Attach", "label": "ملف الوثيقة"}, {"fieldname": "expiry_date", "fieldtype": "Date", "label": "تاريخ انتهاء الصلاحية"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "issued_by", "fieldtype": "Data", "label": "الجهة المصدرة"}, {"fieldname": "issued_date", "fieldtype": "Date", "label": "تاريخ الإصدار"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "notes", "fieldtype": "Text", "label": "ملاحظات"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Car Document", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}