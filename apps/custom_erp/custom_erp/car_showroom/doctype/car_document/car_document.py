# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe.utils import getdate, nowdate

class CarDocument(Document):
	def validate(self):
		self.validate_document()
		self.validate_dates()
		
	def validate_document(self):
		"""التحقق من صحة المستند"""
		if self.document_name:
			self.document_name = self.document_name.strip()
		
		# التحقق من وجود الملف
		if self.document_type in ['استمارة', 'رخصة القيادة', 'جواز السفر'] and not self.document_file:
			frappe.msgprint(f"يُنصح بإرفاق ملف لمستند {self.document_type}", alert=True)
	
	def validate_dates(self):
		"""التحقق من صحة التواريخ"""
		if self.expiry_date:
			if getdate(self.expiry_date) < getdate(nowdate()):
				frappe.msgprint(f"تحذير: مستند {self.document_name} منتهي الصلاحية", 
					alert=True, indicator="red")
			elif getdate(self.expiry_date) <= getdate(frappe.utils.add_days(nowdate(), 30)):
				frappe.msgprint(f"تنبيه: مستند {self.document_name} سينتهي خلال شهر", 
					alert=True, indicator="orange")