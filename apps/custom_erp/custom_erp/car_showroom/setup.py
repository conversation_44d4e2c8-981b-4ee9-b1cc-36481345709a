# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe import _

def create_car_item_groups():
	"""إنشاء مجموعات أصناف السيارات"""
	groups = [
		{
			"item_group_name": "سيارات",
			"parent_item_group": "All Item Groups",
			"is_group": 0
		},
		{
			"item_group_name": "سيارات فاخرة",
			"parent_item_group": "سيارات",
			"is_group": 0
		},
		{
			"item_group_name": "سيارات اقتصادية", 
			"parent_item_group": "سيارات",
			"is_group": 0
		},
		{
			"item_group_name": "سيارات رياضية",
			"parent_item_group": "سيارات", 
			"is_group": 0
		}
	]
	
	for group_data in groups:
		if not frappe.db.exists("Item Group", group_data["item_group_name"]):
			item_group = frappe.new_doc("Item Group")
			item_group.update(group_data)
			item_group.insert(ignore_permissions=True)

def create_customer_groups():
	"""إنشاء مجموعات العملاء"""
	groups = [
		{
			"customer_group_name": "عملاء السيارات",
			"parent_customer_group": "All Customer Groups",
			"is_group": 0
		},
		{
			"customer_group_name": "عملاء VIP",
			"parent_customer_group": "عملاء السيارات",
			"is_group": 0
		},
		{
			"customer_group_name": "عملاء التجزئة",
			"parent_customer_group": "عملاء السيارات",
			"is_group": 0
		},
		{
			"customer_group_name": "عملاء الجملة",
			"parent_customer_group": "عملاء السيارات",
			"is_group": 0
		}
	]
	
	for group_data in groups:
		if not frappe.db.exists("Customer Group", group_data["customer_group_name"]):
			customer_group = frappe.new_doc("Customer Group")
			customer_group.update(group_data)
			customer_group.insert(ignore_permissions=True)

def create_sales_person_tree():
	"""إنشاء شجرة موظفي المبيعات"""
	if not frappe.db.exists("Sales Person", "مدير المبيعات"):
		sales_manager = frappe.new_doc("Sales Person")
		sales_manager.sales_person_name = "مدير المبيعات"
		sales_manager.parent_sales_person = "All Sales Persons"
		sales_manager.is_group = 1
		sales_manager.insert(ignore_permissions=True)
	
	sales_persons = [
		"موظف مبيعات 1",
		"موظف مبيعات 2", 
		"موظف مبيعات 3"
	]
	
	for person in sales_persons:
		if not frappe.db.exists("Sales Person", person):
			sales_person = frappe.new_doc("Sales Person")
			sales_person.sales_person_name = person
			sales_person.parent_sales_person = "مدير المبيعات"
			sales_person.is_group = 0
			sales_person.insert(ignore_permissions=True)

def create_print_formats():
	"""إنشاء تنسيقات الطباعة"""
	print_formats = [
		{
			"name": "Car Record Print",
			"doc_type": "Car Record",
			"print_format_type": "Jinja",
			"html": get_car_record_print_format()
		},
		{
			"name": "Car Quotation Print",
			"doc_type": "Quotation", 
			"print_format_type": "Jinja",
			"html": get_car_quotation_print_format()
		}
	]
	
	for format_data in print_formats:
		if not frappe.db.exists("Print Format", format_data["name"]):
			print_format = frappe.new_doc("Print Format")
			print_format.update(format_data)
			print_format.insert(ignore_permissions=True)

def get_car_record_print_format():
	"""تنسيق طباعة سجل السيارة"""
	return '''
	<div class="print-format">
		<h2>بيانات السيارة</h2>
		<table class="table table-bordered">
			<tr>
				<th>اسم السيارة</th>
				<td>{{ doc.car_name }}</td>
			</tr>
			<tr>
				<th>الماركة</th>
				<td>{{ doc.brand }}</td>
			</tr>
			<tr>
				<th>الموديل</th>
				<td>{{ doc.model }}</td>
			</tr>
			<tr>
				<th>السنة</th>
				<td>{{ doc.year }}</td>
			</tr>
			<tr>
				<th>اللون</th>
				<td>{{ doc.color }}</td>
			</tr>
			<tr>
				<th>الكيلومترات</th>
				<td>{{ doc.mileage }} كم</td>
			</tr>
			<tr>
				<th>سعر البيع</th>
				<td>{{ doc.selling_price }} ريال</td>
			</tr>
			<tr>
				<th>الحالة</th>
				<td>{{ doc.status }}</td>
			</tr>
		</table>
		
		{% if doc.car_images %}
		<h3>صور السيارة</h3>
		<div class="row">
			{% for image in doc.car_images %}
			<div class="col-md-4">
				<img src="{{ image.image_file }}" class="img-responsive" style="max-height: 200px;">
				<p>{{ image.image_title }}</p>
			</div>
			{% endfor %}
		</div>
		{% endif %}
		
		{% if doc.luxury_features %}
		<h3>الميزات الفاخرة</h3>
		<ul>
			{% for feature in doc.luxury_features %}
			<li>{{ feature.feature_name }}</li>
			{% endfor %}
		</ul>
		{% endif %}
	</div>
	'''

def get_car_quotation_print_format():
	"""تنسيق طباعة عرض سعر السيارة"""
	return '''
	<div class="print-format">
		<h2>عرض سعر السيارة</h2>
		<p><strong>العميل:</strong> {{ doc.customer_name }}</p>
		<p><strong>التاريخ:</strong> {{ doc.transaction_date }}</p>
		<p><strong>صالح حتى:</strong> {{ doc.valid_till }}</p>
		
		<table class="table table-bordered">
			<thead>
				<tr>
					<th>البيان</th>
					<th>الكمية</th>
					<th>السعر</th>
					<th>المجموع</th>
				</tr>
			</thead>
			<tbody>
				{% for item in doc.items %}
				<tr>
					<td>
						<strong>{{ item.item_name }}</strong><br>
						{{ item.description }}
					</td>
					<td>{{ item.qty }}</td>
					<td>{{ item.rate }} ريال</td>
					<td>{{ item.amount }} ريال</td>
				</tr>
				{% endfor %}
			</tbody>
		</table>
		
		<div class="row">
			<div class="col-md-6 col-md-offset-6">
				<table class="table">
					<tr>
						<th>الإجمالي</th>
						<td>{{ doc.grand_total }} ريال</td>
					</tr>
				</table>
			</div>
		</div>
		
		<p><strong>شروط وأحكام:</strong></p>
		<ul>
			<li>العرض صالح لمدة محددة</li>
			<li>الأسعار شاملة ضريبة القيمة المضافة</li>
			<li>السيارة محجوزة لمدة أسبوع من تاريخ العرض</li>
		</ul>
	</div>
	'''