# -*- coding: utf-8 -*-
# اختبار الداشبورد للتأكد من عمل الدوال

import frappe
from frappe.utils import nowdate

def test_dashboard_functions():
    """اختبار دوال الداشبورد"""
    
    print("=== اختبار دوال الداشبورد ===\n")
    
    # اختبار get_car_stats
    try:
        from custom_erp.car_showroom.dashboard.car_dashboard import get_car_stats
        stats = get_car_stats()
        print("✅ get_car_stats تعمل بنجاح")
        print(f"   - السيارات المتاحة: {stats.get('available_cars', 0)}")
        print(f"   - السيارات المباعة: {stats.get('sold_cars', 0)}")
        print(f"   - السيارات المحجوزة: {stats.get('reserved_cars', 0)}")
        print(f"   - قيمة المخزون: {stats.get('inventory_value', 0):,.0f} ريال\n")
    except Exception as e:
        print(f"❌ خطأ في get_car_stats: {str(e)}\n")
    
    # اختبار get_sales_by_brand  
    try:
        from custom_erp.car_showroom.dashboard.car_dashboard import get_sales_by_brand
        brands_data = get_sales_by_brand()
        print("✅ get_sales_by_brand تعمل بنجاح")
        print(f"   - عدد الماركات: {len(brands_data)}")
        for brand in brands_data[:3]:  # أول 3 ماركات
            print(f"   - {brand.get('brand', 'غير محدد')}: {brand.get('count', 0)} سيارة\n")
    except Exception as e:
        print(f"❌ خطأ في get_sales_by_brand: {str(e)}\n")
    
    # اختبار get_monthly_sales
    try:
        from custom_erp.car_showroom.dashboard.car_dashboard import get_monthly_sales
        monthly_data = get_monthly_sales()
        print("✅ get_monthly_sales تعمل بنجاح")
        print(f"   - عدد الأشهر: {len(monthly_data)}")
        for month in monthly_data[:3]:  # أول 3 أشهر
            print(f"   - {month.get('month', 'غير محدد')}: {month.get('cars_sold', 0)} سيارة\n")
    except Exception as e:
        print(f"❌ خطأ في get_monthly_sales: {str(e)}\n")
    
    # اختبار get_charts_data من ملف الداشبورد
    try:
        from custom_erp.www.car_dashboard import get_charts_data
        charts_data = get_charts_data()
        print("✅ get_charts_data تعمل بنجاح")
        print(f"   - بيانات الماركات: {len(charts_data.get('sales_by_brand', []))} عنصر")
        print(f"   - بيانات الأشهر: {len(charts_data.get('monthly_sales', []))} عنصر\n")
    except Exception as e:
        print(f"❌ خطأ في get_charts_data: {str(e)}\n")
    
    print("=== انتهى الاختبار ===")

if __name__ == "__main__":
    test_dashboard_functions()