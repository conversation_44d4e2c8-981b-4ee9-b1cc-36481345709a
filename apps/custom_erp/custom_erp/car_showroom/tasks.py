# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import nowdate, add_days, getdate
from frappe import _

def send_maintenance_alerts():
	"""إرسال تنبيهات الصيانة اليومية"""
	try:
		# السيارات التي تحتاج صيانة خلال الأسبوع القادم
		cars_due_maintenance = frappe.db.sql("""
			SELECT 
				name, car_name, brand, model, next_service_date, 
				owner, sales_person
			FROM `tabCar Record`
			WHERE next_service_date BETWEEN %s AND %s
			AND status != 'مباعة'
			AND docstatus = 1
		""", [nowdate(), add_days(nowdate(), 7)], as_dict=1)
		
		if cars_due_maintenance:
			# إرسال إشعار للمديرين
			managers = frappe.get_all("User", 
				filters={"role_profile_name": ["in", ["System Manager", "Car Showroom Manager"]]},
				fields=["name", "email", "full_name"])
			
			message = "السيارات التالية تحتاج صيانة خلال الأسبوع القادم:\n\n"
			
			for car in cars_due_maintenance:
				message += f"• {car.car_name} ({car.brand} {car.model}) - تاريخ الصيانة: {car.next_service_date}\n"
			
			for manager in managers:
				if manager.email:
					try:
						frappe.sendmail(
							recipients=[manager.email],
							subject="تنبيه: سيارات تحتاج صيانة",
							message=message
						)
					except Exception as e:
						frappe.log_error(f"خطأ في إرسال بريد صيانة إلى {manager.email}: {str(e)}")
		
		frappe.log("تم إرسال تنبيهات الصيانة بنجاح")
		
	except Exception as e:
		frappe.log_error(f"خطأ في إرسال تنبيهات الصيانة: {str(e)}")

def update_inventory_aging():
	"""تحديث تقادم المخزون أسبوعياً"""
	try:
		# السيارات التي مضى عليها أكثر من 6 أشهر في المخزون
		old_inventory = frappe.db.sql("""
			SELECT 
				name, car_name, brand, model, acquisition_date,
				DATEDIFF(CURDATE(), acquisition_date) as days_in_inventory,
				selling_price
			FROM `tabCar Record`
			WHERE acquisition_date <= %s
			AND status IN ('متاحة', 'محجوزة')
			AND docstatus = 1
		""", [add_days(nowdate(), -180)], as_dict=1)
		
		if old_inventory:
			# إنشاء تقرير أسبوعي للمخزون القديم
			managers = frappe.get_all("User", 
				filters={"role_profile_name": ["in", ["System Manager", "Car Showroom Manager"]]},
				fields=["name", "email", "full_name"])
			
			message = "تقرير المخزون القديم (أكثر من 6 أشهر):\n\n"
			total_value = 0
			
			for car in old_inventory:
				days = car.days_in_inventory
				months = round(days / 30, 1)
				message += f"• {car.car_name} ({car.brand} {car.model}) - {months} شهر - {frappe.utils.fmt_money(car.selling_price)} ريال\n"
				total_value += car.selling_price or 0
			
			message += f"\nإجمالي قيمة المخزون القديم: {frappe.utils.fmt_money(total_value)} ريال"
			message += f"\nعدد السيارات: {len(old_inventory)} سيارة"
			
			for manager in managers:
				if manager.email:
					try:
						frappe.sendmail(
							recipients=[manager.email],
							subject="تقرير المخزون القديم الأسبوعي",
							message=message
						)
					except Exception as e:
						frappe.log_error(f"خطأ في إرسال تقرير المخزون إلى {manager.email}: {str(e)}")
		
		frappe.log("تم تحديث تقادم المخزون بنجاح")
		
	except Exception as e:
		frappe.log_error(f"خطأ في تحديث تقادم المخزون: {str(e)}")

def auto_update_car_status():
	"""تحديث تلقائي لحالة السيارات"""
	try:
		# تحديث السيارات المحجوزة لفترة طويلة
		frappe.db.sql("""
			UPDATE `tabCar Record`
			SET status = 'متاحة'
			WHERE status = 'محجوزة'
			AND reservation_date <= %s
			AND docstatus = 1
		""", [add_days(nowdate(), -30)])
		
		frappe.log("تم تحديث حالة السيارات المحجوزة تلقائياً")
		
	except Exception as e:
		frappe.log_error(f"خطأ في التحديث التلقائي لحالة السيارات: {str(e)}")

def generate_monthly_reports():
	"""إنشاء التقارير الشهرية"""
	try:
		from custom_erp.car_showroom.dashboard.car_dashboard import (
			get_car_stats, get_monthly_sales, get_sales_by_brand
		)
		
		# إحضار البيانات
		stats = get_car_stats()
		monthly_sales = get_monthly_sales()
		sales_by_brand = get_sales_by_brand()
		
		# إنشاء التقرير
		report = f"""
		تقرير شهري لمعرض السيارات
		========================
		
		الإحصائيات العامة:
		- إجمالي السيارات: {stats.get('total_cars', 0)}
		- السيارات المتاحة: {stats.get('available_cars', 0)}
		- السيارات المباعة: {stats.get('sold_cars', 0)}
		- قيمة المخزون: {frappe.utils.fmt_money(stats.get('inventory_value', 0))} ريال
		
		المبيعات الشهرية:
		"""
		
		for sale in monthly_sales[:6]:  # آخر 6 أشهر
			report += f"- {sale.month}: {sale.cars_sold} سيارة بقيمة {frappe.utils.fmt_money(sale.total_revenue)} ريال\n"
		
		report += "\nالمبيعات حسب الماركة:\n"
		for brand in sales_by_brand[:10]:  # أفضل 10 ماركات
			report += f"- {brand.brand}: {brand.count} سيارة بقيمة {frappe.utils.fmt_money(brand.total_sales)} ريال\n"
		
		# إرسال التقرير للإدارة
		managers = frappe.get_all("User", 
			filters={"role_profile_name": ["in", ["System Manager", "Car Showroom Manager"]]},
			fields=["name", "email", "full_name"])
		
		for manager in managers:
			if manager.email:
				try:
					frappe.sendmail(
						recipients=[manager.email],
						subject="التقرير الشهري لمعرض السيارات",
						message=report
					)
				except Exception as e:
					frappe.log_error(f"خطأ في إرسال التقرير الشهري إلى {manager.email}: {str(e)}")
		
		frappe.log("تم إنشاء وإرسال التقرير الشهري بنجاح")
		
	except Exception as e:
		frappe.log_error(f"خطأ في إنشاء التقرير الشهري: {str(e)}")