# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import flt, getdate, nowdate, date_diff
from frappe import _

def execute(filters=None):
	columns = get_columns()
	data = get_data(filters)
	return columns, data

def get_columns():
	return [
		{
			"label": _("اسم السيارة"),
			"fieldname": "car_name",
			"fieldtype": "Link",
			"options": "Car Record",
			"width": 200
		},
		{
			"label": _("الماركة"),
			"fieldname": "brand",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("الموديل"),
			"fieldname": "model",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("السنة"),
			"fieldname": "year",
			"fieldtype": "Int",
			"width": 80
		},
		{
			"label": _("اللون"),
			"fieldname": "color",
			"fieldtype": "Data",
			"width": 100
		},
		{
			"label": _("الكيلومترات"),
			"fieldname": "mileage",
			"fieldtype": "Float",
			"width": 100
		},
		{
			"label": _("الحالة"),
			"fieldname": "status",
			"fieldtype": "Data",
			"width": 100
		},
		{
			"label": _("سعر الشراء"),
			"fieldname": "purchase_price",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("سعر البيع"),
			"fieldname": "selling_price",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("أقل سعر مقبول"),
			"fieldname": "minimum_price",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("الموقع"),
			"fieldname": "location",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("تاريخ الاستحواذ"),
			"fieldname": "acquisition_date",
			"fieldtype": "Date",
			"width": 100
		},
		{
			"label": _("أيام في المخزون"),
			"fieldname": "days_in_inventory",
			"fieldtype": "Int",
			"width": 120
		},
		{
			"label": _("الحالة الخارجية"),
			"fieldname": "exterior_condition",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("الحالة الداخلية"),
			"fieldname": "interior_condition",
			"fieldtype": "Data",
			"width": 120
		}
	]

def get_data(filters):
	conditions = get_conditions(filters)
	
	data = frappe.db.sql("""
		SELECT 
			name as car_name,
			brand,
			model,
			year,
			color,
			mileage,
			status,
			purchase_price,
			selling_price,
			minimum_price,
			location,
			acquisition_date,
			CASE 
				WHEN acquisition_date IS NOT NULL 
				THEN DATEDIFF(CURDATE(), acquisition_date)
				ELSE NULL
			END as days_in_inventory,
			exterior_condition,
			interior_condition
		FROM `tabCar Record`
		WHERE 1=1
		{conditions}
		ORDER BY 
			CASE status
				WHEN 'متاحة' THEN 1
				WHEN 'محجوزة' THEN 2
				WHEN 'قيد الصيانة' THEN 3
				WHEN 'قيد التقييم' THEN 4
				WHEN 'مباعة' THEN 5
				ELSE 6
			END,
			acquisition_date DESC
	""".format(conditions=conditions), filters, as_dict=1)
	
	return data

def get_conditions(filters):
	conditions = ""
	
	if filters.get("status"):
		conditions += " AND status = %(status)s"
	
	if filters.get("brand"):
		conditions += " AND brand = %(brand)s"
	
	if filters.get("year_from"):
		conditions += " AND year >= %(year_from)s"
	
	if filters.get("year_to"):
		conditions += " AND year <= %(year_to)s"
	
	if filters.get("price_from"):
		conditions += " AND selling_price >= %(price_from)s"
		
	if filters.get("price_to"):
		conditions += " AND selling_price <= %(price_to)s"
	
	if filters.get("location"):
		conditions += " AND location LIKE %(location)s"
		filters["location"] = f"%{filters['location']}%"
	
	if filters.get("exclude_sold"):
		conditions += " AND status != 'مباعة'"
	
	return conditions