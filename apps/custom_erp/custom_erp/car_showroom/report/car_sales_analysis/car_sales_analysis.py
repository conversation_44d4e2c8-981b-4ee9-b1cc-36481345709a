# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import flt, formatdate, getdate
from frappe import _

def execute(filters=None):
	columns = get_columns()
	data = get_data(filters)
	return columns, data

def get_columns():
	return [
		{
			"label": _("اسم السيارة"),
			"fieldname": "car_name",
			"fieldtype": "Link",
			"options": "Car Record",
			"width": 200
		},
		{
			"label": _("الماركة"),
			"fieldname": "brand",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("الموديل"),
			"fieldname": "model",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("السنة"),
			"fieldname": "year",
			"fieldtype": "Int",
			"width": 80
		},
		{
			"label": _("العميل"),
			"fieldname": "customer",
			"fieldtype": "Link",
			"options": "Customer",
			"width": 150
		},
		{
			"label": _("تاريخ البيع"),
			"fieldname": "sale_date",
			"fieldtype": "Date",
			"width": 100
		},
		{
			"label": _("سعر الشراء"),
			"fieldname": "purchase_price",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("سعر البيع"),
			"fieldname": "selling_price",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("الربح"),
			"fieldname": "profit",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("هامش الربح %"),
			"fieldname": "profit_margin",
			"fieldtype": "Percent",
			"width": 120
		},
		{
			"label": _("موظف المبيعات"),
			"fieldname": "sales_person",
			"fieldtype": "Link",
			"options": "Sales Person",
			"width": 150
		},
		{
			"label": _("الحالة"),
			"fieldname": "status",
			"fieldtype": "Data",
			"width": 100
		}
	]

def get_data(filters):
	conditions = get_conditions(filters)
	
	data = frappe.db.sql("""
		SELECT 
			name as car_name,
			brand,
			model,
			year,
			customer,
			sale_date,
			purchase_price,
			selling_price,
			(selling_price - IFNULL(purchase_price, 0)) as profit,
			CASE 
				WHEN purchase_price > 0 THEN 
					((selling_price - purchase_price) / purchase_price) * 100
				ELSE 0
			END as profit_margin,
			sales_person,
			status
		FROM `tabCar Record`
		WHERE status = 'مباعة'
		{conditions}
		ORDER BY sale_date DESC
	""".format(conditions=conditions), filters, as_dict=1)
	
	return data

def get_conditions(filters):
	conditions = ""
	
	if filters.get("from_date"):
		conditions += " AND sale_date >= %(from_date)s"
	
	if filters.get("to_date"):
		conditions += " AND sale_date <= %(to_date)s"
	
	if filters.get("brand"):
		conditions += " AND brand = %(brand)s"
	
	if filters.get("customer"):
		conditions += " AND customer = %(customer)s"
	
	if filters.get("sales_person"):
		conditions += " AND sales_person = %(sales_person)s"
		
	return conditions