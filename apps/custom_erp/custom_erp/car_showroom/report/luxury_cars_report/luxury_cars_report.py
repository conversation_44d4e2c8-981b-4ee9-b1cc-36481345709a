# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import flt
from frappe import _

def execute(filters=None):
	columns = get_columns()
	data = get_data(filters)
	return columns, data

def get_columns():
	return [
		{
			"label": _("اسم السيارة"),
			"fieldname": "car_name",
			"fieldtype": "Link",
			"options": "Car Record",
			"width": 200
		},
		{
			"label": _("الماركة"),
			"fieldname": "brand",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("الموديل"),
			"fieldname": "model",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("السنة"),
			"fieldname": "year",
			"fieldtype": "Int",
			"width": 80
		},
		{
			"label": _("اللون"),
			"fieldname": "color",
			"fieldtype": "Data",
			"width": 100
		},
		{
			"label": _("سعر البيع"),
			"fieldname": "selling_price",
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"label": _("الحالة"),
			"fieldname": "status",
			"fieldtype": "Data",
			"width": 100
		},
		{
			"label": _("ماركة فاخرة"),
			"fieldname": "is_luxury_brand",
			"fieldtype": "Check",
			"width": 100
		},
		{
			"label": _("عدد الميزات الفاخرة"),
			"fieldname": "luxury_features_count",
			"fieldtype": "Int",
			"width": 130
		},
		{
			"label": _("عدد ميزات الأمان"),
			"fieldname": "safety_features_count",
			"fieldtype": "Int",
			"width": 130
		},
		{
			"label": _("العميل"),
			"fieldname": "customer",
			"fieldtype": "Link",
			"options": "Customer",
			"width": 150
		},
		{
			"label": _("تاريخ البيع"),
			"fieldname": "sale_date",
			"fieldtype": "Date",
			"width": 100
		}
	]

def get_data(filters):
	conditions = get_conditions(filters)
	
	# تحديد الحد الأدنى للسعر للسيارات الفاخرة
	luxury_price_threshold = filters.get("luxury_price_threshold", 150000)
	
	data = frappe.db.sql("""
		SELECT 
			cr.name as car_name,
			cr.brand,
			cr.model,
			cr.year,
			cr.color,
			cr.selling_price,
			cr.status,
			cr.customer,
			cr.sale_date,
			COALESCE(cb.is_luxury_brand, 0) as is_luxury_brand,
			(SELECT COUNT(*) FROM `tabCar Luxury Feature` clf WHERE clf.parent = cr.name) as luxury_features_count,
			(SELECT COUNT(*) FROM `tabCar Safety Feature` csf WHERE csf.parent = cr.name) as safety_features_count
		FROM `tabCar Record` cr
		LEFT JOIN `tabCar Brand` cb ON cr.brand = cb.name
		WHERE (
			cb.is_luxury_brand = 1 
			OR cr.selling_price >= %(luxury_price_threshold)s
			OR (SELECT COUNT(*) FROM `tabCar Luxury Feature` clf WHERE clf.parent = cr.name) >= 3
		)
		{conditions}
		ORDER BY cr.selling_price DESC, cr.creation DESC
	""".format(conditions=conditions), 
	dict(filters, luxury_price_threshold=luxury_price_threshold), as_dict=1)
	
	return data

def get_conditions(filters):
	conditions = ""
	
	if filters.get("status"):
		conditions += " AND cr.status = %(status)s"
	
	if filters.get("brand"):
		conditions += " AND cr.brand = %(brand)s"
	
	if filters.get("year_from"):
		conditions += " AND cr.year >= %(year_from)s"
	
	if filters.get("year_to"):
		conditions += " AND cr.year <= %(year_to)s"
	
	if filters.get("price_from"):
		conditions += " AND cr.selling_price >= %(price_from)s"
		
	if filters.get("price_to"):
		conditions += " AND cr.selling_price <= %(price_to)s"
	
	if filters.get("only_luxury_brands"):
		conditions += " AND cb.is_luxury_brand = 1"
	
	if filters.get("min_luxury_features"):
		conditions += " AND (SELECT COUNT(*) FROM `tabCar Luxury Feature` clf WHERE clf.parent = cr.name) >= %(min_luxury_features)s"
	
	return conditions