# -*- coding: utf-8 -*-
# إنشاء بيانات تجريبية لاختبار الداشبورد

import frappe
from frappe.utils import nowdate, add_days, add_months
import random

def create_sample_cars_for_dashboard():
    """إنشاء سيارات تجريبية لاختبار الداشبورد"""
    
    # التأكد من وجود الماركات
    brands = ["تويوتا", "هوندا", "نيسان", "هيونداي", "كيا"]
    
    for brand_name in brands:
        if not frappe.db.exists("Car Brand", brand_name):
            brand = frappe.new_doc("Car Brand")
            brand.brand_name = brand_name
            brand.brand_name_english = brand_name
            brand.is_luxury_brand = 0 if brand_name not in ["مرسيدس", "BMW"] else 1
            brand.country_of_origin = "اليابان" if brand_name in ["تويوتا", "هوندا", "نيسان"] else "كوريا الجنوبية"
            brand.insert(ignore_permissions=True)
    
    # إنشاء سيارات تجريبية
    cars_data = [
        # سيارات متاحة
        {"name": "كامري 2023", "brand": "تويوتا", "status": "متاحة", "price": 95000},
        {"name": "أكورد 2023", "brand": "هوندا", "status": "متاحة", "price": 98000},
        {"name": "التيما 2023", "brand": "نيسان", "status": "متاحة", "price": 87000},
        {"name": "إلنترا 2023", "brand": "هيونداي", "status": "متاحة", "price": 82000},
        {"name": "أوبتيما 2023", "brand": "كيا", "status": "متاحة", "price": 89000},
        
        # سيارات محجوزة
        {"name": "كامري 2022", "brand": "تويوتا", "status": "محجوزة", "price": 88000},
        {"name": "أكورد 2022", "brand": "هوندا", "status": "محجوزة", "price": 92000},
        
        # سيارات مباعة (بتواريخ مختلفة لاختبار الرسوم البيانية)
        {"name": "كامري 2021", "brand": "تويوتا", "status": "مباعة", "price": 85000, "sale_date": add_months(nowdate(), -1)},
        {"name": "أكورد 2021", "brand": "هوندا", "status": "مباعة", "price": 89000, "sale_date": add_months(nowdate(), -2)},
        {"name": "التيما 2021", "brand": "نيسان", "status": "مباعة", "price": 78000, "sale_date": add_months(nowdate(), -1)},
        {"name": "إلنترا 2021", "brand": "هيونداي", "status": "مباعة", "price": 75000, "sale_date": add_months(nowdate(), -3)},
        {"name": "كامري GLE", "brand": "تويوتا", "status": "مباعة", "price": 92000, "sale_date": add_months(nowdate(), -1)},
        {"name": "أكورد EX", "brand": "هوندا", "status": "مباعة", "price": 95000, "sale_date": add_months(nowdate(), -2)},
        
        # سيارات قيد الصيانة
        {"name": "التيما 2020", "brand": "نيسان", "status": "قيد الصيانة", "price": 72000},
    ]
    
    created_count = 0
    for car_data in cars_data:
        # التحقق من عدم وجود السيارة
        if not frappe.db.exists("Car Record", {"car_name": car_data["name"]}):
            try:
                car = frappe.new_doc("Car Record")
                car.car_name = car_data["name"]
                car.brand = car_data["brand"]
                car.model = car_data["name"].split()[0]  # أول كلمة كموديل
                car.year = 2023 if "2023" in car_data["name"] else (2022 if "2022" in car_data["name"] else 2021)
                car.color = random.choice(["أبيض", "أسود", "فضي", "أحمر", "أزرق"])
                car.mileage = random.randint(5000, 50000)
                car.fuel_type = "بنزين"
                car.transmission = "أوتوماتيك"
                car.status = car_data["status"]
                car.purchase_price = car_data["price"] - 10000  # سعر الشراء أقل بـ 10 آلاف
                car.selling_price = car_data["price"]
                car.minimum_price = car_data["price"] - 5000  # الحد الأدنى
                car.acquisition_date = add_days(nowdate(), -random.randint(30, 365))
                
                if car_data.get("sale_date"):
                    car.sale_date = car_data["sale_date"]
                
                car.insert(ignore_permissions=True)
                created_count += 1
                
            except Exception as e:
                print(f"خطأ في إنشاء السيارة {car_data['name']}: {str(e)}")
    
    frappe.db.commit()
    print(f"تم إنشاء {created_count} سيارة تجريبية للداشبورد")
    
    return created_count

def test_dashboard_with_sample_data():
    """اختبار الداشبورد مع البيانات التجريبية"""
    
    print("=== إنشاء بيانات تجريبية ===")
    created = create_sample_cars_for_dashboard()
    
    if created > 0:
        print(f"تم إنشاء {created} سيارة تجريبية")
        
        # اختبار الدوال
        print("\n=== اختبار دوال الداشبورد ===")
        
        try:
            from custom_erp.car_showroom.dashboard.car_dashboard import get_car_stats, get_sales_by_brand, get_monthly_sales
            
            # الإحصائيات
            stats = get_car_stats()
            print(f"✅ إحصائيات:")
            print(f"   - السيارات المتاحة: {stats.get('available_cars', 0)}")
            print(f"   - السيارات المباعة: {stats.get('sold_cars', 0)}")
            print(f"   - السيارات المحجوزة: {stats.get('reserved_cars', 0)}")
            print(f"   - قيمة المخزون: {stats.get('inventory_value', 0):,.0f} ريال")
            
            # المبيعات حسب الماركة
            brands = get_sales_by_brand()
            print(f"\n✅ المبيعات حسب الماركة ({len(brands)} ماركة):")
            for brand in brands:
                print(f"   - {brand['brand']}: {brand['count']} سيارة")
            
            # المبيعات الشهرية
            monthly = get_monthly_sales()
            print(f"\n✅ المبيعات الشهرية ({len(monthly)} شهر):")
            for month in monthly:
                print(f"   - {month['month']}: {month['cars_sold']} سيارة")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
    
    else:
        print("لم يتم إنشاء بيانات جديدة (قد تكون موجودة مسبقاً)")

if __name__ == "__main__":
    test_dashboard_with_sample_data()