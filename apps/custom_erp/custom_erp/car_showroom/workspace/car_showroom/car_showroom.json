{"category": "<PERSON><PERSON><PERSON>", "charts": [{"chart_name": "Car Sales by Brand", "label": "Car Sales by Brand"}, {"chart_name": "Monthly Car Sales", "label": "Monthly Car Sales"}], "creation": "2024-01-01 00:00:00.000000", "developer_mode_only": 0, "disable_user_customization": 0, "docstatus": 0, "doctype": "Workspace", "extends": "", "extends_another_page": 0, "hide_custom": 0, "icon": "car", "idx": 0, "is_default": 0, "is_standard": 1, "label": "Car Showroom", "name": "Car Showroom", "links": [{"hidden": 0, "is_query_report": 0, "label": "Car Management", "link_count": 0, "link_name": "", "link_to": "", "link_type": "Card Break", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Car Record", "link_count": 0, "link_name": "Car Record", "link_to": "Car Record", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Car Brand", "link_count": 0, "link_name": "Car Brand", "link_to": "Car Brand", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Car Promotion", "link_count": 0, "link_name": "Car Promotion", "link_to": "Car Promotion", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Reports", "link_count": 0, "link_name": "", "link_to": "", "link_type": "Card Break", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 1, "label": "Car Sales Analysis", "link_count": 0, "link_name": "Car Sales Analysis", "link_to": "Car Sales Analysis", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Car Inventory Report", "link_count": 0, "link_name": "Car Inventory Report", "link_to": "Car Inventory Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Luxury Cars Report", "link_count": 0, "link_name": "Luxury Cars Report", "link_to": "Luxury Cars Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Dashboard", "link_count": 0, "link_name": "", "link_to": "", "link_type": "Card Break", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Car Dashboard", "link_count": 0, "link_name": "car-dashboard", "link_to": "car-dashboard", "link_type": "Page", "onboard": 0, "type": "Link"}], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [{"document_type": "Car Record", "label": "Available Cars", "query": "status = 'متاحة'"}, {"document_type": "Car Record", "label": "Reserved Cars", "query": "status = 'محجوزة'"}, {"document_type": "Car Record", "label": "Sold Cars", "query": "status = 'مباعة'"}], "roles": [], "sequence_id": 1.0, "shortcuts": [{"color": "Green", "doc_view": "List", "label": "Car Record", "link_to": "Car Record", "type": "DocType"}, {"color": "Blue", "doc_view": "List", "label": "Car Brand", "link_to": "Car Brand", "type": "DocType"}, {"color": "Purple", "doc_view": "List", "label": "Car Promotion", "link_to": "Car Promotion", "type": "DocType"}, {"color": "Orange", "doc_view": "", "label": "Car Dashboard", "link_to": "car-dashboard", "type": "Page"}], "title": "معرض السيارات", "content": "[]"}