# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import getdate, add_months, nowdate
import random

def create_sample_sold_cars():
    """إنشاء بيانات تجريبية للسيارات المباعة لعرض المخططات"""
    
    # التحقق من وجود ماركات
    brands = frappe.get_all("Car Brand", fields=["name"])
    if not brands:
        # إنشاء ماركات تجريبية
        sample_brands = ["تويوتا", "هوندا", "نيسان", "فورد", "شيفروليه", "بي إم دبليو", "مرسيدس"]
        for brand in sample_brands:
            if not frappe.db.exists("Car Brand", brand):
                brand_doc = frappe.new_doc("Car Brand")
                brand_doc.brand_name = brand
                brand_doc.insert(ignore_permissions=True)
        
        brands = frappe.get_all("Car Brand", fields=["name"])
    
    # إنشاء سيارات مباعة تجريبية
    models = ["كامري", "أكورد", "ألتيما", "فيوجن", "ماليبو", "X5", "C-Class"]
    
    for i in range(20):  # 20 سيارة مباعة
        try:
            brand = random.choice(brands)["name"]
            model = random.choice(models)
            
            car = frappe.new_doc("Car Record")
            car.car_name = f"{brand} {model} {random.randint(2018, 2024)}"
            car.brand = brand
            car.model = model
            car.year = random.randint(2018, 2024)
            car.status = "مباعة"
            car.purchase_price = random.randint(50000, 200000)
            car.selling_price = car.purchase_price + random.randint(10000, 50000)
            car.sale_date = add_months(getdate(), -random.randint(1, 12))
            car.customer = "عميل تجريبي " + str(i+1)
            
            car.insert(ignore_permissions=True)
            print(f"تم إنشاء سيارة مباعة: {car.car_name}")
            
        except Exception as e:
            print(f"خطأ في إنشاء السيارة {i+1}: {str(e)}")
            continue
    
    frappe.db.commit()
    print("تم إنشاء البيانات التجريبية للمخططات")

if __name__ == "__main__":
    frappe.init(site='site1.local')
    frappe.connect()
    create_sample_sold_cars()