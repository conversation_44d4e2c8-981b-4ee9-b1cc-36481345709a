# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import flt, getdate, nowdate, add_months
from frappe import _

@frappe.whitelist()
def get_car_stats():
	"""إحصائيات السيارات"""
	stats = {}
	
	# إجمالي السيارات
	stats['total_cars'] = frappe.db.count('Car Record')
	
	# السيارات المتاحة
	stats['available_cars'] = frappe.db.count('Car Record', {'status': 'متاحة'})
	
	# السيارات المباعة
	stats['sold_cars'] = frappe.db.count('Car Record', {'status': 'مباعة'})
	
	# السيارات المحجوزة
	stats['reserved_cars'] = frappe.db.count('Car Record', {'status': 'محجوزة'})
	
	# السيارات قيد الصيانة
	stats['maintenance_cars'] = frappe.db.count('Car Record', {'status': 'قيد الصيانة'})
	
	# قيمة المخزون
	inventory_value = frappe.db.sql("""
		SELECT SUM(selling_price) as total_value
		FROM `tabCar Record`
		WHERE status != 'مباعة'
	""")[0][0] or 0
	
	stats['inventory_value'] = inventory_value
	
	# متوسط سعر السيارات
	avg_price = frappe.db.sql("""
		SELECT AVG(selling_price) as avg_price
		FROM `tabCar Record`
		WHERE status != 'مباعة'
	""")[0][0] or 0
	
	stats['average_price'] = avg_price
	
	return stats

@frappe.whitelist()
def get_sales_by_brand():
	"""مبيعات حسب الماركة"""
	try:
		data = frappe.db.sql("""
			SELECT 
				brand,
				COUNT(*) as count,
				SUM(selling_price) as total_sales,
				AVG(selling_price) as avg_price
			FROM `tabCar Record`
			WHERE status = 'مباعة' 
			AND sale_date >= %s
			GROUP BY brand
			ORDER BY total_sales DESC
		""", [add_months(nowdate(), -12)], as_dict=1)
		
		return data or []
	except Exception:
		return []

@frappe.whitelist()
def get_monthly_sales():
	"""المبيعات الشهرية"""
	try:
		data = frappe.db.sql("""
			SELECT 
				DATE_FORMAT(sale_date, '%%Y-%%m') as month,
				COUNT(*) as cars_sold,
				SUM(selling_price) as total_revenue,
				SUM(selling_price - IFNULL(purchase_price, 0)) as total_profit
			FROM `tabCar Record`
			WHERE status = 'مباعة' 
			AND sale_date >= %s
			GROUP BY DATE_FORMAT(sale_date, '%%Y-%%m')
			ORDER BY month DESC
		""", [add_months(nowdate(), -12)], as_dict=1)
		
		return data or []
	except Exception:
		return []

@frappe.whitelist()
def get_inventory_aging():
	"""تقادم المخزون"""
	data = frappe.db.sql("""
		SELECT 
			car_name,
			brand,
			model,
			year,
			acquisition_date,
			selling_price,
			DATEDIFF(CURDATE(), acquisition_date) as days_in_inventory,
			CASE 
				WHEN DATEDIFF(CURDATE(), acquisition_date) <= 30 THEN 'جديد'
				WHEN DATEDIFF(CURDATE(), acquisition_date) <= 90 THEN 'متوسط'
				WHEN DATEDIFF(CURDATE(), acquisition_date) <= 180 THEN 'قديم'
				ELSE 'قديم جداً'
			END as aging_category
		FROM `tabCar Record`
		WHERE status IN ('متاحة', 'محجوزة')
		ORDER BY days_in_inventory DESC
	""", as_dict=1)
	
	return data

@frappe.whitelist()
def get_customer_analysis():
	"""تحليل العملاء"""
	data = frappe.db.sql("""
		SELECT 
			customer,
			COUNT(*) as cars_purchased,
			SUM(selling_price) as total_spent,
			AVG(selling_price) as avg_spent,
			MAX(sale_date) as last_purchase
		FROM `tabCar Record`
		WHERE status = 'مباعة' AND customer IS NOT NULL
		GROUP BY customer
		ORDER BY total_spent DESC
		LIMIT 20
	""", as_dict=1)
	
	return data