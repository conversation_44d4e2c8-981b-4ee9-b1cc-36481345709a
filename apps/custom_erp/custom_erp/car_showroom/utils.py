# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.utils import flt, getdate, nowdate, add_days
from frappe import _

@frappe.whitelist()
def get_car_status_options():
	"""خيارات حالة السيارة"""
	return ["متاحة", "محجوزة", "مباعة", "قيد الصيانة", "قيد التقييم"]

@frappe.whitelist()
def get_fuel_type_options():
	"""خيارات نوع الوقود"""
	return ["بنزين", "ديزل", "هجين", "كهربائي", "غاز طبيعي"]

@frappe.whitelist()
def get_transmission_options():
	"""خيارات ناقل الحركة"""
	return ["يدوي", "أوتوماتيك", "CVT", "مزدوج القابض"]

@frappe.whitelist()
def calculate_car_value(purchase_price, year, mileage=0):
	"""حساب القيمة المقدرة للسيارة"""
	if not purchase_price or not year:
		return 0
	
	current_year = frappe.utils.now_datetime().year
	age = current_year - int(year)
	
	# معدل الاستهلاك السنوي (15%)
	depreciation_rate = 0.15
	
	# تقليل إضافي بناءً على الكيلومترات
	mileage_factor = 1.0
	if mileage:
		if mileage > 200000:
			mileage_factor = 0.7
		elif mileage > 100000:
			mileage_factor = 0.85
		elif mileage > 50000:
			mileage_factor = 0.95
	
	# حساب القيمة
	depreciated_value = flt(purchase_price) * pow((1 - depreciation_rate), age)
	estimated_value = depreciated_value * mileage_factor
	
	return max(estimated_value, flt(purchase_price) * 0.1)  # أدنى قيمة 10% من سعر الشراء

@frappe.whitelist()
def get_brand_suggestions():
	"""اقتراحات الماركات"""
	brands = frappe.get_all("Car Brand", 
		fields=["name", "brand_name_english", "is_luxury_brand"],
		order_by="name")
	return brands

@frappe.whitelist()
def validate_vin_number(vin):
	"""التحقق من صحة رقم الهيكل"""
	if not vin:
		return {"valid": False, "message": "رقم الهيكل مطلوب"}
	
	# التحقق من الطول (17 رقم/حرف)
	if len(vin) != 17:
		return {"valid": False, "message": "رقم الهيكل يجب أن يكون 17 رقم/حرف"}
	
	# التحقق من عدم التكرار
	exists = frappe.db.exists("Car Record", {"vin_number": vin})
	if exists:
		return {"valid": False, "message": "رقم الهيكل موجود مسبقاً"}
	
	return {"valid": True, "message": "رقم الهيكل صحيح"}

@frappe.whitelist()
def get_similar_cars(brand=None, model=None, year=None, exclude_car=None):
	"""البحث عن سيارات مشابهة"""
	conditions = []
	params = []
	
	if brand:
		conditions.append("brand = %s")
		params.append(brand)
	
	if model:
		conditions.append("model = %s")
		params.append(model)
	
	if year:
		conditions.append("year BETWEEN %s AND %s")
		params.extend([int(year) - 2, int(year) + 2])
	
	if exclude_car:
		conditions.append("name != %s")
		params.append(exclude_car)
	
	conditions.append("status != 'مباعة'")
	
	where_clause = " AND ".join(conditions) if conditions else "1=1"
	
	data = frappe.db.sql(f"""
		SELECT 
			name, car_name, brand, model, year, 
			selling_price, status, mileage
		FROM `tabCar Record`
		WHERE {where_clause}
		ORDER BY selling_price
		LIMIT 10
	""", params, as_dict=1)
	
	return data

@frappe.whitelist()
def generate_car_report(car_record):
	"""إنشاء تقرير السيارة"""
	car = frappe.get_doc("Car Record", car_record)
	
	# معلومات أساسية
	report_data = {
		"car_info": {
			"name": car.car_name,
			"brand": car.brand,
			"model": car.model,
			"year": car.year,
			"color": car.color,
			"mileage": car.mileage,
			"fuel_type": car.fuel_type,
			"transmission": car.transmission
		},
		"financial_info": {
			"purchase_price": car.purchase_price,
			"selling_price": car.selling_price,
			"minimum_price": car.minimum_price,
			"profit_margin": car.profit_margin
		},
		"condition_info": {
			"exterior_condition": car.exterior_condition,
			"interior_condition": car.interior_condition,
			"mechanical_condition": car.mechanical_condition
		},
		"features": {
			"luxury_features": [f.feature_name for f in car.luxury_features],
			"safety_features": [f.feature_name for f in car.safety_features]
		},
		"documents": [d.document_name for d in car.car_documents],
		"images": [i.image_file for i in car.car_images]
	}
	
	return report_data

@frappe.whitelist()
def bulk_update_car_status(cars, new_status):
	"""تحديث حالة عدة سيارات"""
	if not isinstance(cars, list):
		cars = [cars]
	
	updated_count = 0
	errors = []
	
	for car_name in cars:
		try:
			car_doc = frappe.get_doc("Car Record", car_name)
			old_status = car_doc.status
			car_doc.status = new_status
			car_doc.save()
			
			# إضافة تعليق
			car_doc.add_comment("Comment", f"تم تغيير الحالة من {old_status} إلى {new_status}")
			updated_count += 1
			
		except Exception as e:
			errors.append(f"خطأ في تحديث {car_name}: {str(e)}")
	
	return {
		"updated_count": updated_count,
		"errors": errors,
		"message": f"تم تحديث {updated_count} سيارة بنجاح"
	}

@frappe.whitelist()
def get_car_maintenance_alerts():
	"""تنبيهات صيانة السيارات"""
	# السيارات التي تحتاج صيانة
	due_maintenance = frappe.db.sql("""
		SELECT name, car_name, brand, model, next_service_date
		FROM `tabCar Record`
		WHERE next_service_date <= %s
		AND status != 'مباعة'
		ORDER BY next_service_date
	""", [add_days(nowdate(), 7)], as_dict=1)
	
	# السيارات القديمة في المخزون
	old_inventory = frappe.db.sql("""
		SELECT name, car_name, brand, model, acquisition_date,
			DATEDIFF(CURDATE(), acquisition_date) as days_in_inventory
		FROM `tabCar Record`
		WHERE acquisition_date <= %s
		AND status IN ('متاحة', 'محجوزة')
		ORDER BY acquisition_date
	""", [add_days(nowdate(), -180)], as_dict=1)
	
	return {
		"maintenance_due": due_maintenance,
		"old_inventory": old_inventory
	}

@frappe.whitelist()
def export_car_data(filters=None):
	"""تصدير بيانات السيارات"""
	conditions = ["1=1"]
	params = []
	
	if filters:
		if filters.get("status"):
			conditions.append("status = %s")
			params.append(filters["status"])
		
		if filters.get("brand"):
			conditions.append("brand LIKE %s")
			params.append(f"%{filters['brand']}%")
	
	where_clause = " AND ".join(conditions)
	
	data = frappe.db.sql(f"""
		SELECT 
			name, car_name, brand, model, year, color,
			mileage, fuel_type, transmission, status,
			purchase_price, selling_price, profit_margin,
			acquisition_date, sale_date, customer
		FROM `tabCar Record`
		WHERE {where_clause}
		ORDER BY creation DESC
	""", params, as_dict=1)
	
	return data