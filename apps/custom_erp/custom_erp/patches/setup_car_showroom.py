# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

import frappe
from frappe import _

def execute():
    """تشغيل إعدادات معرض السيارات بعد الهجرة"""
    
    try:
        # استيراد دالة after_install بشكل صحيح
        from custom_erp.car_showroom.install import after_install
        
        # تشغيل الإعدادات
        after_install()
        
        print("تم تشغيل إعدادات معرض السيارات بنجاح!")
        
    except Exception as e:
        frappe.log_error(f"خطأ في تشغيل إعدادات معرض السيارات: {str(e)}")
        print(f"تحذير: فشل في تشغيل إعدادات معرض السيارات: {str(e)}")
        # لا نرمي خطأ لتجنب توقف الهجرة
        pass