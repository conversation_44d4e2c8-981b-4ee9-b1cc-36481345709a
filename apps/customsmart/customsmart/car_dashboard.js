// 📊 لوحة قيادة مخزون السيارات
// Car Inventory Dashboard

frappe.provide('car_showroom');

car_showroom.CarDashboard = class CarDashboard {
    constructor() {
        this.init();
    }

    init() {
        this.create_dashboard();
        this.load_data();
        this.setup_refresh_timer();
    }

    create_dashboard() {
        // إنشاء HTML للوحة القيادة
        $('body').append(`
            <div id="car-inventory-dashboard" style="display: none; position: fixed; top: 60px; right: 20px; 
                 width: 350px; background: white; border: 1px solid #ddd; border-radius: 8px; 
                 box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000; max-height: 80vh; overflow-y: auto;">
                
                <div style="padding: 15px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
                    <h4 style="margin: 0; color: #333; display: flex; align-items: center;">
                        🚗 مخزون السيارات
                        <button id="close-dashboard" style="margin-left: auto; border: none; background: none; font-size: 18px; cursor: pointer;">×</button>
                    </h4>
                    <small style="color: #666;">آخر تحديث: <span id="last-update">الآن</span></small>
                </div>

                <div id="dashboard-content" style="padding: 15px;">
                    <div class="loading-spinner" style="text-align: center; padding: 20px;">
                        <div style="border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                        <p style="margin-top: 10px; color: #666;">جارِ التحميل...</p>
                    </div>
                </div>

                <div style="padding: 10px 15px; border-top: 1px solid #eee; background: #f8f9fa; border-radius: 0 0 8px 8px;">
                    <button id="refresh-dashboard" class="btn btn-sm btn-primary" style="width: 100%;">
                        🔄 تحديث البيانات
                    </button>
                </div>
            </div>

            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .status-badge {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: bold;
                    margin-right: 5px;
                }
                .status-available { background: #d4edda; color: #155724; }
                .status-sold { background: #f8d7da; color: #721c24; }
                .status-reserved { background: #fff3cd; color: #856404; }
                .status-maintenance { background: #cce7ff; color: #004085; }
            </style>
        `);

        // تفعيل الأحداث
        this.setup_events();
    }

    setup_events() {
        // إغلاق اللوحة
        $(document).on('click', '#close-dashboard', () => {
            $('#car-inventory-dashboard').hide();
        });

        // تحديث البيانات
        $(document).on('click', '#refresh-dashboard', () => {
            this.load_data();
        });

        // إضافة زر في القائمة الرئيسية
        setTimeout(() => {
            if ($('.navbar-right .dropdown-toggle:contains("Help")').length) {
                $('.navbar-right .dropdown-toggle:contains("Help")').parent().before(`
                    <li>
                        <a href="#" id="show-car-dashboard" style="color: #333;">
                            📊 مخزون السيارات
                        </a>
                    </li>
                `);
            }
        }, 2000);

        $(document).on('click', '#show-car-dashboard', (e) => {
            e.preventDefault();
            $('#car-inventory-dashboard').toggle();
            if ($('#car-inventory-dashboard').is(':visible')) {
                this.load_data();
            }
        });
    }

    load_data() {
        // عرض مؤشر التحميل
        $('#dashboard-content').html(`
            <div class="loading-spinner" style="text-align: center; padding: 20px;">
                <div style="border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                <p style="margin-top: 10px; color: #666;">جارِ التحميل...</p>
            </div>
        `);

        // جلب البيانات من الخادم
        frappe.call({
            method: 'customsmart.car_inventory_management.get_car_inventory_summary',
            callback: (r) => {
                if (r.message) {
                    this.render_data(r.message);
                } else {
                    this.show_error('فشل في جلب البيانات');
                }
            },
            error: () => {
                this.show_error('خطأ في الاتصال بالخادم');
            }
        });

        // تحديث وقت آخر تحديث
        $('#last-update').text(frappe.datetime.get_time(frappe.datetime.now_time()));
    }

    render_data(data) {
        const total = data.total_cars || 0;
        const available = data.available_cars || 0;
        const sold = data.sold_cars || 0;
        const reserved = data.reserved_cars || 0;

        let html = `
            <!-- الملخص الإجمالي -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h5 style="margin: 0 0 10px 0; text-align: center;">📊 الملخص الإجمالي</h5>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; text-align: center;">
                    <div>
                        <div style="font-size: 24px; font-weight: bold;">${total}</div>
                        <div style="font-size: 12px; opacity: 0.9;">إجمالي السيارات</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; font-weight: bold; color: #90EE90;">${available}</div>
                        <div style="font-size: 12px; opacity: 0.9;">متاح للبيع</div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الحالات -->
            <div style="margin-bottom: 15px;">
                <h6 style="margin-bottom: 10px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 5px;">
                    📈 تفاصيل الحالات
                </h6>
                
                <div style="display: grid; gap: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 5px;">
                        <span>✅ متاح للبيع</span>
                        <span class="status-badge status-available">${available}</span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 5px;">
                        <span>🔴 مباع</span>
                        <span class="status-badge status-sold">${sold}</span>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 5px;">
                        <span>⏳ محجوز</span>
                        <span class="status-badge status-reserved">${reserved}</span>
                    </div>
                </div>
            </div>
        `;

        // إضافة تفاصيل السيارات المتاحة
        if (data.cars_detail && data.cars_detail.length > 0) {
            const availableCars = data.cars_detail.filter(car => car.status === 'Available');
            
            if (availableCars.length > 0) {
                html += `
                    <div style="margin-bottom: 15px;">
                        <h6 style="margin-bottom: 10px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 5px;">
                            🚗 السيارات المتاحة (${availableCars.length})
                        </h6>
                        <div style="max-height: 200px; overflow-y: auto;">
                `;

                availableCars.slice(0, 10).forEach(car => {
                    html += `
                        <div style="padding: 8px; border: 1px solid #eee; border-radius: 5px; margin-bottom: 5px; background: white;">
                            <div style="font-weight: bold; color: #333; margin-bottom: 3px;">
                                ${car.car_name || 'سيارة'}
                            </div>
                            <div style="font-size: 11px; color: #666;">
                                🔢 ${car.chassis_no}<br>
                                🎨 ${car.car_color} | 📅 ${car.car_model}
                            </div>
                        </div>
                    `;
                });

                if (availableCars.length > 10) {
                    html += `
                        <div style="text-align: center; padding: 8px; color: #666; font-size: 12px;">
                            ... و ${availableCars.length - 10} سيارة أخرى
                        </div>
                    `;
                }

                html += `</div></div>`;
            }
        }

        // إضافة إحصائيات إضافية
        html += `
            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; text-align: center;">
                <small style="color: #666;">
                    💡 نصيحة: انقر على "تحديث البيانات" للحصول على أحدث المعلومات
                </small>
            </div>
        `;

        $('#dashboard-content').html(html);
    }

    show_error(message) {
        $('#dashboard-content').html(`
            <div style="text-align: center; padding: 20px; color: #dc3545;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <p>${message}</p>
                <button onclick="car_showroom.dashboard.load_data()" class="btn btn-sm btn-primary">
                    إعادة المحاولة
                </button>
            </div>
        `);
    }

    setup_refresh_timer() {
        // تحديث تلقائي كل 5 دقائق
        setInterval(() => {
            if ($('#car-inventory-dashboard').is(':visible')) {
                this.load_data();
            }
        }, 300000); // 5 minutes
    }
}

// تفعيل اللوحة عند تحميل الصفحة
$(document).ready(() => {
    car_showroom.dashboard = new car_showroom.CarDashboard();
    
    // إضافة اختصار لوحة المفاتيح
    $(document).keydown((e) => {
        // Ctrl + Shift + I لإظهار/إخفاء اللوحة
        if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
            e.preventDefault();
            $('#car-inventory-dashboard').toggle();
            if ($('#car-inventory-dashboard').is(':visible')) {
                car_showroom.dashboard.load_data();
            }
        }
    });
});

// إضافة وظائف مساعدة
frappe.provide('car_showroom.utils');

car_showroom.utils.check_car_availability = (chassis_no) => {
    return new Promise((resolve, reject) => {
        frappe.call({
            method: 'customsmart.car_inventory_management.check_car_availability_api',
            args: { chassis_no: chassis_no },
            callback: (r) => {
                if (r.message) {
                    resolve(r.message);
                } else {
                    reject('فشل في فحص السيارة');
                }
            },
            error: (err) => {
                reject(err);
            }
        });
    });
};

car_showroom.utils.get_available_cars = () => {
    return new Promise((resolve, reject) => {
        frappe.call({
            method: 'customsmart.car_inventory_management.get_available_cars_api',
            callback: (r) => {
                if (r.message) {
                    resolve(r.message);
                } else {
                    reject('فشل في جلب السيارات المتاحة');
                }
            },
            error: (err) => {
                reject(err);
            }
        });
    });
};

// إضافة إشعارات للمخزون المنخفض
car_showroom.utils.check_low_stock = () => {
    frappe.call({
        method: 'customsmart.car_inventory_management.get_car_inventory_summary',
        callback: (r) => {
            if (r.message && r.message.available_cars < 5) {
                frappe.show_alert({
                    message: `⚠️ تنبيه: المخزون المتاح منخفض (${r.message.available_cars} سيارات فقط)`,
                    indicator: 'orange'
                }, 10);
            }
        }
    });
};

// فحص المخزون عند تحميل الصفحة
$(document).ready(() => {
    setTimeout(() => {
        car_showroom.utils.check_low_stock();
    }, 3000);
});