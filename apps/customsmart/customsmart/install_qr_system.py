# -*- coding: utf-8 -*-

import frappe
from customsmart.final_qr_print_format import create_final_qr_print_format

def install_qr_system():
    """تثبيت نظام QR شامل للفواتير"""
    
    print("🚀 بدء تثبيت نظام QR للفواتير...")
    
    try:
        # 1. إنشاء Print Format مع QR
        print("📄 إنشاء Print Format مع QR...")
        print_format_name = create_final_qr_print_format()
        
        # 2. إنشاء الحقول المخصصة
        print("⚙️ إنشاء الحقول المخصصة...")
        create_qr_custom_field()
        
        # 3. تحديث Client Script
        print("💻 تحديث Client Script...")
        create_qr_client_script()
        
        print("✅ تم تثبيت نظام QR بنجاح!")
        print(f"📋 Print Format الجديد: {print_format_name}")
        print("🔍 يمكنك الآن طباعة الفواتير مع QR كود شامل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {str(e)}")
        frappe.log_error(f"QR System Installation Error: {str(e)}")
        return False



def create_qr_custom_field():
    """إنشاء حقول مخصصة لحفظ بيانات QR"""
    
    custom_fields = [
        {
            "fieldname": "qr_detailed_data",
            "label": "QR Detailed Data",
            "fieldtype": "Long Text",
            "hidden": 1,
            "read_only": 1,
            "no_copy": 1
        },
        {
            "fieldname": "qr_zatca_data", 
            "label": "QR ZATCA Data",
            "fieldtype": "Data",
            "hidden": 1,
            "read_only": 1,
            "no_copy": 1
        }
    ]
    
    for field in custom_fields:
        if not frappe.db.exists("Custom Field", {"dt": "Sales Invoice", "fieldname": field["fieldname"]}):
            custom_field = frappe.get_doc({
                "doctype": "Custom Field",
                "dt": "Sales Invoice",
                "fieldname": field["fieldname"],
                "label": field["label"],
                "fieldtype": field["fieldtype"],
                "hidden": field.get("hidden", 0),
                "read_only": field.get("read_only", 0),
                "no_copy": field.get("no_copy", 0),
                "owner": "Administrator"
            })
            custom_field.insert(ignore_permissions=True)
            print(f"✅ تم إنشاء حقل: {field['fieldname']}")
        else:
            print(f"ℹ️ الحقل موجود مسبقاً: {field['fieldname']}")
    
    frappe.db.commit()

def create_qr_client_script():
    """إنشاء Client Script لعرض QR"""
    
    script_code = '''
// Client Script: QR Code Display for Sales Invoice
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // إضافة زر عرض QR Code للفواتير المؤكدة
        if (frm.doc.docstatus === 1 && frm.doc.qr_detailed_data) {
            frm.add_custom_button(__('Show QR Codes'), function() {
                show_qr_codes_modal(frm.doc);
            }, __('Actions'));
        }
    }
});

function show_qr_codes_modal(doc) {
    // إنشاء حوار لعرض QR Codes
    const dialog = new frappe.ui.Dialog({
        title: __('Invoice QR Codes') + ' - ' + doc.name,
        size: 'large',
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'qr_display',
                options: `
                    <div style="text-align: center; padding: 20px;">
                        <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
                            <div style="margin: 20px; padding: 20px; border: 2px solid #1f4e79; border-radius: 10px; background-color: #f8f9fa;">
                                <h4 style="color: #1f4e79; margin-bottom: 15px;">QR كود شامل</h4>
                                <div id="detailed-qr-container" style="margin: 15px 0;"></div>
                                <div style="font-size: 12px; color: #666; margin-top: 10px;">
                                    يحتوي على جميع بيانات الفاتورة والسيارة
                                </div>
                            </div>
                            
                            <div style="margin: 20px; padding: 20px; border: 2px solid #28a745; border-radius: 10px; background-color: #f8f9fa;">
                                <h4 style="color: #28a745; margin-bottom: 15px;">ZATCA QR</h4>
                                <div id="zatca-qr-container" style="margin: 15px 0;"></div>
                                <div style="font-size: 12px; color: #666; margin-top: 10px;">
                                    متوافق مع معايير الهيئة الضريبية السعودية
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 30px; padding: 20px; background-color: #e9ecef; border-radius: 8px;">
                            <h5>بيانات QR الشامل:</h5>
                            <pre style="text-align: left; font-size: 10px; background-color: white; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">${doc.qr_detailed_data || 'غير متوفر'}</pre>
                        </div>
                        
                        <div style="margin-top: 15px; padding: 20px; background-color: #d4edda; border-radius: 8px;">
                            <h5>بيانات ZATCA:</h5>
                            <pre style="text-align: left; font-size: 12px; background-color: white; padding: 10px; border-radius: 4px;">${doc.qr_zatca_data || 'غير متوفر'}</pre>
                        </div>
                    </div>
                `
            }
        ],
        primary_action_label: __('Close'),
        primary_action: function() {
            dialog.hide();
        }
    });
    
    dialog.show();
    
    // توليد QR Codes باستخدام مكتبة خارجية
    setTimeout(() => {
        // تحميل مكتبة QR Code إذا لم تكن محملة
        if (typeof QRCode === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js';
            script.onload = function() {
                generateQRCodes(doc);
            };
            document.head.appendChild(script);
        } else {
            generateQRCodes(doc);
        }
    }, 100);
}

function generateQRCodes(doc) {
    // توليد QR Code للبيانات الشاملة
    if (doc.qr_detailed_data && typeof QRCode !== 'undefined') {
        QRCode.toDataURL(doc.qr_detailed_data, {
            width: 200,
            height: 200,
            margin: 2,
            color: {
                dark: '#1f4e79',
                light: '#FFFFFF'
            }
        }, function(err, url) {
            if (!err) {
                document.getElementById('detailed-qr-container').innerHTML = 
                    '<img src="' + url + '" style="max-width: 200px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';
            }
        });
    }
    
    // توليد QR Code لبيانات ZATCA
    if (doc.qr_zatca_data && typeof QRCode !== 'undefined') {
        QRCode.toDataURL(doc.qr_zatca_data, {
            width: 150,
            height: 150,
            margin: 2,
            color: {
                dark: '#28a745',
                light: '#FFFFFF'
            }
        }, function(err, url) {
            if (!err) {
                document.getElementById('zatca-qr-container').innerHTML = 
                    '<img src="' + url + '" style="max-width: 150px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';
            }
        });
    }
}
'''

    script_name = "QR Display for Sales Invoice"
    
    if frappe.db.exists("Client Script", script_name):
        client_script = frappe.get_doc("Client Script", script_name)
        client_script.script = script_code
        client_script.save()
        print(f"✅ تم تحديث Client Script: {script_name}")
    else:
        client_script = frappe.get_doc({
            "doctype": "Client Script",
            "name": script_name,
            "dt": "Sales Invoice",
            "view": "Form",
            "script": script_code,
            "enabled": 1
        })
        client_script.insert(ignore_permissions=True)
        print(f"✅ تم إنشاء Client Script: {script_name}")
    
    frappe.db.commit()

def uninstall_qr_system():
    """إلغاء تثبيت نظام QR"""
    print("🗑️ إلغاء تثبيت نظام QR...")
    
    # حذف Print Format
    if frappe.db.exists("Print Format", "Car Showroom QR Tax Invoice"):
        frappe.delete_doc("Print Format", "Car Showroom QR Tax Invoice")
        print("✅ تم حذف Print Format")
    
    # حذف Server Script
    if frappe.db.exists("Server Script", "QR Generator for Sales Invoice"):
        frappe.delete_doc("Server Script", "QR Generator for Sales Invoice")
        print("✅ تم حذف Server Script")
    
    # حذف Client Script
    if frappe.db.exists("Client Script", "QR Display for Sales Invoice"):
        frappe.delete_doc("Client Script", "QR Display for Sales Invoice")
        print("✅ تم حذف Client Script")
    
    frappe.db.commit()
    print("✅ تم إلغاء التثبيت بنجاح")

if __name__ == "__main__":
    install_qr_system()