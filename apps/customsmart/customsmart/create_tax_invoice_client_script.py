# -*- coding: utf-8 -*-

import frappe

def create_tax_invoice_client_script():
    """إنشاء Client Script لتحسين تجربة الفاتورة الضريبية"""
    
    script_name = "Tax Invoice Enhancement"
    
    script_code = """
// تحسين تجربة المستخدم للفاتورة الضريبية

frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // إضافة زر طباعة الفاتورة الضريبية
        if (frm.doc.docstatus === 1) {
            frm.add_custom_button(__('طباعة فاتورة ضريبية'), function() {
                frappe.utils.print(
                    frm.doc.doctype,
                    frm.doc.name,
                    'فاتورة ضريبية'
                );
            }, __('Print'));
            
            // زر عرض QR Code
            frm.add_custom_button(__('عرض QR Code'), function() {
                show_qr_code_dialog(frm);
            }, __('Actions'));
        }
        
        // تحديث معلومات رسوم اللوحات
        if (frm.doc.custom_plate_fees) {
            frm.dashboard.add_comment(
                __('رسوم إصدار لوحات: {0}', [format_currency(frm.doc.custom_plate_fees)]),
                'blue'
            );
        }
    },
    
    custom_plate_fees: function(frm) {
        // إعادة حساب الإجمالي عند تغيير رسوم اللوحات
        frm.trigger('calculate_taxes_and_totals');
    },
    
    before_submit: function(frm) {
        // تأكيد قبل الحفظ
        if (frm.doc.custom_plate_fees && frm.doc.custom_plate_fees > 0) {
            frappe.msgprint({
                title: __('تأكيد رسوم اللوحات'),
                message: __('تم إضافة رسوم إصدار لوحات بقيمة {0}', [format_currency(frm.doc.custom_plate_fees)]),
                indicator: 'green'
            });
        }
    }
});

// دالة عرض QR Code في نافذة منبثقة
function show_qr_code_dialog(frm) {
    if (!frm.doc.qr_zatca_data) {
        frappe.msgprint({
            title: __('QR غير متوفر'),
            message: __('يرجى حفظ الفاتورة أولاً لتوليد QR Code'),
            indicator: 'orange'
        });
        return;
    }
    
    let d = new frappe.ui.Dialog({
        title: __('QR Code للفاتورة'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'qr_display',
                options: `
                    <div style="text-align: center; padding: 20px;">
                        <div id="qr-dialog-container"></div>
                        <p style="margin-top: 15px; font-size: 12px; color: #666;">
                            ${__('QR Code للفاتورة رقم: {0}', [frm.doc.name])}
                        </p>
                        <p style="font-size: 11px; color: #888;">
                            ${__('يحتوي على: اسم الشركة، الرقم الضريبي، التاريخ، المبلغ، الضريبة')}
                        </p>
                    </div>
                `
            }
        ],
        primary_action_label: __('إغلاق'),
        primary_action: function() {
            d.hide();
        }
    });
    
    d.show();
    
    // توليد QR Code في النافذة المنبثقة
    setTimeout(() => {
        try {
            if (typeof QRCode !== 'undefined') {
                new QRCode(document.getElementById('qr-dialog-container'), {
                    text: frm.doc.qr_zatca_data,
                    width: 200,
                    height: 200,
                    colorDark: '#000000',
                    colorLight: '#ffffff'
                });
            } else {
                // تحميل مكتبة QR إذا لم تكن متوفرة
                frappe.require('https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js', () => {
                    new QRCode(document.getElementById('qr-dialog-container'), {
                        text: frm.doc.qr_zatca_data,
                        width: 200,
                        height: 200,
                        colorDark: '#000000',
                        colorLight: '#ffffff'
                    });
                });
            }
        } catch (error) {
            console.error('خطأ في توليد QR:', error);
            document.getElementById('qr-dialog-container').innerHTML = 
                '<div style="width: 200px; height: 200px; border: 2px dashed #ddd; display: flex; align-items: center; justify-content: center;">QR Code</div>';
        }
    }, 500);
}

// تحسين عرض المعلومات في القائمة
frappe.listview_settings['Sales Invoice'] = {
    add_fields: ['custom_plate_fees', 'qr_zatca_data'],
    get_indicator: function(doc) {
        if (doc.outstanding_amount > 0) {
            return [__("Unpaid"), "orange", "outstanding_amount,>,0"];
        } else if (doc.outstanding_amount < 0) {
            return [__("Credit Note Issued"), "darkgrey", "outstanding_amount,<,0"];
        } else if (doc.docstatus == 1) {
            return [__("Paid"), "green", "outstanding_amount,=,0"];
        }
    },
    onload: function(listview) {
        // إضافة معلومات QR في القائمة
        listview.page.add_menu_item(__('طباعة فواتير ضريبية مجمعة'), function() {
            let selected = listview.get_checked_items();
            if (selected.length === 0) {
                frappe.msgprint(__('يرجى اختيار فواتير للطباعة'));
                return;
            }
            
            // طباعة مجمعة للفواتير المختارة
            selected.forEach(doc => {
                if (doc.docstatus === 1) {
                    frappe.utils.print('Sales Invoice', doc.name, 'فاتورة ضريبية');
                }
            });
        });
    }
};
"""
    
    print(f"💻 إنشاء Client Script: {script_name}...")
    
    if frappe.db.exists("Client Script", script_name):
        client_script = frappe.get_doc("Client Script", script_name)
        client_script.script = script_code
        client_script.save()
        print(f"✅ تم تحديث Client Script: {script_name}")
    else:
        client_script = frappe.get_doc({
            "doctype": "Client Script",
            "name": script_name,
            "dt": "Sales Invoice",
            "view": "Form",
            "script": script_code,
            "enabled": 1
        })
        client_script.insert(ignore_permissions=True)
        print(f"✅ تم إنشاء Client Script: {script_name}")
    
    frappe.db.commit()
    return script_name

def install_complete_tax_invoice_system():
    """تثبيت نظام الفاتورة الضريبية الكامل"""
    
    print("🚀 تثبيت نظام الفاتورة الضريبية الكامل...")
    
    try:
        # 1. إنشاء Print Format
        from customsmart.create_tax_invoice_format import install_tax_invoice_system
        install_tax_invoice_system()
        
        # 2. إنشاء Client Script
        create_tax_invoice_client_script()
        
        # 3. مسح cache
        frappe.clear_cache()
        
        print("✅ تم تثبيت نظام الفاتورة الضريبية الكامل بنجاح!")
        print("📄 Print Format: فاتورة ضريبية")
        print("💻 Client Script: Tax Invoice Enhancement")
        print("🔍 يمكنك الآن:")
        print("   - طباعة فواتير ضريبية مع QR Code")
        print("   - عرض QR Code في نافذة منبثقة")
        print("   - استخدام رسوم إصدار اللوحات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {str(e)}")
        frappe.log_error(f"Complete Tax Invoice Installation Error: {str(e)}")
        return False

if __name__ == "__main__":
    install_complete_tax_invoice_system()