# -*- coding: utf-8 -*-

import frappe
import qrcode
import base64
from io import BytesIO
import json
from datetime import datetime

def generate_invoice_qr_data(doc):
    """
    توليد بيانات QR للفاتورة الضريبية وفقاً لمعايير ZATCA
    """
    try:
        # البيانات الأساسية للفاتورة
        basic_data = {
            "invoice_number": doc.name,
            "invoice_date": str(doc.posting_date),
            "invoice_time": str(doc.posting_time),
            "company_name": doc.company,
            "customer_name": doc.customer_name,
            "grand_total": float(doc.grand_total),
            "total_taxes": float(doc.total_taxes_and_charges or 0),
            "currency": doc.currency
        }
        
        # بيانات العميل
        customer_data = {
            "customer_address": doc.customer_address or "",
            "contact_mobile": doc.contact_mobile or "",
            "tax_id": getattr(doc, 'tax_id', '') or ""
        }
        
        # بيانات السيارة (إذا كانت متوفرة)
        car_data = {}
        if hasattr(doc, 'chassis_no') and doc.chassis_no:
            car_data.update({
                "chassis_no": doc.chassis_no,
                "engine_no": getattr(doc, 'engine_no', ''),
                "car_model": getattr(doc, 'car_model', ''),
                "car_color": getattr(doc, 'car_color', ''),
                "manufacture_year": getattr(doc, 'manufacture_year', ''),
                "car_brand": getattr(doc, 'car_brand', '')
            })
        
        # بيانات الأصناف
        items_data = []
        for item in doc.items:
            items_data.append({
                "item_name": item.item_name,
                "qty": float(item.qty),
                "rate": float(item.rate),
                "amount": float(item.amount),
                "description": item.description or ""
            })
        
        # بيانات ZATCA المطلوبة
        zatca_data = {
            "seller_name": doc.company,
            "vat_registration_number": frappe.db.get_value("Company", doc.company, "tax_id") or "",
            "invoice_timestamp": f"{doc.posting_date}T{doc.posting_time}",
            "invoice_total": float(doc.grand_total),
            "vat_total": float(doc.total_taxes_and_charges or 0)
        }
        
        # دمج جميع البيانات
        qr_data = {
            "basic_info": basic_data,
            "customer_info": customer_data,
            "car_info": car_data,
            "items": items_data,
            "zatca_compliance": zatca_data,
            "generated_at": datetime.now().isoformat()
        }
        
        return qr_data
        
    except Exception as e:
        frappe.log_error(f"خطأ في توليد بيانات QR: {str(e)}")
        return {"error": "فشل في توليد بيانات QR"}

def generate_qr_code_base64(doc):
    """
    توليد QR كود كـ base64 string
    """
    try:
        # الحصول على بيانات QR
        qr_data = generate_invoice_qr_data(doc)
        
        # تحويل البيانات إلى JSON
        qr_json = json.dumps(qr_data, ensure_ascii=False, indent=2)
        
        # إنشاء QR كود
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        qr.add_data(qr_json)
        qr.make(fit=True)
        
        # إنشاء الصورة
        img = qr.make_image(fill_color="black", back_color="white")
        
        # تحويل إلى base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
        
    except Exception as e:
        frappe.log_error(f"خطأ في توليد QR كود: {str(e)}")
        return None

def generate_zatca_simple_qr(doc):
    """
    توليد QR بسيط وفقاً لمعايير ZATCA الأساسية
    """
    try:
        # تنسيق البيانات حسب معايير ZATCA
        zatca_fields = [
            doc.company,  # اسم البائع
            frappe.db.get_value("Company", doc.company, "tax_id") or "",  # الرقم الضريبي
            f"{doc.posting_date}T{doc.posting_time}",  # التاريخ والوقت
            str(doc.grand_total),  # المبلغ الإجمالي
            str(doc.total_taxes_and_charges or 0)  # مبلغ الضريبة
        ]
        
        # ربط البيانات بفاصل
        zatca_string = "|".join(zatca_fields)
        
        # إنشاء QR كود
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=8,
            border=2,
        )
        
        qr.add_data(zatca_string)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
        
    except Exception as e:
        frappe.log_error(f"خطأ في توليد ZATCA QR: {str(e)}")
        return None

@frappe.whitelist()
def get_invoice_qr_code(docname, qr_type="detailed"):
    """
    API endpoint للحصول على QR code للفاتورة
    """
    try:
        doc = frappe.get_doc("Sales Invoice", docname)
        
        if qr_type == "zatca":
            qr_code = generate_zatca_simple_qr(doc)
        else:
            qr_code = generate_qr_code_base64(doc)
        
        if qr_code:
            # إرجاع الصورة مباشرة
            import frappe.response
            from frappe.utils import get_files_path
            import base64
            
            # فصل base64 data
            if qr_code.startswith('data:image/png;base64,'):
                img_data = qr_code.split(',')[1]
                img_binary = base64.b64decode(img_data)
                
                frappe.response['type'] = 'binary'
                frappe.response['filecontent'] = img_binary
                frappe.response['content_type'] = 'image/png'
                return
            
        return {"error": "Failed to generate QR code"}
        
    except Exception as e:
        frappe.log_error(f"QR Code Generation Error: {str(e)}")
        return {"error": str(e)}

def generate_qr_on_submit(doc, method):
    """توليد QR كود عند تأكيد الفاتورة"""
    try:
        if doc.docstatus == 1:  # فقط للفواتير المؤكدة
            # توليد بيانات QR الشاملة
            qr_data = generate_invoice_qr_data(doc)
            qr_detailed_json = json.dumps(qr_data, ensure_ascii=False)
            
            # توليد بيانات ZATCA للفاتورة الضريبية
            company_name = "مؤسسة السرعة الفاخرة للسيارات"
            company_tax_id = "301377763200003"
            zatca_data = "|".join([
                company_name,
                company_tax_id,
                f"{doc.posting_date}T{doc.posting_time}",
                str(doc.grand_total),
                str(doc.total_taxes_and_charges or 0)
            ])
            
            # حفظ البيانات في الفاتورة
            doc.db_set("qr_detailed_data", qr_detailed_json, update_modified=False)
            doc.db_set("qr_zatca_data", zatca_data, update_modified=False)
            
    except Exception as e:
        frappe.log_error(f"QR Generation Error: {str(e)}", "QR Generator")

@frappe.whitelist()
def get_invoice_qr_data(docname):
    """
    API endpoint للحصول على بيانات QR في صيغة JSON
    """
    try:
        doc = frappe.get_doc("Sales Invoice", docname)
        qr_data = generate_invoice_qr_data(doc)
        
        return {
            "data": qr_data,
            "success": True
        }
    except Exception as e:
        frappe.log_error(f"QR Data Generation Error: {str(e)}")
        return {
            "error": str(e),
            "success": False
        }