# -*- coding: utf-8 -*-

import frappe
from customsmart.qr_generator import generate_qr_code_base64, generate_zatca_simple_qr

def create_qr_enhanced_print_format():
    """إنشاء Print Format محسّن مع QR كود"""
    
    print_format_html = '''
    <div class="print-format">
        <style>
            .print-format {
                font-family: Arial, sans-serif;
                margin: 20px;
                direction: rtl;
                text-align: right;
                position: relative;
            }
            .header {
                text-align: center;
                border-bottom: 3px solid #1f4e79;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            .company-name {
                font-size: 28px;
                font-weight: bold;
                color: #1f4e79;
                margin-bottom: 10px;
            }
            .invoice-title {
                font-size: 24px;
                color: #d32f2f;
                font-weight: bold;
                margin: 15px 0;
            }
            .invoice-details {
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 5px;
            }
            .customer-details, .invoice-info {
                flex: 1;
                margin: 0 10px;
            }
            .section-title {
                font-size: 16px;
                font-weight: bold;
                color: #1f4e79;
                margin-bottom: 10px;
                border-bottom: 2px solid #1f4e79;
                padding-bottom: 5px;
            }
            .car-details {
                background-color: #e3f2fd;
                padding: 15px;
                margin: 20px 0;
                border-radius: 5px;
                border-left: 4px solid #2196f3;
            }
            .car-info-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
                margin-top: 10px;
            }
            .car-info-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px dotted #ccc;
            }
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                background-color: white;
            }
            .items-table th {
                background-color: #1f4e79;
                color: white;
                padding: 12px;
                text-align: center;
                font-weight: bold;
            }
            .items-table td {
                padding: 10px;
                text-align: center;
                border-bottom: 1px solid #ddd;
            }
            .items-table tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .totals-section {
                float: left;
                width: 300px;
                margin-top: 20px;
                direction: ltr;
                text-align: left;
            }
            .total-row {
                display: flex;
                justify-content: space-between;
                padding: 8px 15px;
                margin: 5px 0;
                background-color: #f0f0f0;
                border-radius: 3px;
            }
            .grand-total {
                background-color: #1f4e79 !important;
                color: white !important;
                font-weight: bold;
                font-size: 16px;
            }
            .tax-info {
                background-color: #fff3cd;
                border: 1px solid #ffc107;
                padding: 15px;
                margin: 20px 0;
                border-radius: 5px;
                color: #856404;
                text-align: center;
            }
            .footer {
                margin-top: 50px;
                text-align: center;
                clear: both;
                position: relative;
            }
            .signature-section {
                display: flex;
                justify-content: space-around;
                margin: 40px 0;
            }
            .signature-box {
                text-align: center;
                border-top: 2px solid #000;
                padding-top: 10px;
                width: 200px;
            }
            .qr-section {
                position: absolute;
                bottom: 20px;
                right: 20px;
                text-align: center;
                direction: ltr;
            }
            .qr-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
            .qr-code {
                width: 120px;
                height: 120px;
                border: 2px solid #1f4e79;
                border-radius: 8px;
                padding: 5px;
                background-color: white;
            }
            .qr-label {
                font-size: 10px;
                color: #666;
                text-align: center;
                direction: rtl;
            }
            .qr-zatca {
                width: 80px;
                height: 80px;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 2px;
                background-color: white;
            }
            @media print {
                .print-format {
                    margin: 0;
                }
                .qr-section {
                    position: fixed;
                    bottom: 30px;
                    right: 30px;
                }
            }
        </style>
        
        <div class="header">
            <div class="company-name">{{ doc.company }}</div>
            <div style="font-size: 14px; color: #666;">
                معرض السيارات | Car Showroom
            </div>
            <div class="invoice-title">
                فاتورة ضريبية | Tax Invoice
            </div>
            <div style="font-size: 12px; margin-top: 10px;">
                رقم الفاتورة: {{ doc.name }} | Invoice No: {{ doc.name }}
            </div>
        </div>

        <div class="invoice-details">
            <div class="customer-details">
                <div class="section-title">بيانات العميل | Customer Details</div>
                <div><strong>الاسم | Name:</strong> {{ doc.customer_name }}</div>
                <div><strong>العنوان | Address:</strong> {{ doc.customer_address or "غير محدد" }}</div>
                <div><strong>الهاتف | Phone:</strong> {{ doc.contact_mobile or "غير محدد" }}</div>
                {% if doc.tax_id %}
                <div><strong>الرقم الضريبي | Tax ID:</strong> {{ doc.tax_id }}</div>
                {% endif %}
            </div>
            <div class="invoice-info">
                <div class="section-title">بيانات الفاتورة | Invoice Info</div>
                <div><strong>التاريخ | Date:</strong> {{ doc.posting_date }}</div>
                <div><strong>وقت الإصدار | Time:</strong> {{ doc.posting_time }}</div>
                <div><strong>حالة الدفع | Payment Status:</strong> {{ doc.status }}</div>
                <div><strong>طريقة الدفع | Payment Method:</strong> {{ doc.mode_of_payment or "نقدي" }}</div>
            </div>
        </div>

        {% if doc.chassis_no or doc.engine_no or doc.car_color or doc.car_model or doc.manufacture_year %}
        <div class="car-details">
            <div class="section-title">تفاصيل السيارة | Car Details</div>
            <div class="car-info-grid">
                {% if doc.chassis_no %}
                <div class="car-info-item">
                    <span><strong>رقم الهيكل:</strong></span>
                    <span>{{ doc.chassis_no }}</span>
                </div>
                {% endif %}
                {% if doc.engine_no %}
                <div class="car-info-item">
                    <span><strong>رقم المحرك:</strong></span>
                    <span>{{ doc.engine_no }}</span>
                </div>
                {% endif %}
                {% if doc.car_color %}
                <div class="car-info-item">
                    <span><strong>اللون:</strong></span>
                    <span>{{ doc.car_color }}</span>
                </div>
                {% endif %}
                {% if doc.car_model %}
                <div class="car-info-item">
                    <span><strong>الطراز:</strong></span>
                    <span>{{ doc.car_model }}</span>
                </div>
                {% endif %}
                {% if doc.manufacture_year %}
                <div class="car-info-item">
                    <span><strong>سنة الصنع:</strong></span>
                    <span>{{ doc.manufacture_year }}</span>
                </div>
                {% endif %}
                {% if doc.car_brand %}
                <div class="car-info-item">
                    <span><strong>الماركة:</strong></span>
                    <span>{{ doc.car_brand }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <table class="items-table">
            <thead>
                <tr>
                    <th>الرقم | No.</th>
                    <th>الصنف | Item</th>
                    <th>الوصف | Description</th>
                    <th>الكمية | Qty</th>
                    <th>السعر | Rate</th>
                    <th>المبلغ | Amount</th>
                </tr>
            </thead>
            <tbody>
                {% for item in doc.items %}
                <tr>
                    <td>{{ item.idx }}</td>
                    <td>{{ item.item_name }}</td>
                    <td>{{ item.description or "-" }}</td>
                    <td>{{ "%.0f"|format(item.qty) }}</td>
                    <td>{{ "%.2f"|format(item.rate) }}</td>
                    <td>{{ "%.2f"|format(item.amount) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي | Subtotal:</span>
                <span>{{ "%.2f"|format(doc.total) }}</span>
            </div>
            {% if doc.total_taxes_and_charges %}
            <div class="total-row">
                <span>الضريبة | Tax:</span>
                <span>{{ "%.2f"|format(doc.total_taxes_and_charges) }}</span>
            </div>
            {% endif %}
            <div class="total-row grand-total">
                <span>المجموع الكلي | Grand Total:</span>
                <span>{{ "%.2f"|format(doc.grand_total) }}</span>
            </div>
        </div>

        <div style="clear: both;"></div>

        {% if doc.total_taxes_and_charges %}
        <div class="tax-info">
            <strong>معلومات الضريبة | Tax Information:</strong><br>
            هذه فاتورة ضريبية صادرة وفقاً لأنظمة الضريبة المطبقة
            <br>
            This is a tax invoice issued in accordance with applicable tax regulations
        </div>
        {% endif %}

        <div class="footer">
            <div style="margin-bottom: 20px;">
                <strong>شكراً لاختياركم معرضنا | Thank you for choosing our showroom</strong>
            </div>
            
            <div class="signature-section">
                <div class="signature-box">
                    <div>توقيع العميل</div>
                    <div>Customer Signature</div>
                </div>
                <div class="signature-box">
                    <div>توقيع المعرض</div>
                    <div>Showroom Signature</div>
                </div>
            </div>
            
            <div style="margin-top: 30px; font-size: 12px; color: #666;">
                تم إنشاء هذه الفاتورة بواسطة نظام ERP | Generated by ERP System
            </div>
        </div>

        <!-- QR Code Section -->
        <div class="qr-section">
            <div class="qr-container">
                <!-- QR كود شامل -->
                <div>
                    <img class="qr-code" src="{{ frappe.utils.get_url() }}/api/method/customsmart.qr_generator.get_invoice_qr_code?docname={{ doc.name }}&type=detailed" alt="QR Code"/>
                    <div class="qr-label">
                        QR شامل<br>
                        Detailed QR
                    </div>
                </div>
                
                <!-- QR كود ZATCA -->
                <div style="margin-top: 10px;">
                    <img class="qr-zatca" src="{{ frappe.utils.get_url() }}/api/method/customsmart.qr_generator.get_invoice_qr_code?docname={{ doc.name }}&type=zatca" alt="ZATCA QR"/>
                    <div class="qr-label">
                        ZATCA QR
                    </div>
                </div>
            </div>
        </div>
    </div>
    '''
    
    # إنشاء أو تحديث Print Format
    if frappe.db.exists("Print Format", "Car Showroom Tax Invoice with QR"):
        # تحديث الموجود
        print_format = frappe.get_doc("Print Format", "Car Showroom Tax Invoice with QR")
        print_format.html = print_format_html
        print_format.save()
        print("✅ تم تحديث Print Format مع QR كود")
    else:
        # إنشاء جديد
        print_format = frappe.get_doc({
            "doctype": "Print Format",
            "name": "Car Showroom Tax Invoice with QR",
            "doc_type": "Sales Invoice",
            "print_format_builder": 0,
            "standard": "No",
            "custom_format": 1,
            "html": print_format_html,
            "print_format_type": "Jinja",
            "font_size": 12,
            "show_section_headings": 1,
            "line_breaks": 1,
            "absolute_value": 0,
            "align_labels_right": 1,
            "raw_printing": 0,
            "default_print_language": "ar"
        })
        print_format.insert(ignore_permissions=True)
        print("✅ تم إنشاء Print Format جديد مع QR كود")
    
    frappe.db.commit()

if __name__ == "__main__":
    create_qr_enhanced_print_format()