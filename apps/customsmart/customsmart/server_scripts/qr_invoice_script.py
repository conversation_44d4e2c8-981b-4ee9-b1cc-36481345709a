# -*- coding: utf-8 -*-

import frappe
from customsmart.qr_generator import generate_qr_code_base64, generate_zatca_simple_qr

def create_qr_server_script():
    """إنشاء Server Script لتوليد QR كود للفواتير"""
    
    script_code = '''
# Server Script لتوليد QR كود للفواتير
import frappe
import qrcode
import base64
from io import BytesIO
import json
from datetime import datetime

def generate_invoice_qr_base64(doc):
    """توليد QR كود للفاتورة"""
    try:
        # البيانات الأساسية
        qr_data = {
            "invoice_number": doc.name,
            "company": doc.company,
            "customer": doc.customer_name,
            "date": str(doc.posting_date),
            "time": str(doc.posting_time),
            "total": float(doc.grand_total),
            "tax": float(doc.total_taxes_and_charges or 0),
            "currency": doc.currency
        }
        
        # بيانات السيارة إذا وُجدت
        if hasattr(doc, 'chassis_no') and doc.chassis_no:
            qr_data.update({
                "chassis_no": doc.chassis_no,
                "engine_no": getattr(doc, 'engine_no', ''),
                "car_model": getattr(doc, 'car_model', ''),
                "car_color": getattr(doc, 'car_color', ''),
                "manufacture_year": getattr(doc, 'manufacture_year', '')
            })
        
        # تحويل إلى JSON
        qr_json = json.dumps(qr_data, ensure_ascii=False)
        
        # إنشاء QR كود
        qr = qrcode.QRCode(version=1, box_size=8, border=2)
        qr.add_data(qr_json)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    except:
        return None

# إضافة QR كود للفاتورة
if doc.doctype == "Sales Invoice" and doc.docstatus == 1:
    doc.qr_code = generate_invoice_qr_base64(doc)
'''

    # التحقق من وجود Server Script
    if not frappe.db.exists("Server Script", "Generate Invoice QR Code"):
        server_script = frappe.get_doc({
            "doctype": "Server Script",
            "name": "Generate Invoice QR Code",
            "script_type": "DocType Event",
            "reference_doctype": "Sales Invoice",
            "event_frequency": "After Save",
            "script": script_code,
            "enabled": 1
        })
        server_script.insert(ignore_permissions=True)
        frappe.db.commit()
        print("✅ تم إنشاء Server Script لتوليد QR كود")
    else:
        print("ℹ️ Server Script موجود مسبقاً")

if __name__ == "__main__":
    create_qr_server_script()