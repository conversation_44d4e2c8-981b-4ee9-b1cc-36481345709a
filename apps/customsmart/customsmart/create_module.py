#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء Module للتطبيق
Create Module for the app
"""

import frappe

def create_custom_smart_module():
    """إنشاء Module Custom Smart"""
    
    print("📦 إنشاء Module: Custom Smart...")
    
    if not frappe.db.exists("Module Def", "Custom Smart"):
        module = frappe.get_doc({
            "doctype": "Module Def",
            "module_name": "Custom Smart",
            "app_name": "customsmart",
            "description": "Car Showroom Management System"
        })
        module.insert(ignore_permissions=True)
        frappe.db.commit()
        print("✅ تم إنشاء Module: Custom Smart")
    else:
        print("⚠️ Module Custom Smart موجود بالفعل")

if __name__ == "__main__":
    create_custom_smart_module()