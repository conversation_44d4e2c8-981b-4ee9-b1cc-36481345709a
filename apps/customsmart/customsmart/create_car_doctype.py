#!/usr/bin/env python3
"""إنشاء DocType Car Record مع البيانات التجريبية"""

import frappe

def create_car_record_doctype():
    """إنشاء DocType Car Record"""
    
    print("🚗 إنشاء DocType: Car Record...")
    
    if frappe.db.exists("DocType", "Car Record"):
        print("⚠️ DocType Car Record موجود بالفعל")
        return
    
    # إنشاء DocType
    doctype_doc = frappe.get_doc({
        "doctype": "DocType",
        "name": "Car Record",
        "module": "Custom",
        "custom": 1,
        "autoname": "naming_series:",
        "title_field": "car_name",
        "search_fields": "chassis_no,engine_no,car_color,car_model",
        "sort_field": "creation",
        "sort_order": "DESC",
        "fields": [
            {
                "fieldname": "naming_series",
                "label": "Series",
                "fieldtype": "Select",
                "options": "CAR-REC-.YYYY.-",
                "default": "CAR-REC-.YYYY.-",
                "reqd": 1,
                "read_only": 0
            },
            {
                "fieldname": "basic_info_section",
                "label": "Basic Information",
                "fieldtype": "Section Break"
            },
            {
                "fieldname": "car_name",
                "label": "Car Name",
                "fieldtype": "Data",
                "reqd": 1,
                "in_list_view": 1,
                "bold": 1
            },
            {
                "fieldname": "car_model",
                "label": "Car Model", 
                "fieldtype": "Data",
                "reqd": 1,
                "in_list_view": 1
            },
            {
                "fieldname": "column_break_1",
                "fieldtype": "Column Break"
            },
            {
                "fieldname": "car_color",
                "label": "Car Color",
                "fieldtype": "Data",
                "reqd": 1,
                "in_list_view": 1
            },
            {
                "fieldname": "manufacture_year",
                "label": "Manufacture Year",
                "fieldtype": "Int",
                "reqd": 1
            },
            {
                "fieldname": "technical_section",
                "label": "Technical Details",
                "fieldtype": "Section Break"
            },
            {
                "fieldname": "chassis_no",
                "label": "Chassis Number",
                "fieldtype": "Data",
                "reqd": 1,
                "unique": 1,
                "in_list_view": 1,
                "bold": 1
            },
            {
                "fieldname": "engine_no",
                "label": "Engine Number",
                "fieldtype": "Data", 
                "reqd": 1,
                "unique": 1,
                "in_list_view": 1
            },
            {
                "fieldname": "column_break_2",
                "fieldtype": "Column Break"
            },
            {
                "fieldname": "item_code",
                "label": "Item Code",
                "fieldtype": "Link",
                "options": "Item",
                "reqd": 1
            },
            {
                "fieldname": "selling_price",
                "label": "Selling Price",
                "fieldtype": "Currency",
                "reqd": 1
            },
            {
                "fieldname": "status_section",
                "label": "Status",
                "fieldtype": "Section Break"
            },
            {
                "fieldname": "status",
                "label": "Status",
                "fieldtype": "Select",
                "options": "Available\nSold\nReserved\nMaintenance\nDamaged",
                "default": "Available",
                "reqd": 1,
                "in_list_view": 1,
                "bold": 1
            },
            {
                "fieldname": "purchase_date",
                "label": "Purchase Date",
                "fieldtype": "Date",
                "default": "Today"
            },
            {
                "fieldname": "column_break_3",
                "fieldtype": "Column Break"
            },
            {
                "fieldname": "notes",
                "label": "Notes",
                "fieldtype": "Text"
            }
        ],
        "permissions": [
            {
                "role": "System Manager",
                "read": 1,
                "write": 1,
                "create": 1,
                "delete": 1
            },
            {
                "role": "Sales User",
                "read": 1,
                "write": 1,
                "create": 1,
                "delete": 0
            },
            {
                "role": "Sales Manager",
                "read": 1,
                "write": 1,
                "create": 1,
                "delete": 1
            }
        ]
    })
    
    doctype_doc.insert(ignore_permissions=True)
    frappe.db.commit()
    print("✅ تم إنشاء DocType: Car Record بنجاح")

def create_sample_car_records():
    """إنشاء سجلات السيارات التجريبية"""
    
    print("📊 إنشاء البيانات التجريبية...")
    
    # التأكد من وجود الصنف
    if not frappe.db.exists("Item", "بيع السيارات"):
        item = frappe.get_doc({
            "doctype": "Item",
            "item_code": "بيع السيارات",
            "item_name": "بيع السيارات",
            "item_group": "السيارات",
            "stock_uom": "Nos",
            "is_stock_item": 1,
            "valuation_rate": 0,
            "standard_rate": 4000000
        })
        try:
            item.insert(ignore_permissions=True)
            print("✅ تم إنشاء Item: بيع السيارات")
        except:
            print("⚠️ Item بيع السيارات موجود بالفعل أو لا يمكن إنشاؤه")
    
    # إنشاء سجلات السيارات
    sample_cars = [
        {
            "car_name": "سيارة تجريبية - أحمر 2023",
            "chassis_no": "11223",
            "engine_no": "11223",
            "car_color": "احمر",
            "car_model": "2023",
            "manufacture_year": 2023,
            "item_code": "بيع السيارات",
            "selling_price": 4000000,
            "status": "Available",
            "notes": "سيارة تجريبية للاختبار"
        },
        {
            "car_name": "Toyota Camry أبيض 2024",
            "chassis_no": "TOY-CAMRY-2024-001",
            "engine_no": "TOY-ENG-2024-001",
            "car_color": "أبيض",
            "car_model": "Camry 2024",
            "manufacture_year": 2024,
            "item_code": "بيع السيارات",
            "selling_price": 5500000,
            "status": "Available",
            "notes": "تويوتا كامري أبيض فاخر"
        },
        {
            "car_name": "BMW X5 أسود 2024",
            "chassis_no": "BMW-X5-2024-001",
            "engine_no": "BMW-ENG-2024-001",
            "car_color": "أسود",
            "car_model": "X5 2024", 
            "manufacture_year": 2024,
            "item_code": "بيع السيارات",
            "selling_price": 8000000,
            "status": "Available",
            "notes": "BMW X5 أسود فاخر مع جميع الخيارات"
        },
        {
            "car_name": "Honda Civic فضي 2024",
            "chassis_no": "HONDA-CIVIC-2024-001",
            "engine_no": "HONDA-ENG-2024-001",
            "car_color": "فضي",
            "car_model": "Civic 2024",
            "manufacture_year": 2024,
            "item_code": "بيع السيارات",
            "selling_price": 3800000,
            "status": "Available", 
            "notes": "هوندا سيفيك فضي اقتصادي"
        },
        {
            "car_name": "Mercedes C200 أزرق 2024",
            "chassis_no": "MERC-C200-2024-001",
            "engine_no": "MERC-ENG-2024-001",
            "car_color": "أزرق",
            "car_model": "C200 2024",
            "manufacture_year": 2024,
            "item_code": "بيع السيارات",
            "selling_price": 7200000,
            "status": "Reserved",
            "notes": "مرسيدس C200 أزرق محجوز للعميل أحمد"
        }
    ]
    
    created_count = 0
    for car_data in sample_cars:
        if not frappe.db.exists("Car Record", {"chassis_no": car_data["chassis_no"]}):
            try:
                car = frappe.get_doc({
                    "doctype": "Car Record",
                    **car_data
                })
                car.insert(ignore_permissions=True)
                created_count += 1
                print(f"✅ تم إنشاء سيارة: {car_data['car_name']}")
            except Exception as e:
                print(f"⚠️ خطأ في إنشاء {car_data['car_name']}: {str(e)}")
        else:
            print(f"⚠️ السيارة موجودة بالفعل: {car_data['car_name']}")
    
    frappe.db.commit()
    print(f"✅ تم إنشاء {created_count} سجل سيارة جديد")

def main():
    """الوظيفة الرئيسية"""
    
    print("=" * 60)
    print("🚀 بدء إنشاء DocType Car Record والبيانات التجريبية")
    print("🚀 Starting Car Record DocType and Sample Data Creation")
    print("=" * 60)
    
    try:
        # إنشاء DocType
        create_car_record_doctype()
        
        # إنشاء البيانات التجريبية
        create_sample_car_records()
        
        print("=" * 60)
        print("✅ تم إنشاء كل شيء بنجاح!")
        print("✅ Everything created successfully!")
        print("=" * 60)
        print("\n📋 ما تم إنشاؤه:")
        print("1. ✅ DocType: Car Record")
        print("2. ✅ 5 سجلات سيارات تجريبية")
        print("3. ✅ صنف: بيع السيارات")
        print("\n🎯 يمكنك الآن:")
        print("- إنشاء فواتير مبيعات بدون أخطاء")
        print("- ربط السيارات تلقائياً")
        print("- استخدام جميع الميزات المخصصة")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ خطأ في الإنشاء: {str(e)}")
        print(f"❌ Creation Error: {str(e)}")
        print("=" * 60)
        frappe.db.rollback()
        return False

if __name__ == "__main__":
    main()