#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة مخزون السيارات
Car Inventory Management System
"""

import frappe
from frappe import _

def get_item_strategy_recommendation():
    """توصيات استراتيجية الأصناف"""
    
    return {
        "recommended_strategy": "single_item_multiple_cars",
        "reasons": [
            "سهولة إدارة المخزون",
            "تبسيط عملية الفوترة", 
            "مرونة في التسعير",
            "سهولة التقارير"
        ],
        "implementation": "استخدام صنف واحد مع ربط Car Record"
    }

def create_car_sales_item_strategy():
    """إنشاء استراتيجية بيع السيارات"""
    
    print("📊 إنشاء استراتيجية إدارة أصناف السيارات...")
    
    # الصنف الأساسي الموحد
    main_item = {
        "item_code": "CAR-SALE",
        "item_name": "بيع سيارة",
        "item_name_english": "Car Sale",
        "item_group": "السيارات",
        "stock_uom": "Nos",
        "is_stock_item": 0,  # خدمة وليس مخزون
        "is_sales_item": 1,
        "is_purchase_item": 0,
        "valuation_method": "Serial No",
        "has_serial_no": 1,  # كل سيارة لها رقم تسلسلي
        "serial_no_series": "CAR-SERIAL-.####",
        "description": "صنف موحد لبيع جميع أنواع السيارات - يتم تحديد التفاصيل من خلال Car Record"
    }
    
    return main_item

def get_car_inventory_status(chassis_no=None):
    """الحصول على حالة مخزون السيارة"""
    
    if chassis_no:
        # حالة سيارة محددة
        car = frappe.get_doc("Car Record", {"chassis_no": chassis_no})
        return {
            "chassis_no": car.chassis_no,
            "car_name": car.car_name,
            "status": car.status,
            "available": 1 if car.status == "Available" else 0,
            "location": car.get("location", "المعرض الرئيسي")
        }
    else:
        # حالة جميع السيارات
        cars = frappe.get_all("Car Record", 
            fields=["chassis_no", "car_name", "status", "car_color", "car_model"],
            filters={"status": ["in", ["Available", "Reserved", "Sold"]]}
        )
        
        summary = {
            "total_cars": len(cars),
            "available": len([c for c in cars if c.status == "Available"]),
            "reserved": len([c for c in cars if c.status == "Reserved"]),
            "sold": len([c for c in cars if c.status == "Sold"]),
            "cars_detail": cars
        }
        
        return summary

def get_available_cars_for_sale():
    """الحصول على السيارات المتاحة للبيع"""
    
    available_cars = frappe.get_all("Car Record",
        fields=[
            "name", "chassis_no", "car_name", "car_color", 
            "car_model", "manufacture_year", "selling_price"
        ],
        filters={"status": "Available"},
        order_by="creation desc"
    )
    
    return available_cars

def check_car_availability(chassis_no):
    """فحص توفر السيارة"""
    
    if not frappe.db.exists("Car Record", {"chassis_no": chassis_no}):
        return {
            "available": False,
            "message": f"السيارة برقم شاسيه {chassis_no} غير موجودة",
            "status": "not_found"
        }
    
    car = frappe.get_doc("Car Record", {"chassis_no": chassis_no})
    
    if car.status == "Available":
        return {
            "available": True,
            "message": f"السيارة {car.car_name} متاحة للبيع",
            "status": "available",
            "car_details": {
                "name": car.car_name,
                "color": car.car_color,
                "model": car.car_model,
                "price": car.selling_price
            }
        }
    elif car.status == "Sold":
        return {
            "available": False,
            "message": f"السيارة {car.car_name} مباعة بالفعل",
            "status": "sold"
        }
    elif car.status == "Reserved":
        return {
            "available": False,
            "message": f"السيارة {car.car_name} محجوزة",
            "status": "reserved"
        }
    else:
        return {
            "available": False,
            "message": f"السيارة {car.car_name} غير متاحة - الحالة: {car.status}",
            "status": car.status.lower()
        }

def create_car_inventory_report():
    """إنشاء تقرير مخزون السيارات"""
    
    print("📊 إنشاء تقرير مخزون السيارات...")
    
    # الحصول على جميع السيارات
    cars = frappe.get_all("Car Record",
        fields=[
            "chassis_no", "car_name", "car_color", "car_model", 
            "manufacture_year", "selling_price", "status", "creation"
        ],
        order_by="creation desc"
    )
    
    # تجميع البيانات
    report_data = {
        "report_date": frappe.utils.today(),
        "total_cars": len(cars),
        "available_cars": len([c for c in cars if c.status == "Available"]),
        "sold_cars": len([c for c in cars if c.status == "Sold"]),
        "reserved_cars": len([c for c in cars if c.status == "Reserved"]),
        "cars_by_color": {},
        "cars_by_model": {},
        "cars_by_year": {},
        "total_value": 0,
        "available_value": 0,
        "cars_detail": cars
    }
    
    # تحليل البيانات
    for car in cars:
        # تجميع حسب اللون
        color = car.car_color
        if color not in report_data["cars_by_color"]:
            report_data["cars_by_color"][color] = 0
        report_data["cars_by_color"][color] += 1
        
        # تجميع حسب الموديل
        model = car.car_model
        if model not in report_data["cars_by_model"]:
            report_data["cars_by_model"][model] = 0
        report_data["cars_by_model"][model] += 1
        
        # تجميع حسب السنة
        year = car.manufacture_year
        if year not in report_data["cars_by_year"]:
            report_data["cars_by_year"][year] = 0
        report_data["cars_by_year"][year] += 1
        
        # حساب القيمة الإجمالية
        price = car.selling_price or 0
        report_data["total_value"] += price
        
        if car.status == "Available":
            report_data["available_value"] += price
    
    return report_data

def get_low_stock_alerts():
    """تنبيهات انخفاض المخزون"""
    
    # في حالة السيارات، "انخفاض المخزون" يعني قلة السيارات المتاحة
    available_cars = frappe.get_all("Car Record",
        filters={"status": "Available"},
        fields=["car_model", "car_color"]
    )
    
    # تجميع حسب الموديل واللون
    combinations = {}
    for car in available_cars:
        key = f"{car.car_model} - {car.car_color}"
        if key not in combinations:
            combinations[key] = 0
        combinations[key] += 1
    
    # البحث عن المجموعات ذات العدد المنخفض
    low_stock = []
    for combination, count in combinations.items():
        if count <= 1:  # سيارة واحدة أو أقل
            low_stock.append({
                "combination": combination,
                "count": count,
                "alert_level": "critical" if count == 0 else "warning"
            })
    
    return low_stock

# API Functions للاستخدام في JavaScript
@frappe.whitelist()
def get_car_inventory_summary():
    """API للحصول على ملخص المخزون"""
    return get_car_inventory_status()

@frappe.whitelist()
def check_car_availability_api(chassis_no):
    """API لفحص توفر السيارة"""
    return check_car_availability(chassis_no)

@frappe.whitelist()
def get_available_cars_api():
    """API للحصول على السيارات المتاحة"""
    return get_available_cars_for_sale()

if __name__ == "__main__":
    # تشغيل التقرير
    report = create_car_inventory_report()
    print(f"📊 تقرير المخزون:")
    print(f"إجمالي السيارات: {report['total_cars']}")
    print(f"المتاح للبيع: {report['available_cars']}")
    print(f"المباع: {report['sold_cars']}")
    print(f"المحجوز: {report['reserved_cars']}")
    print(f"القيمة الإجمالية: {report['total_value']:,.0f}")
    print(f"قيمة المتاح: {report['available_value']:,.0f}")