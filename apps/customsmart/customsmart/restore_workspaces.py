# -*- coding: utf-8 -*-

import frappe

def restore_default_workspaces():
    """استعادة workspaces الافتراضية"""
    
    try:
        print("🔧 استعادة Workspaces الافتراضية...")
        
        # حذف cache
        frappe.clear_cache()
        
        # إعادة بناء workspace
        from frappe.utils.install import create_workspace
        
        # قائمة workspaces الأساسية لـ ERPNext
        default_workspaces = [
            "Home",
            "Accounting", 
            "Selling",
            "Buying",
            "Stock",
            "Assets",
            "Projects",
            "CRM",
            "Support",
            "HR",
            "Manufacturing",
            "Website",
            "Tools",
            "Settings"
        ]
        
        # إعادة تحميل workspaces
        for workspace in default_workspaces:
            try:
                if frappe.db.exists("Workspace", workspace):
                    print(f"✅ Workspace '{workspace}' موجود")
                else:
                    print(f"⚠️ Workspace '{workspace}' غير موجود")
            except Exception as e:
                print(f"❌ خطأ في فحص workspace '{workspace}': {str(e)}")
        
        # إعادة تحميل workspace settings
        frappe.reload_doc("core", "doctype", "workspace")
        frappe.reload_doc("core", "doctype", "workspace_link")
        frappe.reload_doc("core", "doctype", "workspace_chart")
        
        # تنظيف permissions
        frappe.db.sql("""
            UPDATE `tabWorkspace` 
            SET public = 1 
            WHERE name IN ('Home', 'Accounting', 'Selling', 'Buying', 'Stock')
        """)
        
        frappe.db.commit()
        print("✅ تم استعادة Workspaces بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعادة Workspaces: {str(e)}")
        frappe.log_error(f"Workspace Restore Error: {str(e)}")
        return False

def fix_workspace_permissions():
    """إصلاح صلاحيات Workspaces"""
    
    try:
        print("🔧 إصلاح صلاحيات Workspaces...")
        
        # تحديث جميع workspaces لتكون عامة
        frappe.db.sql("""
            UPDATE `tabWorkspace` 
            SET public = 1, 
                for_user = '',
                parent_page = ''
            WHERE 1=1
        """)
        
        # حذف roles غير الضرورية
        frappe.db.sql("""
            DELETE FROM `tabWorkspace Link`
            WHERE parent IN (
                SELECT name FROM `tabWorkspace` 
                WHERE name IN ('Home', 'Accounting', 'Selling', 'Buying', 'Stock')
            )
        """)
        
        frappe.db.commit()
        print("✅ تم إصلاح صلاحيات Workspaces")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح صلاحيات: {str(e)}")
        return False

def reset_sidebar():
    """إعادة تعيين الشريط الجانبي"""
    
    try:
        print("🔧 إعادة تعيين الشريط الجانبي...")
        
        # حذف تخصيصات المستخدمين للـ sidebar
        frappe.db.sql("DELETE FROM `tabUser Settings` WHERE doctype = 'Workspace'")
        
        # إعادة تحديد ترتيب workspaces
        workspace_order = [
            "Home", "Accounting", "Selling", "Buying", "Stock", 
            "Assets", "Projects", "CRM", "Support", "HR", 
            "Manufacturing", "Website", "Tools", "Settings"
        ]
        
        for idx, workspace in enumerate(workspace_order):
            if frappe.db.exists("Workspace", workspace):
                frappe.db.sql("""
                    UPDATE `tabWorkspace` 
                    SET sequence_id = %s 
                    WHERE name = %s
                """, (idx, workspace))
        
        frappe.db.commit()
        print("✅ تم إعادة تعيين الشريط الجانبي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين الشريط الجانبي: {str(e)}")
        return False

def complete_workspace_fix():
    """حل شامل لمشكلة Workspaces"""
    
    print("🚀 بدء الحل الشامل لمشكلة Workspaces...")
    
    # الخطوة 1: استعادة workspaces
    restore_success = restore_default_workspaces()
    
    # الخطوة 2: إصلاح الصلاحيات
    permissions_success = fix_workspace_permissions()
    
    # الخطوة 3: إعادة تعيين الشريط الجانبي
    sidebar_success = reset_sidebar()
    
    # الخطوة 4: مسح cache
    frappe.clear_cache()
    
    if restore_success and permissions_success and sidebar_success:
        print("✅ تم حل مشكلة Workspaces بنجاح!")
        print("🔄 يرجى إعادة تحميل الصفحة لرؤية النتائج")
        return True
    else:
        print("❌ فشل في حل بعض جوانب المشكلة")
        return False

if __name__ == "__main__":
    complete_workspace_fix()