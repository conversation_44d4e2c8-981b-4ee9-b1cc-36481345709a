[{"docstatus": 0, "doctype": "<PERSON><PERSON>", "dt": "Sales Invoice", "enabled": 1, "modified": "2025-06-27 18:17:08.420428", "module": null, "name": "Sales Invoice Car Showroom", "script": "\n// Car Showroom Sales Invoice Customizations\nfrappe.ui.form.on('Sales Invoice', {\n    refresh: function(frm) {\n        // Add custom print button\n        if (frm.doc.docstatus === 1) {\n            frm.add_custom_button(__('🖨️ Print Car Invoice'), function() {\n                frappe.utils.print(\n                    frm.doc.doctype,\n                    frm.doc.name,\n                    'Car Showroom Tax Invoice'\n                );\n            }, __('Print'));\n        }\n        \n        // Improve field display\n        frm.toggle_display('update_stock', false);\n        frm.toggle_display('is_pos', false);\n        \n        // Add car invoice dashboard message\n        if (!frm.doc.__islocal && frm.doc.chassis_no) {\n            frm.dashboard.set_headline(\n                `🚗 ${__('Car Sale Invoice')} - ${__('Chassis No')}: ${frm.doc.chassis_no}`\n            );\n        }\n    },\n    \n    customer: function(frm) {\n        if (frm.doc.customer) {\n            frappe.call({\n                method: 'frappe.client.get',\n                args: {\n                    doctype: 'Customer',\n                    name: frm.doc.customer\n                },\n                callback: function(r) {\n                    if (r.message) {\n                        frm.set_value('customer_name', r.message.customer_name);\n                    }\n                }\n            });\n        }\n    }\n});\n\nfrappe.ui.form.on('Sales Invoice Item', {\n    item_code: function(frm, cdt, cdn) {\n        var row = locals[cdt][cdn];\n        if (row.item_code) {\n            frappe.call({\n                method: 'frappe.client.get_list',\n                args: {\n                    doctype: 'Car Record',\n                    filters: {\n                        'item_code': row.item_code,\n                        'status': 'Available'\n                    },\n                    fields: ['name', 'chassis_no', 'engine_no', 'car_color', 'car_model', 'manufacture_year', 'selling_price']\n                },\n                callback: function(r) {\n                    if (r.message && r.message.length > 0) {\n                        var car = r.message[0];\n                        frappe.model.set_value(cdt, cdn, 'car_record', car.name);\n                        frappe.model.set_value(cdt, cdn, 'chassis_number', car.chassis_no);\n                        frappe.model.set_value(cdt, cdn, 'engine_number', car.engine_no);\n                        frappe.model.set_value(cdt, cdn, 'car_color_item', car.car_color);\n                        frappe.model.set_value(cdt, cdn, 'rate', car.selling_price);\n                        \n                        // Update car details in invoice header\n                        frm.set_value('chassis_no', car.chassis_no);\n                        frm.set_value('engine_no', car.engine_no);\n                        frm.set_value('car_color', car.car_color);\n                        frm.set_value('car_model', car.car_model);\n                        frm.set_value('manufacture_year', car.manufacture_year);\n                        \n                        frappe.show_alert({\n                            message: `${__('Car linked successfully')}: ${car.chassis_no}`,\n                            indicator: 'green'\n                        });\n                    } else {\n                        frappe.show_alert({\n                            message: __('Car not found for this item'),\n                            indicator: 'orange'\n                        });\n                    }\n                }\n            });\n        }\n    }\n});\n", "view": "Form"}]