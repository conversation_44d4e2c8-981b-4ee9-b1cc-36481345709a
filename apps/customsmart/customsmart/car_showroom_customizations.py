# -*- coding: utf-8 -*-
# Copyright (c) 2024, moneer and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields

def add_car_showroom_fields():
    """إضافة الحقول المخصصة لمعرض السيارات"""
    
    # إنشاء الحقول المخصصة لفاتورة المبيعات
    car_showroom_fields = {
        "Sales Invoice": [
            {
                "fieldname": "car_details_section",
                "label": "Car Details",
                "fieldtype": "Section Break",
                "insert_after": "customer_address",
                "collapsible": 1
            },
            {
                "fieldname": "chassis_no",
                "label": "Chassis Number",
                "fieldtype": "Data",
                "insert_after": "car_details_section",
                "translatable": 0
            },
            {
                "fieldname": "engine_no", 
                "label": "Engine Number",
                "fieldtype": "Data",
                "insert_after": "chassis_no",
                "translatable": 0
            },
            {
                "fieldname": "col_break_car_1",
                "fieldtype": "Column Break",
                "insert_after": "engine_no"
            },
            {
                "fieldname": "car_color",
                "label": "Car Color", 
                "fieldtype": "Data",
                "insert_after": "col_break_car_1",
                "translatable": 0
            },
            {
                "fieldname": "car_model",
                "label": "Car Model",
                "fieldtype": "Data", 
                "insert_after": "car_color",
                "translatable": 0
            },
            {
                "fieldname": "col_break_car_2",
                "fieldtype": "Column Break",
                "insert_after": "car_model"
            },
            {
                "fieldname": "manufacture_year",
                "label": "Manufacture Year",
                "fieldtype": "Int",
                "insert_after": "col_break_car_2"
            },
            {
                "fieldname": "showroom_name",
                "label": "Showroom Name",
                "fieldtype": "Data",
                "insert_after": "manufacture_year",
                "translatable": 0
            }
        ],
        "Sales Invoice Item": [
            {
                "fieldname": "car_record",
                "label": "Car Record",
                "fieldtype": "Link",
                "options": "Car Record",
                "insert_after": "item_code",
                "read_only": 1
            },
            {
                "fieldname": "chassis_number",
                "label": "Chassis No",
                "fieldtype": "Data",
                "insert_after": "car_record",
                "read_only": 1,
                "fetch_from": "car_record.chassis_no"
            },
            {
                "fieldname": "engine_number",
                "label": "Engine No", 
                "fieldtype": "Data",
                "insert_after": "chassis_number",
                "read_only": 1,
                "fetch_from": "car_record.engine_no"
            },
            {
                "fieldname": "car_color_item",
                "label": "Car Color",
                "fieldtype": "Data",
                "insert_after": "engine_number",
                "read_only": 1,
                "fetch_from": "car_record.car_color",
                "in_list_view": 1
            }
        ]
    }
    
    create_custom_fields(car_showroom_fields, update=True)
    frappe.db.commit()
    print("✅ تم إنشاء الحقول المخصصة لمعرض السيارات بنجاح")

def create_car_showroom_print_format():
    """إنشاء نموذج طباعة مخصص لفاتورة معرض السيارات"""
    
    print_format_html = '''
    <div class="print-format">
        <style>
            .print-format {
                font-family: Arial, sans-serif;
                margin: 20px;
                direction: rtl;
                text-align: right;
            }
            .header {
                text-align: center;
                border-bottom: 3px solid #1f4e79;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            .company-name {
                font-size: 28px;
                font-weight: bold;
                color: #1f4e79;
                margin-bottom: 10px;
            }
            .invoice-title {
                font-size: 24px;
                color: #d32f2f;
                font-weight: bold;
                margin: 15px 0;
            }
            .invoice-details {
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
            }
            .customer-details, .invoice-info {
                width: 48%;
            }
            .section-title {
                font-size: 16px;
                font-weight: bold;
                color: #1f4e79;
                border-bottom: 2px solid #1f4e79;
                padding-bottom: 5px;
                margin-bottom: 10px;
            }
            .car-details {
                background-color: #e3f2fd;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                border: 2px solid #1f4e79;
            }
            .car-info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-top: 15px;
            }
            .car-info-item {
                display: flex;
                justify-content: space-between;
                padding: 8px;
                background-color: white;
                border-radius: 5px;
                border: 1px solid #ddd;
            }
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
                font-size: 14px;
            }
            .items-table th, .items-table td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            .items-table th {
                background-color: #1f4e79;
                color: white;
                font-weight: bold;
            }
            .items-table tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .totals-section {
                float: left;
                width: 300px;
                margin-top: 20px;
            }
            .total-row {
                display: flex;
                justify-content: space-between;
                padding: 8px;
                border-bottom: 1px solid #ddd;
            }
            .grand-total {
                background-color: #1f4e79;
                color: white;
                font-weight: bold;
                font-size: 16px;
            }
            .footer {
                margin-top: 50px;
                border-top: 2px solid #1f4e79;
                padding-top: 20px;
                text-align: center;
            }
            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 40px;
            }
            .signature-box {
                width: 200px;
                text-align: center;
                border-top: 1px solid #333;
                padding-top: 10px;
            }
            .tax-info {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            @media print {
                .print-format {
                    margin: 0;
                }
            }
        </style>
        
        <div class="header">
            <div class="company-name">{{ doc.company }}</div>
            <div style="font-size: 14px; color: #666;">
                معرض السيارات | Car Showroom
            </div>
            <div class="invoice-title">
                فاتورة ضريبية | Tax Invoice
            </div>
            <div style="font-size: 12px; margin-top: 10px;">
                رقم الفاتورة: {{ doc.name }} | Invoice No: {{ doc.name }}
            </div>
        </div>

        <div class="invoice-details">
            <div class="customer-details">
                <div class="section-title">بيانات العميل | Customer Details</div>
                <div><strong>الاسم | Name:</strong> {{ doc.customer_name }}</div>
                <div><strong>العنوان | Address:</strong> {{ doc.customer_address or "غير محدد" }}</div>
                <div><strong>الهاتف | Phone:</strong> {{ doc.contact_mobile or "غير محدد" }}</div>
                {% if doc.tax_id %}
                <div><strong>الرقم الضريبي | Tax ID:</strong> {{ doc.tax_id }}</div>
                {% endif %}
            </div>
            <div class="invoice-info">
                <div class="section-title">بيانات الفاتورة | Invoice Info</div>
                <div><strong>التاريخ | Date:</strong> {{ doc.posting_date }}</div>
                <div><strong>وقت الإصدار | Time:</strong> {{ doc.posting_time }}</div>
                <div><strong>حالة الدفع | Payment Status:</strong> {{ doc.status }}</div>
                <div><strong>طريقة الدفع | Payment Method:</strong> {{ doc.mode_of_payment or "نقدي" }}</div>
            </div>
        </div>

        {% if doc.chassis_no or doc.engine_no or doc.car_color or doc.car_model or doc.manufacture_year %}
        <div class="car-details">
            <div class="section-title">تفاصيل السيارة | Car Details</div>
            <div class="car-info-grid">
                {% if doc.chassis_no %}
                <div class="car-info-item">
                    <span><strong>رقم الهيكل:</strong></span>
                    <span>{{ doc.chassis_no }}</span>
                </div>
                {% endif %}
                {% if doc.engine_no %}
                <div class="car-info-item">
                    <span><strong>رقم المحرك:</strong></span>
                    <span>{{ doc.engine_no }}</span>
                </div>
                {% endif %}
                {% if doc.car_color %}
                <div class="car-info-item">
                    <span><strong>اللون:</strong></span>
                    <span>{{ doc.car_color }}</span>
                </div>
                {% endif %}
                {% if doc.car_model %}
                <div class="car-info-item">
                    <span><strong>الموديل:</strong></span>
                    <span>{{ doc.car_model }}</span>
                </div>
                {% endif %}
                {% if doc.manufacture_year %}
                <div class="car-info-item">
                    <span><strong>سنة الصنع:</strong></span>
                    <span>{{ doc.manufacture_year }}</span>
                </div>
                {% endif %}
                {% if doc.showroom_name %}
                <div class="car-info-item">
                    <span><strong>اسم المعرض:</strong></span>
                    <span>{{ doc.showroom_name }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <table class="items-table">
            <thead>
                <tr>
                    <th>م | #</th>
                    <th>الصنف | Item</th>
                    <th>الوصف | Description</th>
                    <th>الكمية | Qty</th>
                    <th>السعر | Rate</th>
                    <th>المبلغ | Amount</th>
                    {% if doc.items[0].chassis_number %}
                    <th>رقم الهيكل | Chassis</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in doc.items %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ item.item_code }}</td>
                    <td>{{ item.description or item.item_name }}</td>
                    <td>{{ item.qty }}</td>
                    <td>{{ "%.2f"|format(item.rate) }}</td>
                    <td>{{ "%.2f"|format(item.amount) }}</td>
                    {% if item.chassis_number %}
                    <td>{{ item.chassis_number }}</td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي | Subtotal:</span>
                <span>{{ "%.2f"|format(doc.net_total) }}</span>
            </div>
            {% if doc.total_taxes_and_charges %}
            <div class="total-row">
                <span>الضريبة | Tax:</span>
                <span>{{ "%.2f"|format(doc.total_taxes_and_charges) }}</span>
            </div>
            {% endif %}
            <div class="total-row grand-total">
                <span>المجموع الكلي | Grand Total:</span>
                <span>{{ "%.2f"|format(doc.grand_total) }}</span>
            </div>
        </div>

        <div style="clear: both;"></div>

        {% if doc.total_taxes_and_charges %}
        <div class="tax-info">
            <strong>معلومات الضريبة | Tax Information:</strong><br>
            هذه فاتورة ضريبية صادرة وفقاً لأنظمة الضريبة المطبقة
            <br>
            This is a tax invoice issued in accordance with applicable tax regulations
        </div>
        {% endif %}

        <div class="footer">
            <div style="margin-bottom: 20px;">
                <strong>شكراً لاختياركم معرضنا | Thank you for choosing our showroom</strong>
            </div>
            
            <div class="signature-section">
                <div class="signature-box">
                    <div>توقيع العميل</div>
                    <div>Customer Signature</div>
                </div>
                <div class="signature-box">
                    <div>توقيع المعرض</div>
                    <div>Showroom Signature</div>
                </div>
            </div>
            
            <div style="margin-top: 30px; font-size: 12px; color: #666;">
                تم إنشاء هذه الفاتورة بواسطة نظام ERP | Generated by ERP System
            </div>
        </div>
    </div>
    '''
    
    # إنشاء Print Format
    if not frappe.db.exists("Print Format", "Car Showroom Tax Invoice"):
        print_format = frappe.get_doc({
            "doctype": "Print Format",
            "name": "Car Showroom Tax Invoice",
            "doc_type": "Sales Invoice",
            "print_format_builder": 0,
            "standard": "No",
            "custom_format": 1,
            "html": print_format_html,
            "print_format_type": "Jinja",
            "font_size": 12,
            "show_section_headings": 1,
            "line_breaks": 1,
            "absolute_value": 0,
            "align_labels_right": 1,
            "raw_printing": 0,
            "default_print_language": "ar"
        })
        print_format.insert(ignore_permissions=True)
        frappe.db.commit()
        print("✅ تم إنشاء نموذج الطباعة المخصص بنجاح")
    else:
        print("⚠️  نموذج الطباعة موجود بالفعل")

def setup_car_showroom_hooks():
    """إعداد hooks الخاصة بمعرض السيارات"""
    
    # إضافة document events لـ Sales Invoice
    doc_events = {
        "Sales Invoice": {
            "validate": "customsmart.car_showroom_customizations.validate_car_invoice",
            "on_submit": "customsmart.car_showroom_customizations.update_car_status_on_submit"
        }
    }
    
    return doc_events

def validate_car_invoice(doc, method):
    """التحقق من صحة فاتورة السيارة"""
    
    # Check if this is a car sale invoice
    if not has_car_items(doc):
        return
    
    # Validate car records exist and are available
    for item in doc.items:
        if item.car_record:
            car_record = frappe.get_doc("Car Record", item.car_record)
            
            # Check if car is available
            if car_record.status != "Available":
                frappe.throw(f"Car {car_record.chassis_no} is not available for sale. Current status: {car_record.status}")
            
            # Update main invoice fields from car record
            if not doc.chassis_no:
                doc.chassis_no = car_record.chassis_no
            if not doc.engine_no:
                doc.engine_no = car_record.engine_no
            if not doc.car_color:
                doc.car_color = car_record.car_color
            if not doc.car_model:
                doc.car_model = car_record.car_model
            if not doc.manufacture_year:
                doc.manufacture_year = car_record.manufacture_year

def update_car_status_on_submit(doc, method):
    """تحديث حالة السيارة عند إرسال الفاتورة"""
    
    # Check if this is a car sale invoice
    if not has_car_items(doc):
        return
    
    # Update car status to "Sold"
    for item in doc.items:
        if item.car_record:
            car_record = frappe.get_doc("Car Record", item.car_record)
            car_record.status = "Sold"
            car_record.save(ignore_permissions=True)
            
            frappe.msgprint(f"Car status updated to 'Sold' for chassis: {car_record.chassis_no}")

def has_car_items(doc):
    """فحص ما إذا كانت الفاتورة تحتوي على أصناف سيارات"""
    for item in doc.items:
        if item.car_record:
            return True
    return False

if __name__ == "__main__":
    # تشغيل التخصيصات
    add_car_showroom_fields()
    create_car_showroom_print_format()