{"add_total_row": 1, "columns": [{"fieldname": "car_name", "fieldtype": "Data", "label": "Car Name", "width": 200}, {"fieldname": "chassis_no", "fieldtype": "Data", "label": "<PERSON><PERSON><PERSON> Number", "width": 150}, {"fieldname": "engine_no", "fieldtype": "Data", "label": "Engine Number", "width": 150}, {"fieldname": "car_color", "fieldtype": "Data", "label": "Color", "width": 100}, {"fieldname": "car_model", "fieldtype": "Data", "label": "Model", "width": 120}, {"fieldname": "manufacture_year", "fieldtype": "Int", "label": "Year", "width": 80}, {"fieldname": "status", "fieldtype": "Data", "label": "Status", "width": 100}, {"fieldname": "purchase_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Purchase Price", "width": 120}, {"fieldname": "selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Selling <PERSON>", "width": 120}, {"fieldname": "purchase_date", "fieldtype": "Date", "label": "Purchase Date", "width": 100}], "creation": "2024-12-27 10:00:00.000000", "docstatus": 0, "doctype": "Report", "filters": [{"fieldname": "status", "fieldtype": "Select", "label": "Status", "mandatory": 0, "options": "\nAvailable\nSold\nReserved\nMaintenance\nDamaged", "wildcard_filter": 0}, {"fieldname": "car_model", "fieldtype": "Data", "label": "Car Model", "mandatory": 0, "wildcard_filter": 1}, {"fieldname": "car_color", "fieldtype": "Data", "label": "Car Color", "mandatory": 0, "wildcard_filter": 1}, {"fieldname": "manufacture_year", "fieldtype": "Int", "label": "Manufacture Year", "mandatory": 0, "wildcard_filter": 0}], "idx": 2, "is_standard": "Yes", "module": "customsmart", "name": "Car Inventory Report", "owner": "Administrator", "prepared_report": 0, "query": "SELECT \n    car_name,\n    chassis_no,\n    engine_no,\n    car_color,\n    car_model,\n    manufacture_year,\n    status,\n    purchase_price,\n    selling_price,\n    purchase_date\nFROM \n    `tabCar Record`\nWHERE \n    (IFNULL(%(status)s, '') = '' OR status = %(status)s)\n    AND (IFNULL(%(car_model)s, '') = '' OR car_model LIKE CONCAT('%%', %(car_model)s, '%%'))\n    AND (IFNULL(%(car_color)s, '') = '' OR car_color LIKE CONCAT('%%', %(car_color)s, '%%'))\n    AND (IFNULL(%(manufacture_year)s, 0) = 0 OR manufacture_year = %(manufacture_year)s)\nORDER BY \n    creation DESC", "ref_doctype": "Car Record", "report_name": "Car Inventory Report", "report_type": "Script Report", "roles": [{"role": "System Manager"}, {"role": "Sales Manager"}, {"role": "Sales User"}]}