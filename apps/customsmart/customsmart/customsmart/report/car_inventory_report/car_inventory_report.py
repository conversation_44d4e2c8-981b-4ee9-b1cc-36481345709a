# -*- coding: utf-8 -*-
# Copyright (c) 2024, CustomSmart and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe import _

def execute(filters=None):
    """Execute Car Inventory Report"""
    
    if not filters:
        filters = {}
    
    columns = get_columns()
    data = get_data(filters)
    
    return columns, data

def get_columns():
    """Get report columns"""
    return [
        {
            "fieldname": "car_name",
            "label": _("Car Name"),
            "fieldtype": "Data",
            "width": 200
        },
        {
            "fieldname": "chassis_no",
            "label": _("Chassis Number"),
            "fieldtype": "Data",
            "width": 150
        },
        {
            "fieldname": "engine_no",
            "label": _("Engine Number"),
            "fieldtype": "Data",
            "width": 150
        },
        {
            "fieldname": "car_color",
            "label": _("Color"),
            "fieldtype": "Data",
            "width": 100
        },
        {
            "fieldname": "car_model",
            "label": _("Model"),
            "fieldtype": "Data",
            "width": 120
        },
        {
            "fieldname": "manufacture_year",
            "label": _("Year"),
            "fieldtype": "Int",
            "width": 80
        },
        {
            "fieldname": "status",
            "label": _("Status"),
            "fieldtype": "Data",
            "width": 100
        },
        {
            "fieldname": "purchase_price",
            "label": _("Purchase Price"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "selling_price",
            "label": _("Selling Price"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "purchase_date",
            "label": _("Purchase Date"),
            "fieldtype": "Date",
            "width": 100
        }
    ]

def get_data(filters):
    """Get report data"""
    
    sql_query = """
        SELECT 
            car_name,
            chassis_no,
            engine_no,
            car_color,
            car_model,
            manufacture_year,
            status,
            purchase_price,
            selling_price,
            purchase_date
        FROM 
            `tabCar Record`
        WHERE 
            1=1
            {status_filter}
            {car_model_filter}
            {car_color_filter}
            {manufacture_year_filter}
        ORDER BY 
            creation DESC
    """
    
    # Build dynamic filters
    status_filter = ""
    if filters.get('status'):
        status_filter = "AND status = %(status)s"
    
    car_model_filter = ""
    if filters.get('car_model'):
        car_model_filter = "AND car_model LIKE %(car_model)s"
        filters['car_model'] = f"%{filters['car_model']}%"
    
    car_color_filter = ""
    if filters.get('car_color'):
        car_color_filter = "AND car_color LIKE %(car_color)s"
        filters['car_color'] = f"%{filters['car_color']}%"
    
    manufacture_year_filter = ""
    if filters.get('manufacture_year'):
        manufacture_year_filter = "AND manufacture_year = %(manufacture_year)s"
    
    # Format the query with filters
    formatted_query = sql_query.format(
        status_filter=status_filter,
        car_model_filter=car_model_filter,
        car_color_filter=car_color_filter,
        manufacture_year_filter=manufacture_year_filter
    )
    
    # Execute query
    data = frappe.db.sql(formatted_query, filters, as_dict=1)
    
    return data or []