{"add_total_row": 1, "columns": [{"fieldname": "brand", "fieldtype": "Data", "label": "Brand", "width": 120}, {"fieldname": "total_inventory", "fieldtype": "Int", "label": "Total Inventory", "width": 120}, {"fieldname": "sold_units", "fieldtype": "Int", "label": "Sold Units", "width": 100}, {"fieldname": "available_units", "fieldtype": "Int", "label": "Available Units", "width": 120}, {"fieldname": "total_revenue", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Revenue", "width": 150}, {"fieldname": "avg_selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Avg <PERSON>", "width": 150}, {"fieldname": "sell_through_rate", "fieldtype": "Percent", "label": "Sell Through %", "width": 120}, {"fieldname": "inventory_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Inventory Value", "width": 150}], "creation": "2024-12-27 10:00:00.000000", "docstatus": 0, "doctype": "Report", "filters": [{"fieldname": "from_date", "fieldtype": "Date", "label": "From Date", "mandatory": 0, "wildcard_filter": 0, "default": "Today"}, {"fieldname": "to_date", "fieldtype": "Date", "label": "To Date", "mandatory": 0, "wildcard_filter": 0, "default": "Today"}], "idx": 4, "is_standard": "Yes", "module": "customsmart", "name": "Brand Performance Report", "owner": "Administrator", "prepared_report": 0, "query": "SELECT \n    CASE \n        WHEN car_model LIKE '%Toyota%' OR car_model LIKE '%تويوتا%' THEN 'Toyota'\n        WHEN car_model LIKE '%BMW%' THEN 'BMW'\n        WHEN car_model LIKE '%Mercedes%' OR car_model LIKE '%مرسيدس%' THEN 'Mercedes'\n        WHEN car_model LIKE '%Honda%' OR car_model LIKE '%هوندا%' THEN 'Honda'\n        WHEN car_model LIKE '%Nissan%' OR car_model LIKE '%نيسان%' THEN 'Nissan'\n        WHEN car_model LIKE '%Hyundai%' OR car_model LIKE '%هيونداي%' THEN 'Hyundai'\n        WHEN car_model LIKE '%Kia%' OR car_model LIKE '%كيا%' THEN 'Kia'\n        WHEN car_model LIKE '%Audi%' OR car_model LIKE '%أودي%' THEN 'Audi'\n        WHEN car_model LIKE '%Ford%' OR car_model LIKE '%فورد%' THEN 'Ford'\n        ELSE 'Other'\n    END as brand,\n    COUNT(*) as total_inventory,\n    SUM(CASE WHEN status = 'Sold' THEN 1 ELSE 0 END) as sold_units,\n    SUM(CASE WHEN status = 'Available' THEN 1 ELSE 0 END) as available_units,\n    SUM(CASE WHEN status = 'Sold' THEN selling_price ELSE 0 END) as total_revenue,\n    CASE \n        WHEN SUM(CASE WHEN status = 'Sold' THEN 1 ELSE 0 END) > 0 \n        THEN SUM(CASE WHEN status = 'Sold' THEN selling_price ELSE 0 END) / SUM(CASE WHEN status = 'Sold' THEN 1 ELSE 0 END)\n        ELSE 0\n    END as avg_selling_price,\n    CASE \n        WHEN COUNT(*) > 0 \n        THEN (SUM(CASE WHEN status = 'Sold' THEN 1 ELSE 0 END) / COUNT(*)) * 100\n        ELSE 0\n    END as sell_through_rate,\n    SUM(CASE WHEN status = 'Available' THEN selling_price ELSE 0 END) as inventory_value\nFROM \n    `tabCar Record`\nWHERE \n    (IFNULL(%(from_date)s, '') = '' OR purchase_date >= %(from_date)s)\n    AND (IFNULL(%(to_date)s, '') = '' OR purchase_date <= %(to_date)s)\nGROUP BY \n    brand\nORDER BY \n    total_revenue DESC", "ref_doctype": "Car Record", "report_name": "Brand Performance Report", "report_type": "Query Report", "roles": [{"role": "System Manager"}, {"role": "Sales Manager"}, {"role": "Sales User"}]}