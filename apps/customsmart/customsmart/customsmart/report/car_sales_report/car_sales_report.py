# -*- coding: utf-8 -*-
# Copyright (c) 2024, CustomSmart and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe import _
from frappe.utils import today, add_days, formatdate

def execute(filters=None):
    """Execute Car Sales Report"""
    
    if not filters:
        filters = {}
    
    # Set default dates if not provided
    if not filters.get('from_date'):
        filters['from_date'] = add_days(today(), -30)  # Last 30 days
    
    if not filters.get('to_date'):
        filters['to_date'] = today()
    
    columns = get_columns()
    data = get_data(filters)
    
    return columns, data

def get_columns():
    """Get report columns"""
    return [
        {
            "fieldname": "car_name",
            "label": _("Car Name"),
            "fieldtype": "Data",
            "width": 200
        },
        {
            "fieldname": "chassis_no",
            "label": _("Chassis Number"),
            "fieldtype": "Data",
            "width": 150
        },
        {
            "fieldname": "customer",
            "label": _("Customer"),
            "fieldtype": "Link",
            "options": "Customer",
            "width": 150
        },
        {
            "fieldname": "posting_date",
            "label": _("Sale Date"),
            "fieldtype": "Date",
            "width": 100
        },
        {
            "fieldname": "selling_price",
            "label": _("Selling Price"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "purchase_price",
            "label": _("Purchase Price"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "profit",
            "label": _("Profit"),
            "fieldtype": "Currency",
            "width": 100
        },
        {
            "fieldname": "car_color",
            "label": _("Color"),
            "fieldtype": "Data",
            "width": 80
        },
        {
            "fieldname": "car_model",
            "label": _("Model"),
            "fieldtype": "Data",
            "width": 120
        }
    ]

def get_data(filters):
    """Get report data"""
    
    sql_query = """
        SELECT 
            cr.car_name,
            cr.chassis_no,
            si.customer,
            si.posting_date,
            cr.selling_price,
            cr.purchase_price,
            (cr.selling_price - cr.purchase_price) as profit,
            cr.car_color,
            cr.car_model
        FROM 
            `tabCar Record` cr
            INNER JOIN `tabSales Invoice Item` sii ON cr.name = sii.car_record
            INNER JOIN `tabSales Invoice` si ON sii.parent = si.name
        WHERE 
            si.docstatus = 1
            AND cr.status = 'Sold'
            AND si.posting_date >= %(from_date)s
            AND si.posting_date <= %(to_date)s
            {car_model_filter}
            {customer_filter}
        ORDER BY 
            si.posting_date DESC
    """
    
    # Build dynamic filters
    car_model_filter = ""
    if filters.get('car_model'):
        car_model_filter = "AND cr.car_model LIKE %(car_model)s"
        filters['car_model'] = f"%{filters['car_model']}%"
    
    customer_filter = ""
    if filters.get('customer'):
        customer_filter = "AND si.customer = %(customer)s"
    
    # Format the query with filters
    formatted_query = sql_query.format(
        car_model_filter=car_model_filter,
        customer_filter=customer_filter
    )
    
    # Execute query
    data = frappe.db.sql(formatted_query, filters, as_dict=1)
    
    return data or []