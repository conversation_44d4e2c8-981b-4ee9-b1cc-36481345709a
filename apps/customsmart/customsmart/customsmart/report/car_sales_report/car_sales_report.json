{"add_total_row": 1, "columns": [{"fieldname": "car_name", "fieldtype": "Data", "label": "Car Name", "width": 200}, {"fieldname": "chassis_no", "fieldtype": "Data", "label": "<PERSON><PERSON><PERSON> Number", "width": 150}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer", "width": 150}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Sale Date", "width": 100}, {"fieldname": "selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Selling <PERSON>", "width": 120}, {"fieldname": "purchase_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Purchase Price", "width": 120}, {"fieldname": "profit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Profit", "width": 100}, {"fieldname": "car_color", "fieldtype": "Data", "label": "Color", "width": 80}, {"fieldname": "car_model", "fieldtype": "Data", "label": "Model", "width": 120}], "creation": "2024-12-27 10:00:00.000000", "docstatus": 0, "doctype": "Report", "filters": [{"fieldname": "from_date", "fieldtype": "Date", "label": "From Date", "mandatory": 0, "wildcard_filter": 0, "default": "Today"}, {"fieldname": "to_date", "fieldtype": "Date", "label": "To Date", "mandatory": 0, "wildcard_filter": 0, "default": "Today"}, {"fieldname": "car_model", "fieldtype": "Data", "label": "Car Model", "mandatory": 0, "wildcard_filter": 1}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "mandatory": 0, "options": "Customer", "wildcard_filter": 0}], "idx": 1, "is_standard": "Yes", "module": "customsmart", "name": "Car Sales Report", "owner": "Administrator", "prepared_report": 0, "query": "SELECT \n    cr.car_name,\n    cr.chassis_no,\n    si.customer,\n    si.posting_date,\n    cr.selling_price,\n    cr.purchase_price,\n    (cr.selling_price - cr.purchase_price) as profit,\n    cr.car_color,\n    cr.car_model\nFROM \n    `tabCar Record` cr\n    INNER JOIN `tabSales Invoice Item` sii ON cr.name = sii.car_record\n    INNER JOIN `tabSales Invoice` si ON sii.parent = si.name\nWHERE \n    si.docstatus = 1\n    AND cr.status = 'Sold'\n    AND (IFNULL(%(from_date)s, '') = '' OR si.posting_date >= %(from_date)s)\n    AND (IFNULL(%(to_date)s, '') = '' OR si.posting_date <= %(to_date)s)\n    AND (IFNULL(%(car_model)s, '') = '' OR cr.car_model LIKE CONCAT('%%', %(car_model)s, '%%'))\n    AND (IFNULL(%(customer)s, '') = '' OR si.customer = %(customer)s)\nORDER BY \n    si.posting_date DESC", "ref_doctype": "Car Record", "report_name": "Car Sales Report", "report_type": "Script Report", "roles": [{"role": "System Manager"}, {"role": "Sales Manager"}, {"role": "Sales User"}]}