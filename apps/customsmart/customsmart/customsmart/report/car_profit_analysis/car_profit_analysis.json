{"add_total_row": 1, "columns": [{"fieldname": "car_model", "fieldtype": "Data", "label": "Car Model", "width": 150}, {"fieldname": "total_cars", "fieldtype": "Int", "label": "Total Cars", "width": 100}, {"fieldname": "cars_sold", "fieldtype": "Int", "label": "Cars Sold", "width": 100}, {"fieldname": "cars_available", "fieldtype": "Int", "label": "Available", "width": 100}, {"fieldname": "total_purchase_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Purchase Cost", "width": 150}, {"fieldname": "total_selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Revenue", "width": 150}, {"fieldname": "total_profit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Profit", "width": 120}, {"fieldname": "profit_margin", "fieldtype": "Percent", "label": "Profit Margin %", "width": 120}, {"fieldname": "avg_selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Avg <PERSON>", "width": 150}], "creation": "2024-12-27 10:00:00.000000", "docstatus": 0, "doctype": "Report", "filters": [{"fieldname": "from_date", "fieldtype": "Date", "label": "From Date", "mandatory": 0, "wildcard_filter": 0, "default": "Today"}, {"fieldname": "to_date", "fieldtype": "Date", "label": "To Date", "mandatory": 0, "wildcard_filter": 0, "default": "Today"}], "idx": 3, "is_standard": "Yes", "module": "customsmart", "name": "Car Profit Analysis", "owner": "Administrator", "prepared_report": 0, "query": "SELECT \n    cr.car_model,\n    COUNT(*) as total_cars,\n    SUM(CASE WHEN cr.status = 'Sold' THEN 1 ELSE 0 END) as cars_sold,\n    SUM(CASE WHEN cr.status = 'Available' THEN 1 ELSE 0 END) as cars_available,\n    SUM(cr.purchase_price) as total_purchase_cost,\n    SUM(CASE WHEN cr.status = 'Sold' THEN cr.selling_price ELSE 0 END) as total_selling_price,\n    SUM(CASE WHEN cr.status = 'Sold' THEN (cr.selling_price - cr.purchase_price) ELSE 0 END) as total_profit,\n    CASE \n        WHEN SUM(CASE WHEN cr.status = 'Sold' THEN cr.purchase_price ELSE 0 END) > 0 \n        THEN (SUM(CASE WHEN cr.status = 'Sold' THEN (cr.selling_price - cr.purchase_price) ELSE 0 END) / SUM(CASE WHEN cr.status = 'Sold' THEN cr.purchase_price ELSE 0 END)) * 100\n        ELSE 0\n    END as profit_margin,\n    CASE \n        WHEN SUM(CASE WHEN cr.status = 'Sold' THEN 1 ELSE 0 END) > 0 \n        THEN SUM(CASE WHEN cr.status = 'Sold' THEN cr.selling_price ELSE 0 END) / SUM(CASE WHEN cr.status = 'Sold' THEN 1 ELSE 0 END)\n        ELSE 0\n    END as avg_selling_price\nFROM \n    `tabCar Record` cr\nWHERE \n    (IFNULL(%(from_date)s, '') = '' OR cr.purchase_date >= %(from_date)s)\n    AND (IFNULL(%(to_date)s, '') = '' OR cr.purchase_date <= %(to_date)s)\nGROUP BY \n    cr.car_model\nORDER BY \n    total_profit DESC", "ref_doctype": "Car Record", "report_name": "Car Profit Analysis", "report_type": "Query Report", "roles": [{"role": "System Manager"}, {"role": "Sales Manager"}]}