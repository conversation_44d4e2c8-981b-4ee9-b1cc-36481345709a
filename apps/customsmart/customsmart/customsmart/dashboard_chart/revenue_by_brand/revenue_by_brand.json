{"chart_name": "Revenue by Brand", "chart_type": "Bar", "color": null, "creation": "2024-12-27 10:00:00.000000", "custom_options": "{}", "docstatus": 0, "doctype": "Dashboard Chart", "document_type": "Car Record", "dynamic_filters_json": "{}", "filters_json": "{\"status\": \"Sold\"}", "group_by_based_on": "car_model", "group_by_type": "Sum", "idx": 0, "is_public": 1, "is_standard": 1, "module": "customsmart", "name": "Revenue by Brand", "number_of_groups": 5, "owner": "Administrator", "source": "", "time_interval": "Monthly", "timeseries": 0, "type": "Group By", "use_report_chart": 0, "value_based_on": "selling_price", "y_axis": []}