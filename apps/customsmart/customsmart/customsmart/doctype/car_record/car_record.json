{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-06-27 18:33:00.040749", "custom": 1, "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "car_details_section", "car_name", "car_model", "car_brand", "column_break_1", "manufacture_year", "car_color", "body_type", "technical_section", "chassis_no", "engine_no", "column_break_2", "engine_capacity", "fuel_type", "transmission", "inventory_section", "item_code", "purchase_date", "column_break_3", "purchase_price", "selling_price", "status", "additional_section", "notes", "column_break_4", "insurance_expiry", "registration_no"], "fields": [{"default": "CAR-REC-.YYYY.-", "fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "CAR-REC-.YYYY.-", "reqd": 1}, {"fieldname": "car_details_section", "fieldtype": "Section Break", "label": "Car Details"}, {"bold": 1, "fieldname": "car_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Car Name", "reqd": 1}, {"fieldname": "car_model", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Car Model", "reqd": 1}, {"fieldname": "car_brand", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Car Brand", "reqd": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "manufacture_year", "fieldtype": "Int", "in_list_view": 1, "label": "Manufacture Year", "reqd": 1}, {"fieldname": "car_color", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Car Color", "reqd": 1}, {"default": "Sedan", "fieldname": "body_type", "fieldtype": "Select", "label": "Body Type", "options": "Sedan\nSUV\nHatchback\nCoupe\nConvertible\nTruck\nVan\nOther"}, {"collapsible": 1, "fieldname": "technical_section", "fieldtype": "Section Break", "label": "Technical Details"}, {"bold": 1, "fieldname": "chassis_no", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "<PERSON><PERSON><PERSON> Number", "reqd": 1, "unique": 1}, {"bold": 1, "fieldname": "engine_no", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Engine Number", "reqd": 1, "unique": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "engine_capacity", "fieldtype": "Float", "label": "Engine Capacity (CC)", "precision": "1"}, {"default": "Petrol", "fieldname": "fuel_type", "fieldtype": "Select", "label": "Fuel Type", "options": "Petrol\nDiesel\nHybrid\nElectric\nOther"}, {"default": "Automatic", "fieldname": "transmission", "fieldtype": "Select", "label": "Transmission", "options": "Manual\nAutomatic\nCVT\nOther"}, {"collapsible": 1, "fieldname": "inventory_section", "fieldtype": "Section Break", "label": "Inventory Information"}, {"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"default": "Today", "fieldname": "purchase_date", "fieldtype": "Date", "label": "Purchase Date"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "purchase_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Purchase Price", "reqd": 1}, {"fieldname": "selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Selling <PERSON>", "reqd": 1}, {"bold": 1, "default": "Available", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Available\nSold\nReserved\nMaintenance\nDamaged", "reqd": 1}, {"collapsible": 1, "fieldname": "additional_section", "fieldtype": "Section Break", "label": "Additional Information"}, {"description": "Any additional notes about the car", "fieldname": "notes", "fieldtype": "Text", "label": "Notes"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "insurance_expiry", "fieldtype": "Date", "label": "Insurance Expiry Date"}, {"fieldname": "registration_no", "fieldtype": "Data", "label": "Registration Number"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-27 18:33:00.040749", "modified_by": "Administrator", "module": "customsmart", "name": "Car Record", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "search_fields": "chassis_no,engine_no,car_color,car_model", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "car_name", "track_changes": 1}