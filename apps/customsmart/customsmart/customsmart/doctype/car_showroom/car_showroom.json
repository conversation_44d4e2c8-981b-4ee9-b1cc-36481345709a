{"actions": [], "allow_rename": 1, "autoname": "field:showroom_name", "creation": "2025-06-27 18:33:02.289373", "custom": 1, "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["showroom_details", "showroom_name", "showroom_code", "column_break_1", "manager_name", "phone", "email", "address_section", "address_line_1", "address_line_2", "column_break_2", "city", "state", "postal_code", "business_section", "license_no", "tax_id", "column_break_3", "established_date", "status"], "fields": [{"fieldname": "showroom_details", "fieldtype": "Section Break", "label": "Showroom Details"}, {"bold": 1, "fieldname": "showroom_name", "fieldtype": "Data", "in_list_view": 1, "label": "Showroom Name", "reqd": 1, "unique": 1}, {"fieldname": "showroom_code", "fieldtype": "Data", "in_list_view": 1, "label": "Showroom Code", "reqd": 1, "unique": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "manager_name", "fieldtype": "Data", "label": "Manager Name", "reqd": 1}, {"fieldname": "phone", "fieldtype": "Phone", "in_list_view": 1, "label": "Phone", "reqd": 1}, {"fieldname": "email", "fieldtype": "Data", "label": "Email", "options": "Email"}, {"collapsible": 1, "fieldname": "address_section", "fieldtype": "Section Break", "label": "Address Information"}, {"fieldname": "address_line_1", "fieldtype": "Data", "label": "Address Line 1", "reqd": 1}, {"fieldname": "address_line_2", "fieldtype": "Data", "label": "Address Line 2"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "city", "fieldtype": "Data", "in_list_view": 1, "label": "City", "reqd": 1}, {"fieldname": "state", "fieldtype": "Data", "label": "State/Province"}, {"fieldname": "postal_code", "fieldtype": "Data", "label": "Postal Code"}, {"collapsible": 1, "fieldname": "business_section", "fieldtype": "Section Break", "label": "Business Information"}, {"fieldname": "license_no", "fieldtype": "Data", "label": "License Number"}, {"fieldname": "tax_id", "fieldtype": "Data", "label": "Tax ID"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "established_date", "fieldtype": "Date", "label": "Established Date"}, {"default": "Active", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Active\nInactive\nSuspended", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-27 18:33:02.289373", "modified_by": "Administrator", "module": "customsmart", "name": "Car Showroom", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1}], "search_fields": "showroom_code,city,phone", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "showroom_name", "track_changes": 1}