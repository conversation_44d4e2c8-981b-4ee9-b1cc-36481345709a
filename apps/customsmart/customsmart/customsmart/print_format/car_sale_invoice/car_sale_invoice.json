{"align_labels_right": 0, "creation": "2024-12-27 12:00:00.000000", "custom_format": 1, "default_print_language": "ar", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font_size": 12, "format_data": "[{\"fieldname\": \"print_templates\", \"fieldtype\": \"Code\", \"options\": \"HTML\"}]", "html": "<!-- Car Sale Invoice Print Format -->\n<style>\n  /* CSS Styles for Car Sale Invoice */\n  @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');\n  \n  * {\n    box-sizing: border-box;\n  }\n  \n  body {\n    font-family: 'Cairo', 'Arial', sans-serif;\n    margin: 0;\n    padding: 20px;\n    color: #333;\n    direction: rtl;\n    line-height: 1.6;\n  }\n  \n  .invoice-container {\n    max-width: 210mm;\n    margin: 0 auto;\n    background: white;\n    box-shadow: 0 0 10px rgba(0,0,0,0.1);\n    padding: 20px;\n  }\n  \n  .header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 30px;\n    padding-bottom: 20px;\n    border-bottom: 3px solid #2c5aa0;\n  }\n  \n  .header-right {\n    text-align: right;\n    flex: 1;\n    padding-right: 20px;\n  }\n  \n  .header-left {\n    text-align: left;\n    flex: 1;\n    padding-left: 20px;\n    direction: ltr;\n  }\n  \n  .header-center {\n    text-align: center;\n    flex: 0 0 150px;\n  }\n  \n  .company-name {\n    font-size: 18px;\n    font-weight: 700;\n    color: #2c5aa0;\n    margin-bottom: 8px;\n  }\n  \n  .company-details {\n    font-size: 12px;\n    line-height: 1.8;\n    color: #555;\n  }\n  \n  .logo {\n    max-width: 120px;\n    max-height: 120px;\n    border-radius: 10px;\n  }\n  \n  .invoice-title {\n    text-align: center;\n    margin: 30px 0;\n    padding: 15px;\n    background: linear-gradient(135deg, #2c5aa0, #1e3d72);\n    color: white;\n    border-radius: 8px;\n  }\n  \n  .invoice-title h1 {\n    margin: 0;\n    font-size: 24px;\n    font-weight: 600;\n  }\n  \n  .invoice-title h2 {\n    margin: 5px 0 0 0;\n    font-size: 18px;\n    font-weight: 400;\n    opacity: 0.9;\n  }\n  \n  .info-section {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 30px;\n    gap: 30px;\n  }\n  \n  .customer-info, .invoice-details {\n    flex: 1;\n    background: #f8f9fa;\n    padding: 20px;\n    border-radius: 8px;\n    border-right: 4px solid #2c5aa0;\n  }\n  \n  .info-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: #2c5aa0;\n    margin-bottom: 15px;\n    border-bottom: 2px solid #e9ecef;\n    padding-bottom: 8px;\n  }\n  \n  .info-row {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 8px;\n    font-size: 14px;\n  }\n  \n  .info-label {\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  .info-value {\n    color: #212529;\n  }\n  \n  .items-table {\n    width: 100%;\n    border-collapse: collapse;\n    margin: 30px 0;\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n    border-radius: 8px;\n    overflow: hidden;\n  }\n  \n  .items-table th {\n    background: linear-gradient(135deg, #2c5aa0, #1e3d72);\n    color: white;\n    padding: 15px 10px;\n    text-align: center;\n    font-weight: 600;\n    font-size: 14px;\n  }\n  \n  .items-table td {\n    padding: 12px 10px;\n    text-align: center;\n    border-bottom: 1px solid #e9ecef;\n    font-size: 13px;\n  }\n  \n  .items-table tbody tr:nth-child(even) {\n    background-color: #f8f9fa;\n  }\n  \n  .items-table tbody tr:hover {\n    background-color: #e3f2fd;\n  }\n  \n  .totals-section {\n    display: flex;\n    justify-content: space-between;\n    margin-top: 30px;\n    gap: 30px;\n  }\n  \n  .qr-section {\n    flex: 0 0 200px;\n    text-align: center;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n  }\n  \n  .qr-code {\n    width: 150px;\n    height: 150px;\n    margin: 0 auto 10px;\n    border: 2px solid #2c5aa0;\n    border-radius: 8px;\n  }\n  \n  .totals-table {\n    flex: 1;\n    background: white;\n    border-radius: 8px;\n    overflow: hidden;\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  }\n  \n  .totals-table table {\n    width: 100%;\n    border-collapse: collapse;\n  }\n  \n  .totals-table th {\n    background: #2c5aa0;\n    color: white;\n    padding: 12px 15px;\n    text-align: right;\n    font-weight: 600;\n  }\n  \n  .totals-table td {\n    padding: 10px 15px;\n    border-bottom: 1px solid #e9ecef;\n    text-align: left;\n  }\n  \n  .total-row {\n    background: linear-gradient(135deg, #28a745, #20c997) !important;\n    color: white !important;\n    font-weight: 700 !important;\n    font-size: 16px !important;\n  }\n  \n  .amount {\n    font-family: 'Courier New', monospace;\n    font-weight: 600;\n  }\n  \n  .footer {\n    margin-top: 40px;\n    padding-top: 20px;\n    border-top: 2px solid #e9ecef;\n    text-align: center;\n    color: #6c757d;\n    font-size: 12px;\n  }\n  \n  .thank-you {\n    font-size: 18px;\n    font-weight: 600;\n    color: #2c5aa0;\n    margin-bottom: 10px;\n  }\n  \n  @media print {\n    body {\n      padding: 0;\n    }\n    \n    .invoice-container {\n      box-shadow: none;\n      padding: 10px;\n    }\n    \n    .header {\n      margin-bottom: 20px;\n    }\n    \n    .invoice-title {\n      margin: 20px 0;\n    }\n  }\n</style>\n\n<div class=\"invoice-container\">\n  <!-- Header Section -->\n  <div class=\"header\">\n    <!-- Arabic Company Info (Right) -->\n    <div class=\"header-right\">\n      <div class=\"company-name\">معرض ماجد الحارثي للسيارات</div>\n      <div class=\"company-details\">\n        لبيع وشراء جميع أنواع السيارات<br>\n        رقم السجل التجاري: 4032027099<br>\n        تصريح مرور: 403<br>\n        جوال: 0555771113<br>\n        الرقم الضريبي: 301140389500003\n      </div>\n    </div>\n    \n    <!-- Logo (Center) -->\n    <div class=\"header-center\">\n      {% if letter_head and letter_head.image %}\n        <img src=\"{{ letter_head.image }}\" class=\"logo\" alt=\"Logo\">\n      {% else %}\n        <div style=\"width: 120px; height: 120px; background: #2c5aa0; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;\">\n          🚗\n        </div>\n      {% endif %}\n    </div>\n    \n    <!-- English Company Info (Left) -->\n    <div class=\"header-left\">\n      <div class=\"company-name\">Majed Al-Harthi Automotive Exhibit</div>\n      <div class=\"company-details\">\n        For Sell and Buying All Types of Cars<br>\n        C.R.: 4032027099<br>\n        Traffic Lic: 403<br>\n        Mobile: 0555771113<br>\n        VAT NO.: 301140389500003\n      </div>\n    </div>\n  </div>\n  \n  <!-- Invoice Title -->\n  <div class=\"invoice-title\">\n    <h1>فاتورة ضريبية | Tax Invoice</h1>\n    <h2>{{ doc.name }}</h2>\n  </div>\n  \n  <!-- Customer and Invoice Info -->\n  <div class=\"info-section\">\n    <!-- Customer Information -->\n    <div class=\"customer-info\">\n      <div class=\"info-title\">بيانات العميل | Customer Information</div>\n      <div class=\"info-row\">\n        <span class=\"info-label\">الاسم | Name:</span>\n        <span class=\"info-value\">{{ doc.customer_name or doc.customer }}</span>\n      </div>\n      {% if doc.contact_mobile %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">رقم الجوال | Mobile:</span>\n        <span class=\"info-value\">{{ doc.contact_mobile }}</span>\n      </div>\n      {% endif %}\n      {% if doc.tax_id %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">الرقم الضريبي | VAT No:</span>\n        <span class=\"info-value\">{{ doc.tax_id }}</span>\n      </div>\n      {% endif %}\n      {% if doc.customer_address %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">العنوان | Address:</span>\n        <span class=\"info-value\">{{ doc.customer_address }}</span>\n      </div>\n      {% endif %}\n    </div>\n    \n    <!-- Invoice Details -->\n    <div class=\"invoice-details\">\n      <div class=\"info-title\">تفاصيل الفاتورة | Invoice Details</div>\n      <div class=\"info-row\">\n        <span class=\"info-label\">رقم الفاتورة | Invoice No:</span>\n        <span class=\"info-value\">{{ doc.name }}</span>\n      </div>\n      <div class=\"info-row\">\n        <span class=\"info-label\">تاريخ الفاتورة | Date:</span>\n        <span class=\"info-value\">{{ frappe.utils.formatdate(doc.posting_date, \"dd/MM/yyyy\") }}</span>\n      </div>\n      {% if doc.due_date %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">تاريخ الاستحقاق | Due Date:</span>\n        <span class=\"info-value\">{{ frappe.utils.formatdate(doc.due_date, \"dd/MM/yyyy\") }}</span>\n      </div>\n      {% endif %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">طريقة الدفع | Payment:</span>\n        <span class=\"info-value\">{{ doc.mode_of_payment or \"نقداً | Cash\" }}</span>\n      </div>\n    </div>\n  </div>\n  \n  <!-- Items Table -->\n  <table class=\"items-table\">\n    <thead>\n      <tr>\n        <th style=\"width: 5%\">م<br>No.</th>\n        <th style=\"width: 15%\">رقم الهيكل<br>Chassis No.</th>\n        <th style=\"width: 20%\">نوع السيارة<br>Car Type</th>\n        <th style=\"width: 10%\">اللون<br>Color</th>\n        <th style=\"width: 15%\">الموديل<br>Model</th>\n        <th style=\"width: 12%\">السعر<br>Price</th>\n        <th style=\"width: 8%\">الضريبة<br>VAT</th>\n        <th style=\"width: 15%\">الإجمالي<br>Total</th>\n      </tr>\n    </thead>\n    <tbody>\n      {% for item in doc.items %}\n      {% set car_record = frappe.get_doc(\"Car Record\", item.car_record) if item.car_record else None %}\n      <tr>\n        <td>{{ loop.index }}</td>\n        <td>{{ car_record.chassis_no if car_record else \"-\" }}</td>\n        <td>{{ car_record.car_name if car_record else item.item_name }}</td>\n        <td>{{ car_record.car_color if car_record else \"-\" }}</td>\n        <td>{{ car_record.car_model if car_record else \"-\" }}</td>\n        <td class=\"amount\">{{ \"%.2f\"|format(item.rate) }}</td>\n        <td class=\"amount\">{{ \"%.2f\"|format(item.rate * 0.15) }}</td>\n        <td class=\"amount\">{{ \"%.2f\"|format(item.amount) }}</td>\n      </tr>\n      {% endfor %}\n    </tbody>\n  </table>\n  \n  <!-- Totals Section -->\n  <div class=\"totals-section\">\n    <!-- QR Code Section -->\n    <div class=\"qr-section\">\n      <div style=\"font-weight: 600; margin-bottom: 10px;\">رمز الاستجابة السريعة<br>QR Code</div>\n      {% if doc.qr_code %}\n        <img src=\"data:image/png;base64,{{ doc.qr_code }}\" class=\"qr-code\" alt=\"QR Code\">\n      {% else %}\n        <div class=\"qr-code\" style=\"display: flex; align-items: center; justify-content: center; background: #f8f9fa; font-size: 14px; color: #6c757d;\">\n          QR Code<br>Not Available\n        </div>\n      {% endif %}\n    </div>\n    \n    <!-- Totals Table -->\n    <div class=\"totals-table\">\n      <table>\n        <thead>\n          <tr>\n            <th style=\"width: 60%;\">البيان | Description</th>\n            <th style=\"width: 40%;\">المبلغ | Amount (ر.س)</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr>\n            <td>الإجمالي قبل الضريبة | Subtotal</td>\n            <td class=\"amount\">{{ \"%.2f\"|format(doc.total) }}</td>\n          </tr>\n          <tr>\n            <td>ضريبة القيمة المضافة (15%) | VAT (15%)</td>\n            <td class=\"amount\">{{ \"%.2f\"|format(doc.total_taxes_and_charges) }}</td>\n          </tr>\n          <tr>\n            <td>رسوم إصدار لوحات | Plate Issuance Fees</td>\n            <td class=\"amount\">0.00</td>\n          </tr>\n          <tr class=\"total-row\">\n            <td>قيمة الفاتورة النهائية | Grand Total</td>\n            <td class=\"amount\">{{ \"%.2f\"|format(doc.grand_total) }}</td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n  </div>\n  \n  <!-- Footer -->\n  <div class=\"footer\">\n    <div class=\"thank-you\">شكراً لثقتكم بنا | Thank You for Your Trust</div>\n    <div>هذه فاتورة ضريبية معتمدة وفقاً لأنظمة هيئة الزكاة والضريبة والجمارك</div>\n    <div>This is an approved tax invoice according to ZATCA regulations</div>\n    <div style=\"margin-top: 10px; font-size: 11px;\">\n      تم إنشاء هذه الفاتورة إلكترونياً | This invoice was generated electronically<br>\n      {{ frappe.utils.now_datetime().strftime(\"%Y-%m-%d %H:%M:%S\") }}\n    </div>\n  </div>\n</div>", "idx": 1, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "module": "customsmart", "name": "Car Sale Invoice", "owner": "Administrator", "print_format_builder": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "No"}