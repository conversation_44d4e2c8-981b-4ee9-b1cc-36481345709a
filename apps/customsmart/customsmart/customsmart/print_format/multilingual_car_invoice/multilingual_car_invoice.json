{"align_labels_right": 0, "creation": "2025-06-27 21:00:00.000000", "custom_format": 1, "default_print_language": "ar", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font_size": 12, "format_data": "[{\"fieldname\": \"print_templates\", \"fieldtype\": \"Code\", \"options\": \"HTML\"}]", "html": "<!-- Multilingual Car Sale Invoice Print Format -->\n{%- set print_language = lang or frappe.local.lang or 'ar' -%}\n{%- set is_arabic = print_language == 'ar' -%}\n{%- set direction = 'rtl' if is_arabic else 'ltr' -%}\n{%- set text_align = 'right' if is_arabic else 'left' -%}\n\n<style>\n  @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');\n  \n  * {\n    box-sizing: border-box;\n  }\n  \n  body {\n    font-family: {{ \"'Cairo', 'Arial', sans-serif\" if is_arabic else \"'Arial', 'Helvetica', sans-serif\" }};\n    margin: 0;\n    padding: 20px;\n    color: #333;\n    direction: {{ direction }};\n    line-height: 1.6;\n  }\n  \n  .invoice-container {\n    max-width: 210mm;\n    margin: 0 auto;\n    background: white;\n    box-shadow: 0 0 10px rgba(0,0,0,0.1);\n    padding: 20px;\n  }\n  \n  .header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 30px;\n    padding-bottom: 20px;\n    border-bottom: 3px solid #2c5aa0;\n  }\n  \n  .header-{{ 'right' if is_arabic else 'left' }} {\n    text-align: {{ text_align }};\n    flex: 1;\n    padding-{{ 'right' if is_arabic else 'left' }}: 20px;\n  }\n  \n  .header-{{ 'left' if is_arabic else 'right' }} {\n    text-align: {{ 'left' if is_arabic else 'right' }};\n    flex: 1;\n    padding-{{ 'left' if is_arabic else 'right' }}: 20px;\n    direction: {{ 'ltr' if is_arabic else 'rtl' }};\n  }\n  \n  .header-center {\n    text-align: center;\n    flex: 0 0 150px;\n  }\n  \n  .company-name {\n    font-size: 18px;\n    font-weight: 700;\n    color: #2c5aa0;\n    margin-bottom: 8px;\n  }\n  \n  .company-details {\n    font-size: 12px;\n    line-height: 1.8;\n    color: #555;\n  }\n  \n  .logo {\n    max-width: 120px;\n    max-height: 120px;\n    border-radius: 10px;\n  }\n  \n  .invoice-title {\n    text-align: center;\n    margin: 30px 0;\n    padding: 15px;\n    background: linear-gradient(135deg, #2c5aa0, #1e3d72);\n    color: white;\n    border-radius: 8px;\n  }\n  \n  .invoice-title h1 {\n    margin: 0;\n    font-size: 24px;\n    font-weight: 600;\n  }\n  \n  .invoice-title h2 {\n    margin: 5px 0 0 0;\n    font-size: 18px;\n    font-weight: 400;\n    opacity: 0.9;\n  }\n  \n  .info-section {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 30px;\n    gap: 30px;\n  }\n  \n  .customer-info, .invoice-details {\n    flex: 1;\n    background: #f8f9fa;\n    padding: 20px;\n    border-radius: 8px;\n    border-{{ 'right' if is_arabic else 'left' }}: 4px solid #2c5aa0;\n  }\n  \n  .info-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: #2c5aa0;\n    margin-bottom: 15px;\n    border-bottom: 2px solid #e9ecef;\n    padding-bottom: 8px;\n  }\n  \n  .info-row {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 8px;\n    font-size: 14px;\n  }\n  \n  .info-label {\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  .info-value {\n    color: #212529;\n  }\n  \n  .items-table {\n    width: 100%;\n    border-collapse: collapse;\n    margin: 30px 0;\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n    border-radius: 8px;\n    overflow: hidden;\n  }\n  \n  .items-table th {\n    background: linear-gradient(135deg, #2c5aa0, #1e3d72);\n    color: white;\n    padding: 15px 10px;\n    text-align: center;\n    font-weight: 600;\n    font-size: 14px;\n  }\n  \n  .items-table td {\n    padding: 12px 10px;\n    text-align: center;\n    border-bottom: 1px solid #e9ecef;\n    font-size: 13px;\n  }\n  \n  .items-table tbody tr:nth-child(even) {\n    background-color: #f8f9fa;\n  }\n  \n  .items-table tbody tr:hover {\n    background-color: #e3f2fd;\n  }\n  \n  .totals-section {\n    display: flex;\n    justify-content: space-between;\n    margin-top: 30px;\n    gap: 30px;\n    direction: {{ direction }};\n  }\n  \n  .qr-section {\n    flex: 0 0 200px;\n    text-align: center;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n  }\n  \n  .qr-code {\n    width: 150px;\n    height: 150px;\n    margin: 0 auto 10px;\n    border: 2px solid #2c5aa0;\n    border-radius: 8px;\n  }\n  \n  .totals-table {\n    flex: 1;\n    background: white;\n    border-radius: 8px;\n    overflow: hidden;\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  }\n  \n  .totals-table table {\n    width: 100%;\n    border-collapse: collapse;\n  }\n  \n  .totals-table th {\n    background: #2c5aa0;\n    color: white;\n    padding: 12px 15px;\n    text-align: {{ text_align }};\n    font-weight: 600;\n  }\n  \n  .totals-table td {\n    padding: 10px 15px;\n    border-bottom: 1px solid #e9ecef;\n    text-align: {{ 'left' if is_arabic else 'right' }};\n  }\n  \n  .total-row {\n    background: linear-gradient(135deg, #28a745, #20c997) !important;\n    color: white !important;\n    font-weight: 700 !important;\n    font-size: 16px !important;\n  }\n  \n  .amount {\n    font-family: 'Courier New', monospace;\n    font-weight: 600;\n    direction: ltr;\n  }\n  \n  .footer {\n    margin-top: 40px;\n    padding-top: 20px;\n    border-top: 2px solid #e9ecef;\n    text-align: center;\n    color: #6c757d;\n    font-size: 12px;\n  }\n  \n  .thank-you {\n    font-size: 18px;\n    font-weight: 600;\n    color: #2c5aa0;\n    margin-bottom: 10px;\n  }\n  \n  @media print {\n    body {\n      padding: 0;\n    }\n    \n    .invoice-container {\n      box-shadow: none;\n      padding: 10px;\n    }\n    \n    .header {\n      margin-bottom: 20px;\n    }\n    \n    .invoice-title {\n      margin: 20px 0;\n    }\n  }\n</style>\n\n<!-- Define translations -->\n{%- set translations = {\n  'ar': {\n    'company_name': 'معرض ماجد الحارثي للسيارات',\n    'company_subtitle': 'لبيع وشراء جميع أنواع السيارات',\n    'commercial_reg': 'رقم السجل التجاري',\n    'traffic_license': 'تصريح مرور',\n    'mobile': 'جوال',\n    'vat_number': 'الرقم الضريبي',\n    'tax_invoice': 'فاتورة ضريبية',\n    'customer_info': 'بيانات العميل',\n    'name': 'الاسم',\n    'mobile_no': 'رقم الجوال',\n    'vat_no': 'الرقم الضريبي',\n    'address': 'العنوان',\n    'invoice_details': 'تفاصيل الفاتورة',\n    'invoice_no': 'رقم الفاتورة',\n    'date': 'تاريخ الفاتورة',\n    'due_date': 'تاريخ الاستحقاق',\n    'payment_method': 'طريقة الدفع',\n    'cash': 'نقداً',\n    'no': 'م',\n    'chassis_no': 'رقم الهيكل',\n    'car_type': 'نوع السيارة',\n    'color': 'اللون',\n    'model': 'الموديل',\n    'price': 'السعر',\n    'vat': 'الضريبة',\n    'total': 'الإجمالي',\n    'qr_code': 'رمز الاستجابة السريعة',\n    'qr_not_available': 'غير متوفر',\n    'description': 'البيان',\n    'amount': 'المبلغ (ر.س)',\n    'subtotal': 'الإجمالي قبل الضريبة',\n    'vat_15': 'ضريبة القيمة المضافة (15%)',\n    'plate_fees': 'رسوم إصدار لوحات',\n    'grand_total': 'قيمة الفاتورة النهائية',\n    'thank_you': 'شكراً لثقتكم بنا',\n    'zatca_note': 'هذه فاتورة ضريبية معتمدة وفقاً لأنظمة هيئة الزكاة والضريبة والجمارك',\n    'electronic_note': 'تم إنشاء هذه الفاتورة إلكترونياً'\n  },\n  'en': {\n    'company_name': 'Majed Al-Harthi Automotive Exhibit',\n    'company_subtitle': 'For Sell and Buying All Types of Cars',\n    'commercial_reg': 'C.R.',\n    'traffic_license': 'Traffic Lic',\n    'mobile': 'Mobile',\n    'vat_number': 'VAT NO.',\n    'tax_invoice': 'Tax Invoice',\n    'customer_info': 'Customer Information',\n    'name': 'Name',\n    'mobile_no': 'Mobile',\n    'vat_no': 'VAT No',\n    'address': 'Address',\n    'invoice_details': 'Invoice Details',\n    'invoice_no': 'Invoice No',\n    'date': 'Date',\n    'due_date': 'Due Date',\n    'payment_method': 'Payment Method',\n    'cash': 'Cash',\n    'no': 'No.',\n    'chassis_no': 'Chassis No.',\n    'car_type': 'Car Type',\n    'color': 'Color',\n    'model': 'Model',\n    'price': 'Price',\n    'vat': 'VAT',\n    'total': 'Total',\n    'qr_code': 'QR Code',\n    'qr_not_available': 'Not Available',\n    'description': 'Description',\n    'amount': 'Amount (SAR)',\n    'subtotal': 'Subtotal',\n    'vat_15': 'VAT (15%)',\n    'plate_fees': 'Plate Issuance Fees',\n    'grand_total': 'Grand Total',\n    'thank_you': 'Thank You for Your Trust',\n    'zatca_note': 'This is an approved tax invoice according to ZATCA regulations',\n    'electronic_note': 'This invoice was generated electronically'\n  }\n} -%}\n\n{%- set t = translations[print_language] -%}\n\n<div class=\"invoice-container\">\n  <!-- Header Section -->\n  <div class=\"header\">\n    {% if is_arabic %}\n    <!-- Arabic Layout -->\n    <div class=\"header-right\">\n      <div class=\"company-name\">{{ t.company_name }}</div>\n      <div class=\"company-details\">\n        {{ t.company_subtitle }}<br>\n        {{ t.commercial_reg }}: **********<br>\n        {{ t.traffic_license }}: 403<br>\n        {{ t.mobile }}: **********<br>\n        {{ t.vat_number }}: 301140389500003\n      </div>\n    </div>\n    {% else %}\n    <!-- English Layout -->\n    <div class=\"header-left\">\n      <div class=\"company-name\">{{ t.company_name }}</div>\n      <div class=\"company-details\">\n        {{ t.company_subtitle }}<br>\n        {{ t.commercial_reg }}: **********<br>\n        {{ t.traffic_license }}: 403<br>\n        {{ t.mobile }}: **********<br>\n        {{ t.vat_number }}: 301140389500003\n      </div>\n    </div>\n    {% endif %}\n    \n    <!-- Logo (Center) -->\n    <div class=\"header-center\">\n      {% if letter_head and letter_head.image %}\n        <img src=\"{{ letter_head.image }}\" class=\"logo\" alt=\"Logo\">\n      {% else %}\n        <div style=\"width: 120px; height: 120px; background: #2c5aa0; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;\">\n          🚗\n        </div>\n      {% endif %}\n    </div>\n  </div>\n  \n  <!-- Invoice Title -->\n  <div class=\"invoice-title\">\n    <h1>{{ t.tax_invoice }}</h1>\n    <h2>{{ doc.name }}</h2>\n  </div>\n  \n  <!-- Customer and Invoice Info -->\n  <div class=\"info-section\">\n    <!-- Customer Information -->\n    <div class=\"customer-info\">\n      <div class=\"info-title\">{{ t.customer_info }}</div>\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.name }}:</span>\n        <span class=\"info-value\">{{ doc.customer_name or doc.customer }}</span>\n      </div>\n      {% if doc.contact_mobile %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.mobile_no }}:</span>\n        <span class=\"info-value\">{{ doc.contact_mobile }}</span>\n      </div>\n      {% endif %}\n      {% if doc.tax_id %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.vat_no }}:</span>\n        <span class=\"info-value\">{{ doc.tax_id }}</span>\n      </div>\n      {% endif %}\n      {% if doc.customer_address %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.address }}:</span>\n        <span class=\"info-value\">{{ doc.customer_address }}</span>\n      </div>\n      {% endif %}\n    </div>\n    \n    <!-- Invoice Details -->\n    <div class=\"invoice-details\">\n      <div class=\"info-title\">{{ t.invoice_details }}</div>\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.invoice_no }}:</span>\n        <span class=\"info-value\">{{ doc.name }}</span>\n      </div>\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.date }}:</span>\n        <span class=\"info-value\">{{ frappe.utils.formatdate(doc.posting_date, \"dd/MM/yyyy\") }}</span>\n      </div>\n      {% if doc.due_date %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.due_date }}:</span>\n        <span class=\"info-value\">{{ frappe.utils.formatdate(doc.due_date, \"dd/MM/yyyy\") }}</span>\n      </div>\n      {% endif %}\n      <div class=\"info-row\">\n        <span class=\"info-label\">{{ t.payment_method }}:</span>\n        <span class=\"info-value\">{{ doc.mode_of_payment or t.cash }}</span>\n      </div>\n    </div>\n  </div>\n  \n  <!-- Items Table -->\n  <table class=\"items-table\">\n    <thead>\n      <tr>\n        <th style=\"width: 5%\">{{ t.no }}</th>\n        <th style=\"width: 15%\">{{ t.chassis_no }}</th>\n        <th style=\"width: 20%\">{{ t.car_type }}</th>\n        <th style=\"width: 10%\">{{ t.color }}</th>\n        <th style=\"width: 15%\">{{ t.model }}</th>\n        <th style=\"width: 12%\">{{ t.price }}</th>\n        <th style=\"width: 8%\">{{ t.vat }}</th>\n        <th style=\"width: 15%\">{{ t.total }}</th>\n      </tr>\n    </thead>\n    <tbody>\n      {% for item in doc.items %}\n      {% set car_record = frappe.get_doc(\"Car Record\", item.car_record) if item.car_record else None %}\n      <tr>\n        <td>{{ loop.index }}</td>\n        <td>{{ car_record.chassis_no if car_record else \"-\" }}</td>\n        <td>{{ car_record.car_name if car_record else item.item_name }}</td>\n        <td>{{ car_record.car_color if car_record else \"-\" }}</td>\n        <td>{{ car_record.car_model if car_record else \"-\" }}</td>\n        <td class=\"amount\">{{ \"%.2f\"|format(item.rate) }}</td>\n        <td class=\"amount\">{{ \"%.2f\"|format(item.rate * 0.15) }}</td>\n        <td class=\"amount\">{{ \"%.2f\"|format(item.amount) }}</td>\n      </tr>\n      {% endfor %}\n    </tbody>\n  </table>\n  \n  <!-- Totals Section -->\n  <div class=\"totals-section\">\n    <!-- QR Code Section -->\n    <div class=\"qr-section\">\n      <div style=\"font-weight: 600; margin-bottom: 10px;\">{{ t.qr_code }}</div>\n      {% if doc.qr_code %}\n        <img src=\"data:image/png;base64,{{ doc.qr_code }}\" class=\"qr-code\" alt=\"QR Code\">\n      {% else %}\n        <div class=\"qr-code\" style=\"display: flex; align-items: center; justify-content: center; background: #f8f9fa; font-size: 14px; color: #6c757d;\">\n          {{ t.qr_not_available }}\n        </div>\n      {% endif %}\n    </div>\n    \n    <!-- Totals Table -->\n    <div class=\"totals-table\">\n      <table>\n        <thead>\n          <tr>\n            <th style=\"width: 60%;\">{{ t.description }}</th>\n            <th style=\"width: 40%;\">{{ t.amount }}</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr>\n            <td>{{ t.subtotal }}</td>\n            <td class=\"amount\">{{ \"%.2f\"|format(doc.total) }}</td>\n          </tr>\n          <tr>\n            <td>{{ t.vat_15 }}</td>\n            <td class=\"amount\">{{ \"%.2f\"|format(doc.total_taxes_and_charges) }}</td>\n          </tr>\n          <tr>\n            <td>{{ t.plate_fees }}</td>\n            <td class=\"amount\">0.00</td>\n          </tr>\n          <tr class=\"total-row\">\n            <td>{{ t.grand_total }}</td>\n            <td class=\"amount\">{{ \"%.2f\"|format(doc.grand_total) }}</td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n  </div>\n  \n  <!-- Footer -->\n  <div class=\"footer\">\n    <div class=\"thank-you\">{{ t.thank_you }}</div>\n    <div>{{ t.zatca_note }}</div>\n    <div style=\"margin-top: 10px; font-size: 11px;\">\n      {{ t.electronic_note }}<br>\n      {{ frappe.utils.now_datetime().strftime(\"%Y-%m-%d %H:%M:%S\") }}\n    </div>\n  </div>\n</div>", "idx": 1, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "module": "customsmart", "name": "Multilingual Car Invoice", "owner": "Administrator", "print_format_builder": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "No"}