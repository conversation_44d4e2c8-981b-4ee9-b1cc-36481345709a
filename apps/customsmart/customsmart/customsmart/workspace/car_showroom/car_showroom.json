{"charts": [{"chart_name": "Car Sales Monthly", "label": "Monthly Car Sales"}, {"chart_name": "Car Inventory Status", "label": "Current Inventory Status"}, {"chart_name": "Revenue by Brand", "label": "Revenue by Car Brand"}], "content": "[{\"id\": \"_header\", \"type\": \"header\", \"data\": {\"text\": \"<span class='h4'><b><i class='fa fa-car text-extra-muted'></i> Car Showroom Management</b></span>\", \"col\": 12}}, {\"id\": \"_description\", \"type\": \"paragraph\", \"data\": {\"text\": \"<br><span class='text-muted'>Complete car showroom management system with inventory tracking, sales management, and comprehensive reporting.</span>\", \"col\": 12}}]", "creation": "2024-12-27 10:00:00.000000", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "car", "idx": 0, "is_hidden": 0, "label": "Car Showroom", "links": [{"hidden": 0, "is_query_report": 0, "label": "Car Inventory", "link_name": "Car Record", "link_to": "Car Record", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Sales Invoice", "link_name": "Sales Invoice", "link_to": "Sales Invoice", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Customer", "link_name": "Customer", "link_to": "Customer", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Quotation", "link_name": "Quotation", "link_to": "Quotation", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payment Entry", "link_name": "Payment Entry", "link_to": "Payment Entry", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "customsmart", "name": "Car Showroom", "number_cards": [{"aggregate_function_based_on": "", "document_type": "Car Record", "dynamic_filters_json": "{}", "filters_json": "{\"status\": \"Available\"}", "function": "Count", "is_public": 1, "label": "Available Cars", "show_percentage_stats": 1, "stats_time_interval": "Monthly"}, {"aggregate_function_based_on": "", "document_type": "Car Record", "dynamic_filters_json": "{}", "filters_json": "{\"status\": \"Sold\"}", "function": "Count", "is_public": 1, "label": "Cars Sold", "show_percentage_stats": 1, "stats_time_interval": "Monthly"}, {"aggregate_function_based_on": "selling_price", "document_type": "Car Record", "dynamic_filters_json": "{}", "filters_json": "{\"status\": \"Available\"}", "function": "Sum", "is_public": 1, "label": "Available Inventory Value", "show_percentage_stats": 0, "stats_time_interval": "Monthly"}, {"aggregate_function_based_on": "grand_total", "document_type": "Sales Invoice", "dynamic_filters_json": "{}", "filters_json": "{\"docstatus\": 1}", "function": "Sum", "is_public": 1, "label": "Total Revenue", "show_percentage_stats": 1, "stats_time_interval": "Monthly"}], "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "public": 1, "shortcuts": [{"color": "Blue", "doc_view": "List", "icon": "car", "is_query_report": 0, "label": "Car Record", "link_to": "Car Record", "restrict_to_domain": "", "stats_filter": "{\"status\": \"Available\"}", "type": "DocType"}, {"color": "Green", "doc_view": "List", "icon": "file-invoice-dollar", "is_query_report": 0, "label": "Sales Invoice", "link_to": "Sales Invoice", "restrict_to_domain": "", "stats_filter": "{\"docstatus\": 1}", "type": "DocType"}, {"color": "Purple", "doc_view": "List", "icon": "users", "is_query_report": 0, "label": "Customer", "link_to": "Customer", "restrict_to_domain": "", "stats_filter": "{}", "type": "DocType"}, {"color": "Orange", "doc_view": "List", "icon": "file-alt", "is_query_report": 0, "label": "Quotation", "link_to": "Quotation", "restrict_to_domain": "", "stats_filter": "{\"status\": \"Open\"}", "type": "DocType"}, {"color": "Red", "doc_view": "List", "icon": "credit-card", "is_query_report": 0, "label": "Payment Entry", "link_to": "Payment Entry", "restrict_to_domain": "", "stats_filter": "{\"docstatus\": 1}", "type": "DocType"}]}