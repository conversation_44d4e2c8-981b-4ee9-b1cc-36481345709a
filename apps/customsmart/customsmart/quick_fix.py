#!/usr/bin/env python3
"""إصلاح سريع لمشكلة Car Record"""

import frappe

def quick_fix():
    """إنشاء Car Record DocType بسرعة"""
    
    print("🔧 بدء الإصلاح السريع...")
    
    # إنشاء DocType Car Record
    if not frappe.db.exists("DocType", "Car Record"):
        print("📋 إنشاء DocType: Car Record...")
        
        doctype_doc = frappe.get_doc({
            "doctype": "DocType",
            "name": "Car Record",
            "module": "Custom",
            "custom": 1,
            "autoname": "naming_series:",
            "title_field": "car_name",
            "fields": [
                {
                    "fieldname": "naming_series",
                    "label": "Series",
                    "fieldtype": "Select",
                    "options": "CAR-REC-.YYYY.-",
                    "default": "CAR-REC-.YYYY.-",
                    "reqd": 1
                },
                {
                    "fieldname": "car_name",
                    "label": "Car Name",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "chassis_no",
                    "label": "Chassis Number",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "unique": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "engine_no",
                    "label": "Engine Number",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "unique": 1
                },
                {
                    "fieldname": "car_color",
                    "label": "Car Color",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "car_model",
                    "label": "Car Model",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "manufacture_year",
                    "label": "Manufacture Year",
                    "fieldtype": "Int",
                    "reqd": 1
                },
                {
                    "fieldname": "item_code",
                    "label": "Item Code",
                    "fieldtype": "Link",
                    "options": "Item",
                    "reqd": 1
                },
                {
                    "fieldname": "selling_price",
                    "label": "Selling Price",
                    "fieldtype": "Currency",
                    "reqd": 1
                },
                {
                    "fieldname": "status",
                    "label": "Status",
                    "fieldtype": "Select",
                    "options": "Available\nSold\nReserved\nMaintenance",
                    "default": "Available",
                    "reqd": 1,
                    "in_list_view": 1
                }
            ],
            "permissions": [
                {
                    "role": "System Manager",
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 1
                },
                {
                    "role": "Sales User",
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 0
                }
            ]
        })
        
        doctype_doc.insert(ignore_permissions=True)
        frappe.db.commit()
        print("✅ تم إنشاء DocType: Car Record")
    else:
        print("⚠️ DocType Car Record موجود بالفعل")
    
    # إنشاء بيانات تجريبية
    create_sample_data()
    
    print("✅ تم الانتهاء من الإصلاح السريع")

def create_sample_data():
    """إنشاء بيانات تجريبية للاختبار"""
    
    print("📊 إنشاء البيانات التجريبية...")
    
    # إنشاء سجل سيارة للاختبار
    sample_cars = [
        {
            "car_name": "سيارة تجريبية - أحمر",
            "chassis_no": "11223",
            "engine_no": "11223",
            "car_color": "احمر",
            "car_model": "2023",
            "manufacture_year": 2023,
            "item_code": "بيع السيارات",
            "selling_price": 4000000,
            "status": "Available"
        },
        {
            "car_name": "Toyota Camry - أبيض",
            "chassis_no": "TOY-12345",
            "engine_no": "TOY-67890",
            "car_color": "أبيض",
            "car_model": "Camry 2024",
            "manufacture_year": 2024,
            "item_code": "بيع السيارات",
            "selling_price": 5500000,
            "status": "Available"
        },
        {
            "car_name": "BMW X5 - أسود",
            "chassis_no": "BMW-54321",
            "engine_no": "BMW-98765",
            "car_color": "أسود",
            "car_model": "X5 2024",
            "manufacture_year": 2024,
            "item_code": "بيع السيارات",
            "selling_price": 8000000,
            "status": "Available"
        }
    ]
    
    for car_data in sample_cars:
        if not frappe.db.exists("Car Record", {"chassis_no": car_data["chassis_no"]}):
            try:
                car = frappe.get_doc({
                    "doctype": "Car Record",
                    **car_data
                })
                car.insert(ignore_permissions=True)
                print(f"✅ تم إنشاء سيارة: {car_data['car_name']}")
            except Exception as e:
                print(f"⚠️ خطأ في إنشاء {car_data['car_name']}: {str(e)}")
    
    frappe.db.commit()
    print("✅ تم إنشاء جميع البيانات التجريبية")

if __name__ == "__main__":
    quick_fix()