#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء DocType Car Record لإدارة سجلات السيارات
Create Car Record DocType for managing car records
"""

import frappe
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields

def create_car_record_doctype():
    """إنشاء DocType Car Record"""
    
    print("🚗 إنشاء DocType: Car Record...")
    
    # إنشاء DocType Car Record
    if not frappe.db.exists("DocType", "Car Record"):
        car_record_doctype = frappe.get_doc({
            "doctype": "DocType",
            "name": "Car Record",
            "module": "customsmart",
            "custom": 1,
            "is_submittable": 0,
            "is_child_table": 0,
            "track_changes": 1,
            "autoname": "naming_series:",
            "title_field": "car_name",
            "search_fields": "chassis_no,engine_no,car_color,car_model",
            "sort_field": "creation",
            "sort_order": "DESC",
            "fields": [
                # Naming Series
                {
                    "fieldname": "naming_series",
                    "label": "Series",
                    "fieldtype": "Select",
                    "options": "CAR-REC-.YYYY.-",
                    "default": "CAR-REC-.YYYY.-",
                    "reqd": 1,
                    "hidden": 0,
                    "read_only": 0
                },
                
                # Basic Car Information
                {
                    "fieldname": "car_details_section",
                    "label": "Car Details",
                    "fieldtype": "Section Break",
                    "collapsible": 0
                },
                {
                    "fieldname": "car_name",
                    "label": "Car Name",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1,
                    "in_standard_filter": 1,
                    "bold": 1
                },
                {
                    "fieldname": "car_model",
                    "label": "Car Model",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1,
                    "in_standard_filter": 1
                },
                {
                    "fieldname": "car_brand",
                    "label": "Car Brand",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1,
                    "in_standard_filter": 1
                },
                {
                    "fieldname": "column_break_1",
                    "fieldtype": "Column Break"
                },
                {
                    "fieldname": "manufacture_year",
                    "label": "Manufacture Year",
                    "fieldtype": "Int",
                    "reqd": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "car_color",
                    "label": "Car Color",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1,
                    "in_standard_filter": 1
                },
                {
                    "fieldname": "body_type",
                    "label": "Body Type",
                    "fieldtype": "Select",
                    "options": "Sedan\nSUV\nHatchback\nCoupe\nConvertible\nTruck\nVan\nOther",
                    "default": "Sedan"
                },
                
                # Technical Details
                {
                    "fieldname": "technical_section",
                    "label": "Technical Details",
                    "fieldtype": "Section Break",
                    "collapsible": 1
                },
                {
                    "fieldname": "chassis_no",
                    "label": "Chassis Number",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "unique": 1,
                    "in_list_view": 1,
                    "in_standard_filter": 1,
                    "bold": 1
                },
                {
                    "fieldname": "engine_no",
                    "label": "Engine Number",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "unique": 1,
                    "in_list_view": 1,
                    "in_standard_filter": 1,
                    "bold": 1
                },
                {
                    "fieldname": "column_break_2",
                    "fieldtype": "Column Break"
                },
                {
                    "fieldname": "engine_capacity",
                    "label": "Engine Capacity (CC)",
                    "fieldtype": "Float",
                    "precision": 1
                },
                {
                    "fieldname": "fuel_type",
                    "label": "Fuel Type",
                    "fieldtype": "Select",
                    "options": "Petrol\nDiesel\nHybrid\nElectric\nOther",
                    "default": "Petrol"
                },
                {
                    "fieldname": "transmission",
                    "label": "Transmission",
                    "fieldtype": "Select",
                    "options": "Manual\nAutomatic\nCVT\nOther",
                    "default": "Automatic"
                },
                
                # Inventory Information
                {
                    "fieldname": "inventory_section",
                    "label": "Inventory Information",
                    "fieldtype": "Section Break",
                    "collapsible": 1
                },
                {
                    "fieldname": "item_code",
                    "label": "Item Code",
                    "fieldtype": "Link",
                    "options": "Item",
                    "reqd": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "purchase_date",
                    "label": "Purchase Date",
                    "fieldtype": "Date",
                    "default": "Today"
                },
                {
                    "fieldname": "column_break_3",
                    "fieldtype": "Column Break"
                },
                {
                    "fieldname": "purchase_price",
                    "label": "Purchase Price",
                    "fieldtype": "Currency",
                    "reqd": 1
                },
                {
                    "fieldname": "selling_price",
                    "label": "Selling Price",
                    "fieldtype": "Currency",
                    "reqd": 1
                },
                {
                    "fieldname": "status",
                    "label": "Status",
                    "fieldtype": "Select",
                    "options": "Available\nSold\nReserved\nMaintenance\nDamaged",
                    "default": "Available",
                    "reqd": 1,
                    "in_list_view": 1,
                    "in_standard_filter": 1,
                    "bold": 1
                },
                
                # Additional Information
                {
                    "fieldname": "additional_section",
                    "label": "Additional Information",
                    "fieldtype": "Section Break",
                    "collapsible": 1
                },
                {
                    "fieldname": "notes",
                    "label": "Notes",
                    "fieldtype": "Text",
                    "description": "Any additional notes about the car"
                },
                {
                    "fieldname": "column_break_4",
                    "fieldtype": "Column Break"
                },
                {
                    "fieldname": "insurance_expiry",
                    "label": "Insurance Expiry Date",
                    "fieldtype": "Date"
                },
                {
                    "fieldname": "registration_no",
                    "label": "Registration Number",
                    "fieldtype": "Data"
                }
            ],
            
            # Permissions
            "permissions": [
                {
                    "role": "System Manager",
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 1,
                    "submit": 0,
                    "cancel": 0,
                    "amend": 0
                },
                {
                    "role": "Sales User",
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 0,
                    "submit": 0,
                    "cancel": 0,
                    "amend": 0
                },
                {
                    "role": "Sales Manager",
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 1,
                    "submit": 0,
                    "cancel": 0,
                    "amend": 0
                }
            ]
        })
        
        car_record_doctype.insert(ignore_permissions=True)
        frappe.db.commit()
        print("✅ تم إنشاء DocType: Car Record")
    else:
        print("⚠️ DocType Car Record موجود بالفعل")

def create_car_showroom_doctype():
    """إنشاء DocType Car Showroom لإدارة معارض السيارات"""
    
    print("🏢 إنشاء DocType: Car Showroom...")
    
    if not frappe.db.exists("DocType", "Car Showroom"):
        car_showroom_doctype = frappe.get_doc({
            "doctype": "DocType",
            "name": "Car Showroom",
            "module": "customsmart",
            "custom": 1,
            "is_submittable": 0,
            "is_child_table": 0,
            "track_changes": 1,
            "autoname": "field:showroom_name",
            "title_field": "showroom_name",
            "search_fields": "showroom_code,city,phone",
            "sort_field": "creation",
            "sort_order": "DESC",
            "fields": [
                # Basic Information
                {
                    "fieldname": "showroom_details",
                    "label": "Showroom Details",
                    "fieldtype": "Section Break"
                },
                {
                    "fieldname": "showroom_name",
                    "label": "Showroom Name",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "unique": 1,
                    "in_list_view": 1,
                    "bold": 1
                },
                {
                    "fieldname": "showroom_code",
                    "label": "Showroom Code",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "unique": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "column_break_1",
                    "fieldtype": "Column Break"
                },
                {
                    "fieldname": "manager_name",
                    "label": "Manager Name",
                    "fieldtype": "Data",
                    "reqd": 1
                },
                {
                    "fieldname": "phone",
                    "label": "Phone",
                    "fieldtype": "Phone",
                    "reqd": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "email",
                    "label": "Email",
                    "fieldtype": "Data",
                    "options": "Email"
                },
                
                # Address Information
                {
                    "fieldname": "address_section",
                    "label": "Address Information",
                    "fieldtype": "Section Break",
                    "collapsible": 1
                },
                {
                    "fieldname": "address_line_1",
                    "label": "Address Line 1",
                    "fieldtype": "Data",
                    "reqd": 1
                },
                {
                    "fieldname": "address_line_2",
                    "label": "Address Line 2",
                    "fieldtype": "Data"
                },
                {
                    "fieldname": "column_break_2",
                    "fieldtype": "Column Break"
                },
                {
                    "fieldname": "city",
                    "label": "City",
                    "fieldtype": "Data",
                    "reqd": 1,
                    "in_list_view": 1
                },
                {
                    "fieldname": "state",
                    "label": "State/Province",
                    "fieldtype": "Data"
                },
                {
                    "fieldname": "postal_code",
                    "label": "Postal Code",
                    "fieldtype": "Data"
                },
                
                # Business Information
                {
                    "fieldname": "business_section",
                    "label": "Business Information",
                    "fieldtype": "Section Break",
                    "collapsible": 1
                },
                {
                    "fieldname": "license_no",
                    "label": "License Number",
                    "fieldtype": "Data"
                },
                {
                    "fieldname": "tax_id",
                    "label": "Tax ID",
                    "fieldtype": "Data"
                },
                {
                    "fieldname": "column_break_3",
                    "fieldtype": "Column Break"
                },
                {
                    "fieldname": "established_date",
                    "label": "Established Date",
                    "fieldtype": "Date"
                },
                {
                    "fieldname": "status",
                    "label": "Status",
                    "fieldtype": "Select",
                    "options": "Active\nInactive\nSuspended",
                    "default": "Active",
                    "reqd": 1,
                    "in_list_view": 1
                }
            ],
            
            "permissions": [
                {
                    "role": "System Manager",
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 1
                },
                {
                    "role": "Sales Manager",
                    "read": 1,
                    "write": 1,
                    "create": 1,
                    "delete": 0
                },
                {
                    "role": "Sales User",
                    "read": 1,
                    "write": 0,
                    "create": 0,
                    "delete": 0
                }
            ]
        })
        
        car_showroom_doctype.insert(ignore_permissions=True)
        frappe.db.commit()
        print("✅ تم إنشاء DocType: Car Showroom")
    else:
        print("⚠️ DocType Car Showroom موجود بالفعل")

def create_sample_data():
    """إنشاء بيانات تجريبية للاختبار"""
    
    print("📊 إنشاء بيانات تجريبية...")
    
    # Create Item Group "Cars" if doesn't exist
    if not frappe.db.exists("Item Group", "Cars"):
        item_group = frappe.get_doc({
            "doctype": "Item Group",
            "item_group_name": "Cars",
            "parent_item_group": "All Item Groups",
            "is_group": 0
        })
        item_group.insert(ignore_permissions=True)
        print("✅ تم إنشاء Item Group: Cars")
    
    # Sample Items for cars
    sample_items = [
        {
            "item_code": "TOYOTA-CAMRY-2024",
            "item_name": "Toyota Camry 2024",
            "item_group": "Cars",
            "stock_uom": "Nos",
            "is_stock_item": 1,
            "valuation_rate": 100000,
            "standard_rate": 120000
        },
        {
            "item_code": "BMW-X5-2024",
            "item_name": "BMW X5 2024",
            "item_group": "Cars",
            "stock_uom": "Nos",
            "is_stock_item": 1,
            "valuation_rate": 200000,
            "standard_rate": 250000
        },
        {
            "item_code": "HONDA-CIVIC-2024",
            "item_name": "Honda Civic 2024",
            "item_group": "Cars",
            "stock_uom": "Nos",
            "is_stock_item": 1,
            "valuation_rate": 80000,
            "standard_rate": 95000
        }
    ]
    
    # Create Items
    for item_data in sample_items:
        if not frappe.db.exists("Item", item_data["item_code"]):
            item = frappe.get_doc({
                "doctype": "Item",
                **item_data
            })
            item.insert(ignore_permissions=True)
            print(f"✅ تم إنشاء Item: {item_data['item_code']}")
    
    # Sample Car Records
    sample_cars = [
        {
            "car_name": "Toyota Camry White 2024",
            "car_model": "Camry 2024",
            "car_brand": "Toyota",
            "manufacture_year": 2024,
            "car_color": "White",
            "body_type": "Sedan",
            "chassis_no": "JTDBF4E2XD5012345",
            "engine_no": "2AR-FE-123456",
            "engine_capacity": 2500,
            "fuel_type": "Petrol",
            "transmission": "Automatic",
            "item_code": "TOYOTA-CAMRY-2024",
            "purchase_price": 100000,
            "selling_price": 120000,
            "status": "Available"
        },
        {
            "car_name": "BMW X5 Black 2024",
            "car_model": "X5 2024",
            "car_brand": "BMW",
            "manufacture_year": 2024,
            "car_color": "Black",
            "body_type": "SUV",
            "chassis_no": "WBAFR9C50ED123456",
            "engine_no": "N55B30-789012",
            "engine_capacity": 3000,
            "fuel_type": "Petrol",
            "transmission": "Automatic",
            "item_code": "BMW-X5-2024",
            "purchase_price": 200000,
            "selling_price": 250000,
            "status": "Available"
        },
        {
            "car_name": "Honda Civic Silver 2024",
            "car_model": "Civic 2024",
            "car_brand": "Honda",
            "manufacture_year": 2024,
            "car_color": "Silver",
            "body_type": "Sedan",
            "chassis_no": "2HGFC2F59MH123456",
            "engine_no": "L15B7-345678",
            "engine_capacity": 1500,
            "fuel_type": "Petrol",
            "transmission": "CVT",
            "item_code": "HONDA-CIVIC-2024",
            "purchase_price": 80000,
            "selling_price": 95000,
            "status": "Available"
        }
    ]
    
    # Create Car Records
    for car_data in sample_cars:
        if not frappe.db.exists("Car Record", {"chassis_no": car_data["chassis_no"]}):
            car = frappe.get_doc({
                "doctype": "Car Record",
                **car_data
            })
            car.insert(ignore_permissions=True)
            print(f"✅ تم إنشاء Car Record: {car_data['car_name']}")
    
    # Sample Showroom
    if not frappe.db.exists("Car Showroom", "Premium Motors"):
        showroom = frappe.get_doc({
            "doctype": "Car Showroom",
            "showroom_name": "Premium Motors",
            "showroom_code": "PM001",
            "manager_name": "Ahmed Al-Rashid",
            "phone": "+966501234567",
            "email": "<EMAIL>",
            "address_line_1": "King Fahd Road",
            "city": "Riyadh",
            "state": "Riyadh Province",
            "postal_code": "12345",
            "status": "Active"
        })
        showroom.insert(ignore_permissions=True)
        print("✅ تم إنشاء Car Showroom: Premium Motors")
    
    frappe.db.commit()
    print("✅ تم إنشاء جميع البيانات التجريبية")

def install_car_doctypes():
    """تثبيت جميع DocTypes المطلوبة"""
    
    print("🚀 بدء تثبيت DocTypes لمعرض السيارات...")
    
    try:
        # Create DocTypes
        create_car_record_doctype()
        create_car_showroom_doctype()
        
        # Create sample data
        create_sample_data()
        
        print("✅ تم تثبيت جميع DocTypes بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تثبيت DocTypes: {str(e)}")
        frappe.log_error(f"Car DocTypes Installation Error: {str(e)}")
        return False

if __name__ == "__main__":
    install_car_doctypes()