# -*- coding: utf-8 -*-

import frappe

def fix_workspace_visibility():
    """إصلاح مشكلة اختفاء Workspaces"""
    
    print("🔧 إصلاح مشكلة Workspaces...")
    
    # تحديث جميع workspaces لتكون عامة
    frappe.db.sql("""
        UPDATE `tabWorkspace` 
        SET public = 1, 
            for_user = '',
            parent_page = ''
        WHERE name IN (
            'Home', 'Accounting', 'Selling', 'Buying', 'Stock', 
            'Assets', 'Projects', 'CRM', 'Support', 'HR', 
            'Manufacturing', 'Website', 'Tools', 'Settings'
        )
    """)
    
    # إعادة ترتيب Workspaces
    workspace_order = [
        ("Home", 1),
        ("Accounting", 2), 
        ("Selling", 3),
        ("Buying", 4),
        ("Stock", 5),
        ("Assets", 6),
        ("Projects", 7),
        ("CRM", 8),
        ("Support", 9),
        ("HR", 10),
        ("Manufacturing", 11),
        ("Website", 12),
        ("Tools", 13),
        ("Settings", 14)
    ]
    
    for workspace, sequence in workspace_order:
        if frappe.db.exists("Workspace", workspace):
            frappe.db.sql("""
                UPDATE `tabWorkspace` 
                SET sequence_id = %s 
                WHERE name = %s
            """, (sequence, workspace))
    
    # تحديث إعدادات إضافية
    frappe.db.sql("""
        UPDATE `tabWorkspace` 
        SET restrict_to_domain = '',
            is_standard = 1
        WHERE name IN (
            'Home', 'Accounting', 'Selling', 'Buying', 'Stock',
            'Assets', 'Projects', 'CRM', 'Support', 'HR',
            'Manufacturing', 'Website', 'Tools', 'Settings'
        )
    """)
    
    # commit التغييرات
    frappe.db.commit()
    
    # مسح cache
    frappe.clear_cache()
    
    print("✅ تم إصلاح مشكلة Workspaces بنجاح!")
    return True

if __name__ == "__main__":
    fix_workspace_visibility()