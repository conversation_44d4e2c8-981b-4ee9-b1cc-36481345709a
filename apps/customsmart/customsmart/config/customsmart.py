# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from frappe import _

def get_data():
	return [
		{
			"label": _("إدارة معرض السيارات"),
			"icon": "fa fa-car",
			"items": [
				{
					"type": "doctype",
					"name": "Car Record", 
					"label": _("سجل السيارات"),
					"description": _("إدارة جميع السيارات في المعرض"),
					"icon": "fa fa-car"
				},
				{
					"type": "doctype",
					"name": "Car Showroom",
					"label": _("معرض السيارات"),
					"description": _("إعدادات معرض السيارات"),
					"icon": "fa fa-building"
				}
			]
		},
		{
			"label": _("التقارير"),
			"icon": "fa fa-bar-chart",
			"items": [
				{
					"type": "report",
					"name": "Car Sales Report",
					"label": _("تقرير مبيعات السيارات"),
					"doctype": "Car Record",
					"is_query_report": True,
					"icon": "fa fa-line-chart"
				},
				{
					"type": "report",
					"name": "Car Inventory Report", 
					"label": _("تقرير مخزون السيارات"),
					"doctype": "Car Record",
					"is_query_report": True,
					"icon": "fa fa-warehouse"
				}
			]
		}
	]