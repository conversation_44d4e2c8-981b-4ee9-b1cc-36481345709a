# -*- coding: utf-8 -*-

import frappe

def setup_customsmart_safely():
    """إعداد آمن للتطبيق المخصص"""
    
    print("🚀 بدء الإعداد الآمن للتطبيق المخصص...")
    
    # 1. إصلاح الـ workspaces أولاً
    print("📋 إصلاح Workspaces...")
    from customsmart.workspace_fix import safe_workspace_fix
    safe_workspace_fix()
    
    # 2. إضافة الحقول المخصصة للفواتير
    print("🔧 إضافة حقول QR للفواتير...")
    create_qr_custom_fields()
    
    # 3. مسح cache نهائي
    print("🧹 مسح Cache...")
    frappe.clear_cache()
    
    print("✅ تم إعداد التطبيق المخصص بنجاح!")
    print("🔄 يرجى إعادة تحميل المتصفح")

def create_qr_custom_fields():
    """إضافة حقول QR للفواتير"""
    
    custom_fields = [
        {
            "fieldname": "qr_detailed_data",
            "label": "QR Detailed Data",
            "fieldtype": "Long Text",
            "hidden": 1,
            "read_only": 1,
            "no_copy": 1,
            "description": "بيانات QR شاملة للفاتورة"
        },
        {
            "fieldname": "qr_zatca_data", 
            "label": "QR ZATCA Data",
            "fieldtype": "Data",
            "hidden": 1,
            "read_only": 1,
            "no_copy": 1,
            "description": "بيانات QR متوافقة مع ZATCA"
        }
    ]
    
    for field in custom_fields:
        if not frappe.db.exists("Custom Field", {"dt": "Sales Invoice", "fieldname": field["fieldname"]}):
            try:
                custom_field = frappe.get_doc({
                    "doctype": "Custom Field",
                    "dt": "Sales Invoice",
                    "fieldname": field["fieldname"],
                    "label": field["label"],
                    "fieldtype": field["fieldtype"],
                    "hidden": field.get("hidden", 0),
                    "read_only": field.get("read_only", 0),
                    "no_copy": field.get("no_copy", 0),
                    "description": field.get("description", ""),
                    "owner": "Administrator"
                })
                custom_field.insert(ignore_permissions=True)
                print(f"✅ تم إنشاء حقل: {field['fieldname']}")
            except Exception as e:
                print(f"⚠️ خطأ في إنشاء حقل {field['fieldname']}: {str(e)}")
        else:
            print(f"ℹ️ الحقل موجود مسبقاً: {field['fieldname']}")
    
    frappe.db.commit()

def check_system_health():
    """فحص صحة النظام بعد التطبيق المخصص"""
    
    print("🏥 فحص صحة النظام...")
    
    # فحص الـ workspaces
    visible_workspaces = frappe.db.count("Workspace", {"public": 1})
    print(f"📋 عدد Workspaces المرئية: {visible_workspaces}")
    
    # فحص الحقول المخصصة
    qr_fields = frappe.db.count("Custom Field", {
        "dt": "Sales Invoice", 
        "fieldname": ["in", ["qr_detailed_data", "qr_zatca_data"]]
    })
    print(f"🔧 عدد حقول QR: {qr_fields}")
    
    # فحص الملفات
    import os
    js_files = [
        "sales_invoice_custom.js",
        "multilingual_print.js", 
        "qr_invoice.js"
    ]
    
    for js_file in js_files:
        path = f"/home/<USER>/frappe-bench/apps/customsmart/customsmart/public/js/{js_file}"
        if os.path.exists(path):
            print(f"✅ ملف موجود: {js_file}")
        else:
            print(f"❌ ملف مفقود: {js_file}")
    
    print("✅ انتهى فحص صحة النظام")

if __name__ == "__main__":
    setup_customsmart_safely()
    check_system_health()