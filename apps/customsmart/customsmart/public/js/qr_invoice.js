// QR Code Invoice Generator for Print Format

frappe.provide('customsmart.qr');

// تحميل مكتبة QR.js
frappe.require([
    'https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js'
], function() {
    console.log('QR Code library loaded');
});

customsmart.qr = {
    // توليد بيانات QR للفاتورة
    generate_invoice_qr_data: function(doc) {
        const qr_data = {
            invoice_number: doc.name,
            company: doc.company,
            customer: doc.customer_name,
            date: doc.posting_date,
            time: doc.posting_time,
            total: doc.grand_total,
            tax: doc.total_taxes_and_charges || 0,
            currency: doc.currency
        };
        
        // إضافة بيانات السيارة إذا وُجدت
        if (doc.chassis_no) {
            qr_data.chassis_no = doc.chassis_no;
            qr_data.engine_no = doc.engine_no || '';
            qr_data.car_model = doc.car_model || '';
            qr_data.car_color = doc.car_color || '';
            qr_data.manufacture_year = doc.manufacture_year || '';
            qr_data.car_brand = doc.car_brand || '';
        }
        
        return JSON.stringify(qr_data, null, 2);
    },
    
    // توليد بيانات ZATCA
    generate_zatca_qr_data: function(doc) {
        const company_tax_id = frappe.boot.company_info && frappe.boot.company_info[doc.company] 
            ? frappe.boot.company_info[doc.company].tax_id || ''
            : '';
            
        const zatca_fields = [
            doc.company,                           // اسم البائع
            company_tax_id,                        // الرقم الضريبي
            `${doc.posting_date}T${doc.posting_time}`, // التاريخ والوقت
            doc.grand_total.toString(),            // المبلغ الإجمالي
            (doc.total_taxes_and_charges || 0).toString() // مبلغ الضريبة
        ];
        
        return zatca_fields.join('|');
    },
    
    // توليد QR كود كـ base64
    generate_qr_code: function(data, size = 200) {
        return new Promise((resolve, reject) => {
            if (typeof QRCode === 'undefined') {
                reject('QR Code library not loaded');
                return;
            }
            
            QRCode.toDataURL(data, {
                width: size,
                height: size,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function(err, url) {
                if (err) {
                    reject(err);
                } else {
                    resolve(url);
                }
            });
        });
    },
    
    // إدراج QR في الطباعة
    insert_qr_in_print: function(doc, print_format) {
        const detailed_data = this.generate_invoice_qr_data(doc);
        const zatca_data = this.generate_zatca_qr_data(doc);
        
        // توليد QR كود تفصيلي
        this.generate_qr_code(detailed_data, 120).then(detailed_qr => {
            // توليد QR كود ZATCA
            this.generate_qr_code(zatca_data, 80).then(zatca_qr => {
                // العثور على عنصر QR في الطباعة واستبداله
                const qr_elements = document.querySelectorAll('.qr-placeholder');
                qr_elements.forEach(element => {
                    if (element.dataset.type === 'detailed') {
                        element.innerHTML = `<img src="${detailed_qr}" style="max-width: 120px; max-height: 120px;">`;
                    } else if (element.dataset.type === 'zatca') {
                        element.innerHTML = `<img src="${zatca_qr}" style="max-width: 80px; max-height: 80px;">`;
                    }
                });
            });
        }).catch(error => {
            console.error('QR Code generation failed:', error);
        });
    }
};

// Hook عند طباعة الفاتورة
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // إضافة زر QR Code
        if (frm.doc.docstatus === 1) {
            frm.add_custom_button(__('Generate QR Code'), function() {
                customsmart.qr.show_qr_dialog(frm.doc);
            }, __('Actions'));
        }
    }
});

// حوار إظهار QR Code
customsmart.qr.show_qr_dialog = function(doc) {
    const detailed_data = this.generate_invoice_qr_data(doc);
    const zatca_data = this.generate_zatca_qr_data(doc);
    
    const dialog = new frappe.ui.Dialog({
        title: __('Invoice QR Codes'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'qr_codes',
                options: `
                    <div style="text-align: center;">
                        <h4>QR كود شامل - Detailed QR</h4>
                        <div id="detailed-qr" style="margin: 20px 0;"></div>
                        
                        <h4>QR كود ZATCA - ZATCA QR</h4>
                        <div id="zatca-qr" style="margin: 20px 0;"></div>
                        
                        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                            <h5>بيانات QR الشامل:</h5>
                            <pre style="text-align: left; font-size: 10px;">${detailed_data}</pre>
                        </div>
                        
                        <div style="margin-top: 15px; padding: 15px; background-color: #e3f2fd; border-radius: 5px;">
                            <h5>بيانات ZATCA:</h5>
                            <pre style="text-align: left; font-size: 12px;">${zatca_data}</pre>
                        </div>
                    </div>
                `
            }
        ],
        primary_action_label: __('Close'),
        primary_action: function() {
            dialog.hide();
        }
    });
    
    dialog.show();
    
    // توليد QR codes بعد إظهار الحوار
    setTimeout(() => {
        customsmart.qr.generate_qr_code(detailed_data, 200).then(qr => {
            document.getElementById('detailed-qr').innerHTML = `<img src="${qr}" style="max-width: 200px;">`;
        });
        
        customsmart.qr.generate_qr_code(zatca_data, 150).then(qr => {
            document.getElementById('zatca-qr').innerHTML = `<img src="${qr}" style="max-width: 150px;">`;
        });
    }, 100);
};