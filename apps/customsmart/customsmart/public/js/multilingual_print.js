/**
 * JavaScript للطباعة متعددة اللغات
 * يضيف خيارات اللغة لفواتير المبيعات
 */

frappe.ui.form.on('Sales Invoice', {
    onload: function(frm) {
        // إضافة أزرار الطباعة متعددة اللغات
        add_multilingual_print_buttons(frm);
    },
    
    refresh: function(frm) {
        // إضافة أزرار الطباعة متعددة اللغات
        add_multilingual_print_buttons(frm);
    }
});

function add_multilingual_print_buttons(frm) {
    if (frm.doc.docstatus === 1) { // فقط للمستندات المؤكدة
        
        // زر الطباعة بالعربية
        frm.add_custom_button(__('Print in Arabic'), function() {
            print_invoice_with_language(frm, 'ar');
        }, __('Print'));
        
        // زر الطباعة بالإنجليزية
        frm.add_custom_button(__('Print in English'), function() {
            print_invoice_with_language(frm, 'en');
        }, __('Print'));
        
        // زر معاينة بالعربية
        frm.add_custom_button(__('Preview Arabic'), function() {
            preview_invoice_with_language(frm, 'ar');
        }, __('Print'));
        
        // زر معاينة بالإنجليزية
        frm.add_custom_button(__('Preview English'), function() {
            preview_invoice_with_language(frm, 'en');
        }, __('Print'));
    }
}

function print_invoice_with_language(frm, language) {
    const print_format = 'Multilingual Car Invoice';
    
    // إنشاء URL للطباعة مع اللغة المحددة
    const print_url = frappe.urllib.get_full_url(
        `/api/method/frappe.utils.print_format.download_pdf?` +
        `doctype=Sales%20Invoice&` +
        `name=${encodeURIComponent(frm.doc.name)}&` +
        `format=${encodeURIComponent(print_format)}&` +
        `lang=${language}&` +
        `no_letterhead=0`
    );
    
    // فتح نافذة طباعة جديدة
    const print_window = window.open(print_url, '_blank');
    
    // إظهار رسالة تأكيد
    frappe.msgprint({
        title: __('Printing'),
        message: `${__('Invoice is being prepared for printing in')} ${language === 'ar' ? 'العربية' : 'English'}`,
        indicator: 'blue'
    });
}

function preview_invoice_with_language(frm, language) {
    const print_format = 'Multilingual Car Invoice';
    
    // إنشاء URL للمعاينة مع اللغة المحددة
    const preview_url = frappe.urllib.get_full_url(
        `/printview?` +
        `doctype=Sales%20Invoice&` +
        `name=${encodeURIComponent(frm.doc.name)}&` +
        `format=${encodeURIComponent(print_format)}&` +
        `lang=${language}&` +
        `no_letterhead=0&` +
        `letterhead=${encodeURIComponent(frm.doc.letter_head || '')}&` +
        `settings={}`
    );
    
    // فتح نافذة معاينة جديدة
    window.open(preview_url, '_blank', 'width=1024,height=768,scrollbars=yes,resizable=yes');
    
    // إظهار رسالة تأكيد
    frappe.msgprint({
        title: __('Preview'),
        message: `${__('Opening invoice preview in')} ${language === 'ar' ? 'العربية' : 'English'}`,
        indicator: 'green'
    });
}

// إضافة ترجمات
frappe.provide('frappe._messages');

$.extend(frappe._messages, {
    'Print in Arabic': 'طباعة بالعربية',
    'Print in English': 'طباعة بالإنجليزية', 
    'Preview Arabic': 'معاينة بالعربية',
    'Preview English': 'معاينة بالإنجليزية',
    'Printing': 'جاري الطباعة',
    'Preview': 'معاينة',
    'Invoice is being prepared for printing in': 'جاري إعداد الفاتورة للطباعة باللغة',
    'Opening invoice preview in': 'فتح معاينة الفاتورة باللغة'
});

// إضافة ستايل مخصص للأزرار
frappe.ready(function() {
    $('<style>').text(`
        .btn-group .btn[data-label*="Arabic"] {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            color: white !important;
        }
        
        .btn-group .btn[data-label*="English"] {
            background-color: #007bff !important;
            border-color: #007bff !important;
            color: white !important;
        }
        
        .btn-group .btn[data-label*="Preview"] {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: white !important;
        }
    `).appendTo('head');
});