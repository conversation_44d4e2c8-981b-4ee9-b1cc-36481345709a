// Car Showroom Sales Invoice Customizations
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // Add custom buttons
        if (frm.doc.docstatus === 1) {
            frm.add_custom_button(__('Print Car Invoice'), function() {
                frappe.utils.print(
                    frm.doc.doctype,
                    frm.doc.name,
                    'Car Showroom Tax Invoice'
                );
            }, __('Print'));
        }
        
        // Hide inappropriate fields for car showroom
        frm.toggle_display('update_stock', false);
        frm.toggle_display('is_pos', false);
        
        // Update field labels with translation support
        frm.set_df_property('customer', 'label', __('Customer'));
        frm.set_df_property('posting_date', 'label', __('Invoice Date'));
        
        // Add dashboard message for car invoice
        if (!frm.doc.__islocal && frm.doc.chassis_no) {
            frm.dashboard.set_headline(
                `🚗 ${__('Car Sale Invoice')} - ${__('Chassis No')}: ${frm.doc.chassis_no}`
            );
        }
    },
    
    customer: function(frm) {
        // Auto-update customer data
        if (frm.doc.customer) {
            frappe.call({
                method: 'frappe.client.get',
                args: {
                    doctype: 'Customer',
                    name: frm.doc.customer
                },
                callback: function(r) {
                    if (r.message) {
                        frm.set_value('customer_name', r.message.customer_name);
                    }
                }
            });
        }
    },
    
    // Update car details from first item
    items_add: function(frm, cdt, cdn) {
        update_car_details_from_item(frm, cdt, cdn);
    },
    
    validate: function(frm) {
        // Check for car details completion
        if (frm.doc.items) {
            frm.doc.items.forEach(function(item) {
                if (item.car_record && (!item.chassis_number || !item.engine_number)) {
                    frappe.msgprint({
                        title: __('Missing Car Details'),
                        message: __('Please ensure car details are properly linked for item: {0}', [item.item_code]),
                        indicator: 'red'
                    });
                }
            });
        }
    }
});

frappe.ui.form.on('Sales Invoice Item', {
    item_code: function(frm, cdt, cdn) {
        var row = locals[cdt][cdn];
        if (row.item_code) {
            // Search for car linked to item
            frappe.call({
                method: 'frappe.client.get_list',
                args: {
                    doctype: 'Car Record',
                    filters: {
                        'item_code': row.item_code,
                        'status': 'Available'
                    },
                    fields: ['name', 'chassis_no', 'engine_no', 'car_color', 'car_model', 'manufacture_year', 'selling_price']
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        var car = r.message[0];
                        frappe.model.set_value(cdt, cdn, 'car_record', car.name);
                        frappe.model.set_value(cdt, cdn, 'chassis_number', car.chassis_no);
                        frappe.model.set_value(cdt, cdn, 'engine_number', car.engine_no);
                        frappe.model.set_value(cdt, cdn, 'car_color_item', car.car_color);
                        frappe.model.set_value(cdt, cdn, 'rate', car.selling_price);
                        
                        // Update car details in invoice header
                        update_invoice_car_details(frm, car);
                        
                        // Show success message
                        frappe.show_alert({
                            message: __('Car linked successfully') + ': ' + car.chassis_no,
                            indicator: 'green'
                        });
                    } else {
                        frappe.show_alert({
                            message: __('Car not found for this item'),
                            indicator: 'orange'
                        });
                    }
                }
            });
        }
    },
    
    car_record: function(frm, cdt, cdn) {
        var row = locals[cdt][cdn];
        if (row.car_record) {
            frappe.call({
                method: 'frappe.client.get',
                args: {
                    doctype: 'Car Record',
                    name: row.car_record
                },
                callback: function(r) {
                    if (r.message) {
                        var car = r.message;
                        frappe.model.set_value(cdt, cdn, 'chassis_number', car.chassis_no);
                        frappe.model.set_value(cdt, cdn, 'engine_number', car.engine_no);
                        frappe.model.set_value(cdt, cdn, 'car_color_item', car.car_color);
                        
                        // Update car details in invoice header
                        update_invoice_car_details(frm, car);
                    }
                }
            });
        }
    }
});

function update_invoice_car_details(frm, car) {
    // Update car details in invoice header
    if (car) {
        frm.set_value('chassis_no', car.chassis_no);
        frm.set_value('engine_no', car.engine_no);
        frm.set_value('car_color', car.car_color);
        frm.set_value('car_model', car.car_model);
        frm.set_value('manufacture_year', car.manufacture_year);
    }
}

function update_car_details_from_item(frm, cdt, cdn) {
    var row = locals[cdt][cdn];
    if (row.car_record) {
        frappe.call({
            method: 'frappe.client.get',
            args: {
                doctype: 'Car Record',
                name: row.car_record
            },
            callback: function(r) {
                if (r.message) {
                    update_invoice_car_details(frm, r.message);
                }
            }
        });
    }
}

// Additional print customizations
frappe.ui.form.on('Sales Invoice', {
    before_print: function(frm) {
        // Set custom print format as default
        frm.print_preview.print_format = 'Car Showroom Tax Invoice';
    }
});