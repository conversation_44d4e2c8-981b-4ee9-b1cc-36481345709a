#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تثبيت تخصيصات معرض السيارات
Car Showroom Customizations Installer
"""

import frappe
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields

def install_car_showroom_customizations():
    """تثبيت جميع تخصيصات معرض السيارات"""
    
    print("🚀 بدء تثبيت تخصيصات معرض السيارات...")
    print("🚀 Starting Car Showroom Customizations Installation...")
    
    try:
        # 1. إنشاء DocTypes المطلوبة أولاً
        from customsmart.car_record_doctype import install_car_doctypes
        print("📋 إنشاء DocTypes المطلوبة...")
        install_car_doctypes()
        
        # 2. إنشاء الحقول المخصصة
        create_custom_fields_for_car_showroom()
        
        # 3. إنشاء نموذج الطباعة
        create_car_showroom_print_format()
        
        # 4. إن<PERSON><PERSON><PERSON> Client Script
        create_sales_invoice_client_script()
        
        # 5. تطبيق الإعدادات
        frappe.db.commit()
        
        print("✅ تم تثبيت جميع تخصيصات معرض السيارات بنجاح!")
        print("✅ Car Showroom Customizations Installed Successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {str(e)}")
        print(f"❌ Installation Error: {str(e)}")
        frappe.db.rollback()
        return False

def create_custom_fields_for_car_showroom():
    """إنشاء الحقول المخصصة"""
    
    print("📝 إنشاء الحقول المخصصة...")
    
    car_showroom_fields = {
        "Sales Invoice": [
            {
                "fieldname": "car_details_section",
                "label": "Car Details",
                "fieldtype": "Section Break",
                "insert_after": "customer_address",
                "collapsible": 1,
                "collapsible_depends_on": "eval:doc.chassis_no || doc.engine_no"
            },
            {
                "fieldname": "chassis_no",
                "label": "Chassis Number",
                "fieldtype": "Data",
                "insert_after": "car_details_section",
                "translatable": 0,
                "bold": 1
            },
            {
                "fieldname": "engine_no", 
                "label": "Engine Number",
                "fieldtype": "Data",
                "insert_after": "chassis_no",
                "translatable": 0,
                "bold": 1
            },
            {
                "fieldname": "col_break_car_1",
                "fieldtype": "Column Break",
                "insert_after": "engine_no"
            },
            {
                "fieldname": "car_color",
                "label": "Car Color", 
                "fieldtype": "Data",
                "insert_after": "col_break_car_1",
                "translatable": 0
            },
            {
                "fieldname": "car_model",
                "label": "Car Model",
                "fieldtype": "Data", 
                "insert_after": "car_color",
                "translatable": 0
            },
            {
                "fieldname": "col_break_car_2",
                "fieldtype": "Column Break",
                "insert_after": "car_model"
            },
            {
                "fieldname": "manufacture_year",
                "label": "Manufacture Year",
                "fieldtype": "Int",
                "insert_after": "col_break_car_2"
            },
            {
                "fieldname": "showroom_name",
                "label": "Showroom Name",
                "fieldtype": "Data",
                "insert_after": "manufacture_year",
                "translatable": 0
            }
        ],
        "Sales Invoice Item": [
            {
                "fieldname": "car_record",
                "label": "Car Record",
                "fieldtype": "Link",
                "options": "Car Record",
                "insert_after": "item_code",
                "read_only": 1,
                "in_list_view": 1
            },
            {
                "fieldname": "chassis_number",
                "label": "Chassis No",
                "fieldtype": "Data",
                "insert_after": "car_record",
                "read_only": 1,
                "fetch_from": "car_record.chassis_no",
                "in_list_view": 1
            },
            {
                "fieldname": "engine_number",
                "label": "Engine No", 
                "fieldtype": "Data",
                "insert_after": "chassis_number",
                "read_only": 1,
                "fetch_from": "car_record.engine_no"
            },
            {
                "fieldname": "car_color_item",
                "label": "Car Color",
                "fieldtype": "Data",
                "insert_after": "engine_number",
                "read_only": 1,
                "fetch_from": "car_record.car_color",
                "in_list_view": 1
            }
        ]
    }
    
    create_custom_fields(car_showroom_fields, update=True)
    print("✅ تم إنشاء الحقول المخصصة")

def create_car_showroom_print_format():
    """إنشاء نموذج الطباعة المخصص"""
    
    print("🖨️  إنشاء نموذج الطباعة...")
    
    if frappe.db.exists("Print Format", "Car Showroom Tax Invoice"):
        print("⚠️  نموذج الطباعة موجود بالفعل، سيتم التحديث...")
        frappe.delete_doc("Print Format", "Car Showroom Tax Invoice")
    
    print_format_html = '''
    <div class="print-format">
        <style>
            .print-format {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                direction: rtl;
                text-align: right;
                color: #333;
                line-height: 1.6;
            }
            .header {
                text-align: center;
                border-bottom: 3px solid #2c5aa0;
                padding-bottom: 20px;
                margin-bottom: 30px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px 10px 0 0;
            }
            .company-name {
                font-size: 32px;
                font-weight: bold;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .invoice-title {
                font-size: 26px;
                color: #ffd700;
                font-weight: bold;
                margin: 15px 0;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
            .invoice-number {
                font-size: 16px;
                background-color: rgba(255,255,255,0.2);
                padding: 10px;
                border-radius: 5px;
                margin-top: 15px;
            }
            .invoice-details {
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
                gap: 20px;
            }
            .customer-details, .invoice-info {
                width: 48%;
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
            .section-title {
                font-size: 18px;
                font-weight: bold;
                color: #2c5aa0;
                border-bottom: 2px solid #2c5aa0;
                padding-bottom: 8px;
                margin-bottom: 15px;
                text-align: center;
            }
            .detail-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px solid #eee;
            }
            .detail-item:last-child {
                border-bottom: none;
            }
            .car-details {
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 25px;
                border: 2px solid #2196f3;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .car-info-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }
            .car-info-item {
                display: flex;
                justify-content: space-between;
                padding: 12px;
                background-color: white;
                border-radius: 8px;
                border: 1px solid #ddd;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            .car-info-item strong {
                color: #2c5aa0;
            }
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
                font-size: 14px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                border-radius: 8px;
                overflow: hidden;
            }
            .items-table th, .items-table td {
                border: 1px solid #ddd;
                padding: 15px 12px;
                text-align: center;
            }
            .items-table th {
                background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
                color: white;
                font-weight: bold;
                font-size: 15px;
            }
            .items-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .items-table tr:hover {
                background-color: #e3f2fd;
            }
            .totals-section {
                float: left;
                width: 350px;
                margin-top: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .total-row {
                display: flex;
                justify-content: space-between;
                padding: 12px 20px;
                border-bottom: 1px solid #ddd;
                font-size: 15px;
            }
            .total-row:last-child {
                border-bottom: none;
            }
            .grand-total {
                background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
                color: white;
                font-weight: bold;
                font-size: 18px;
            }
            .footer {
                margin-top: 60px;
                border-top: 3px solid #2c5aa0;
                padding-top: 30px;
                text-align: center;
            }
            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 50px;
                gap: 20px;
            }
            .signature-box {
                width: 250px;
                text-align: center;
                border-top: 2px solid #333;
                padding-top: 15px;
                font-weight: bold;
            }
            .tax-info {
                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                border: 2px solid #ffc107;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 25px;
                text-align: center;
                font-weight: bold;
            }
            .thank-you {
                font-size: 20px;
                color: #2c5aa0;
                font-weight: bold;
                margin-bottom: 20px;
            }
            @media print {
                .print-format {
                    margin: 0;
                    padding: 10px;
                }
                .header {
                    -webkit-print-color-adjust: exact;
                    color-adjust: exact;
                }
            }
        </style>
        
        <div class="header">
            <div class="company-name">{{ doc.company }}</div>
            <div style="font-size: 16px; margin: 10px 0;">
                🚗 معرض السيارات الممتاز | Premium Car Showroom 🚗
            </div>
            <div class="invoice-title">
                📋 فاتورة ضريبية | Tax Invoice 📋
            </div>
            <div class="invoice-number">
                رقم الفاتورة: {{ doc.name }} | Invoice No: {{ doc.name }}
            </div>
        </div>

        <div class="invoice-details">
            <div class="customer-details">
                <div class="section-title">👤 بيانات العميل | Customer Details</div>
                <div class="detail-item">
                    <strong>الاسم | Name:</strong>
                    <span>{{ doc.customer_name }}</span>
                </div>
                <div class="detail-item">
                    <strong>كود العميل | Customer Code:</strong>
                    <span>{{ doc.customer }}</span>
                </div>
                {% if doc.customer_address %}
                <div class="detail-item">
                    <strong>العنوان | Address:</strong>
                    <span>{{ doc.customer_address }}</span>
                </div>
                {% endif %}
                {% if doc.contact_mobile %}
                <div class="detail-item">
                    <strong>الهاتف | Phone:</strong>
                    <span>{{ doc.contact_mobile }}</span>
                </div>
                {% endif %}
                {% if doc.tax_id %}
                <div class="detail-item">
                    <strong>الرقم الضريبي | Tax ID:</strong>
                    <span>{{ doc.tax_id }}</span>
                </div>
                {% endif %}
            </div>
            <div class="invoice-info">
                <div class="section-title">📄 بيانات الفاتورة | Invoice Info</div>
                <div class="detail-item">
                    <strong>التاريخ | Date:</strong>
                    <span>{{ frappe.utils.formatdate(doc.posting_date, "dd-MM-yyyy") }}</span>
                </div>
                <div class="detail-item">
                    <strong>الوقت | Time:</strong>
                    <span>{{ doc.posting_time }}</span>
                </div>
                <div class="detail-item">
                    <strong>الحالة | Status:</strong>
                    <span>{{ doc.status }}</span>
                </div>
                {% if doc.mode_of_payment %}
                <div class="detail-item">
                    <strong>طريقة الدفع | Payment:</strong>
                    <span>{{ doc.mode_of_payment }}</span>
                </div>
                {% endif %}
                {% if doc.due_date %}
                <div class="detail-item">
                    <strong>تاريخ الاستحقاق | Due Date:</strong>
                    <span>{{ frappe.utils.formatdate(doc.due_date, "dd-MM-yyyy") }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        {% if doc.chassis_no or doc.engine_no or doc.car_color or doc.car_model or doc.manufacture_year %}
        <div class="car-details">
            <div class="section-title">🚗 تفاصيل السيارة | Car Details</div>
            <div class="car-info-grid">
                {% if doc.chassis_no %}
                <div class="car-info-item">
                    <strong>🔢 رقم الهيكل | Chassis:</strong>
                    <span>{{ doc.chassis_no }}</span>
                </div>
                {% endif %}
                {% if doc.engine_no %}
                <div class="car-info-item">
                    <strong>⚙️ رقم المحرك | Engine:</strong>
                    <span>{{ doc.engine_no }}</span>
                </div>
                {% endif %}
                {% if doc.car_color %}
                <div class="car-info-item">
                    <strong>🎨 اللون | Color:</strong>
                    <span>{{ doc.car_color }}</span>
                </div>
                {% endif %}
                {% if doc.car_model %}
                <div class="car-info-item">
                    <strong>🚙 الموديل | Model:</strong>
                    <span>{{ doc.car_model }}</span>
                </div>
                {% endif %}
                {% if doc.manufacture_year %}
                <div class="car-info-item">
                    <strong>📅 سنة الصنع | Year:</strong>
                    <span>{{ doc.manufacture_year }}</span>
                </div>
                {% endif %}
                {% if doc.showroom_name %}
                <div class="car-info-item">
                    <strong>🏢 المعرض | Showroom:</strong>
                    <span>{{ doc.showroom_name }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%">م | #</th>
                    <th style="width: 15%">كود الصنف | Item Code</th>
                    <th style="width: 25%">الوصف | Description</th>
                    <th style="width: 8%">الكمية | Qty</th>
                    <th style="width: 12%">السعر | Rate</th>
                    <th style="width: 12%">المبلغ | Amount</th>
                    {% if doc.items[0].chassis_number %}
                    <th style="width: 12%">رقم الهيكل | Chassis</th>
                    {% endif %}
                    {% if doc.items[0].car_color_item %}
                    <th style="width: 10%">اللون | Color</th>
                     {% if doc.taxes[0].chassis_number %}
                    <th style="width: 12%">Tax Amount</th
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in doc.items %}
                <tr>
                    <td><strong>{{ loop.index }}</strong></td>
                    <td>{{ item.item_code }}</td>
                    <td style="text-align: right;">{{ item.description or item.item_name }}</td>
                    <td><strong>{{ item.qty }}</strong></td>
                    <td>{{ "{:,.2f}".format(item.rate) }}</td>
                    <td><strong>{{ "{:,.2f}".format(item.amount) }}</strong></td>
                    {% if item.chassis_number %}
                    <td>{{ item.chassis_number }}</td>
                    {% endif %}
                    {% if item.car_color_item %}
                    <td>{{ item.car_color_item }}</td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="totals-section">
            <div class="total-row">
                <span><strong>المجموع الفرعي | Subtotal:</strong></span>
                <span><strong>{{ "{:,.2f}".format(doc.net_total) }}</strong></span>
            </div>
            {% if doc.total_taxes_and_charges %}
            <div class="total-row">
                <span><strong>الضريبة | Tax ({{ doc.taxes[0].rate if doc.taxes else '15' }}%):</strong></span>
                <span><strong>{{ "{:,.2f}".format(doc.total_taxes_and_charges) }}</strong></span>
            </div>
            {% endif %}
            {% if doc.discount_amount %}
            <div class="total-row">
                <span><strong>الخصم | Discount:</strong></span>
                <span><strong>-{{ "{:,.2f}".format(doc.discount_amount) }}</strong></span>
            </div>
            {% endif %}
            <div class="total-row grand-total">
                <span><strong>💰 المجموع الكلي | Grand Total:</strong></span>
                <span><strong>{{ "{:,.2f}".format(doc.grand_total) }} ريال</strong></span>
            </div>
        </div>

        <div style="clear: both;"></div>

        {% if doc.total_taxes_and_charges %}
        <div class="tax-info">
            <strong>ℹ️ معلومات الضريبة | Tax Information</strong><br>
            هذه فاتورة ضريبية صادرة وفقاً لأنظمة ضريبة القيمة المضافة في المملكة العربية السعودية
            <br>
            This is a tax invoice issued in accordance with VAT regulations in the Kingdom of Saudi Arabia
        </div>
        {% endif %}

        <div class="footer">
            <div class="thank-you">
                🙏 شكراً لاختياركم معرضنا | Thank you for choosing our showroom 🙏
            </div>
            
            <div class="signature-section">
                <div class="signature-box">
                    <div>✍️ توقيع العميل</div>
                    <div>Customer Signature</div>
                </div>
                <div class="signature-box">
                    <div>✍️ توقيع المعرض</div>
                    <div>Showroom Signature</div>
                </div>
            </div>
            
            <div style="margin-top: 40px; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 20px;">
                <strong>💻 تم إنشاء هذه الفاتورة بواسطة نظام ERPNext | Generated by ERPNext System</strong><br>
                📧 للاستعلامات: <EMAIL> | For inquiries: <EMAIL><br>
                📞 هاتف: +966-11-1234567 | Phone: +966-11-1234567
            </div>
        </div>
    </div>
    '''
    
    print_format = frappe.get_doc({
        "doctype": "Print Format",
        "name": "Car Showroom Tax Invoice",
        "doc_type": "Sales Invoice", 
        "print_format_builder": 0,
        "standard": "No",
        "custom_format": 1,
        "html": print_format_html,
        "print_format_type": "Jinja",
        "font_size": 12,
        "show_section_headings": 1,
        "line_breaks": 1,
        "absolute_value": 0,
        "align_labels_right": 1,
        "raw_printing": 0,
        "default_print_language": "ar"
    })
    
    print_format.insert(ignore_permissions=True)
    print("✅ تم إنشاء نموذج الطباعة")

def create_sales_invoice_client_script():
    """إنشاء Client Script للتحكم في واجهة المستخدم"""
    
    print("⚡ إنشاء Client Script...")
    
    if frappe.db.exists("Client Script", "Sales Invoice Car Showroom"):
        frappe.delete_doc("Client Script", "Sales Invoice Car Showroom")
    
    client_script_code = """
// Car Showroom Sales Invoice Customizations
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // Add custom print button
        if (frm.doc.docstatus === 1) {
            frm.add_custom_button(__('🖨️ Print Car Invoice'), function() {
                frappe.utils.print(
                    frm.doc.doctype,
                    frm.doc.name,
                    'Car Showroom Tax Invoice'
                );
            }, __('Print'));
        }
        
        // Improve field display
        frm.toggle_display('update_stock', false);
        frm.toggle_display('is_pos', false);
        
        // Add car invoice dashboard message
        if (!frm.doc.__islocal && frm.doc.chassis_no) {
            frm.dashboard.set_headline(
                `🚗 ${__('Car Sale Invoice')} - ${__('Chassis No')}: ${frm.doc.chassis_no}`
            );
        }
    },
    
    customer: function(frm) {
        if (frm.doc.customer) {
            frappe.call({
                method: 'frappe.client.get',
                args: {
                    doctype: 'Customer',
                    name: frm.doc.customer
                },
                callback: function(r) {
                    if (r.message) {
                        frm.set_value('customer_name', r.message.customer_name);
                    }
                }
            });
        }
    }
});

frappe.ui.form.on('Sales Invoice Item', {
    item_code: function(frm, cdt, cdn) {
        var row = locals[cdt][cdn];
        if (row.item_code) {
            frappe.call({
                method: 'frappe.client.get_list',
                args: {
                    doctype: 'Car Record',
                    filters: {
                        'item_code': row.item_code,
                        'status': 'Available'
                    },
                    fields: ['name', 'chassis_no', 'engine_no', 'car_color', 'car_model', 'manufacture_year', 'selling_price']
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        var car = r.message[0];
                        frappe.model.set_value(cdt, cdn, 'car_record', car.name);
                        frappe.model.set_value(cdt, cdn, 'chassis_number', car.chassis_no);
                        frappe.model.set_value(cdt, cdn, 'engine_number', car.engine_no);
                        frappe.model.set_value(cdt, cdn, 'car_color_item', car.car_color);
                        frappe.model.set_value(cdt, cdn, 'rate', car.selling_price);
                        
                        // Update car details in invoice header
                        frm.set_value('chassis_no', car.chassis_no);
                        frm.set_value('engine_no', car.engine_no);
                        frm.set_value('car_color', car.car_color);
                        frm.set_value('car_model', car.car_model);
                        frm.set_value('manufacture_year', car.manufacture_year);
                        
                        frappe.show_alert({
                            message: `${__('Car linked successfully')}: ${car.chassis_no}`,
                            indicator: 'green'
                        });
                    } else {
                        frappe.show_alert({
                            message: __('Car not found for this item'),
                            indicator: 'orange'
                        });
                    }
                }
            });
        }
    }
});
"""
    
    client_script = frappe.get_doc({
        "doctype": "Client Script",
        "name": "Sales Invoice Car Showroom",
        "dt": "Sales Invoice",
        "view": "Form",
        "script": client_script_code,
        "enabled": 1
    })
    
    client_script.insert(ignore_permissions=True)
    print("✅ تم إنشاء Client Script")

if __name__ == "__main__":
    install_car_showroom_customizations()