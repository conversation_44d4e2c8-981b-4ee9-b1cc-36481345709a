# -*- coding: utf-8 -*-

import frappe

def safe_workspace_fix():
    """حل آمن لمشكلة اختفاء Workspaces"""
    
    try:
        print("🔧 بدء إصلاح مشكلة Workspaces...")
        
        # الخطوة 1: تحديث جميع workspaces لتكون عامة
        frappe.db.sql("""
            UPDATE `tabWorkspace` 
            SET public = 1, 
                for_user = '',
                parent_page = ''
            WHERE name IN (
                'Home', 'Accounting', 'Selling', 'Buying', 'Stock', 
                'Assets', 'Projects', 'CRM', 'Support', 'HR', 
                'Manufacturing', 'Website', 'Tools', 'Settings'
            )
        """)
        
        # الخطوة 2: إعادة ترتيب Workspaces
        workspace_order = [
            ("Home", 1), ("Accounting", 2), ("Selling", 3), ("Buying", 4), 
            ("Stock", 5), ("Assets", 6), ("Projects", 7), ("CRM", 8), 
            ("Support", 9), ("HR", 10), ("Manufacturing", 11), 
            ("Website", 12), ("Tools", 13), ("Settings", 14)
        ]
        
        for workspace, sequence in workspace_order:
            if frappe.db.exists("Workspace", workspace):
                frappe.db.set_value("Workspace", workspace, "sequence_id", sequence)
                print(f"✅ تم تحديث {workspace}")
        
        # الخطوة 3: إزالة قيود المجال
        frappe.db.sql("""
            UPDATE `tabWorkspace` 
            SET restrict_to_domain = ''
            WHERE name IN (
                'Home', 'Accounting', 'Selling', 'Buying', 'Stock',
                'Assets', 'Projects', 'CRM', 'Support', 'HR',
                'Manufacturing', 'Website', 'Tools', 'Settings'
            )
        """)
        
        # الخطوة 4: commit التغييرات
        frappe.db.commit()
        
        # الخطوة 5: مسح cache
        frappe.clear_cache()
        
        print("✅ تم إصلاح مشكلة Workspaces بنجاح!")
        print("🔄 يرجى إعادة تحميل المتصفح لرؤية النتائج")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {str(e)}")
        frappe.log_error(f"Workspace Fix Error: {str(e)}")
        return False

def check_workspace_status():
    """فحص حالة Workspaces"""
    
    print("📊 فحص حالة Workspaces...")
    
    workspaces = frappe.db.sql("""
        SELECT name, public, for_user, parent_page, sequence_id, restrict_to_domain
        FROM `tabWorkspace`
        WHERE name IN (
            'Home', 'Accounting', 'Selling', 'Buying', 'Stock',
            'Assets', 'Projects', 'CRM', 'Support', 'HR',
            'Manufacturing', 'Website', 'Tools', 'Settings'
        )
        ORDER BY sequence_id
    """, as_dict=True)
    
    for ws in workspaces:
        status = "✅ مرئي" if ws.public else "❌ مخفي"
        print(f"{ws.name}: {status} (sequence: {ws.sequence_id})")
    
    return workspaces

def reset_user_workspace_settings():
    """إعادة تعيين إعدادات workspace للمستخدمين"""
    
    try:
        print("👤 إعادة تعيين إعدادات المستخدمين...")
        
        # حذف تخصيصات workspace للمستخدمين إذا وُجدت
        try:
            frappe.db.sql("DELETE FROM `tabUserSettings` WHERE doctype = 'Workspace'")
        except:
            pass
        
        # حذف workspace customizations إذا وُجدت
        try:
            frappe.db.sql("""
                DELETE FROM `tabWorkspace Settings` 
                WHERE parent IN (
                    SELECT name FROM `tabUser` WHERE enabled = 1
                )
            """)
        except:
            pass
        
        frappe.db.commit()
        print("✅ تم إعادة تعيين إعدادات المستخدمين")
        
    except Exception as e:
        print(f"⚠️ تحذير: {str(e)}")

if __name__ == "__main__":
    # فحص الحالة الحالية
    check_workspace_status()
    
    # تطبيق الإصلاح
    safe_workspace_fix()
    
    # إعادة تعيين إعدادات المستخدمين
    reset_user_workspace_settings()
    
    # فحص الحالة بعد الإصلاح
    print("\n" + "="*50)
    print("حالة Workspaces بعد الإصلاح:")
    check_workspace_status()