# -*- coding: utf-8 -*-

import frappe
import json

def create_tax_invoice_print_format():
    """إنشاء تنسيق فاتورة ضريبية مع QR Code"""
    
    print("📄 إنشاء تنسيق الفاتورة الضريبية...")
    
    # HTML للفاتورة الضريبية مع QR
    html_content = """
<style>
    body {
        font-family: Arial, sans-serif;
        direction: rtl;
        font-size: 14px;
        margin: 0;
        padding: 0;
    }
    .header, .footer {
        padding: 5px 10px;
    }
    .invoice-title {
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin: 15px 0;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 5px;
    }
    th, td {
        border: 1px solid #000;
        padding: 5px;
        text-align: center;
    }
    .totals {
        margin-top: 10px;
        width: 40%;
        float: left;
    }
    .qr-code {
        text-align: center;
        margin: 10px 0;
    }
    .qr-code img {
        max-width: 150px;
        max-height: 150px;
    }
</style>

<div class="header">
  <div style="display: flex; justify-content: space-between; align-items: center; padding: 5px 0; border-bottom: 2px solid black;">
    <div style="text-align: right; padding: 5px; width: 50%; font-size: 14px;">
      <h2 style="margin: 0;">مؤسسة السرعة الفاخرة للسيارات</h2>
      <p style="margin: 0;">
        <strong>سجل تجاري:</strong>7041878401 <br>
        <strong>الرقم الضريبي:</strong> 301377763200003<br>
        <strong>رقم الجوال:</strong> 0555522138
      </p>
    </div>
    <div style="text-align: center; padding-bottom: 5px;">
      <img src="https://shamsfu.newsmart.tech/files/%D8%B4%D8%B9%D8%A7%D8%B1%20%D9%85%D8%B9%D8%B1%D8%B6%20%D8%A7%D9%84%D8%B3%D8%B1%D8%B9%D8%A9%20%D8%A7%D9%84%D9%81%D8%A7%D8%AE%D8%B1%D8%A9.png" alt="شعار الشركة" style="max-height: 200px;">
    </div>
    <div style="text-align: left; padding: 5px; width: 50%; font-size: 13px;">
      <h2 style="margin: 0;">Luxury Speed Establishment Cars</h2>
      <p style="margin: 0;">
        <strong>C.R:</strong> 704187840<br>
        <strong>VAT:</strong> 301377763200003<br>
        <strong>PHONE:</strong> 0555522138
      </p>
    </div>
  </div>
</div>

<div class="invoice-title">فاتورة ضريبية مبسطة</div>

<!-- بيانات العميل -->
<div style="display: flex; justify-content: space-between; align-items: flex-start; margin-top: 10px; font-size: 13px;">
  <div style="text-align: right; width: 50%; padding: 5px;">
    <div><strong>العميل:</strong> {{ doc.customer_name }}</div>
    <div style="margin-top: 5px;"><strong>الرقم الضريبي للعميل:</strong> {{ doc.tax_id or 'غير محدد' }}</div>
  </div>
  <div style="text-align: left; width: 50%; padding: 5px;">
    <div><strong>رقم الفاتورة:</strong> {{ doc.name }}</div>
    <div style="margin-top: 5px;"><strong>التاريخ:</strong> {{ doc.posting_date }}</div>
  </div>
</div>

<!-- QR Code -->
<div class="qr-code">
  <div style="display: inline-block; border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
    <div id="qr-code-container">
      {% if doc.qr_zatca_data %}
        <canvas id="qr-canvas"></canvas>
      {% else %}
        <div style="width: 128px; height: 128px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; font-size: 12px;">
          يرجى حفظ الفاتورة أولاً
        </div>
      {% endif %}
    </div>
    <div style="font-size: 12px; margin-top: 5px;">QR Code للفاتورة</div>
  </div>
</div>

<!-- جدول الأصناف -->
<table>
    <thead>
        <tr>
            <th>م</th>
            <th>رقم الهيكل</th>
            <th>الصنف</th>
            <th>اللون</th>
            <th>الموديل</th>
            <th>الكمية</th>
            <th>السعر</th>
            <th>الإجمالي قبل الضريبة</th>
            <th>الضريبة 15%</th>
            <th>الإجمالي بعد الضريبة</th>
        </tr>
    </thead>
    <tbody>
        {% for item in doc.items %}
        <tr>
            <td>{{ loop.index }}</td>
            <td>{{ item.custom_chassis_no or item.custom_engine_no or '' }}</td>
            <td>{{ item.item_name }}</td>
            <td>{{ item.custom_car_color or '' }}</td>
            <td>{{ item.custom_car_model or '' }}</td>
            <td>{{ item.qty }}</td>
            <td>{{ frappe.utils.fmt_money(item.rate, currency=doc.currency) }}</td>
            <td>{{ frappe.utils.fmt_money(item.amount, currency=doc.currency) }}</td>
            <td>{{ frappe.utils.fmt_money(item.tax_amount or 0, currency=doc.currency) }}</td>
            <td>{{ frappe.utils.fmt_money(item.amount + (item.tax_amount or 0), currency=doc.currency) }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- الإجماليات -->
<div style="font-size: 13px; margin-top: 30px; width: 100%; direction: rtl;">
  <div style="display: flex; justify-content: space-between;">
    <div style="width: 48%;">
      {% set right_items = [
        ("الإجمالي قبل الضريبة", doc.net_total),
        ("ضريبة القيمة المضافة 15%", doc.total_taxes_and_charges),
        ("الإجمالي بعد الضريبة", doc.grand_total),
        ("qيمة الفاتورة الضريبية", doc.grand_total)
      ] %}
      {% for label, value in right_items %}
      <div style="margin-bottom: 6px;">
        <div style="border-bottom: 1px solid black; display: inline-block; padding-bottom: 4px;">
          <span>{{ label }} : {{ frappe.utils.fmt_money(value, currency=doc.currency) }}</span>
        </div>
      </div>
      {% endfor %}
    </div>
    <div style="width: 48%;">
      {% set left_items = [
        ("رسوم إصدار لوحات", doc.custom_plate_fees or 0),
        ("المدفوع", doc.paid_amount or 0),
        ("المتبقي", doc.outstanding_amount or 0),
        ("قيمة الفاتورة النهائي", doc.grand_total)
      ] %}
      {% for label, value in left_items %}
      <div style="margin-bottom: 6px;">
        <div style="border-bottom: 1px solid black; display: inline-block; padding-bottom: 4px;">
          <span>{{ label }} : {{ frappe.utils.fmt_money(value, currency=doc.currency) }}</span>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>

<!-- ذيل الصفحة -->
<div class="footer" style="font-size: 11px; text-align: center; margin-top: 20px; padding-top: 10px; direction: rtl;">
  <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
      <div style="width: 30%; text-align: right;">
          <strong>الموظف</strong><br>
          ___________________
      </div>
      <div style="width: 30%; text-align: center;"></div>
      <div style="width: 30%; text-align: left;">
          <strong>توقيع العميل</strong><br>
          ___________________
      </div>
  </div>
  <div style="margin: 10px 0;">
      لقد استلمت السيارة / السيارات المذكورة أعلاه سليمة، وفعالة، وفي حالة جيدة وكاملة العدة واللوازم، وغير منقوصة، ولا يوجد بها أي عيب من العيوب.<br>
      ولستُ مسؤولًا عن هذه السيارة / السيارات، مسؤولية كاملة من الناحية الأمنية، وهذا إقرار مني بذلك، وعليه أوقع.
  </div>
  <div style="font-weight: bold; margin: 8px 0;">
      ** أي فاتورة لا تحمل ختم وتوقيع صاحب المعرض تعتبر لاغية **
  </div>
  <div style="background-color: #eee; padding: 6px; font-weight: bold;">
      المملكة العربية السعودية - جدة
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // توليد QR Code للفاتورة
    generateInvoiceQR();
});

function generateInvoiceQR() {
    try {
        const qrData = '{{ doc.qr_zatca_data or "" }}';
        
        if (qrData && typeof QRCode !== 'undefined') {
            // إنشاء QR Code من البيانات المحفوظة
            const qrCode = new QRCode(document.getElementById('qr-canvas'), {
                text: qrData,
                width: 128,
                height: 128,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.M
            });
        } else if (!qrData) {
            // إذا لم توجد بيانات QR، إنشاء بيانات مؤقتة
            const tempData = [
                'مؤسسة السرعة الفاخرة للسيارات',
                '301377763200003',
                '{{ doc.posting_date }}T{{ doc.posting_time }}',
                '{{ doc.grand_total }}',
                '{{ doc.total_taxes_and_charges or 0 }}'
            ].join('|');
            
            if (typeof QRCode !== 'undefined') {
                new QRCode(document.getElementById('qr-canvas') || document.getElementById('qr-code-container'), {
                    text: tempData,
                    width: 128,
                    height: 128,
                    colorDark: '#000000',
                    colorLight: '#ffffff'
                });
            }
        }
    } catch (error) {
        console.error('خطأ في توليد QR Code:', error);
    }
}
</script>

<!-- تحميل مكتبة QR Code -->
<script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
"""

    # إنشاء Print Format
    print_format_name = "فاتورة ضريبية"
    
    if frappe.db.exists("Print Format", print_format_name):
        print_format = frappe.get_doc("Print Format", print_format_name)
        print_format.html = html_content
        print_format.save()
        print(f"✅ تم تحديث Print Format: {print_format_name}")
    else:
        print_format = frappe.get_doc({
            "doctype": "Print Format",
            "name": print_format_name,
            "doc_type": "Sales Invoice",
            "print_format_type": "Jinja",
            "html": html_content,
            "standard": "No",
            "disabled": 0,
            "print_format_builder": 0,
            "margin_top": 15,
            "margin_bottom": 15,
            "margin_left": 10,
            "margin_right": 10,
            "page_size": "A4",
            "orientation": "Portrait"
        })
        print_format.insert(ignore_permissions=True)
        print(f"✅ تم إنشاء Print Format: {print_format_name}")
    
    frappe.db.commit()
    return print_format_name

def add_custom_fields_for_tax_invoice():
    """إضافة حقول مخصصة للفاتورة الضريبية"""
    
    print("🔧 إضافة حقول مخصصة للفاتورة الضريبية...")
    
    # حقول إضافية للفاتورة
    custom_fields = [
        {
            "fieldname": "custom_plate_fees",
            "label": "رسوم إصدار لوحات",
            "fieldtype": "Currency",
            "default": 0,
            "insert_after": "total_taxes_and_charges"
        }
    ]
    
    for field in custom_fields:
        if not frappe.db.exists("Custom Field", {"dt": "Sales Invoice", "fieldname": field["fieldname"]}):
            try:
                custom_field = frappe.get_doc({
                    "doctype": "Custom Field",
                    "dt": "Sales Invoice",
                    "fieldname": field["fieldname"],
                    "label": field["label"],
                    "fieldtype": field["fieldtype"],
                    "default": field.get("default", ""),
                    "insert_after": field.get("insert_after", ""),
                    "owner": "Administrator"
                })
                custom_field.insert(ignore_permissions=True)
                print(f"✅ تم إنشاء حقل: {field['fieldname']}")
            except Exception as e:
                print(f"⚠️ خطأ في إنشاء حقل {field['fieldname']}: {str(e)}")
        else:
            print(f"ℹ️ الحقل موجود مسبقاً: {field['fieldname']}")
    
    frappe.db.commit()

def install_tax_invoice_system():
    """تثبيت نظام الفاتورة الضريبية كاملاً"""
    
    print("🚀 تثبيت نظام الفاتورة الضريبية...")
    
    try:
        # 1. إنشاء الحقول المخصصة
        add_custom_fields_for_tax_invoice()
        
        # 2. إنشاء Print Format
        print_format_name = create_tax_invoice_print_format()
        
        # 3. مسح cache
        frappe.clear_cache()
        
        print("✅ تم تثبيت نظام الفاتورة الضريبية بنجاح!")
        print(f"📄 اسم التنسيق: {print_format_name}")
        print("🔍 يمكنك الآن استخدام التنسيق الجديد في طباعة الفواتير")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {str(e)}")
        frappe.log_error(f"Tax Invoice Installation Error: {str(e)}")
        return False

def check_print_formats():
    """فحص Print Formats المتوفرة"""
    
    print("📋 Print Formats للـ Sales Invoice:")
    formats = frappe.get_all('Print Format', 
                            filters={'doc_type': 'Sales Invoice'}, 
                            fields=['name', 'disabled'])
    
    for fmt in formats:
        status = 'مفعل' if not fmt.disabled else 'معطل'
        print(f"- {fmt.name}: {status}")
    
    return formats

if __name__ == "__main__":
    install_tax_invoice_system()