# CustomSmart - Car Showroom ERP Customizations

A comprehensive ERPNext customization app for car showrooms and automotive businesses.

## 🚗 Features

### Sales Invoice Customizations
- **Car Details Fields**: Chassis number, engine number, color, model, manufacture year
- **Auto-linking**: Automatic connection between items and car records
- **Bilingual Support**: Arabic and English labels
- **Validation**: Ensures complete car information before saving

### Professional Print Format
- **Tax Invoice Template**: Beautiful bilingual tax invoice design
- **Car Information Display**: Comprehensive car details section
- **Tax Compliance**: ZATCA-compatible tax information
- **Professional Layout**: Modern design with company branding

### Smart JavaScript Integration
- **Auto-population**: Car details auto-fill from Car Record
- **Custom Buttons**: Quick print button for car invoices
- **User Experience**: Enhanced form interactions
- **Real-time Updates**: Dynamic field updates based on selections

## 📦 Installation

The customizations are already installed on `site1.local`. For other sites:

```bash
# Install customizations
bench --site [your-site] execute customsmart.install_car_showroom.install_car_showroom_customizations

# Restart system
bench restart
```

## 🎯 Usage

1. **Create Sales Invoice**: Go to Sales Invoice → New
2. **Select Customer**: Enter customer details
3. **Add Car Item**: Select car item in items table
4. **Auto-linking**: Car details will populate automatically
5. **Print**: Use "Print Car Invoice" button or select "Car Showroom Tax Invoice" format

## 🔧 Technical Details

### Custom Fields Added
- Sales Invoice: 8 new fields for car details
- Sales Invoice Item: 3 new fields for car linking

### Files Structure
```
customsmart/
├── car_showroom_customizations.py    # Main customization logic
├── install_car_showroom.py          # Installation script
├── public/js/sales_invoice_custom.js # Frontend JavaScript
├── hooks.py                         # ERPNext hooks
└── fixtures/                       # Exported customizations
    ├── custom_field.json
    ├── print_format.json
    ├── client_script.json
    └── property_setter.json
```

## 🌟 Key Components

### Print Format: "Car Showroom Tax Invoice"
- Responsive design with modern styling
- Bilingual (Arabic/English) layout
- Professional car dealership branding
- Tax compliance information
- Signature sections for customer and showroom

### JavaScript Enhancements
- Smart item-to-car linking
- Real-time field updates
- Custom print button
- Form validation and user feedback

## 🛠️ Customization

### Modify Print Format
1. Go to Print Format → Car Showroom Tax Invoice
2. Edit HTML in the format
3. Save changes

### Add New Fields
1. Edit `car_showroom_customizations.py`
2. Add fields to the `car_showroom_fields` dictionary
3. Run installation script to apply changes

### Update JavaScript
1. Edit `public/js/sales_invoice_custom.js`
2. Add new functions or modify existing ones
3. Clear cache and restart

## 📋 Requirements

- ERPNext v13+ 
- Car Record DocType (for auto-linking features)
- customsmart app installed

## 🔗 Integration

Works seamlessly with:
- ERPNext Sales module
- Car Record management system
- Print and PDF generation
- Tax calculation systems

## 📞 Support

For issues or enhancements:
1. Check the troubleshooting section in the Arabic guide
2. Review error logs in ERPNext
3. Test customizations on development site first

## 📄 License

MIT License

---

**🚗 Transform your car showroom operations with professional ERPNext customizations! 🚗**