# دليل تخصيصات معرض السيارات لـ ERPNext
# Car Showroom Customizations Guide for ERPNext

## 📋 نظرة عامة | Overview

تم إنشاء تخصيصات شاملة لنظام ERPNext لإدارة معارض السيارات تشمل:

✅ **تخصيص فواتير المبيعات** - تم إضافة حقول خاصة بتفاصيل السيارات  
✅ **نموذج طباعة مخصص** - فاتورة ضريبية جذابة باللغتين العربية والإنجليزية  
✅ **JavaScript مخصص** - تحسينات لتجربة المستخدم  
✅ **تصدير كامل** - جميع التخصيصات محفوظة في التطبيق المخصص  

---

## 🔧 التخصيصات المضافة | Added Customizations

### 1. الحقول المخصصة في Sales Invoice | Custom Fields in Sales Invoice

#### قسم تفاصيل السيارة | Car Details Section:
- **رقم الهيكل / Chassis Number** (`chassis_no`)
- **رقم المحرك / Engine Number** (`engine_no`) 
- **لون السيارة / Car Color** (`car_color`)
- **الموديل / Model** (`car_model`)
- **سنة الصنع / Manufacture Year** (`manufacture_year`)
- **اسم المعرض / Showroom Name** (`showroom_name`)

#### في جدول الأصناف | In Items Table:
- **سجل السيارة / Car Record** (`car_record`) - ربط مع DocType Car Record
- **رقم الهيكل / Chassis Number** (`chassis_number`) - مجلب من Car Record
- **رقم المحرك / Engine Number** (`engine_number`) - مجلب من Car Record

### 2. نموذج الطباعة | Print Format

**اسم النموذج:** `Car Showroom Tax Invoice`

#### المميزات:
- 🎨 **تصميم حديث وجذاب** بألوان احترافية
- 🌍 **ثنائي اللغة** (عربي/إنجليزي) مع دعم RTL
- 📊 **معلومات شاملة**:
  - بيانات الشركة والعميل
  - تفاصيل السيارة المباعة
  - جدول الأصناف مع الأسعار
  - حساب الضرائب
  - معلومات ضريبية متوافقة مع ZATCA
- ✍️ **أقسام للتوقيع** للعميل والمعرض
- 💻 **محسن للطباعة** مع CSS متجاوب

### 3. JavaScript المخصص | Custom JavaScript

#### الوظائف المضافة:
- 🔗 **ربط تلقائي** بين الأصناف وسجلات السيارات
- 🖨️ **زر طباعة مخصص** لنموذج فاتورة السيارات
- 📝 **تحديث تلقائي** لتفاصيل السيارة عند اختيار الصنف
- ⚠️ **تحققات** من اكتمال بيانات السيارة
- 🎯 **تحسينات UI** لإخفاء الحقول غير المناسبة

---

## 📦 الملفات المنشأة | Created Files

### في التطبيق المخصص `customsmart`:

```
apps/customsmart/customsmart/
├── car_showroom_customizations.py       # وظائف Python للتخصيصات
├── install_car_showroom.py             # سكريبت التثبيت
├── public/js/sales_invoice_custom.js   # JavaScript المخصص
├── hooks.py                            # تم تحديثه بالـ hooks
└── fixtures/                          # ملفات التصدير
    ├── custom_field.json              # الحقول المخصصة
    ├── print_format.json              # نماذج الطباعة
    ├── client_script.json             # سكريبتات العميل
    ├── property_setter.json           # إعدادات الخصائص
    └── server_script.json             # سكريبتات الخادم
```

---

## 🚀 التثبيت والاستخدام | Installation & Usage

### تم التثبيت بالفعل على الموقع `site1.local`

### للتحقق من التثبيت:

1. **افتح ERPNext**
2. **انتقل إلى:** `Sales Invoice` → `New`
3. **ستجد قسم جديد:** "Car Details / تفاصيل السيارة"
4. **للطباعة:** استخدم Print Format: `Car Showroom Tax Invoice`

### لتثبيت على موقع آخر:

```bash
# تشغيل التثبيت
bench --site [site-name] execute customsmart.install_car_showroom.install_car_showroom_customizations

# إعادة تشغيل النظام
bench restart
```

---

## 🎯 كيفية الاستخدام | How to Use

### 1. إنشاء فاتورة مبيعات سيارة:

1. **إنشاء فاتورة جديدة:** `Sales Invoice` → `New`
2. **اختيار العميل:** ضع بيانات العميل
3. **إضافة الصنف:** في جدول الأصناف، اختر صنف السيارة
4. **الربط التلقائي:** سيتم ربط السيارة تلقائياً إذا كان مرتبط بـ Car Record
5. **تعبئة التفاصيل:** ستظهر تفاصيل السيارة تلقائياً
6. **الحفظ والإرسال:** احفظ وأرسل الفاتورة

### 2. الطباعة:

1. **بعد إرسال الفاتورة:** اضغط على زر "Print"
2. **اختيار النموذج:** `Car Showroom Tax Invoice`
3. **أو استخدم الزر المخصص:** "🖨️ Print Car Invoice"

### 3. التخصيص الإضافي:

- **تعديل النموذج:** `Print Format` → `Car Showroom Tax Invoice`
- **تعديل الحقول:** `Customize Form` → `Sales Invoice`
- **تعديل JavaScript:** ملف `sales_invoice_custom.js`

---

## 🔧 إعدادات متقدمة | Advanced Settings

### ربط مع Car Record DocType:

إذا كان لديك DocType اسمه `Car Record`:

```javascript
// مثال لهيكل Car Record المطلوب
{
    "name": "CAR-001",
    "item_code": "TOYOTA-CAMRY-2024",
    "chassis_no": "JTDBF4E2XD5012345", 
    "engine_no": "2AR-FE-123456",
    "car_color": "أبيض",
    "car_model": "كامري 2024",
    "manufacture_year": 2024,
    "selling_price": 120000,
    "status": "متاحة"
}
```

### تخصيص النموذج:

لتعديل نموذج الطباعة:
1. انتقل إلى: `Print Format` → `Car Showroom Tax Invoice`
2. عدل في قسم `HTML`
3. احفظ التغييرات

---

## 🛠️ استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة وحلولها:

#### 1. لا تظهر الحقول الجديدة:
```bash
# إعادة تحديث العرض
bench --site site1.local clear-cache
bench restart
```

#### 2. نموذج الطباعة لا يعمل:
- تأكد من وجود Print Format: `Car Showroom Tax Invoice`
- تحقق من صحة HTML في النموذج

#### 3. JavaScript لا يعمل:
- تحقق من وجود الملف: `public/js/sales_invoice_custom.js`
- تأكد من إعدادات hooks.py

#### 4. إعادة التثبيت:
```bash
bench --site site1.local execute customsmart.install_car_showroom.install_car_showroom_customizations
```

---

## 📞 الدعم والتطوير | Support & Development

### للتطوير الإضافي:

1. **إضافة حقول جديدة:** عدل في `car_showroom_customizations.py`
2. **تحسين النموذج:** عدل HTML في `create_car_showroom_print_format()`
3. **إضافة وظائف JS:** عدل `sales_invoice_custom.js`
4. **تحديث التصدير:** شغل `bench --site site1.local export-fixtures --app customsmart`

### الملفات المهمة:
- **Python Logic:** `customsmart/car_showroom_customizations.py`
- **Print Format:** داخل دالة `create_car_showroom_print_format()`
- **Frontend JS:** `customsmart/public/js/sales_invoice_custom.js`
- **Hooks:** `customsmart/hooks.py`

---

## ✅ تم بنجاح | Successfully Completed

🎉 **جميع التخصيصات تم تثبيتها وتصديرها بنجاح!**

### النتائج:
- ✅ **13 حقل مخصص** تم إضافتها لفاتورة المبيعات
- ✅ **نموذج طباعة احترافي** جاهز للاستخدام
- ✅ **JavaScript مخصص** لتحسين تجربة المستخدم
- ✅ **تصدير كامل** في ملفات fixtures
- ✅ **دليل شامل** للاستخدام والصيانة

### للمراجعة والاختبار:
1. ادخل إلى ERPNext على `site1.local`
2. انتقل إلى `Sales Invoice`
3. أنشئ فاتورة جديدة واختبر الحقول الجديدة
4. جرب الطباعة بالنموذج الجديد

**🚗 معرض السيارات الخاص بك جاهز الآن مع نظام فواتير احترافي! 🚗**

---

تم إنشاء هذا الدليل بواسطة: **Zencoder AI Assistant**  
تاريخ الإنشاء: ديسمبر 2024  
الإصدار: 1.0