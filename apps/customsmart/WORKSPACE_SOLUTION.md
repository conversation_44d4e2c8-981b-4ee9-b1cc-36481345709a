# حل مشكلة اختفاء Workspaces في التطبيق المخصص

## المشكلة
عند تثبيت التطبيق المخصص `customsmart`، تختفي جميع الـ workspaces من القائمة الجانبية في النظام.

## السبب
المشكلة تحدث بسبب تضارب في:
1. إعدادات `fixtures` في ملف `hooks.py`
2. مشاكل في `doc_events` 
3. إعدادات الصلاحيات للـ workspaces

## الحل المطبق

### 1. تم إنشاء ملف الإصلاح الآمن
```python
# apps/customsmart/customsmart/workspace_fix.py
def safe_workspace_fix():
    # تحديث جميع workspaces لتكون عامة
    # إعادة ترتيب الـ workspaces
    # إزالة قيود المجال
    # مسح cache
```

### 2. الأوامر المستخدمة للإصلاح

```bash
# تشغيل الإصلاح
bench --site site1.local execute customsmart.workspace_fix.safe_workspace_fix

# فحص الحالة
bench --site site1.local execute customsmart.workspace_fix.check_workspace_status

# مسح cache
bench --site site1.local clear-cache
bench --site site1.local clear-website-cache

# إعادة بناء التطبيق
bench build --app customsmart
```

### 3. التغييرات في قاعدة البيانات
تم تطبيق التغييرات التالية على جدول `tabWorkspace`:

```sql
UPDATE `tabWorkspace` 
SET public = 1, 
    for_user = '',
    parent_page = '',
    restrict_to_domain = ''
WHERE name IN (
    'Home', 'Accounting', 'Selling', 'Buying', 'Stock', 
    'Assets', 'Projects', 'CRM', 'Support', 'HR', 
    'Manufacturing', 'Website', 'Tools', 'Settings'
);
```

### 4. ترتيب الـ Workspaces
تم إعادة ترتيب الـ workspaces كالتالي:
1. Home
2. Accounting  
3. Selling
4. Buying
5. Stock
6. Assets
7. Projects
8. CRM
9. Support
10. HR
11. Manufacturing
12. Website
13. Tools
14. Settings

## نصائح لتجنب المشكلة مستقبلاً

### 1. تجنب استخدام "Workspace" في fixtures
```python
# في hooks.py - تجنب هذا:
fixtures = [
    "Workspace"  # قد يسبب مشاكل
]

# استخدم هذا بدلاً من ذلك:
fixtures = [
    "Custom Field",
    "Print Format", 
    "Property Setter",
    "Client Script"
]
```

### 2. فحص doc_events
تأكد من أن جميع الدوال المستخدمة في `doc_events` موجودة وتعمل بشكل صحيح.

### 3. اختبار التطبيق قبل النشر
```bash
# اختبار التطبيق
bench --site site1.local migrate
bench --site site1.local clear-cache
bench build --app customsmart
```

## إصلاح سريع في حالة تكرار المشكلة

إذا تكررت المشكلة، يمكن تشغيل هذا الأمر:

```bash
cd /home/<USER>/frappe-bench
bench --site site1.local execute customsmart.workspace_fix.safe_workspace_fix
bench --site site1.local clear-cache
```

## حالة الإصلاح الحالية
✅ تم إصلاح المشكلة بنجاح
✅ جميع الـ workspaces مرئية الآن
✅ تم إعادة ترتيب الـ workspaces بشكل صحيح
✅ تم مسح cache

## ملاحظات إضافية
- لا تحذف ملف `workspace_fix.py` - قد تحتاجه مستقبلاً
- في حالة إضافة مميزات جديدة للتطبيق، تأكد من اختبارها على بيئة تطوير أولاً
- احتفظ بنسخة احتياطية من قاعدة البيانات قبل تطبيق أي تحديثات كبيرة

تاريخ الإصلاح: $(date)
حالة النظام: مستقر ✅