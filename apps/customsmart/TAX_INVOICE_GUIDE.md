# 📄 دليل الفاتورة الضريبية - customsmart

## ✅ تم إنشاء الفاتورة الضريبية بنجاح!

### 🎯 ما تم إنجازه

#### 1. إنشاء Print Format "فاتورة ضريبية"
- ✅ تصميم احترافي باللغة العربية
- ✅ معلومات الشركة (مؤسسة السرعة الفاخرة للسيارات)
- ✅ QR Code متوافق مع ZATCA
- ✅ جدول تفصيلي للأصناف مع بيانات السيارات
- ✅ حساب الضرائب والإجماليات
- ✅ منطقة التوقيعات والشروط

#### 2. إضافة QR Code تلقائي
- ✅ توليد QR عند تأكيد الفاتورة
- ✅ بيانات ZATCA (اسم الشركة، الرقم الضريبي، التاريخ، المبلغ، الضريبة)
- ✅ عرض QR في الفاتورة المطبوعة
- ✅ حفظ بيانات QR في قاعدة البيانات

#### 3. حقول مخصصة جديدة
- ✅ `custom_plate_fees` - رسوم إصدار لوحات
- ✅ `qr_detailed_data` - بيانات QR تفصيلية
- ✅ `qr_zatca_data` - بيانات QR متوافقة مع ZATCA

#### 4. تحسينات تجربة المستخدم
- ✅ زر "طباعة فاتورة ضريبية" مخصص
- ✅ زر "عرض QR Code" في نافذة منبثقة
- ✅ حساب تلقائي لرسوم اللوحات
- ✅ رسائل تأكيد وتنبيهات

## 📋 كيفية الاستخدام

### 1. إنشاء فاتورة مبيعات جديدة
1. اذهب إلى **Sales Invoice** → **New**
2. أدخل بيانات العميل والأصناف
3. أضف رسوم إصدار اللوحات (إذا لزم الأمر)
4. احفظ وأكد الفاتورة (**Submit**)

### 2. طباعة الفاتورة الضريبية
**الطريقة الأولى:**
- انقر على زر **"طباعة فاتورة ضريبية"** في شريط الأزرار

**الطريقة الثانية:**
1. انقر على **Print** → **Print Format**
2. اختر **"فاتورة ضريبية"**
3. انقر على **Print**

### 3. عرض QR Code
- انقر على **Actions** → **"عرض QR Code"**
- سيظهر QR في نافذة منبثقة مع معلومات الفاتورة

## 🔧 المعلومات التقنية

### Print Format المستخدم
- **الاسم**: فاتورة ضريبية
- **النوع**: Jinja Template
- **الحجم**: A4 Portrait
- **الهوامش**: 15mm أعلى/أسفل، 10mm يمين/يسار

### بيانات QR Code
```
مؤسسة السرعة الفاخرة للسيارات|301377763200003|2024-01-01T12:00:00|1000.00|150.00
```

### معلومات الشركة في الفاتورة
- **الاسم العربي**: مؤسسة السرعة الفاخرة للسيارات
- **الاسم الإنجليزي**: Luxury Speed Establishment Cars
- **السجل التجاري**: 7041878401
- **الرقم الضريبي**: 301377763200003
- **الهاتف**: 0555522138

## 📊 جدول الأصناف

يعرض الجدول المعلومات التالية:
- رقم متسلسل
- رقم الهيكل/المحرك
- اسم الصنف
- اللون
- الموديل  
- الكمية
- السعر
- الإجمالي قبل الضريبة
- الضريبة 15%
- الإجمالي بعد الضريبة

## 💰 حساب الإجماليات

### الجانب الأيمن
- الإجمالي قبل الضريبة
- ضريبة القيمة المضافة 15%
- الإجمالي بعد الضريبة
- قيمة الفاتورة الضريبية

### الجانب الأيسر
- رسوم إصدار لوحات
- المدفوع
- المتبقي
- قيمة الفاتورة النهائية

## 🖼️ تخطيط الفاتورة

```
┌─────────────────────────────────────────────┐
│            رأس الفاتورة + الشعار            │
├─────────────────────────────────────────────┤
│              فاتورة ضريبية مبسطة            │
├─────────────────────────────────────────────┤
│  بيانات العميل     │    رقم الفاتورة والتاريخ │
├─────────────────────────────────────────────┤
│                QR Code                      │
├─────────────────────────────────────────────┤
│              جدول الأصناف                  │
├─────────────────────────────────────────────┤
│    الإجماليات    │    رسوم ومدفوعات       │
├─────────────────────────────────────────────┤
│            ذيل الفاتورة والتوقيعات          │
└─────────────────────────────────────────────┘
```

## 🚀 ميزات إضافية

### 1. طباعة مجمعة
- يمكن طباعة عدة فواتير ضريبية مرة واحدة من قائمة الفواتير

### 2. التوافق مع ZATCA
- QR Code متوافق مع متطلبات هيئة الزكاة والضريبة والجمارك

### 3. دعم اللغة العربية
- جميع النصوص باللغة العربية
- تخطيط من اليمين إلى اليسار (RTL)

## 🔍 استكشاف الأخطاء

### مشكلة: QR Code لا يظهر
**الحل:**
1. تأكد من تأكيد الفاتورة (Submit)
2. تحديث الصفحة
3. تأكد من وجود اتصال بالإنترنت لتحميل مكتبة QR

### مشكلة: Print Format غير موجود
**الحل:**
```bash
bench --site site1.local execute customsmart.create_tax_invoice_format.create_tax_invoice_print_format
```

### مشكلة: رسوم اللوحات لا تحسب
**الحل:**
1. تأكد من وجود حقل `custom_plate_fees`
2. احفظ الفاتورة مرة أخرى

## 📞 الدعم الفني

في حالة وجود مشاكل:
1. راجع هذا الدليل
2. تأكد من تحديث التطبيق
3. فحص سجل الأخطاء في ERPNext

---

**🎉 تهانينا! تم إنشاء نظام الفاتورة الضريبية بنجاح مع QR Code!**

التاريخ: $(date)  
الحالة: جاهز للاستخدام ✅