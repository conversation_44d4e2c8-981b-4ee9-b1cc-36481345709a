{
	"branches": ["version-15"],
	"plugins": [
		"@semantic-release/commit-analyzer", {
			"preset": "angular",
			"releaseRules": [
				{"breaking": true, "release": false}
			]
		},
		"@semantic-release/release-notes-generator",
		[
			"@semantic-release/exec", {
				"prepareCmd": 'sed -ir "s/[0-9]*\.[0-9]*\.[0-9]*/${nextRelease.version}/" erpnext/__init__.py'
			}
		],
		[
			"@semantic-release/git", {
				"assets": ["erpnext/__init__.py"],
				"message": "chore(release): Bumped to Version ${nextRelease.version}\n\n${nextRelease.notes}"
			}
		],
		"@semantic-release/github"
	]
}
