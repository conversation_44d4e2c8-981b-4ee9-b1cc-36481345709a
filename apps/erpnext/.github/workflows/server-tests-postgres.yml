name: Server (Postgres)

on:
  pull_request:
    paths-ignore:
      - '**.js'
      - '**.md'
      - '**.html'
    types: [opened, labelled, synchronize, reopened]

concurrency:
  group: server-postgres-develop-${{ github.event_name }}-${{ github.event.number || github.event_name == 'workflow_dispatch' && github.run_id || '' }}
  cancel-in-progress: true

jobs:
  test:
    if: ${{ contains(github.event.pull_request.labels.*.name, 'postgres') }}
    runs-on: ubuntu-latest
    timeout-minutes: 60

    strategy:
      fail-fast: false
      matrix:
       container: [1]

    name: Python Unit Tests

    services:
      postgres:
        image: postgres:13.3
        env:
          POSTGRES_PASSWORD: travis
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:

      - name: Clone
        uses: actions/checkout@v2

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'

      - name: Check for valid Python & Merge Conflicts
        run: |
          python -m compileall -f "${GITHUB_WORKSPACE}"
          if grep -lr --exclude-dir=node_modules "^<<<<<<< " "${GITHUB_WORKSPACE}"
              then echo "Found merge conflicts"
              exit 1
          fi

      - name: Setup Node
        uses: actions/setup-node@v2
        with:
          node-version: 18
          check-latest: true

      - name: Add to Hosts
        run: echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v2
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt', '**/pyproject.toml') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: Cache node modules
        uses: actions/cache@v2
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v2
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install
        run: bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
        env:
          DB: postgres
          TYPE: server

      - name: Run Tests
        run: cd ~/frappe-bench/ && bench --site test_site run-parallel-tests --app erpnext --use-orchestrator
        env:
          TYPE: server
          CI_BUILD_ID: ${{ github.run_id }}
          ORCHESTRATOR_URL: http://test-orchestrator.frappe.io
