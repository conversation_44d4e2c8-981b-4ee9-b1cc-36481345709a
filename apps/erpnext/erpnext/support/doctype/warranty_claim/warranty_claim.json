{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2013-01-10 16:34:30", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["naming_series", "status", "complaint_date", "column_break0", "customer", "serial_no", "section_break_7", "complaint", "issue_details", "item_code", "item_name", "description", "column_break1", "warranty_amc_status", "warranty_expiry_date", "amc_expiry_date", "resolution_section", "resolution_date", "resolved_by", "resolution_details", "contact_info", "customer_name", "contact_person", "contact_display", "contact_mobile", "contact_email", "territory", "customer_group", "col_break4", "customer_address", "address_display", "service_address", "more_info", "company", "col_break6", "complaint_raised_by", "from_company", "amended_from"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "SER-WRN-.YYYY.-", "reqd": 1, "set_only_once": 1}, {"default": "Open", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nOpen\nClosed\nWork In Progress\nCancelled", "reqd": 1, "search_index": 1}, {"default": "Today", "fieldname": "complaint_date", "fieldtype": "Date", "label": "Issue Date", "oldfieldname": "complaint_date", "oldfieldtype": "Date", "reqd": 1, "search_index": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "serial_no", "fieldtype": "Link", "label": "Serial No", "options": "Serial No", "search_index": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "reqd": 1, "search_index": 1}, {"fieldname": "section_break_7", "fieldtype": "Section Break"}, {"fieldname": "complaint", "fieldtype": "Text Editor", "label": "Issue", "no_copy": 1, "oldfieldname": "complaint", "oldfieldtype": "Small Text", "reqd": 1}, {"fieldname": "issue_details", "fieldtype": "Section Break", "label": "Item and Warranty Details", "oldfieldtype": "Section Break", "options": "fa fa-ticket"}, {"fetch_from": "serial_no.item_code", "fetch_if_empty": 1, "fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Item Code", "oldfieldname": "item_code", "oldfieldtype": "Link", "options": "<PERSON><PERSON>", "search_index": 1}, {"depends_on": "eval:doc.item_code", "fetch_from": "item_code.item_name", "fieldname": "item_name", "fieldtype": "Data", "label": "Item Name", "oldfieldname": "item_name", "oldfieldtype": "Data", "read_only": 1}, {"depends_on": "eval:doc.item_code", "fetch_from": "item_code.description", "fieldname": "description", "fieldtype": "Small Text", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "read_only": 1, "width": "300px"}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "width": "50%"}, {"fetch_from": "serial_no.maintenance_status", "fetch_if_empty": 1, "fieldname": "warranty_amc_status", "fieldtype": "Select", "label": "Warranty / AMC Status", "options": "\nUnder Warranty\nOut of Warranty\nUnder AMC\nOut of AMC", "search_index": 1}, {"fetch_from": "serial_no.warranty_expiry_date", "fetch_if_empty": 1, "fieldname": "warranty_expiry_date", "fieldtype": "Date", "label": "Warranty Expiry Date"}, {"fetch_from": "serial_no.amc_expiry_date", "fetch_if_empty": 1, "fieldname": "amc_expiry_date", "fieldtype": "Date", "label": "AMC Expiry Date"}, {"fieldname": "resolution_section", "fieldtype": "Section Break", "label": "Resolution", "oldfieldtype": "Section Break", "options": "fa fa-thumbs-up"}, {"fieldname": "resolution_date", "fieldtype": "Datetime", "label": "Resolution Date", "no_copy": 1, "oldfieldname": "resolution_date", "oldfieldtype": "Date", "search_index": 1}, {"fieldname": "resolved_by", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Resolved By", "no_copy": 1, "oldfieldname": "resolved_by", "oldfieldtype": "Link", "options": "User", "search_index": 1}, {"fieldname": "resolution_details", "fieldtype": "Text", "label": "Resolution Details", "no_copy": 1, "oldfieldname": "resolution_details", "oldfieldtype": "Text"}, {"collapsible": 1, "depends_on": "customer", "fieldname": "contact_info", "fieldtype": "Section Break", "label": "Customer Details", "options": "fa fa-bullhorn"}, {"bold": 1, "depends_on": "customer", "fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "in_global_search": 1, "label": "Customer Name", "read_only": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"depends_on": "customer", "fieldname": "contact_display", "fieldtype": "Small Text", "in_global_search": 1, "label": "Contact", "read_only": 1}, {"depends_on": "customer", "fieldname": "contact_mobile", "fieldtype": "Data", "label": "Mobile No", "options": "Phone", "read_only": 1}, {"depends_on": "customer", "fieldname": "contact_email", "fieldtype": "Data", "label": "Contact Email", "options": "Email", "read_only": 1}, {"depends_on": "customer", "fieldname": "territory", "fieldtype": "Link", "label": "Territory", "oldfieldname": "territory", "oldfieldtype": "Link", "options": "Territory", "print_hide": 1, "read_only": 1, "search_index": 1}, {"depends_on": "customer", "fieldname": "customer_group", "fieldtype": "Link", "label": "Customer Group", "options": "Customer Group", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break4", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "customer_address", "fieldtype": "Link", "label": "Customer Address", "options": "Address", "print_hide": 1}, {"depends_on": "customer", "fieldname": "address_display", "fieldtype": "Small Text", "label": "Address", "read_only": 1}, {"depends_on": "customer", "description": "If different than customer address", "fieldname": "service_address", "fieldtype": "Small Text", "label": "Service Address", "oldfieldname": "service_address", "oldfieldtype": "Small Text"}, {"collapsible": 1, "fieldname": "more_info", "fieldtype": "Section Break", "label": "More Information", "options": "fa fa-file-text"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1, "search_index": 1}, {"fieldname": "col_break6", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "complaint_raised_by", "fieldtype": "Data", "label": "Raised By", "oldfieldname": "complaint_raised_by", "oldfieldtype": "Data"}, {"fieldname": "from_company", "fieldtype": "Data", "label": "From Company", "oldfieldname": "from_company", "oldfieldtype": "Data"}, {"fieldname": "amended_from", "fieldtype": "Link", "hidden": 1, "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "<PERSON><PERSON><PERSON>", "print_hide": 1, "width": "150px"}], "icon": "fa fa-bug", "idx": 1, "links": [], "modified": "2023-11-28 17:30:35.676410", "modified_by": "Administrator", "module": "Support", "name": "<PERSON><PERSON><PERSON>", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "write": 1}], "search_fields": "status,customer,customer_name,territory", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer", "title_field": "customer_name"}