{"add_total_row": 0, "creation": "2020-08-10 18:12:42.391224", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "letter_head": "Test 2", "modified": "2020-08-10 18:12:42.391224", "modified_by": "Administrator", "module": "Support", "name": "First Response Time for Issues", "owner": "Administrator", "prepared_report": 0, "query": "select date(creation) as creation_date, avg(mins_to_first_response) from tabIssue where creation > '2016-05-01' group by date(creation) order by creation_date;", "ref_doctype": "Issue", "report_name": "First Response Time for Issues", "report_type": "Script Report", "roles": [{"role": "Support Team"}]}