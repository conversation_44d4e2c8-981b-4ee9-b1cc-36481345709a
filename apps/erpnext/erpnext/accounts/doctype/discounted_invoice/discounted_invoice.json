{"creation": "2019-03-07 12:07:09.416101", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["sales_invoice", "customer", "column_break_3", "posting_date", "outstanding_amount", "debit_to"], "fields": [{"fieldname": "sales_invoice", "fieldtype": "Link", "in_list_view": 1, "label": "Invoice", "options": "Sales Invoice", "reqd": 1, "search_index": 1}, {"fetch_from": "sales_invoice.customer", "fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "Customer", "read_only": 1}, {"fetch_from": "sales_invoice.posting_date", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "read_only": 1}, {"fetch_from": "sales_invoice.outstanding_amount", "fetch_if_empty": 1, "fieldname": "outstanding_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Outstanding Amount", "options": "Company:company:default_currency"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "sales_invoice.debit_to", "fieldname": "debit_to", "fieldtype": "Link", "label": "Debit to", "options": "Account", "read_only": 1}], "istable": 1, "modified": "2020-02-20 16:16:20.724620", "modified_by": "Administrator", "module": "Accounts", "name": "Discounted Invoice", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}