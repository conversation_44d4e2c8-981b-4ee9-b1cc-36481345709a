{"actions": [], "allow_import": 1, "autoname": "ACC-ERR-.YYYY.-.#####", "creation": "2018-04-13 18:25:55.943587", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["posting_date", "rounding_loss_allowance", "column_break_2", "company", "section_break_4", "get_entries", "accounts", "section_break_6", "gain_loss_unbooked", "gain_loss_booked", "column_break_10", "total_gain_loss", "amended_from"], "fields": [{"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "get_entries", "fieldtype": "<PERSON><PERSON>", "label": "Get Entries"}, {"fieldname": "accounts", "fieldtype": "Table", "label": "Exchange Rate Revaluation Account", "no_copy": 1, "options": "Exchange Rate Revaluation Account", "reqd": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Exchange Rate Revaluation", "print_hide": 1, "read_only": 1}, {"fieldname": "gain_loss_unbooked", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Gain/Loss from Revaluation", "options": "Company:company:default_currency", "read_only": 1}, {"description": "Gain/Loss accumulated in foreign currency account. Accounts with '0' balance in either Base or Account currency", "fieldname": "gain_loss_booked", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Gain/Loss already booked", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "total_gain_loss", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Gain/Loss", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"default": "0.05", "description": "Only values between [0,1) are allowed. Like {0.00, 0.04, 0.09, ...}\nEx: If allowance is set at 0.07, accounts that have balance of 0.07 in either of the currencies will be considered as zero balance account", "fieldname": "rounding_loss_allowance", "fieldtype": "Float", "label": "Rounding Loss Allowance", "precision": "9"}], "is_submittable": 1, "links": [], "modified": "2023-06-20 07:29:06.972434", "modified_by": "Administrator", "module": "Accounts", "name": "Exchange Rate Revaluation", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}