{"allow_copy": 0, "allow_import": 0, "allow_rename": 0, "autoname": "field:bank_name", "beta": 0, "creation": "2016-05-04 14:35:00.402544", "custom": 0, "docstatus": 0, "doctype": "DocType", "document_type": "", "fields": [{"allow_on_submit": 0, "bold": 0, "collapsible": 0, "description": "", "fieldname": "settings", "fieldtype": "HTML", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "", "length": 0, "no_copy": 0, "options": "<div>\n<h3> All dimensions in centimeter only </h3>\n</div>", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "has_print_format", "fieldtype": "Check", "hidden": 1, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Has Print Format", "length": 0, "no_copy": 1, "permlevel": 0, "precision": "", "print_hide": 1, "print_hide_if_no_value": 0, "read_only": 1, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "primary_settings", "fieldtype": "Section Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Primary Settings", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "bank_name", "fieldtype": "Data", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Bank Name", "length": 0, "no_copy": 1, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 1, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "Regular", "fieldname": "cheque_size", "fieldtype": "Select", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Cheque Size", "length": 0, "no_copy": 0, "options": "\nRegular\nA4", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "depends_on": "eval:doc.cheque_size==\"A4\"", "fieldname": "starting_position_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Starting position from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "20.00", "fieldname": "cheque_width", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Cheque <PERSON>", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "9.00", "fieldname": "cheque_height", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Cheque Height", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "", "fieldname": "scanned_cheque", "fieldtype": "Attach", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Scanned Cheque", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "column_break_5", "fieldtype": "Column Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "1", "fieldname": "is_account_payable", "fieldtype": "Check", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Is Account Payable", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "1.00", "depends_on": "eval:doc.is_account_payable", "fieldname": "acc_pay_dist_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "9.00", "depends_on": "eval:doc.is_account_payable", "fieldname": "acc_pay_dist_from_left_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from left edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "Acc. Payee", "depends_on": "eval:doc.is_account_payable", "fieldname": "message_to_show", "fieldtype": "Data", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Message to show", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "date_and_payer_settings", "fieldtype": "Section Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "date_settings", "fieldtype": "HTML", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Date Settings", "length": 0, "no_copy": 0, "options": "<label class=\"control-label\" style=\"margin-bottom: 0px;\">Date Settings</label>", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "1.00", "fieldname": "date_dist_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "15.00", "depends_on": "", "fieldname": "date_dist_from_left_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Starting location from left edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "payer_settings", "fieldtype": "Column Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Payer <PERSON>s", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "2.00", "fieldname": "payer_name_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "3.00", "fieldname": "payer_name_from_left_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Starting location from left edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "amount_in_words_and_figure_settings", "fieldtype": "Section Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "html_19", "fieldtype": "HTML", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "length": 0, "no_copy": 0, "options": "<label class=\"control-label\" style=\"margin-bottom: 0px;\">Amount In Words</label>", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "3.00", "fieldname": "amt_in_words_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "4.00", "fieldname": "amt_in_words_from_left_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Starting location from left edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "15.00", "fieldname": "amt_in_word_width", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Width of amount in word", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "0.50", "fieldname": "amt_in_words_line_spacing", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Line spacing for amount in words", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "amount_in_figure", "fieldtype": "Column Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Amount In Figure", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "3.50", "fieldname": "amt_in_figures_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "16.00", "fieldname": "amt_in_figures_from_left_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Starting location from left edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "account_number_and_signatory_settings", "fieldtype": "Section Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "account_no_settings", "fieldtype": "HTML", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "length": 0, "no_copy": 0, "options": "<label class=\"control-label\" style=\"margin-bottom: 0px;\">Account Number Settings</label>", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "5.00", "fieldname": "acc_no_dist_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "4.00", "fieldname": "acc_no_dist_from_left_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Starting location from left edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "signatory_position", "fieldtype": "Column Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Signatory Position", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "6.00", "fieldname": "signatory_from_top_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Distance from top edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "default": "15.00", "fieldname": "signatory_from_left_edge", "fieldtype": "Float", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Starting location from left edge", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "2", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "preview", "fieldtype": "Section Break", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "label": "Preview", "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}, {"allow_on_submit": 0, "bold": 0, "collapsible": 0, "fieldname": "cheque_print_preview", "fieldtype": "HTML", "hidden": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_filter": 0, "in_list_view": 0, "length": 0, "no_copy": 0, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "read_only": 0, "report_hide": 0, "reqd": 0, "search_index": 0, "set_only_once": 0, "unique": 0}], "hide_heading": 0, "hide_toolbar": 0, "idx": 0, "image_view": 0, "in_create": 0, "is_submittable": 0, "issingle": 0, "istable": 0, "max_attachments": 1, "modified": "2016-06-23 20:19:11.694932", "modified_by": "Administrator", "module": "Accounts", "name": "Cheque Print Template", "name_case": "", "owner": "Administrator", "permissions": [{"amend": 0, "apply_user_permissions": 0, "cancel": 0, "create": 1, "delete": 1, "email": 1, "export": 1, "if_owner": 0, "import": 0, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "System Manager", "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"amend": 0, "apply_user_permissions": 0, "cancel": 0, "create": 1, "delete": 1, "email": 1, "export": 1, "if_owner": 0, "import": 0, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"amend": 0, "apply_user_permissions": 0, "cancel": 0, "create": 1, "delete": 1, "email": 1, "export": 1, "if_owner": 0, "import": 0, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}], "quick_entry": 0, "read_only": 0, "read_only_onload": 0, "sort_field": "modified", "sort_order": "DESC", "track_seen": 0}