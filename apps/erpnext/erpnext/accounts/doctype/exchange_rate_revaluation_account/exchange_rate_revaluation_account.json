{"actions": [], "creation": "2018-04-13 18:30:06.110433", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["account", "party_type", "party", "column_break_2", "account_currency", "account_balances", "balance_in_account_currency", "column_break_46yz", "new_balance_in_account_currency", "balances", "current_exchange_rate", "column_break_xown", "new_exchange_rate", "column_break_9", "balance_in_base_currency", "column_break_ukce", "new_balance_in_base_currency", "section_break_ngrs", "gain_loss", "zero_balance"], "fields": [{"fieldname": "account", "fieldtype": "Link", "in_list_view": 1, "label": "Account", "options": "Account", "reqd": 1}, {"fieldname": "party_type", "fieldtype": "Link", "label": "Party Type", "options": "DocType"}, {"fieldname": "party", "fieldtype": "Dynamic Link", "label": "Party", "options": "party_type"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "account_currency", "fieldtype": "Link", "label": "Account <PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "balance_in_account_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Balance In Account <PERSON><PERSON><PERSON>cy", "options": "account_currency", "read_only": 1}, {"fieldname": "balances", "fieldtype": "Section Break"}, {"fieldname": "current_exchange_rate", "fieldtype": "Float", "label": "Current Exchange Rate", "precision": "9", "read_only": 1}, {"fieldname": "balance_in_base_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Balance In Base Currency", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "column_break_9", "fieldtype": "Section Break"}, {"fieldname": "new_exchange_rate", "fieldtype": "Float", "in_list_view": 1, "label": "New Exchange Rate", "precision": "9", "reqd": 1}, {"fieldname": "new_balance_in_base_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "New Balance In Base Currency", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "gain_loss", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Gain/Loss", "options": "Company:company:default_currency", "read_only": 1}, {"default": "0", "description": "This Account has '0' balance in either Base Currency or Account Currency", "fieldname": "zero_balance", "fieldtype": "Check", "label": "Zero Balance"}, {"fieldname": "new_balance_in_account_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "New Balance In Account <PERSON><PERSON><PERSON>cy", "options": "account_currency", "read_only": 1}, {"fieldname": "account_balances", "fieldtype": "Section Break"}, {"fieldname": "column_break_46yz", "fieldtype": "Column Break"}, {"fieldname": "column_break_xown", "fieldtype": "Column Break"}, {"fieldname": "column_break_ukce", "fieldtype": "Column Break"}, {"fieldname": "section_break_ngrs", "fieldtype": "Section Break"}], "istable": 1, "links": [], "modified": "2023-06-22 12:39:56.446722", "modified_by": "Administrator", "module": "Accounts", "name": "Exchange Rate Revaluation Account", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}