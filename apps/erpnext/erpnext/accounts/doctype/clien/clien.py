# Copyright (c) 2024, Frappe Technologies Pvt. Ltd. and contributors
# For license information, please see license.txt

# import frappe
from frappe.model.document import Document


class clien(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from erpnext.accounts.doctype.famliy_members.famliy_members import FamliyMembers
		from frappe.types import DF

		age: DF.Int
		amended_from: DF.Link | None
		dob: DF.Data | None
		email: DF.Data | None
		enable: DF.Check
		famliy_members: DF.Table[FamliyMembers]
		frist_name: DF.Float
		full_name: DF.Data | None
		last_name: DF.Float
		middel_name: DF.Float
		mobile_no: DF.Data | None
	# end: auto-generated types
	pass
