{"actions": [], "allow_rename": 1, "autoname": "PR-.####", "creation": "2024-05-08 16:59:53.357143", "doctype": "DocType", "engine": "InnoDB", "field_order": ["enable", "frist_name", "middel_name", "last_name", "full_name", "column_break_mxt4", "email", "mobile_no", "dob", "age", "section_break_33zk", "famliy_members", "amended_from"], "fields": [{"fieldname": "frist_name", "fieldtype": "Float", "label": "Frist Name"}, {"fieldname": "middel_name", "fieldtype": "Float", "label": "Middel Name"}, {"fieldname": "last_name", "fieldtype": "Float", "label": "Last Name"}, {"bold": 1, "fieldname": "full_name", "fieldtype": "Data", "in_list_view": 1, "label": "Full Name", "read_only": 1}, {"fieldname": "column_break_mxt4", "fieldtype": "Column Break"}, {"fieldname": "email", "fieldtype": "Data", "label": "Email"}, {"fieldname": "mobile_no", "fieldtype": "Data", "label": "Mobile No"}, {"fieldname": "dob", "fieldtype": "Data", "label": "DOB"}, {"fieldname": "age", "fieldtype": "Int", "label": "Age"}, {"fieldname": "section_break_33zk", "fieldtype": "Section Break"}, {"fieldname": "famliy_members", "fieldtype": "Table", "label": "Famliy Members", "options": "Famliy Members"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "clien", "print_hide": 1, "read_only": 1, "search_index": 1}, {"default": "0", "fieldname": "enable", "fieldtype": "Check", "label": "Enable"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2024-05-08 20:37:08.131484", "modified_by": "Administrator", "module": "Accounts", "name": "clien", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1, "track_views": 1}