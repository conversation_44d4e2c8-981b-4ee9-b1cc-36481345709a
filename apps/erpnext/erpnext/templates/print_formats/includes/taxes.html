{%- macro render_discount_amount(doc) -%}
	{%- if doc.discount_amount -%}
		<div class="row">
			<div class="col-xs-5 {%- if doc.align_labels_right %} text-right{%- endif -%}">
				<label>{{ _(doc.meta.get_label('discount_amount')) }}</label></div>
			<div class="col-xs-7 text-right">
				- {{ doc.get_formatted("discount_amount", doc) }}
			</div>
		</div>
	{%- endif -%}
{%- endmacro -%}

<div class="row">
	<div class="col-xs-6"></div>
	<div class="col-xs-6">
		{%- if doc.apply_discount_on == "Net Total" -%}
			{{ render_discount_amount(doc) }}
		{%- endif -%}
		{%- for charge in data -%}
			{%- if (charge.tax_amount or print_settings.print_taxes_with_zero_amount) and (not charge.included_in_print_rate or doc.flags.show_inclusive_tax_in_print) -%}
			<div class="row">
				<div class="col-xs-5 {%- if doc.align_labels_right %} text-right{%- endif -%}">
					<label>{{ charge.get_formatted("description") }}</label>
				</div>
				<div class="col-xs-7 text-right">
					{{ charge.get_formatted('tax_amount', doc) }}
				</div>
			</div>
			{%- endif -%}
		{%- endfor -%}
		{%- if doc.apply_discount_on == "Grand Total" -%}
			{{ render_discount_amount(doc) }}
		{%- endif -%}
	</div>
</div>
