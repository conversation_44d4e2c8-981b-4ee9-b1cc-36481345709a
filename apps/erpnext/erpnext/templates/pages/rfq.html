{% extends "templates/web.html" %}

{% block header %}
<h1 style="margin-top: 10px;">{{ doc.name }}</h1>
{% endblock %}

{% block script %}
<script>{% include "templates/includes/rfq.js" %}</script>
{% endblock %}

{% block breadcrumbs %}
	{% include "templates/includes/breadcrumbs.html" %}
{% endblock %}

{% block header_actions %}
{% if doc.items %}
<button class="btn btn-primary btn-sm"
    type="button">
    {{ _("Make Quotation") }}</button>
{% endif %}
{% endblock %}

{% block page_content %}
<div class="row">
    <div class="col-6">
        <div class="rfq-supplier">{{ doc.supplier }}</div>
	</div>
    <div class="col-6 text-muted text-right h6">
        {{ doc.get_formatted("transaction_date") }}
    </div>
</div>
<div class="rfq-content" style="margin-top:15px">
	<div id="order-container">
			<div id="rfq-items">
				<div class="row cart-item-header">
					<div class="col-sm-5 col-12">
						{{ _("Items") }}
					</div>
					<div class="col-sm-2 col-4 text-right">
						{{ _("Qty") }}
					</div>
					<div class="col-sm-2 col-4 text-right">
						{{ _("Rate") }}
					</div>
					<div class="col-sm-3 col-4 text-right">
						{{ _("Amount") }}
					</div>
				</div>
				<hr>
            {% if doc.items %}
            <div class="rfq-items">
				{% include "templates/includes/rfq/rfq_items.html" %}
            </div>
            {% endif %}
		</div>
        {% if doc.items %}
		<div class="row grand-total-row">
			<div class="col-9 text-right">{{ _("Grand Total") }}</div>
			<div class="col-3 text-right">
			{{doc.currency_symbol}}  <span class="tax-grand-total">0.0</span>
			</div>
		</div>
        {% endif %}
		<div class="row terms">
			<div class="col-6">
				<br><br>
				<p class="text-muted small">{{ _("Notes: ") }}</p>
				<textarea class="form-control terms-feedback" style="height: 100px;"></textarea>
			</div>
		</div>
		<div class="row mt-5">
			<div class="col-12">
				<p class="text-muted small">{{ _("Quotations: ") }}</p>
				{% if doc.rfq_links %}
					<div class="result">
						{% for d in doc.rfq_links %}
							<div class="web-list-item transaction-list-item quotations" idx="{{d.name}}">
								<div class="row">
									<div class="col-sm-6">
										<span class="indicator gray">{{d.name}}</span>
									</div>
									<div class="col-sm-3">
										<span class="small gray">{{d.status}}</span>
									</div>
									<div class="col-sm-3">
										<span class="small gray">{{d.transaction_date}}</span>
									</div>
								</div>
								<a class="transaction-item-link" href="/supplier-quotations/{{d.name}}">Link</a>
							</div>
						{% endfor %}
					</div>
				{% endif %}
			</div>
		</div>
    </div>
</div>
{% endblock %}
