{% extends "templates/web.html" %}
{% from "erpnext/templates/includes/order/order_macros.html" import item_name_and_description %}

{% block breadcrumbs %}
	{% include "templates/includes/breadcrumbs.html" %}
{% endblock %}

{% block title %}{{ doc.name }}{% endblock %}

{% block header %}
	<h1>{{ doc.name }}</h1>
{% endblock %}

{% block header_actions %}
<a class='btn btn-xs btn-light' href='/printview?doctype={{ doc.doctype}}&name={{ doc.name }}&format={{ print_format }}' target="_blank" rel="noopener noreferrer">{{ _("Print") }}</a>
{% endblock %}

{% block page_content %}

<div class="row transaction-subheading">
	<div class="col-xs-6">

		<span class="indicator {{ doc.indicator_color or ("blue" if doc.docstatus==1 else "gray") }}">
			{{ _(doc.get('indicator_title')) or _(doc.status) or _("Submitted") }}
		</span>
	</div>
	<div class="col-xs-6 text-muted text-right small">
		{{ frappe.utils.format_date(doc.transaction_date, 'medium') }}
	</div>
</div>

{% if doc._header %}
{{ doc._header }}
{% endif %}

<div class="order-container">

	<!-- items -->
	<div class="order-item-table">
		<div class="row order-items order-item-header text-muted">
			<div class="col-sm-6 col-xs-6 h6 text-uppercase">
				{{ _("Item") }}
			</div>
			<div class="col-sm-3 col-xs-3 text-right h6 text-uppercase">
				{{ _("Work Order") }}
			</div>
			<div class="col-sm-3 col-xs-3 text-right h6 text-uppercase">
				{{ _("Quantity") }}
			</div>
		</div>
		{% for d in doc.items %}
		{% if d.customer_provided %}
		<div class="row order-items">
			<div class="col-sm-6 col-xs-6">
				{{ item_name_and_description(d) }}
			</div>
			<div class="col-sm-3 col-xs-3 text-right">
				{% for wo in d.work_orders %}
				<p class="text-muted small">{{_(wo.name) }}</p>
				{% endfor %}
			</div>
			<div class="col-sm-3 col-xs-3 text-right">
				{{ d.qty }}
				{% if d.delivered_qty is defined and d.delivered_qty != None %}
				<p class="text-muted small">{{
					_("Delivered: {0}").format(d.delivered_qty) }}</p>
				{% endif %}
			</div>
		</div>
		{% endif %}
		{% endfor %}
	</div>
</div>
{% endblock %}
