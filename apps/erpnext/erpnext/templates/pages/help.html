{% extends "templates/web.html" %}

{% block title %} {{ _("Help") }} {% endblock %}

{% block header %}<h1>{{ _("Help") }}</h1>

<div style="margin-bottom: 20px;">
	<form action="/search_help" style="display: flex;">
	<input name='q' class='form-control' type='text'
		style='max-width: 400px; display: inline-block; margin-right: 10px;'
		value='{{ frappe.form_dict.q or ''}}'
		{% if not frappe.form_dict.q%}placeholder="{{ _("What do you need help with?") }}"{% endif %}>
	<input type='submit'
		class='btn btn-sm btn-light btn-search' value="{{ _("Search") }}">
	</form>
</div>

{% for section in get_started_sections %}
<div style="margin-bottom: 30px;">
	<h2>{{ section["name"] }}</h2>
	{% for item in section["items"] %}
	<div style="margin: 20px 0;">
		<a href="{{ item.link }}"><b>{{ item.title }}</b></a>
		{% if item.description -%}
		<p>{{ item.description }}</p>
		{%- endif %}
	</div>
	{% endfor %}
	<p><a href="/kb/support">{{ _("See All Articles") }}</a></p>
	<hr>
</div>
{% endfor %}

<div style="margin-bottom: 30px;">
	<h2>{{ _("Forum Activity") }}</h2>
	{% for topic in topics %}
	<div style="margin: 20px 0;">
		<a href="{{ topic[post_params.link] }}">
			<b>{{ topic[post_params.title] }}</b>
		</a>
		{% if topic[post_params.description] -%}
		<p>{{ topic[post_params.description] }}</p>
		{%- endif %}
	</div>
	{% endfor %}
	<p><a href="{{ forum_url }}">{{ _("Visit the forums") }}</a></p>
	<hr>
</div>

{% if issues | len > 0 -%}
<div style="margin-bottom: 20px;">
	<h2>{{ _("Your tickets") }}</h2>
		{% for doc in issues %}
			{% include "templates/includes/issue_row.html" %}
		{% endfor %}
	<p><a href="/issues">{{ _("See all open tickets") }}</a></p>
</div>

<a href="/issues?new=1" class="btn btn-primary btn-new btn-sm">
	{{ _("Open a new ticket") }}
</a>
{%- endif %}

{% endblock %}
