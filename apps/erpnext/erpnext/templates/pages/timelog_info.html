{% extends "templates/web.html" %}
{% block title %} {{ doc.name }} {% endblock %}
{% block breadcrumbs %}
<div class="page-breadcrumbs" data-html-block="breadcrumbs">
	<ul class="breadcrumb">
		<li>
			<span class="fa fa-angle-left"></span>
			<a href="/projects?project={{ doc.project }}">{{ doc.project }}</a>
		</li>
</ul>
</div>
{% endblock %}

{% block page_content %}
	<div class=" col-sm-8 ">
		<h1> {{ doc.name }} </h1>
	</div>

	<div class="page-content-block">
		<div class="row">
			<div class="col-sm-12" style="max-width: 500px;">
				<label for="project" class="control-label text-muted small">{{ __("Project") }}</label>
				<input type="text" class="form-control" name="project" readonly value= "{{ doc.project }}">

				<label for="activity_type" class="control-label text-muted small">{{ __("Activity Type") }}</label>
				<input type="text" class="form-control" name="activity_type" readonly value= "{{ doc.activity_type }}">

				<label for="task" class="control-label text-muted small">{{ __("Task") }}</label>
				<input type="text" class="form-control" name="task" readonly value= "{{ doc.task }}">

				<label for="from_time" class="control-label text-muted small">{{ __("From Time") }}</label>
				<input type="text" class="form-control" name="from_time" readonly value= "{{ doc.from_time }}">

				<label for="to_time" class="control-label text-muted small">{{ __("To Time") }}</label>
				<input type="text" class="form-control" name="to_time" readonly value= "{{ doc.to_time }}">

				<label for="to_time" class="control-label text-muted small">{{ __("Hours") }}</label>
				<input type="text" class="form-control" name="Hours" readonly value= "{{ doc.hours }}">

				<label for="status" class="control-label text-muted small">{{ __("Status") }}</label>
				<input type="text" class="form-control" name="status" readonly value= "{{ doc.status }}">

				<label for="Note" class="control-label text-muted small">{{ __("Note") }}</label>
				<textarea class="form-control" name="Hours" readonly> {{ doc.note }} </textarea>
			</div>
		</div>
	</div>
{% endblock %}
