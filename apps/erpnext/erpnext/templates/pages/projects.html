{% extends "templates/web.html" %}

{% block title %}
  {{ doc.project_name }}
{% endblock %}

{% block head_include %}
  <link rel="stylesheet" href="/assets/frappe/css/fonts/fontawesome/font-awesome.min.css">
{% endblock %}

{% block header %}
  <h3 class="my-account-header">{{ doc.project_name }}</h3>
{% endblock %}

{% block style %}
  <style>
    {% include "templates/includes/projects.css" %}
  </style>
{% endblock %}

{% block page_content %}
  <div class="web-list-item transaction-list-item">
    <div class="row align-items-center">
      <div class="col-sm-4 "><b>{{ _("Status") }}: {{ _(doc.status) }}</b></div>
      <div class="col-sm-4 "><b>{{ _("Progress") }}: {{ doc.get_formatted("percent_complete") }}</b></div>
      <div class="col-sm-4 "><b>{{ _("Hours Spent") }}: {{ doc.get_formatted("actual_time") }}</b></div>
    </div>
  </div>

  {{ progress_bar(doc.percent_complete) }}

  <hr>

  <div class="row align-items-center">
    <div class="col-sm-6 my-account-header"> <h4>{{ _("Tasks") }}</h4></div>
    <div class="col-sm-6 text-right">
      <a class="btn btn-secondary btn-light btn-sm" href='/tasks/new?project={{ doc.project_name }}'>{{ _("New task") }}</a>
    </div>
  </div>
  {% if doc.tasks %}
  <div class="website-list">
    <div class="result">
      <div class="web-list-item transaction-list-item">
        <div class="row align-items-center">
          <div class="col-sm-4"><b>{{ _("Tasks") }}</b></div>
          <div class="col-sm-2"><b>{{ _("Status") }}</b></div>
          <div class="col-sm-2"><b>{{ _("End Date") }}</b></div>
          <div class="col-sm-2"><b>{{ _("Assignment") }}</b></div>
          <div class="col-sm-2"><b>{{ _("Modified On") }}</b></div>
        </div>
      </div>
      {% include "erpnext/templates/includes/projects/project_tasks.html" %}
    </div>
  </div>
  {% else %}
    {{ empty_state(_("Task")) }}
  {% endif %}

  <h4 class="my-account-header">{{ _("Timesheets") }}</h4>
  {% if doc.timesheets %}
    <div class="website-list">
      <div class="result">
        <div class="web-list-item transaction-list-item">
          <div class="row align-items-center">
            <div class="col-xs-2"><b>{{ _("Timesheet") }}</b></div>
            <div class="col-xs-2"><b>{{ _("Status") }}</b></div>
            <div class="col-xs-2"><b>{{ _("From") }}</b></div>
            <div class="col-xs-2"><b>{{ _("To") }}</b></div>
            <div class="col-xs-2"><b>{{ _("Modified By") }}</b></div>
            <div class="col-xs-2"><b>{{ _("Modified On") }}</b></div>
          </div>
        </div>
      {% include "erpnext/templates/includes/projects/project_timesheets.html" %}
      </div>
    </div>
  {% else %}
    {{ empty_state(_("Timesheet")) }}
  {% endif %}

  {% if doc.attachments %}
    <div class='padding'></div>

    <h4>{{ _("Attachments") }}</h4>
    <div class="project-attachments">
      {% for attachment in doc.attachments %}
        <div class="attachment">
          <a class="no-decoration attachment-link" href="{{ attachment.file_url }}" target="blank">
            <div class="row">
              <div class="col-xs-9">
                <span class="indicator red file-name">
                  {{ attachment.file_name }}</span>
              </div>
              <div class="col-xs-3">
                <span class="pull-right file-size">{{ attachment.file_size }}</span>
              </div>
            </div>
          </a>
        </div>
      {% endfor %}
    </div>
  {% endif %}

</div>

<script>
  { % include "frappe/public/js/frappe/provide.js" % }
  { % include "frappe/public/js/frappe/form/formatters.js" % }
 </script>

{% endblock %}

{% macro progress_bar(percent_complete) %}
{% if percent_complete %}
  <span class="small py-2">{{ _("Project Progress:") }}</span>
  <div class="progress progress-hg" style="height: 15px;">
    <div
      class="progress-bar progress-bar-{{ 'warning' if percent_complete|round < 100 else 'success' }} active"\
      role="progressbar" aria-valuenow="{{ percent_complete|round|int }}" aria-valuemin="0"\
      aria-valuemax="100" style="width:{{ percent_complete|round|int }}%;">
    </div>
  </div>
{% endif %}
{% endmacro %}


{% macro empty_state(section_name) %}
<div class="frappe-list align-items-center">
  <div class=" text-muted flex justify-center align-center" style="">
    <div class=" text-muted flex text-center">
      <div class="msg-box no-border">
        <div>
          <img src="/assets/frappe/images/ui-states/list-empty-state.svg" alt="Generic Empty State" class="null-state">
        </div>
        <p>{{ _("You haven't created a {0} yet").format(section_name) }}</p>
      </div>
    </div>
  </div>
  </div>
{% endmacro %}