{% var visible_columns = row.get_visible_columns(["item_code",
	"item_name", "amount", "stock_uom", "uom", "qty",
	"s_warehouse", "t_warehouse", "valuation_rate"]);
%}

{% if(!doc) { %}
	<div class="row">
		<div class="col-sm-5 col-xs-4">{%= __("Item") %}</div>
		<div class="col-sm-3 col-xs-4">{%= __("Warehouse") %}</div>
		<div class="col-sm-2 col-xs-2 text-right">{%= __("Qty") %}</div>
		<div class="col-sm-2 col-xs-2 text-right">{%= __("Amount") %}</div>
	</div>
{% } else { %}
	<div class="row">
		<div class="col-sm-5 col-xs-4"><strong>{%= doc.item_code %}</strong>
			{% if(doc.item_name != doc.item_code) { %}
				<br>{%= doc.item_name %}{% } %}
			{% include "templates/form_grid/includes/visible_cols.html" %}
		</div>

        <!-- warehouse -->
		<div class="col-sm-3 col-xs-4">
			{% if(doc.s_warehouse) {
					if(frm.doc.docstatus==0) {
						var color = (doc.s_warehouse && doc.actual_qty < doc.qty) ? "red" : "green";
						var title = color === "red" ? __("Not in Stock") : __("In Stock");
					} else {
						var color = "grey";
						var title = __("Source");
					}
				%}
				 <span class="indicator {{ color }}" title="{{ title }}">
					 {%= doc.s_warehouse %}</span>
            {% }; %}
			{% if(doc.t_warehouse) { %}
				<div><span class="indicator {{ doc.docstatus==1 ? "blue" : "grey" }}" title="{{ __("Target" ) }}">
					{%= doc.t_warehouse %}</span>
				</div>
            {% }; %}
		</div>

		<!-- qty -->
		<div class="col-sm-2 col-xs-2 text-right">
			{%= doc.get_formatted("qty") %}
			<br><small>{%= doc.uom || doc.stock_uom %}</small>
		</div>

		<!-- amount -->
		<div class="col-sm-2 col-xs-2 text-right">
			{%= doc.get_formatted("amount") %}
			<div class="small text-muted">
				{%= doc.get_formatted("valuation_rate") %}
			</div>
		</div>
	</div>
{% } %}
