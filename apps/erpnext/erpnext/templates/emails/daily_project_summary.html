<table border="0" cellpadding="0" cellspacing="0" width="100%">
	<tr>
		<h3>{{ title }}</h3>
	</tr>
</table>
{% for user in users %}
<table class="panel-header" border="0" cellpadding="0" cellspacing="0" width="100%">
	<tr height="10"></tr>
	<tr>
		<td width="15"></td>
		<td valign="top" width="24">
			{% if user.image %}
			<img class="sender-avatar" width="24" height="24" embed="{{ user.image }}"/>
			{% else %}
			<div class="sender-avatar-placeholder">
				{{ user.full_name[0] }}
			</div>
			{% endif %}
		</td>
		<td width="10"></td>
		<td>
			<div class="text-medium text-muted">
				<span>{{ user.full_name }}</span>
			</div>
		</td>
		<td width="15"></td>
	</tr>
	<tr height="10"></tr>
</table>
<table class="panel-body" border="0" cellpadding="0" cellspacing="0" width="100%">
	<tr height="10"></tr>
	<tr>
		<td width="15"></td>
		<td>
			<div>
				{{ user.project_status }}
			</div>
		</td>
		<td width="15"></td>
	</tr>
	<tr height="10"></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
	<tr height="20"></tr>
</table>
{% endfor %}
