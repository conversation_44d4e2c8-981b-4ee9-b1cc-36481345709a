<p>{{ _("Following Material Requests have been raised automatically based on Item's re-order level") + ":" }}<p>
{% for mr in mr_list -%}
<div style="margin-bottom: 30px;">
	<h4 style="margin-bottom: 5px;">{{ frappe.utils.get_link_to_form("Material Request", mr.name) }}</h4>
	<table style="width: 100%; border-spacing: 0; border-collapse: collapse;">
		<thead>
			<tr>
				<th style="border: 1px solid #d1d8dd; width: 35%; text-align: left; padding: 5px;">{{ _("Item") }}</th>
				<th style="border: 1px solid #d1d8dd; width: 35%; text-align: left; padding: 5px;">{{ _("Warehouse") }}</th>
				<th style="border: 1px solid #d1d8dd; width: 10%; text-align: right; padding: 5px;">{{ _("Quantity") }}</th>
				<th style="border: 1px solid #d1d8dd; width: 10%; text-align: left; padding: 5px;">{{ _("UOM") }}</th>
				<th style="border: 1px solid #d1d8dd; width: 10%; text-align: left; padding: 5px;">{{ _("Projected Qty") }}</th>
			</tr>
		</thead>
		<tbody>
			{% for item in mr.get("items") -%}
			<tr>
				<td style="border: 1px solid #d1d8dd; text-align: left; padding: 5px;">
					<b>{{ item.item_code }}</b>
					{% if item.item_code != item.item_name -%} <br> {{ item.item_name }} {%- endif %}
				</td>
				<td style="border: 1px solid #d1d8dd; text-align: left; padding: 5px;">{{ item.warehouse }}</td>
				<td style="border: 1px solid #d1d8dd; text-align: right; padding: 5px;">{{ item.qty }}</td>
				<td style="border: 1px solid #d1d8dd; text-align: left; padding: 5px;">{{ item.uom }}</td>
				<td style="border: 1px solid #d1d8dd; text-align: left; padding: 5px;">{{ frappe.utils.flt(item.projected_qty) + frappe.utils.flt(item.qty) }}</td>
			</tr>
			{%- endfor %}
		</tbody>
	</table>
</div>
{%- endfor %}
