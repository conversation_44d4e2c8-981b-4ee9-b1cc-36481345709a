{% if doc.status == "Open" %}
  <div class="web-list-item transaction-list-item">
    <div class="row">
      <div class="col-xs-2 project-link">
        <a class="transaction-item-link" href="/projects?project={{ doc.name | urlencode }}">Link</a>
        {{ doc.name }}
      </div>
      <div class="col-xs-2 project-name">
        {{ doc.project_name }}
      </div>
      <div class="col-xs-3 text-center">
        {% if doc.percent_complete %}
          {% set pill_class = "green" if doc.percent_complete | round == 100 else
            "orange" %}
          <div class="ellipsis">
            <span class="indicator-pill {{ pill_class }} filterable ellipsis">
              <span>{{ frappe.utils.cint(doc.percent_complete) }}
                %</span>
            </span>
          </div>
        {% else %}
          <span class="indicator-pill {{ " red" if doc.status=="Open" else "darkgrey" }}">
            {{ doc.status }}</span>
        {% endif %}
      </div>
      {% if doc["_assign"] %}
        {% set assigned_users = json.loads(doc["_assign"])%}
        <div class="col-xs-2 project-users">
          {% for user in assigned_users %}
            {% set user_details = frappe
              .db
              .get_value("User", user, [
                "full_name", "user_image"
              ], as_dict = True) %}
            {% if user_details.user_image %}
              <span class="avatar avatar-small" style="width:32px; height:32px;" title="{{ user_details.full_name }}">
                <img src="{{ user_details.user_image }}">
              </span>
            {% else %}
              <span class="avatar avatar-small" style="width:32px; height:32px;" title="{{ user_details.full_name }}">
                <div class='standard-image' style="background-color: #F5F4F4; color: #000;">
                  {{ frappe.utils.get_abbr(user_details.full_name) }}
                </div>
              </span>
            {% endif %}
          {% endfor %}
        </div>
      {% endif %}
      <div class="col-xs-3 text-right small text-muted project-modified-on">
        {{ frappe.utils.pretty_date(doc.modified) }}
      </div>
    </div>
  </div>
{% endif %}
