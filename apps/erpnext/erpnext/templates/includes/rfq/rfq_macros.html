{% from "erpnext/templates/includes/macros.html" import product_image_square, product_image %}

{% macro item_name_and_description(d, doc) %}
	<div class="row">
		<div class="col-3">
			{% if d.image %}
				{{ product_image(d.image) }}
			{% else %}
				<div class="website-image h-100 w-100" style="background-color:var(--gray-100);text-align: center;line-height: 3.6;">
					{{ frappe.utils.get_abbr(d.item_name)}}
				</div>
			{% endif %}
		</div>
		<div class="col-9">
			{{ d.item_code }}
			<p class="text-muted small">{{ d.description }}</p>
			{% set supplier_part_no = frappe.db.get_value("Item Supplier", {'parent': d.item_code, 'supplier': doc.supplier}, "supplier_part_no") %}
			<p class="text-muted small supplier-part-no">
				{% if supplier_part_no %}
					{{_("Supplier Part No") + ": "+ supplier_part_no}}
				{% endif %}
			</p>
		</div>
	</div>
{% endmacro %}
