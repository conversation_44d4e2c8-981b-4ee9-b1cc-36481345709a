<div class="tax-break-up" style="overflow-x: auto;">
	<table class="table table-bordered table-hover">
		<thead>
			<tr>
				{% for key in headers %}
					{% if loop.first %}
						<th class="text-left">{{ key }}</th>
					{% else %}
						<th class="text-right">{{ key }}</th>
					{% endif %}
				{% endfor%}
			</tr>
		</thead>
		<tbody>
			{% for taxes in itemised_tax_data %}
				<tr>
					<td>{{ taxes.item }}</td>
					<td class="text-right">
						{% if doc.get('is_return') %}
							{{ frappe.utils.fmt_money(taxes.taxable_amount |abs, None, doc.currency) }}
						{% else %}
							{{ frappe.utils.fmt_money(taxes.taxable_amount, None, doc.currency) }}
						{% endif %}
					</td>
					{% for tax_account in tax_accounts %}
						{% set tax_details = taxes.get(tax_account) %}
						{% if tax_details %}
							<td class="text-right">
								{% if tax_details.tax_rate or not tax_details.tax_amount %}
									({{ tax_details.tax_rate }}%)
								{% endif %}
								{% if doc.get('is_return') %}
									{{ frappe.utils.fmt_money((tax_details.tax_amount / doc.conversion_rate)|abs, None, doc.currency) }}
								{% else %}
									{{ frappe.utils.fmt_money(tax_details.tax_amount / doc.conversion_rate, None, doc.currency) }}
								{% endif %}
							</td>
						{% else %}
							<td></td>
						{% endif %}
					{% endfor %}
				</tr>
			{% endfor %}
		</tbody>
	</table>
</div>
