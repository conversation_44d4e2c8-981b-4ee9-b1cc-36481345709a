{"actions": [], "autoname": "field:id", "creation": "2022-02-21 11:54:58.414784", "doctype": "DocType", "engine": "InnoDB", "field_order": ["call_details_section", "id", "from", "to", "call_received_by", "employee_user_id", "medium", "start_time", "end_time", "column_break_4", "type", "customer", "status", "duration", "recording_url", "recording_html", "section_break_11", "type_of_call", "summary", "section_break_19", "links"], "fields": [{"fieldname": "id", "fieldtype": "Data", "label": "ID", "read_only": 1, "unique": 1}, {"fieldname": "from", "fieldtype": "Data", "in_list_view": 1, "label": "From", "read_only": 1}, {"fieldname": "to", "fieldtype": "Data", "in_list_view": 1, "label": "To", "read_only": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Ringing\nIn Progress\nCompleted\nFailed\nBusy\nNo Answer\nQueued\nCanceled", "read_only": 1}, {"description": "Call Duration in seconds", "fieldname": "duration", "fieldtype": "Duration", "in_list_view": 1, "label": "Duration", "read_only": 1}, {"fieldname": "recording_url", "fieldtype": "Data", "hidden": 1, "label": "Recording URL"}, {"fieldname": "medium", "fieldtype": "Data", "label": "Medium", "read_only": 1}, {"fieldname": "type", "fieldtype": "Select", "label": "Type", "options": "Incoming\nOutgoing", "read_only": 1}, {"fieldname": "recording_html", "fieldtype": "HTML", "label": "Recording HTML"}, {"fieldname": "section_break_19", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "links", "fieldtype": "Table", "label": "Links", "options": "Dynamic Link"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "summary", "fieldtype": "Small Text", "label": "Summary"}, {"fieldname": "section_break_11", "fieldtype": "Section Break", "hide_border": 1, "label": "Call Summary"}, {"fieldname": "start_time", "fieldtype": "Datetime", "label": "Start Time", "read_only": 1}, {"fieldname": "end_time", "fieldtype": "Datetime", "label": "End Time", "read_only": 1}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer", "read_only": 1}, {"fieldname": "call_details_section", "fieldtype": "Section Break", "label": "Call Details"}, {"fieldname": "employee_user_id", "fieldtype": "Link", "hidden": 1, "label": "Employee User Id", "options": "User"}, {"fieldname": "type_of_call", "fieldtype": "Link", "label": "Type Of Call", "options": "Telephony Call Type"}, {"depends_on": "to", "fieldname": "call_received_by", "fieldtype": "Link", "label": "Call Received By", "options": "Employee", "read_only": 1}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2022-04-14 02:59:22.503202", "modified_by": "Administrator", "module": "Telephony", "name": "Call Log", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Employee"}], "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "from", "track_changes": 1, "track_views": 1}