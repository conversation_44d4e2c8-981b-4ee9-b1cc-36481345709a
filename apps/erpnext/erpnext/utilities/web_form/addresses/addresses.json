{"accept_payment": 0, "allow_comments": 0, "allow_delete": 0, "allow_edit": 1, "allow_incomplete": 0, "allow_multiple": 1, "allow_print": 0, "amount": 0.0, "amount_based_on_field": 0, "anonymous": 0, "apply_document_permissions": 1, "condition_json": "[]", "creation": "2016-06-24 15:50:33.196990", "doc_type": "Address", "docstatus": 0, "doctype": "Web Form", "idx": 0, "is_standard": 1, "list_columns": [], "list_title": "", "login_required": 1, "max_attachment_size": 0, "modified": "2024-01-24 10:28:35.026064", "modified_by": "<EMAIL>", "module": "Utilities", "name": "addresses", "owner": "Administrator", "published": 1, "route": "address", "show_attachments": 0, "show_list": 1, "show_sidebar": 0, "success_url": "/addresses", "title": "Address", "web_form_fields": [{"allow_read_on_all_link_options": 0, "description": "", "fieldname": "address_title", "fieldtype": "Data", "hidden": 0, "label": "Address Title", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "address_type", "fieldtype": "Select", "hidden": 0, "label": "Address Type", "max_length": 0, "max_value": 0, "options": "Billing\nShipping\nOffice\nPersonal\nPlant\nPostal\nShop\nSubsidiary\nWarehouse\nOther", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "address_line1", "fieldtype": "Data", "hidden": 0, "label": "Address Line 1", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "address_line2", "fieldtype": "Data", "hidden": 0, "label": "Address Line 2", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "city", "fieldtype": "Data", "hidden": 0, "label": "City/Town", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "state", "fieldtype": "Data", "hidden": 0, "label": "State", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "pincode", "fieldtype": "Data", "hidden": 0, "label": "Postal Code", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 1, "fieldname": "country", "fieldtype": "Link", "hidden": 0, "label": "Country", "max_length": 0, "max_value": 0, "options": "Country", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldtype": "Column Break", "hidden": 0, "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "email_id", "fieldtype": "Data", "hidden": 0, "label": "Email Address", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "phone", "fieldtype": "Data", "hidden": 0, "label": "Phone", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "default": "0", "description": "", "fieldname": "is_primary_address", "fieldtype": "Check", "hidden": 0, "label": "Preferred Billing Address", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "default": "0", "description": "", "fieldname": "is_shipping_address", "fieldtype": "Check", "hidden": 0, "label": "Preferred Shipping Address", "max_length": 0, "max_value": 0, "read_only": 0, "reqd": 0, "show_in_filter": 0}]}