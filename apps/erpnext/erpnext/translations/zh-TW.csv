Period Name,期間名稱
Salary Mode,薪酬模式
Divorced,離婚
Post Route Key,郵政路線密鑰
Allow Item to be added multiple times in a transaction,允許項目在一個交易中被多次新增
Cancel Material Visit {0} before cancelling this Warranty Claim,材質訪問{0}之前取消此保修索賠取消
Assessment Reports,評估報告
Consumer Products,消費類產品
Notify Supplier,通知供應商
Please select Party Type first,請選擇黨第一型
Customer Items,客戶項目
Costing and Billing,成本核算和計費
Advance account currency should be same as company currency {0},預付科目貨幣應與公司貨幣{0}相同
Token Endpoint,令牌端點
Account {0}: Parent account {1} can not be a ledger,科目{0}：上層科目{1}不能是總帳
Publish Item to hub.erpnext.com,發布項目hub.erpnext.com,
Default Unit of Measure,預設的計量單位
All Sales Partner Contact,所有的銷售合作夥伴聯絡
Leave Approvers,休假審批人
Bio / Cover Letter,自傳/求職信
Investigations,調查
Click Enter To Add,點擊輸入要添加
"Missing value for Password, API Key or Shopify URL",缺少密碼，API密鑰或Shopify網址的值
All Accounts,所有科目
Cannot transfer Employee with status Left,無法轉移狀態為左的員工
"Stopped Production Order cannot be cancelled, Unstop it first to cancel",停止生產訂單無法取消，首先Unstop它取消
Do you really want to scrap this asset?,難道你真的想放棄這項資產？
Update Schedule,更新時間表
Select Default Supplier,選擇默認供應商
New Exchange Rate,新匯率
Currency is required for Price List {0},價格表{0}需填入貨幣種類
* Will be calculated in the transaction.,*將被計算在該交易。
Customer Contact,客戶聯絡
Check availability,檢查可用性
Job Applicant,求職者
This is based on transactions against this Supplier. See timeline below for details,這是基於對這種供應商的交易。詳情請參閱以下時間表
Overproduction Percentage For Work Order,工作訂單的生產率過高百分比
Transport Receipt Date,運輸收貨日期
Sales Order Series,銷售訂單系列
"More than one selection for {0} not \
			allowed",對{0}的多個選擇不是\允許的
Actual type tax cannot be included in Item rate in row {0},實際類型稅不能被包含在商品率排{0}
Allowed To Transact With,允許與
Customer,客戶
Required By,需求來自
Return Against Delivery Note,射向送貨單
Finance Book Detail,財務帳簿細節
% Billed,％已開立帳單
Exchange Rate must be same as {0} {1} ({2}),匯率必須一致{0} {1}（{2}）
Customer Name,客戶名稱
Natural Gas,天然氣
Bank account cannot be named as {0},銀行科目不能命名為{0}
Heads (or groups) against which Accounting Entries are made and balances are maintained.,頭（或組）針對其會計分錄是由和平衡得以維持。
Outstanding for {0} cannot be less than zero ({1}),傑出的{0}不能小於零（ {1} ）
Service Stop Date cannot be before Service Start Date,服務停止日期不能早於服務開始日期
Default 10 mins,預設為10分鐘
Show open,公開顯示
Checkout,查看
Depreciation Start Date,折舊開始日期
Apply On,適用於
Multiple Item prices.,多個項目的價格。
Purchase Order Items To Be Received,未到貨的採購訂單項目
All Supplier Contact,所有供應商聯絡
Support Settings,支持設置
Expected End Date can not be less than Expected Start Date,預計結束日期不能小於預期開始日期
Amazon MWS Settings,亞馬遜MWS設置
Row #{0}: Rate must be same as {1}: {2} ({3} / {4}) ,行＃{0}：速率必須與{1}：{2}（{3} / {4}）
Batch Item Expiry Status,批處理項到期狀態
Bank Draft,銀行匯票
Mode of Payment Account,支付帳戶模式
Consultation,會診
Show Payment Schedule in Print,在打印中顯示付款時間表
Sales and Returns,銷售和退貨
Show Variants,顯示變體
Academic Term,學期
"Maximum benefit of employee {0} exceeds {1} by the sum {2} of benefit application pro-rata component\
			amount and previous claimed amount",員工{0}的最高福利超過{1}，福利應用程序按比例分量\金額和上次索賠金額的總和{2}
Quantity,數量
Customers Without Any Sales Transactions,沒有任何銷售交易的客戶
Accounts table cannot be blank.,賬表不能為空。
Loans (Liabilities),借款（負債）
Encounter Time,遇到時間
Year of Passing,路過的一年
Routing Name,路由名稱
Country of Origin,出生國家
Soil Texture Criteria,土壤質地標準
In Stock,庫存
Primary Contact Details,主要聯繫方式
Open Issues,開放式問題
Production Plan Item,生產計劃項目
User {0} is already assigned to Employee {1},用戶{0}已經被分配給員工{1}
Health Care,保健
Delay in payment (Days),延遲支付（天）
Payment Terms Template Detail,付款條款模板細節
Issue Credit Note,發行信用票據
Lab Prescription,實驗室處方
Delay Days,延遲天數
Serial Number: {0} is already referenced in Sales Invoice: {1},序號：{0}已在銷售發票中引用：{1}
Invoice,發票
Item Weight Details,項目重量細節
Periodicity,週期性
Fiscal Year {0} is required,會計年度{0}是必需的
The minimum distance between rows of plants for optimum growth,植株之間的最小距離，以獲得最佳生長
Defense,防禦
Abbr,縮寫
Total Costing Amount,總成本計算金額
Vehicle No,車輛牌照號碼
Please select Price List,請選擇價格表
Currency Exchange Settings,貨幣兌換設置
Row #{0}: Payment document is required to complete the trasaction,列＃{0}：付款單據才能完成trasaction,
Work In Progress,在製品
Please select date,請選擇日期
Minimum Qty ,最低數量
Finance Book,金融書
Holiday List,假日列表
Accountant,會計人員
Selling Price List,賣價格表
Tobacco Current Use,煙草當前使用
Selling Rate,賣出率
Stock User,庫存用戶
(Ca+Mg)/K,（鈣+鎂）/ K,
Contact Information,聯繫信息
Phone No,電話號碼
Initial Email Notification Sent,初始電子郵件通知已發送
Statement Header Mapping,聲明標題映射
Sales Partners Commission,銷售合作夥伴佣金
Rounding Adjustment,舍入調整
Abbreviation cannot have more than 5 characters,縮寫不能有超過5個字符
Payment Request,付錢請求
To view logs of Loyalty Points assigned to a Customer.,查看分配給客戶的忠誠度積分的日誌。
Value After Depreciation,折舊後
Related,有關
Attendance date can not be less than employee's joining date,考勤日期不得少於員工的加盟日期
Grading Scale Name,分級標準名稱
Add Users to Marketplace,將用戶添加到市場
This is a root account and cannot be edited.,這是一個 root 科目，不能被編輯。
Operations,操作
Cannot set authorization on basis of Discount for {0},不能在折扣的基礎上設置授權{0}
Subscription Start Date,訂閱開始日期
Default receivable accounts to be used if not set in Patient to book Appointment charges.,如果未在患者中設置預約費用，則使用默認應收科目。
"Attach .csv file with two columns, one for the old name and one for the new name",附加.csv文件有兩列，一為舊名稱，一個用於新名稱
From Address 2,來自地址2,
{0} {1} not in any active Fiscal Year.,{0} {1} 不在任何有效的會計年度
Parent Detail docname,家長可採用DocName細節
"Reference: {0}, Item Code: {1} and Customer: {2}",參考：{0}，商品編號：{1}和顧客：{2}
{0} {1} is not present in the parent company,在母公司中不存在{0} {1}
Trial Period End Date Cannot be before Trial Period Start Date,試用期結束日期不能在試用期開始日期之前
Kg,公斤
Tax Withholding Category,預扣稅類別
Cancel the journal entry {0} first,首先取消日記條目{0}
BOM is not specified for subcontracting item {0} at row {1},沒有為行{1}的轉包商品{0}指定BOM,
{0} Result submittted,{0}結果提交
Help Results for,幫助結果
Select Warehouse...,選擇倉庫...
Advertising,廣告
Same Company is entered more than once,同一家公司進入不止一次
Not permitted for {0},不允許{0}
Get items from,取得項目來源
Price Not UOM Dependant,價格不依賴於UOM,
Apply Tax Withholding Amount,申請預扣稅金額
Stock cannot be updated against Delivery Note {0},送貨單{0}不能更新庫存
Total Amount Credited,總金額
Product {0},產品{0}
No items listed,沒有列出項目
Error Description,錯誤說明
Reconcile,調和
Grocery,雜貨
Reading 1,閱讀1,
Pension Funds,養老基金
Gain/Loss,收益/損失
Use Custom Cash Flow Format,使用自定義現金流量格式
All Sales Person,所有的銷售人員
**Monthly Distribution** helps you distribute the Budget/Target across months if you have seasonality in your business.,**月度分配**幫助你分配預算/目標跨越幾個月，如果你在你的業務有季節性。
Not items found,未找到項目
Person Name,人姓名
Sales Invoice Item,銷售發票項目
Credit,信用
Write Off Cost Center,沖銷成本中心
"e.g. ""Primary School"" or ""University""",如“小學”或“大學”
Stock Reports,庫存報告
Warehouse Detail,倉庫的詳細資訊
The Term End Date cannot be later than the Year End Date of the Academic Year to which the term is linked (Academic Year {}). Please correct the dates and try again.,該期限結束日期不能晚於學年年終日期到這個詞聯繫在一起（學年{}）。請更正日期，然後再試一次。
"""Is Fixed Asset"" cannot be unchecked, as Asset record exists against the item",“是固定的資產”不能選中，作為資產記錄存在對項目
Departure Time,出發時間
Tax Type,稅收類型
Completed Work Orders,完成的工作訂單
Forum Posts,論壇帖子
Taxable Amount,應稅金額
You are not authorized to add or update entries before {0},你無權添加或更新{0}之前的條目
Item Image (if not slideshow),產品圖片（如果不是幻燈片）
(Hour Rate / 60) * Actual Operation Time,（工時率/ 60）*實際操作時間
Row #{0}: Reference Document Type must be one of Expense Claim or Journal Entry,行＃{0}：參考文檔類型必須是費用索賠或日記帳分錄之一
Select BOM,選擇BOM,
SMS Log,短信日誌
Cost of Delivered Items,交付項目成本
The holiday on {0} is not between From Date and To Date,在{0}這個節日之間沒有從日期和結束日期
Admission Scheduled,入學時間表
Student Log,學生登錄
Templates of supplier standings.,供應商榜單。
Interested,有興趣
Opening,開盤
From {0} to {1},從{0} {1}
Failed to setup taxes,無法設置稅收
Copy From Item Group,從項目群組複製
Opening Entry,開放報名
Account Pay Only,科目只需支付
Additional Costs,額外費用
Account with existing transaction can not be converted to group.,科目與現有的交易不能被轉換到群組。
Product Enquiry,產品查詢
Validate Batch for Students in Student Group,驗證學生組學生的批次
No leave record found for employee {0} for {1},未找到員工的假期記錄{0} {1}
Unrealized Exchange Gain/Loss Account,未實現的匯兌收益/損失科目
Please enter company first,請先輸入公司
Please select Company first,請首先選擇公司
Under Graduate,根據研究生
Target On,目標在
Total Cost,總成本
Ca/K,鈣/ K,
Employee Loan,員工貸款
Send Payment Request Email,發送付款請求電子郵件
Item {0} does not exist in the system or has expired,項目{0}不存在於系統中或已過期
Leave blank if the Supplier is blocked indefinitely,如果供應商被無限期封鎖，請留空
Real Estate,房地產
Statement of Account,科目狀態
Pharmaceuticals,製藥
Is Fixed Asset,是固定的資產
"Available qty is {0}, you need {1}",可用數量是{0}，則需要{1}
Claim Amount,索賠金額
Work Order has been {0},工單已{0}
Applicable on Purchase Order,適用於採購訂單
Duplicate customer group found in the cutomer group table,在CUTOMER組表中找到重複的客戶群
Location Name,地點名稱
Asset Settings,資產設置
Grade,年級
No of Seats,座位數
Delivered By Supplier,交付供應商
Asset Maintenance Task,資產維護任務
All Contact,所有聯絡
Closing Fiscal Year,截止會計年度
{0} {1} is frozen,{0} {1}被凍結
Please select Existing Company for creating Chart of Accounts,請選擇現有的公司創建會計科目表
Stock Expenses,庫存費用
Select Target Warehouse,選擇目標倉庫
Select Target Warehouse,選擇目標倉庫
Please enter Preferred Contact Email,請輸入首選電子郵件聯繫
Contra Entry,魂斗羅進入
Credit in Company Currency,信用在公司貨幣
Lab Test UOM,實驗室測試UOM,
Installation Status,安裝狀態
Quality Inspection Template,質量檢驗模板
"Do you want to update attendance?<br>Present: {0}\
					<br>Absent: {1}",你想更新考勤？ <br>現任：{0} \ <br>缺席：{1}
Accepted + Rejected Qty must be equal to Received quantity for Item {0},品項{0}的允收＋批退的數量必須等於收到量
Supply Raw Materials for Purchase,供應原料採購
"Cannot ensure delivery by Serial No as \
				Item {0} is added with and without Ensure Delivery by \
				Serial No.",無法通過序列號確保交貨，因為\項目{0}是否添加了確保交貨\序列號
At least one mode of payment is required for POS invoice.,付款中的至少一個模式需要POS發票。
Bank Statement Transaction Invoice Item,銀行對賬單交易發票項目
Show Products as a List,產品展示作為一個列表
Item {0} is not active or end of life has been reached,項目{0}不活躍或生命的盡頭已經達到
Minimum Age,最低年齡
Example: Basic Mathematics,例如：基礎數學
Diff Qty,差異數量
Material Request Detail,材料請求詳情
Default Quotation Validity Days,默認報價有效天數
"To include tax in row {0} in Item rate, taxes in rows {1} must also be included",要包括稅款，行{0}項率，稅收行{1}也必須包括在內
Change Amount,變動金額
Certificate Received,已收到證書
Set Invoice Value for B2C. B2CL and B2CS calculated based on this invoice value.,設置B2C的發票值。 B2CL和B2CS根據此發票值計算。
New BOM,新的物料清單
Prescribed Procedures,規定程序
Show only POS,只顯示POS,
Supplier Group Name,供應商集團名稱
Driving License Categories,駕駛執照類別
Please enter Delivery Date,請輸入交貨日期
Make Depreciation Entry,計提折舊進入
Closed Document,關閉文件
Leave Settings,保留設置
Number of positions cannot be less then current count of employees,職位數量不能少於當前員工人數
Request Type,請求類型
Make Employee,使員工
Broadcasting,廣播
Setup mode of POS (Online / Offline),POS（在線/離線）的設置模式
Disables creation of time logs against Work Orders. Operations shall not be tracked against Work Order,禁止根據工作訂單創建時間日誌。不得根據工作指令跟踪操作
Execution,執行
Details of the operations carried out.,進行的作業細節。
Maintenance Status,維修狀態
Membership Details,會員資格
{0} {1}: Supplier is required against Payable account {2},{0} {1}：需要對供應商應付帳款{2}
Items and Pricing,項目和定價
Total hours: {0},總時間：{0}
From Date should be within the Fiscal Year. Assuming From Date = {0},從日期應該是在財政年度內。假設起始日期＝{0}
Interval,間隔
Preference,偏愛
Individual,個人
Academics User,學術界用戶
Amount In Figure,量圖
Plan for maintenance visits.,規劃維護訪問。
Supplier Scorecard Period,供應商記分卡期
Share Transfer,股份轉讓
Expiring Memberships,即將到期的會員
Customer Groups,客戶群
Financial Statements,財務報表
Students,學生們
Rules for applying pricing and discount.,規則適用的定價和折扣。
Time Slots,時隙
Price List must be applicable for Buying or Selling,價格表必須適用於購買或出售
Installation date cannot be before delivery date for Item {0},品項{0}的安裝日期不能早於交貨日期
Discount on Price List Rate (%),折扣價目表率（％）
Item Template,項目模板
Out Value,輸出值
Bank Statement Settings Item,銀行對賬單設置項目
Woocommerce Settings,Woocommerce設置
Sales Orders,銷售訂單
Multiple Loyalty Program found for the Customer. Please select manually.,為客戶找到多個忠誠度計劃。請手動選擇。
Valuation,計價
Purchase Order Trends,採購訂單趨勢
Go to Customers,轉到客戶
Late Checkin,延遲入住
The request for quotation can be accessed by clicking on the following link,報價請求可以通過點擊以下鏈接進行訪問
SG Creation Tool Course,SG創建工具課程
Payment Description,付款說明
Insufficient Stock,庫存不足
Disable Capacity Planning and Time Tracking,禁用產能規劃和時間跟踪
New Sales Orders,新的銷售訂單
Bank Account,銀行帳戶
You cannot delete Project Type 'External',您不能刪除項目類型“外部”
Select Alternate Item,選擇備用項目
Create User,創建用戶
Default Territory,預設地域
Television,電視
Updated via 'Time Log',經由“時間日誌”更新
Select the customer or supplier.,選擇客戶或供應商。
Advance amount cannot be greater than {0} {1},提前量不能大於{0} {1}
"Time slot skiped, the slot {0} to {1} overlap exisiting slot {2} to {3}",時隙滑動，時隙{0}到{1}與現有時隙{2}重疊到{3}
Enable Perpetual Inventory,啟用永久庫存
Charges Incurred,收費發生
Default Payroll Payable Account,默認情況下，應付職工薪酬帳戶
Update Email Group,更新電子郵件組
Is Opening Entry,是開放登錄
"If unchecked, the item wont be appear in Sales Invoice, but can be used in group test creation. ",如果取消選中，該項目不會出現在銷售發票中，但可用於創建組測試。
Mention if non-standard receivable account applicable,何況，如果不規範應收帳款適用
Instructor Name,導師姓名
Arrear Component,欠費組件
Criteria Setup,條件設置
For Warehouse is required before Submit,對於倉庫之前，需要提交
Medical Code,醫療代號
Connect Amazon with ERPNext,將Amazon與ERPNext連接起來
Please enter Company,請輸入公司名稱
Against Sales Invoice Item,對銷售發票項目
Linked Doctype,鏈接的文檔類型
Net Cash from Financing,從融資淨現金
"LocalStorage is full , did not save",localStorage的滿了，沒救
Address & Contact,地址及聯絡方式
Partner website,合作夥伴網站
Add Item,新增項目
Party Tax Withholding Config,黨的預扣稅配置
Custom Result,自定義結果
Contact Name,聯絡人姓名
Course Assessment Criteria,課程評價標準
Tax Id: ,稅號：
Student ID: ,學生卡：
POS Customer Group,POS客戶群
Practitioner Schedules,從業者時間表
Additional Details,額外細節
Request for purchase.,請求您的報價。
Collected Amount,收集金額
This is based on the Time Sheets created against this project,這是基於對這個項目產生的考勤表
Open Work Orders,打開工作訂單
Out Patient Consulting Charge Item,出患者諮詢費用項目
Credit Months,信貸月份
Fulfilled,達到
Discharge Scheduled,出院預定
Relieving Date must be greater than Date of Joining,解除日期必須大於加入的日期
Cashier,出納員
Row {0}: Please check 'Is Advance' against Account {1} if this is an advance entry.,行{0}：請檢查'是進階'對科目{1}，如果這是一個進階條目。
Warehouse {0} does not belong to company {1},倉庫{0}不屬於公司{1}
Profit & Loss,收益與損失
Please setup Students under Student Groups,請設置學生組的學生
Item Website Specification,項目網站規格
Item {0} has reached its end of life on {1},項{0}已達到其壽命結束於{1}
Is Internal Customer,是內部客戶
"If Auto Opt In is checked, then the customers will be automatically linked with the concerned Loyalty Program (on save)",如果選中自動選擇，則客戶將自動與相關的忠誠度計劃鏈接（保存時）
Stock Reconciliation Item,庫存調整項目
Sales Invoice No,銷售發票號碼
Supply Type,供應類型
Min Order Qty,最小訂貨量
Student Group Creation Tool Course,學生組創建工具課程
Do Not Contact,不要聯絡
People who teach at your organisation,誰在您的組織教人
Software Developer,軟件開發人員
Minimum Order Qty,最低起訂量
Supplier Type,供應商類型
Course Start Date,課程開始日期
Student Batch-Wise Attendance,學生分批出席
Allow user to edit Rate,允許用戶編輯率
Publish in Hub,在發布中心
Student Admission,學生入學
Terretory,Terretory,
Item {0} is cancelled,項{0}將被取消
Depreciation Row {0}: Depreciation Start Date is entered as past date,折舊行{0}：折舊開始日期作為過去的日期輸入
Fulfilment Terms and Conditions,履行條款和條件
Material Request,物料需求
Update Clearance Date,更新日期間隙
Item {0} not found in 'Raw Materials Supplied' table in Purchase Order {1},項目{0}未發現“原材料提供&#39;表中的採購訂單{1}
Total Principal Amount,本金總額
Relation,關係
Mother,母親
Reservation End Time,預訂結束時間
Biennial,雙年展
BOM Variance Report,BOM差異報告
Confirmed orders from Customers.,確認客戶的訂單。
Rejected Quantity,拒絕數量
Payment request {0} created,已創建付款請求{0}
Admitted Datetime,承認日期時間
Backflush raw materials from work-in-progress warehouse,從在製品庫中反沖原料
Open Orders,開放訂單
Low Sensitivity,低靈敏度
Order rescheduled for sync,訂單重新安排同步
Suggestions,建議
Set Item Group-wise budgets on this Territory. You can also include seasonality by setting the Distribution.,在此地域設定跨群組項目間的預算。您還可以通過設定分配來包含季節性。
Payment Term Name,付款條款名稱
Create documents for sample collection,創建樣本收集文件
Payment against {0} {1} cannot be greater than Outstanding Amount {2},對支付{0} {1}不能大於未償還{2}
All Healthcare Service Units,所有醫療服務單位
Mobile No.,手機號碼
Generate Schedule,生成時間表
Expense Head,總支出
Please select Charge Type first,請先選擇付款類別
"You can define all the tasks which need to carried out for this crop here. The day field is used to mention the day on which the task needs to be carried out, 1 being the 1st day, etc.. ",你可以在這裡定義所有需要進行的作業。日場是用來提及任務需要執行的日子，1日是第一天等。
Student Group Student,學生組學生
Education Settings,教育設置
Balance In Base Currency,平衡基礎貨幣
Max Grade,最高等級
New Quotations,新報價
Attendance not submitted for {0} as {1} on leave.,在{0}上沒有針對{1}上的考勤出席。
Payment Order,付款單
Shipping County,航運縣
Learn,學習
Enable Deferred Expense,啟用延期費用
Next Depreciation Date,接下來折舊日期
Activity Cost per Employee,每個員工活動費用
Settings for Accounts,會計設定
Supplier Invoice No exists in Purchase Invoice {0},供應商發票不存在採購發票{0}
Manage Sales Person Tree.,管理銷售人員樹。
"Cannot process route, since Google Maps Settings is disabled.",由於禁用了Google地圖設置，因此無法處理路線。
Outstanding Cheques and Deposits to clear,傑出的支票及存款清除
Synced With Hub,同步轂
Fleet Manager,車隊經理
Row #{0}: {1} can not be negative for item {2},行＃{0}：{1}不能為負值對項{2}
Wrong Password,密碼錯誤
Variant Of,變種
Completed Qty can not be greater than 'Qty to Manufacture',完成數量不能大於“數量來製造”
Closing Account Head,關閉帳戶頭
External Work History,外部工作經歷
Circular Reference Error,循環引用錯誤
Student Report Card,學生報告卡
From Pin Code,來自Pin Code,
Guardian1 Name,Guardian1名稱
In Words (Export) will be visible once you save the Delivery Note.,送貨單一被儲存，(Export)就會顯示出來。
Distance from left edge,從左側邊緣的距離
{0} units of [{1}](#Form/Item/{1}) found in [{2}](#Form/Warehouse/{2}),{0} [{1}]的單位（＃窗體/項目/ {1}）在[{2}]研究發現（＃窗體/倉儲/ {2}）
Industry,行業
Rate & Amount,價格和金額
Transfer Material Against Job Card,轉移材料反對工作卡
Notify by Email on creation of automatic Material Request,在建立自動材料需求時以電子郵件通知
Please set Hotel Room Rate on {},請在{}上設置酒店房價
Multi Currency,多幣種
Invoice Type,發票類型
Delivery Note,送貨單
Setting up Taxes,建立稅
Cost of Sold Asset,出售資產的成本
Payment Entry has been modified after you pulled it. Please pull it again.,付款項被修改，你把它之後。請重新拉。
New Student Batch,新學生批次
{0} entered twice in Item Tax,{0}輸入兩次項目稅
Summary for this week and pending activities,本週和待活動總結
Admitted,錄取
Amount After Depreciation,折舊金額後
Upcoming Calendar Events,即將到來的日曆事件
Variant Attributes,變量屬性
Please select month and year,請選擇年份和月份
Company Email,企業郵箱
Debit Amount in Account Currency,在科目幣種借記金額
Order Value,訂單價值
Order Value,訂單價值
Certified Consultant,認證顧問
Bank/Cash transactions against party or for internal transfer,銀行/現金對一方或內部轉讓交易
Valid for Countries,有效的國家
This Item is a Template and cannot be used in transactions. Item attributes will be copied over into the variants unless 'No Copy' is set,這個項目是一個模板，並且可以在交易不能使用。項目的屬性將被複製到變型，除非“不複製”設置
Grant Application,授予申請
Total Order Considered,總訂貨考慮
Not Certified,未認證
New Asset Value,新資產價值
Rate at which Customer Currency is converted to customer's base currency,公司貨幣被換算成客戶基礎貨幣的匯率
Course Scheduling Tool,排課工具
Row #{0}: Purchase Invoice cannot be made against an existing asset {1},行＃{0}：採購發票不能對現有資產進行{1}
LInked Analysis,LInked分析
POS Closing Voucher,POS關閉憑證
Lapsed,失效
Tax Rate,稅率
Backflush Raw Materials of Subcontract Based On,基於CRM的分包合同反向原材料
Purchase Invoice {0} is already submitted,採購發票{0}已經提交
Row # {0}: Batch No must be same as {1} {2},行＃{0}：批號必須與{1} {2}
Material Request Plan Item,材料申請計劃項目
Convert to non-Group,轉換為非集團
Good/Steady,好/穩定
Invoice Date,發票日期
Debit Amount,借方金額
There can only be 1 Account per Company in {0} {1},只能有每公司1科目{0} {1}
Response Result Key Path,響應結果關鍵路徑
Inter Company Journal Entry,Inter公司日記帳分錄
For quantity {0} should not be grater than work order quantity {1},數量{0}不應超過工單數量{1}
Please see attachment,請參閱附件
% Received,％ 已收
Create Student Groups,創建挺起胸
Weekends,週末
Credit Note Amount,信用額度
Action Document,行動文件
Website URL,網站網址
Instructions,說明
Inspected By,檢查
Maintenance Type,維護類型
{0} - {1} is not enrolled in the Course {2},{0}  -  {1} 未在課程中註冊 {2}
Student Name: ,學生姓名：
Difference,區別
Delay between Delivery Stops,交貨停止之間的延遲
Serial No {0} does not belong to Delivery Note {1},序列號{0}不屬於送貨單{1}
"There seems to be an issue with the server's GoCardless configuration. Don't worry, in case of failure, the amount will get refunded to your account.",服務器的GoCardless配置似乎存在問題。別擔心，如果失敗，這筆款項將退還給您的帳戶。
Add Items,添加項目
Item Quality Inspection Parameter,產品質量檢驗參數
Schedule Date,排定日期
Packed Item,盒裝產品
Default settings for buying transactions.,採購交易的預設設定。
Activity Cost exists for Employee {0} against Activity Type - {1},存在活動費用為員工{0}對活動類型 -  {1}
Mandatory field - Get Students From,強制性領域 - 獲得學生
Mandatory field - Get Students From,強制性領域 - 獲得學生
Enrolled courses,入學課程
Enrolled courses,入學課程
Currency Exchange,外幣兌換
Item Name,項目名稱
Approving User  (above authorized value),批准的用戶（上述授權值）
Credit Balance,貸方餘額
Widowed,寡
Request for Quotation,詢價
Require Lab Test Approval,需要實驗室測試批准
Working Hours,工作時間
Total Outstanding,總計傑出
Change the starting / current sequence number of an existing series.,更改現有系列的開始/當前的序列號。
Strength,強度
Create a new Customer,創建一個新的客戶
Expiring On,即將到期
"If multiple Pricing Rules continue to prevail, users are asked to set Priority manually to resolve conflict.",如果有多個定價規則繼續有效，用戶將被要求手動設定優先順序來解決衝突。
Create Purchase Orders,創建採購訂單
Purchase Register,購買註冊
Rechedule,Rechedule,
Applicable Charges,相關費用
Vehicle Date,車日期
Reason for losing,原因丟失
Please select Drug,請選擇藥物
Lead Owner cannot be same as the Lead,主導所有人不能等同於主導者
Allocated amount can not greater than unadjusted amount,分配的金額不能超過未調整的量更大
Area UOM,區域UOM,
Workstation is closed on the following dates as per Holiday List: {0},工作站在以下日期關閉按假日列表：{0}
Opportunities,機會
Single,單
Total Loan Repayment,總貸款還款
Cost of Goods Sold,銷貨成本
Please enter Cost Center,請輸入成本中心
Dosage,劑量
Sales Order,銷售訂單
Avg. Selling Rate,平均。賣出價
Examiner Name,考官名稱
No Result,沒有結果
Quantity and Rate,數量和速率
% Installed,％已安裝
Classrooms/ Laboratories etc where lectures can be scheduled.,教室/實驗室等在那裡的演講可以預定。
Company currencies of both the companies should match for Inter Company Transactions.,兩家公司的公司貨幣應該符合Inter公司交易。
Please enter company name first,請先輸入公司名稱
Supplier Name,供應商名稱
Read the ERPNext Manual,閱讀ERPNext手冊
01-Sales Return,01-銷售退貨
Temporarily on Hold,暫時擱置
Is Group,是集團
Credit Note {0} has been created automatically,信用票據{0}已自動創建
Automatically Set Serial Nos based on FIFO,自動設置序列號的基礎上FIFO,
Check Supplier Invoice Number Uniqueness,檢查供應商發票編號唯一性
Primary Address Details,主要地址詳情
Asset Maintenance Log,資產維護日誌
'To Case No.' cannot be less than 'From Case No.',“至案件編號”不能少於'從案件編號“
Non Profit,非營利
Not Started,未啟動
Channel Partner,渠道合作夥伴
Old Parent,舊上級
Mandatory field - Academic Year,必修課 - 學年
Mandatory field - Academic Year,必修課 - 學年
{0} {1} is not associated with {2} {3},{0} {1} 未與 {2} {3} 關聯
Customize the introductory text that goes as a part of that email. Each transaction has a separate introductory text.,自定義去作為郵件的一部分的介紹文字。每筆交易都有一個單獨的介紹性文字。
Row {0} : Operation is required against the raw material item {1},行{0}：對原材料項{1}需要操作
Please set default payable account for the company {0},請為公司{0}設置預設應付帳款
Transaction not allowed against stopped Work Order {0},不允許對停止的工單{0}進行交易
Min Doc Count,最小文件計數
Global settings for all manufacturing processes.,所有製造過程中的全域設定。
Accounts Frozen Upto,科目被凍結到
Sent On,發送於
Attribute {0} selected multiple times in Attributes Table,屬性{0}多次選擇在屬性表
Employee record is created using selected field. ,使用所選欄位創建員工記錄。
Not Applicable,不適用
UK,聯合王國
Opening Invoice Item,打開發票項目
Required Date,所需時間
Billing Address,帳單地址
Statement Headers,聲明標題
Billing County,開票縣
"If checked, the tax amount will be considered as already included in the Print Rate / Print Amount",如果選中，稅額將被視為已包含在列印速率/列印數量
Message for Supplier,消息供應商
Work Order,工作指示
Total Qty,總數量
Guardian2 Email ID,Guardian2電子郵件ID,
Guardian2 Email ID,Guardian2電子郵件ID,
Show in Website (Variant),展網站（變體）
Health Concerns,健康問題
Select Payroll Period,選擇工資期
Reserved for sale,保留出售
From Package No.,從包裹編號
To Range,為了範圍
Securities and Deposits,證券及存款
"Can't change valuation method, as there are transactions against some items which does not have it's own valuation method",不能改變估值方法，因為有一些項目沒有自己的估值方法的交易
Attended by Parents,由父母出席
Employee {0} has already applied for {1} on {2} : ,員工{0}已在{2}上申請{1}：
AB Positive,AB積極
Pending activities for today,今天待定活動
Applicable for external driver,適用於外部驅動器
Used for Production Plan,用於生產計劃
Cannot cancel transaction for Completed Work Order.,無法取消已完成工單的交易。
Time Between Operations (in mins),作業間隔時間（以分鐘計）
PO already created for all sales order items,已為所有銷售訂單項創建採購訂單
Occupied,佔據
{0} {1} is cancelled so the action cannot be completed,{0} {1} 被取消，因此無法完成操作
Buyer of Goods and Services.,買家商品和服務。
Accounts Payable,應付帳款
The amount of {0} set in this payment request is different from the calculated amount of all payment plans: {1}. Make sure this is correct before submitting the document.,此付款申請中設置的{0}金額與所有付款計劃的計算金額不同：{1}。在提交文檔之前確保這是正確的。
Allergies,過敏
The selected BOMs are not for the same item,所選的材料清單並不同樣項目
Change Item Code,更改物料代碼
Blood Pressure (systolic),血壓（收縮期）
Valid Upto,到...為止有效
Warn Purchase Orders,警告採購訂單
List a few of your customers. They could be organizations or individuals.,列出一些你的客戶。他們可以是組織或個人。
Enough Parts to Build,足夠的配件組裝
POS Profile User,POS配置文件用戶
Row {0}: Depreciation Start Date is required,行{0}：折舊開始日期是必需的
Service Start Date,服務開始日期
Subscription Invoice,訂閱發票
Direct Income,直接收入
Date TIme,約會時間
"Can not filter based on Account, if grouped by Account",7 。總計：累積總數達到了這一點。
Administrative Officer,政務主任
Setting up company and taxes,建立公司和稅收
Please select Course,請選擇課程
Codification Table,編纂表
Hrs,小時
Please select Company,請選擇公司
Difference Account,差異科目
Supplier GSTIN,供應商GSTIN,
Cannot close task as its dependant task {0} is not closed.,不能因為其依賴的任務{0}沒有關閉關閉任務。
Please enter Warehouse for which Material Request will be raised,請輸入物料需求欲增加的倉庫
Additional Operating Cost,額外的運營成本
Lab Routine,實驗室常規
Cosmetics,化妝品
Please select Completion Date for Completed Asset Maintenance Log,請選擇已完成資產維護日誌的完成日期
"To merge, following properties must be same for both items",若要合併，以下屬性必須為這兩個項目是相同的
Block Supplier,塊供應商
Net Weight,淨重
Emergency Phone,緊急電話
{0} {1} does not exist.,{0} {1} 不存在。
Buy,購買
Serial No Warranty Expiry,序列號保修到期
Offline POS Name,離線POS名稱
Student Application,學生申請
Payment Reference,付款憑據
Hold Type,保持類型
Please define grade for Threshold 0%,請定義等級為閾值0％
Please define grade for Threshold 0%,請定義等級為閾值0％
Bank Statement Transaction Payment Item,銀行對賬單交易付款項目
To Deliver,為了提供
Item,項目
High Sensitivity,高靈敏度
Volunteer Type information.,志願者類型信息。
Cash Flow Mapping Template,現金流量映射模板
Show Return Entries,顯示返回條目
Serial no item cannot be a fraction,序號項目不能是一個分數
Difference (Dr - Cr),差異（Dr - Cr）
Profit and Loss,損益
"Not permitted, configure Lab Test Template as required",不允許，根據需要配置實驗室測試模板
Risk Factors,風險因素
Occupational Hazards and Environmental Factors,職業危害與環境因素
Stock Entries already created for Work Order ,已為工單創建的庫存條目
Respiratory rate,呼吸頻率
Managing Subcontracting,管理轉包
Body Temperature,體溫
Project will be accessible on the website to these users,項目將在網站向這些用戶上訪問
Cannot cancel {0} {1} because Serial No {2} does not belong to the warehouse {3},無法取消{0} {1}，因為序列號{2}不屬於倉庫{3}
Default Deferred Expense Account,默認遞延費用科目
Define Project type.,定義項目類型。
Weighting Function,加權函數
OP Consulting Charge,OP諮詢費
Setup your ,設置你的
Show Marks,顯示標記
Get Latest Query,獲取最新查詢
Rate at which Price list currency is converted to company's base currency,價目表貨幣被換算成公司基礎貨幣的匯率
Account {0} does not belong to company: {1},科目{0}不屬於公司：{1}
Abbreviation already used for another company,另一家公司已使用此縮寫
Default Customer Group,預設客戶群組
"If disable, 'Rounded Total' field will not be visible in any transaction",如果禁用，“圓角總計”字段將不可見的任何交易
Operating Cost,營業成本
Produced Items,生產物品
Match Transaction to Invoices,將交易與發票匹配
Unblock Invoice,取消屏蔽發票
Increment cannot be 0,增量不能為0,
Delete Company Transactions,刪除公司事務
Reference No and Reference Date is mandatory for Bank transaction,參考編號和參考日期是強制性的銀行交易
Add / Edit Taxes and Charges,新增 / 編輯稅金及費用
Supplier Invoice No,供應商發票號碼
For reference,供參考
Appointment Confirmation,預約確認
"Cannot delete Serial No {0}, as it is used in stock transactions",無法刪除序列號{0}，因為它採用的是現貨交易
Closing (Cr),關閉（Cr）
Move Item,移動項目
Warranty Period (Days),保修期限（天數）
Total Credit/ Debit Amount should be same as linked Journal Entry,總信用/借方金額應與鏈接的日記帳分錄相同
Installation Note Item,安裝注意項
Pending Qty,待定數量
{0} {1} is not active,{0} {1}是不活動
Freight and Forwarding Account,貨運和轉運科目
Setup cheque dimensions for printing,設置檢查尺寸打印
Bloated,脹
Supplier Warehouse mandatory for sub-contracted Purchase Receipt,對於轉包的採購入庫單，供應商倉庫是強制性輸入的。
Total Commission,佣金總計
Tax Withholding Account,扣繳稅款科目
Sales Partner,銷售合作夥伴
All Supplier scorecards.,所有供應商記分卡。
Purchase Receipt Required,需要採購入庫單
Rail,軌
Target warehouse in row {0} must be same as Work Order,行{0}中的目標倉庫必須與工單相同
Valuation Rate is mandatory if Opening Stock entered,估價費用是強制性的，如果打開庫存進入
No records found in the Invoice table,沒有在發票表中找到記錄
Please select Company and Party Type first,請選擇公司和黨的第一型
"Already set default in pos profile {0} for user {1}, kindly disabled default",已經在用戶{1}的pos配置文件{0}中設置了默認值，請禁用默認值
Financial / accounting year.,財務／會計年度。
Accumulated Values,累積值
"Sorry, Serial Nos cannot be merged",對不起，序列號無法合併
Customer Group will set to selected group while syncing customers from Shopify,客戶組將在同步Shopify客戶的同時設置為選定的組
Territory is Required in POS Profile,POS Profile中需要領域
Hub User,中心用戶
Make Sales Order,製作銷售訂單
Salary Slip submitted for period from {0} to {1},從{0}到{1}
Project Task,項目任務
Redeemed Points,兌換積分
Lead Id,潛在客戶標識
Grand Total,累計
Section Code,部分代碼
Payslip,工資單
Item Cart,項目車
Fiscal Year Start Date should not be greater than Fiscal Year End Date,會計年度開始日期應不大於財政年度結束日期
Resolution,決議
Personal Bio,個人自傳
Membership ID,會員ID,
Delivered: {0},交貨：{0}
Connected to QuickBooks,連接到QuickBooks,
Payable Account,應付帳款
Type of Payment,付款類型
Billing and Delivery Status,結算和交貨狀態
Repeat Customers,回頭客
Create Variant,創建變體
Shipping Bill Date,運費單日期
Production Plan,生產計劃
Opening Invoice Creation Tool,打開發票創建工具
Sales Return,銷貨退回
Set Qty in Transactions based on Serial No Input,根據序列號輸入設置交易數量
Total Stock Summary,總庫存總結
"You can only plan for upto {0} vacancies and budget {1} \
				for {2} as per staffing plan {3} for parent company {4}.",根據母公司{4}的人員配置計劃{3}，您只能針對{2}計劃最多{0}個職位空缺和預算{1} \。
Posted By,發布者
Delivered by Supplier (Drop Ship),由供應商交貨（直接發運）
Confirmation Message,確認訊息
Database of potential customers.,數據庫的潛在客戶。
Customer or Item,客戶或項目
Customer database.,客戶數據庫。
Quotation To,報價到
Opening (Cr),開啟（Cr ）
Default Unit of Measure for Item {0} cannot be changed directly because you have already made some transaction(s) with another UOM. You will need to create a new Item to use a different Default UOM.,測度項目的默認單位{0}不能直接改變，因為你已經做了一些交易（S）與其他計量單位。您將需要創建一個新的項目，以使用不同的默認計量單位。
Allocated amount can not be negative,分配金額不能為負
Share Balance,份額平衡
AWS Access Key ID,AWS訪問密鑰ID,
Billed Amt,已結算額
A logical Warehouse against which stock entries are made.,對這些庫存分錄帳進行的邏輯倉庫。
Total Outstanding: {0},總計：{0}
Sales Invoice Timesheet,銷售發票時間表
Reference No & Reference Date is required for {0},參考號與參考日期須為{0}
Default Invoice Naming Series,默認發票命名系列
"Create Employee records to manage leaves, expense claims and payroll",建立員工檔案管理葉，報銷和工資
An error occurred during the update process,更新過程中發生錯誤
Restaurant Reservation,餐廳預訂
Proposal Writing,提案寫作
Payment Entry Deduction,輸入付款扣除
Wrapping up,包起來
Notify Customers via Email,通過電子郵件通知客戶
Batch Number Series,批號系列
Another Sales Person {0} exists with the same Employee id,另外銷售人員{0}存在具有相同員工ID,
Authorization Settings,授權設置
Masters,資料主檔
Maximum Assessment Score,最大考核評分
Update Bank Transaction Dates,更新銀行交易日期
Time Tracking,時間跟踪
DUPLICATE FOR TRANSPORTER,輸送機重複
Fiscal Year Company,會計年度公司
DN Detail,DN詳細
Billed,計費
Batch Description,批次說明
Creating student groups,創建學生組
Creating student groups,創建學生組
"Payment Gateway Account not created, please create one manually.",支付閘道科目沒有創建，請手動創建一個。
Not eligible for the admission in this program as per DOB,按照DOB的規定，沒有資格參加本計劃
Sales Taxes and Charges,銷售稅金及費用
Sibling Details,兄弟姐妹詳情
Automatically triggers the feedback request based on conditions.,自動觸發基於條件的反饋請求。
Reason for Resignation,辭退原因
Credit Note Issued,信用票據發行
Invoice/Journal Entry Details,發票/日記帳分錄詳細資訊
{0} '{1}' not in Fiscal Year {2},{0}“ {1}”不在財政年度{2}
Settings for Buying Module,設置購買模塊
Asset {0} does not belong to company {1},資產{0}不屬於公司{1}
Please enter Purchase Receipt first,請先輸入採購入庫單
Supplier Naming By,供應商命名
Default Costing Rate,默認成本核算率
Maintenance Schedule,維護計劃
"Then Pricing Rules are filtered out based on Customer, Customer Group, Territory, Supplier, Supplier Type, Campaign, Sales Partner etc.",然後定價規則將被過濾掉基於客戶，客戶群組，領地，供應商，供應商類型，活動，銷售合作夥伴等。
Net Change in Inventory,在庫存淨變動
Passport Number,護照號碼
Relation with Guardian2,與關係Guardian2,
Manager,經理
From Fiscal Year,從財政年度開始
New credit limit is less than current outstanding amount for the customer. Credit limit has to be atleast {0},新的信用額度小於當前餘額為客戶著想。信用額度是ATLEAST {0}
Please set account in Warehouse {0},請在倉庫{0}中設置會計科目
'Based On' and 'Group By' can not be same,“根據”和“分組依據”不能相同
Sales Person Targets,銷售人員目標
In minutes,在幾分鐘內
Resolution Date,決議日期
Compound,複合
Dispatch Notification,發貨通知
Max number of visit,最大訪問次數
Timesheet created:,創建時間表：
Please set default Cash or Bank account in Mode of Payment {0},請設定現金或銀行帳戶的預設付款方式{0}
Enroll,註冊
GST Settings,GST設置
Currency should be same as Price List Currency: {0},貨幣應與價目表貨幣相同：{0}
Customer Naming By,客戶命名由
Will show the student as Present in Student Monthly Attendance Report,將顯示學生每月學生出勤記錄報告為存在
Depreciation Amount,折舊額
Convert to Group,轉換為集團
Activity Type,活動類型
For individual supplier,對於個別供應商
Base Hour Rate(Company Currency),基數小時率（公司貨幣）
Delivered Amount,交付金額
Redemption Date,贖回日期
Item Balance,項目平衡
Packing List,包裝清單
Purchase Orders given to Suppliers.,購買給供應商的訂單。
Transfer Qty,轉移數量
Asset Location,資產位置
Shipping Zipcode,運輸郵編
Report Settings,報告設置
Projects User,項目用戶
Consumed,消費
{0}: {1} not found in Invoice Details table,{0}：在發票明細表中找不到{1}
Asset Owner Company,資產所有者公司
Round Off Cost Center,四捨五入成本中心
Maintenance Visit {0} must be cancelled before cancelling this Sales Order,維護訪問{0}必須取消這個銷售訂單之前被取消
Material Transfer,物料轉倉
Cost Center Number,成本中心編號
Could not find path for ,找不到路徑
Opening (Dr),開啟（Dr）
Posting timestamp must be after {0},登錄時間戳記必須晚於{0}
To make recurring documents,複製文件
GST Itemised Purchase Register,GST成品採購登記冊
Landed Cost Taxes and Charges,到岸成本稅費
Actual Start Time,實際開始時間
Deferred Expense Account,遞延費用科目
Operation Time,操作時間
Finish,完
Total Billed Hours,帳單總時間
Write Off Amount,核銷金額
Bill No,帳單號碼
Gain/Loss Account on Asset Disposal,在資產處置收益/損失科目
Grouped,分組
Delivery Note Required,要求送貨單
Bank Guarantee Number,銀行擔保編號
Bank Guarantee Number,銀行擔保編號
Assessment Criteria,評估標準
Basic Rate (Company Currency),基礎匯率（公司貨幣）
Split Issue,拆分問題
Student Attendance,學生出勤
Time Sheet,時間表
Backflush Raw Materials Based On,倒沖原物料基於
Port Code,港口代碼
Reserve Warehouse,儲備倉庫
Lead is an Organization,領導是一個組織
Other Details,其他詳細資訊
Suplier,Suplier,
Test Template,測試模板
Chapter information.,章節信息。
Accounts,會計
Odometer Value (Last),里程表值（最後）
Templates of supplier scorecard criteria.,供應商計分卡標準模板。
Marketing,市場營銷
Redeem Loyalty Points,兌換忠誠度積分
Payment Entry is already created,已創建付款輸入
Get Suppliers,獲取供應商
Current Stock,當前庫存
Row #{0}: Asset {1} does not linked to Item {2},行＃{0}：資產{1}不掛項目{2}
Account {0} has been entered multiple times,帳戶{0}已多次輸入
Expenses Included In Valuation,支出計入估值
You can only renew if your membership expires within 30 days,如果您的會員資格在30天內到期，您只能續訂
Show Stock Availability,顯示庫存可用性
Set {0} in asset category {1} or company {2},在資產類別{1}或公司{2}中設置{0}
Longitude,經度
Absent Student Report,缺席學生報告
Crop Spacing UOM,裁剪間隔UOM,
Single Tier Program,單層計劃
Only select if you have setup Cash Flow Mapper documents,只有在設置了“現金流量映射器”文檔時才能選擇
From Address 1,來自地址1,
Next email will be sent on:,接下來的電子郵件將被發送：
Per Week,每個星期
Item has variants.,項目已變種。
Total Student,學生總數
Item {0} not found,項{0}未找到
Stock Value,庫存價值
Tree Type,樹類型
Qty Consumed Per Unit,數量消耗每單位
IGST Account,IGST帳戶
Warranty Expiry Date,保證期到期日
Quantity and Warehouse,數量和倉庫
Commission Rate (%),佣金比率（％）
Please select Program,請選擇程序
Please select Program,請選擇程序
Estimated Cost,估計成本
Link to material requests,鏈接到材料請求
Credit Card Entry,信用卡進入
Company and Accounts,公司與科目
In Value,在數值
Depreciation Options,折舊選項
Either location or employee must be required,必須要求地點或員工
Invalid Posting Time,發佈時間無效
Condition and Formula,條件和公式
Campaign Name,活動名稱
Healthcare Practitioner,醫療從業者
Close Opportunity After Days,關閉機會後日
License Details,許可證詳情
The field From Shareholder cannot be blank,來自股東的字段不能為空
Supply Raw Materials,供應原料
Current Assets,流動資產
{0} is not a stock Item,{0}不是庫存項目
Default Account,預設科目
Please select Sample Retention Warehouse in Stock Settings first,請先在庫存設置中選擇樣品保留倉庫
Please select the Multiple Tier Program type for more than one collection rules.,請為多個收集規則選擇多層程序類型。
Received Amount (Company Currency),收到的款項（公司幣種）
Lead must be set if Opportunity is made from Lead,如果機會是由前導而來，前導必須被設定
Payment Cancelled. Please check your GoCardless Account for more details,付款已取消。請檢查您的GoCardless帳戶以了解更多詳情
Send with Attachment,發送附件
Please select weekly off day,請選擇每週休息日
O Negative,O負面
Planned End Time,計劃結束時間
Sales Person Target Variance Item Group-Wise,銷售人員跨項目群組間的目標差異
Account with existing transaction cannot be converted to ledger,帳戶與現有的交易不能被轉換為總賬
Memebership Type Details,Memebership類型詳細信息
Customer's Purchase Order No,客戶的採購訂單編號
Consume Stock,消費庫存
Budget Against,反對財政預算案
Auto Material Requests Generated,汽車材料的要求生成
Lost,丟失
You can not enter current voucher in 'Against Journal Entry' column,在您不能輸入電流券“對日記帳分錄”專欄
Reserved for manufacturing,預留製造
Opportunity From,機會從
Row {0}: {1} Serial numbers required for Item {2}. You have provided {3}.,行{0}：{1}項目{2}所需的序列號。你已經提供{3}。
Please select a table,請選擇一張桌子
Website Specifications,網站規格
Particulars,細節
Row {0}: Conversion Factor is mandatory,列#{0}：轉換係數是強制性的
"Multiple Price Rules exists with same criteria, please resolve conflict by assigning priority. Price Rules: {0}",海報價格規則，同樣的標準存在，請通過分配優先解決衝突。價格規則：{0}
Exchange Rate Revaluation Account,匯率重估科目
Cannot deactivate or cancel BOM as it is linked with other BOMs,無法關閉或取消BOM，因為它是與其他材料明細表鏈接
Please select Company and Posting Date to getting entries,請選擇公司和發布日期以獲取條目
Maintenance,維護
Get from Patient Encounter,從患者遭遇中獲取
Subscriber,訂戶
Item Attribute Value,項目屬性值
Please Update your Project Status,請更新您的項目狀態
Currency Exchange must be applicable for Buying or for Selling.,貨幣兌換必須適用於買入或賣出。
Maximum sample quantity that can be retained,可以保留的最大樣品數量
How is the Project Progressing Right Now?,項目現在進展如何？
Row {0}# Item {1} cannot be transferred more than {2} against Purchase Order {3},對於採購訂單{3}，行{0}＃項目{1}不能超過{2}
Sales campaigns.,銷售活動。
Make Timesheet,製作時間表
"Standard tax template that can be applied to all Sales Transactions. This template can contain list of tax heads and also other expense / income heads like ""Shipping"", ""Insurance"", ""Handling"" etc.

#### Note,

The tax rate you define here will be the standard tax rate for all **Items**. If there are **Items** that have different rates, they must be added in the **Item Tax** table in the **Item** master.

#### Description of Columns,

1. Calculation Type: 
    - This can be on **Net Total** (that is the sum of basic amount).
    - **On Previous Row Total / Amount** (for cumulative taxes or charges). If you select this option, the tax will be applied as a percentage of the previous row (in the tax table) amount or total.
    - **Actual** (as mentioned).
2. Account Head: The Account ledger under which this tax will be booked,
3. Cost Center: If the tax / charge is an income (like shipping) or expense it needs to be booked against a Cost Center.
4. Description: Description of the tax (that will be printed in invoices / quotes).
5. Rate: Tax rate.
6. Amount: Tax amount.
7. Total: Cumulative total to this point.
8. Enter Row: If based on ""Previous Row Total"" you can select the row number which will be taken as a base for this calculation (default is the previous row).
9. Is this Tax included in Basic Rate?: If you check this, it means that this tax will not be shown below the item table, but will be included in the Basic Rate in your main item table. This is useful where you want give a flat price (inclusive of all taxes) price to customers.","可應用到所有銷售交易稅的標準模板。這個模板可以包含稅收元首和像“送貨”，“保險”還包括其他費用/收入頭列表中，“處理”等

 ####注

稅率您定義，這裡將是標準稅率對所有** **的項目。如果有** **物品具有不同的速率，就必須在**項計稅添加**表中的** **項目主。

 ####列

 1的說明。計算類型：
 - 這可以是在淨** **總（即基本量的總和）。
 - **以前的行總計/金額**（對於累計稅費）。如果選擇此選項，稅收將與前行的百分比（在稅率表）量或總被應用。
 - ** **實際（如前所述）。
 2。帳戶負責人：該帳戶下的台帳此稅收將被黃牌警告
 3。成本中心：如果稅/收費收入（如海運）或費用，它需要對一個成本中心預訂。
 4。說明：稅收的說明（將在發票/報價印刷）。
 5。速度：稅率。
 6。金額：稅額。
 7。總計：累積總數達到了這一點。
 8。輸入行：如果基於“前行匯總”，您可以選擇將被視為這種計算基礎（預設值是前行）的行號。
 9。這是含稅的基本速率？：如果你檢查這一點，就意味著這個稅不會顯示在項目表中，但在你的主項表將被納入基本速率。你想要給一個單位的價格（包括所有稅費）的價格為顧客這是非常有用的。"
Bank A/C No.,銀行A/C No.
Reading 7,7閱讀
Lab Test,實驗室測試
Student Report Generation Tool,學生報告生成工具
Healthcare Schedule Time Slot,醫療保健計劃時間槽
Doc Name,文件名稱
Default settings for Shopping Cart,對購物車的預設設定
Add Timeslots,添加時代
Please set Account in Warehouse {0} or Default Inventory Account in Company {1},請在倉庫{0}中設科目或在公司{1}中設置默認庫存科目
Asset scrapped via Journal Entry {0},通過資產日記帳分錄報廢{0}
Review Invitation Sent,審核邀請已發送
Employee Transfer Property,員工轉移財產
From Time Should Be Less Than To Time,從時間應該少於時間
Biotechnology,生物技術
"Item {0} (Serial No: {1}) cannot be consumed as is reserverd\
						 to fullfill Sales Order {2}.",無法將項目{0}（序列號：{1}）用作reserverd \以完成銷售訂單{2}。
Office Maintenance Expenses,Office維護費用
Go to ,去
Update Price from Shopify To ERPNext Price List,將Shopify更新到ERPNext價目表
Setting up Email Account,設置電子郵件帳戶
Please enter Item first,請先輸入品項
Downtime,停機
Liability,責任
Academic Term: ,學術期限：
Default Cost of Goods Sold Account,銷貨成本科目
Sample quantity {0} cannot be more than received quantity {1},採樣數量{0}不能超過接收數量{1}
Price List not selected,未選擇價格列表
Send Email,發送電子郵件
Warning: Invalid Attachment {0},警告：無效的附件{0}
Max Sample Quantity,最大樣品量
No Permission,無權限
Contract Fulfilment Checklist,合同履行清單
Heart Rate / Pulse,心率/脈搏
Default Bank Account,預設銀行會計科目
"To filter based on Party, select Party Type first",要根據黨的篩選，選擇黨第一類型
'Update Stock' can not be checked because items are not delivered via {0},不能勾選`更新庫存'，因為項目未交付{0}
Acquisition Date,採集日期
Nos,NOS,
Items with higher weightage will be shown higher,具有較高權重的項目將顯示更高的可
Lab Tests and Vital Signs,實驗室測試和重要標誌
Bank Reconciliation Detail,銀行對帳詳細
Row #{0}: Asset {1} must be submitted,行＃{0}：資產{1}必須提交
If subcontracted to a vendor,如果分包給供應商
Student Group is already updated.,學生組已經更新。
Student Group is already updated.,學生組已經更新。
Project Update.,項目更新。
All Customer Contact,所有的客戶聯絡
Tree Details,樹詳細信息
Registered,註冊
Availability Timeslot,可用時間段
Support Analytics,支援分析
"If you have any questions, please get back to us.",如果您有任何疑問，請再次與我們聯繫。
Cash Flow Mapper,現金流量映射器
Website Warehouse,網站倉庫
Minimum Invoice Amount,最小發票金額
{0} {1}: Cost Center {2} does not belong to Company {3},{0} {1}：成本中心{2}不屬於公司{3}
Upload your letter head (Keep it web friendly as 900px by 100px),上傳你的信頭（保持網頁友好，900px乘100px）
{0} {1}: Account {2} cannot be a Group,{0} {1}科目{2}不能是一個群組科目
Timesheet {0} is already completed or cancelled,時間表{0}已完成或取消
No tasks,沒有任務
Sales Invoice {0} created as paid,銷售發票{0}已創建為已付款
Copy Fields to Variant,將字段複製到變式
Opening Accumulated Depreciation,打開累計折舊
Program Enrollment Tool,計劃註冊工具
C-Form records,C-往績紀錄
The shares already exist,股份已經存在
Customer and Supplier,客戶和供應商
Email Digest Settings,電子郵件摘要設定
Thank you for your business!,感謝您的業務！
Support queries from customers.,客戶支持查詢。
Action Doctype,行動Doctype,
Retirement Age,退休年齡
Moving Average Rate,移動平均房價
Select Items,選擇項目
To Shareholder,給股東
{0} against Bill {1} dated {2},{0}針對帳單{1}日期{2}
From State,來自州
Setup Institution,設置機構
Allocating leaves...,分配葉子......
Vehicle/Bus Number,車輛/巴士號碼
Course Schedule,課程表
"You have to Deduct Tax for Unsubmitted Tax Exemption Proof and Unclaimed \
					Employee Benefits in the last Salary Slip of Payroll Period",您必須在工資核算期的最後一個工資單上扣除未提交的免稅證明和無人認領的\員工福利稅
Quote Status,報價狀態
Completion Status,完成狀態
Hotel Room Pricing Item,酒店房間定價項目
Tier Name,等級名稱
Enter retirement age in years,在年內進入退休年齡
Target Warehouse,目標倉庫
Please select a warehouse,請選擇一個倉庫
Starting location from left edge,從左邊起始位置
Allow over delivery or receipt upto this percent,允許在交付或接收高達百分之這
All Item Groups,所有項目群組
Item To Manufacture,產品製造
{0} {1} status is {2},{0} {1}的狀態為{2}
Collection Temperature ,收集溫度
Provide Email Address registered in company,提供公司註冊郵箱地址
Enable Checkout,啟用結帳
Purchase Order to Payment,採購訂單到付款
Projected Qty,預計數量
Interval UOM,間隔UOM,
"Reselect, if the chosen address is edited after save",重新選擇，如果所選地址在保存後被編輯
Item Variant {0} already exists with same attributes,項目變種{0}已經具有相同屬性的存在
Hub Publishing Details,Hub發布細節
'Opening',“開放”
Open To Do,開做
Via Customer Portal,通過客戶門戶
Delivery Note Message,送貨單留言
SGST Amount,SGST金額
Result Format,結果格式
Expenses,開支
Item Variant Attribute,產品規格屬性
Purchase Receipt Trends,採購入庫趨勢
Research & Development,研究與發展
Amount to Bill,帳單數額
Registration Details,註冊細節
Total Billed Amount,總開單金額
Re-Order Qty,重新排序數量
BOM #{0}: Raw material cannot be same as main Item,物料清單＃{0}：原始材料與主要項目不能相同
Total Applicable Charges in Purchase Receipt Items table must be same as Total Taxes and Charges,在外購入庫單項目表總的相關費用必須是相同的總稅費
Incentives,獎勵
Requested Numbers,請求號碼
Evening,晚間
Bypass credit limit check at Sales Order,在銷售訂單旁邊繞過信貸限額檢查
"Enabling 'Use for Shopping Cart', as Shopping Cart is enabled and there should be at least one Tax Rule for Shopping Cart",作為啟用的購物車已啟用“使用購物車”，而應該有購物車至少有一個稅務規則
Stock Details,庫存詳細訊息
Project Value,專案值
Point-of-Sale,銷售點
Fee Creation Status,費用創建狀態
"Account balance already in Credit, you are not allowed to set 'Balance Must Be' as 'Debit'",科目餘額已歸為貸方，不允許設為借方
Balance must be,餘額必須
Expense Claim Rejected Message,報銷回絕訊息
Available Qty,可用數量
Default Warehouse to to create Sales Order and Delivery Note,默認倉庫到創建銷售訂單和交貨單
On Previous Row Total,在上一行共
Rejected Qty,被拒絕的數量
Action Field,行動領域
Manage Customer,管理客戶
Always synch your products from Amazon MWS before synching the Orders details,在同步訂單詳細信息之前，始終從亞馬遜MWS同步您的產品
Delivery Stops,交貨停止
Cannot change Service Stop Date for item in row {0},無法更改行{0}中項目的服務停止日期
Incoming Rate,傳入速率
Final Assessment Grades,最終評估等級
The name of your company for which you are setting up this system.,您的公司要為其設立這個系統的名稱。
Setup your Institute in ERPNext,在ERPNext中設置您的研究所
Hold,持有
Alternate Item,替代項目
Progress Details,進度細節
Date of Joining,加入日期
Is Subcontracted,轉包
Item Attribute Values,項目屬性值
Examination Result,考試成績
Purchase Receipt,採購入庫單
Received Items To Be Billed,待付款的收受品項
Currency exchange rate master.,貨幣匯率的主人。
Reference Doctype must be one of {0},參考文檔類型必須是一個{0}
Filter Total Zero Qty,過濾器總計零數量
Unable to find Time Slot in the next {0} days for Operation {1},找不到時隙在未來{0}天操作{1}
Plan material for sub-assemblies,計劃材料為子組件
Sales Partners and Territory,銷售合作夥伴和地區
BOM {0} must be active,BOM {0}必須是積極的
No Items available for transfer,沒有可用於傳輸的項目
Change Release Date,更改發布日期
Finished product quantity <b>{0}</b> and For Quantity <b>{1}</b> cannot be different,成品數量<b>{0}</b>和數量<b>{1}</b>不能不同
Closing (Opening + Total),閉幕（開幕+總計）
Dispatch Notification Attachment,發貨通知附件
Depreciation Entry,折舊分錄
Please select the document type first,請先選擇文檔類型
Cancel Material Visits {0} before cancelling this Maintenance Visit,取消取消此保養訪問之前，材質訪問{0}
Rate or Discount,價格或折扣
One Sided,單面
Serial No {0} does not belong to Item {1},序列號{0}不屬於項目{1}
Required Qty,所需數量
Custom Data,自定義數據
Warehouses with existing transaction can not be converted to ledger.,與現有的交易倉庫不能轉換到總帳。
Serial no is mandatory for the item {0},序列號對於項目{0}是強制性的
Total Amount,總金額
From Date and To Date lie in different Fiscal Year,從日期和到期日位於不同的財政年度
The Patient {0} do not have customer refrence to invoice,患者{0}沒有客戶參考發票
Internet Publishing,互聯網出版
Number,數
Creating {0} Invoice,創建{0}發票
Medical Code Standard,醫療代碼標準
Item Group Defaults,項目組默認值
Please save before assigning task.,在分配任務之前請保存。
Balance Value,餘額
Lab Technician,實驗室技術員
Sales Price List,銷售價格表
"If checked, a customer will be created, mapped to Patient.
Patient Invoices will be created against this Customer. You can also select existing Customer while creating Patient.",如果選中，將創建一個客戶，映射到患者。將針對該客戶創建病人發票。您也可以在創建患者時選擇現有客戶。
Customer isn't enrolled in any Loyalty Program,客戶未加入任何忠誠度計劃
Account Currency,科目幣種
Sample ID,樣品編號
Please mention Round Off Account in Company,請註明舍入科目的公司
Range,範圍
Default Payable Accounts,預設應付帳款
Components,組件
Search Term Param Name,搜索字詞Param Name,
Item Barcode,商品條碼
Endpoints,端點
Item Variants {0} updated,項目變種{0}更新
Reading 6,6閱讀
Cannot {0} {1} {2} without any negative outstanding invoice,無法{0} {1} {2}沒有任何負面的優秀發票
From Folio No,來自Folio No,
Purchase Invoice Advance,購買發票提前
Row {0}: Credit entry can not be linked with a {1},行{0}：信用記錄無法被鏈接的{1}
Define budget for a financial year.,定義預算財政年度。
ERPNext Account,ERPNext帳戶
{0} is blocked so this transaction cannot proceed,{0}被阻止，所以此事務無法繼續
Action if Accumulated Monthly Budget Exceeded on MR,如果累計每月預算超過MR，則採取行動
Operation completed for how many finished goods?,操作完成多少成品？
Healthcare Practitioner {0} not available on {1},{1}上沒有醫療從業者{0}
Payment Terms Template,付款條款模板
The Brand,品牌
Allow Multiple Material Consumption,允許多種材料消耗
Exit Interview Details,退出面試細節
Is Purchase Item,是購買項目
Purchase Invoice,採購發票
Allow multiple Material Consumption against a Work Order,針對工作單允許多種材料消耗
Voucher Detail No,券詳細說明暫無
New Sales Invoice,新的銷售發票
Total Outgoing Value,出貨總計值
Appointments,約會
Opening Date and Closing Date should be within same Fiscal Year,開幕日期和截止日期應在同一會計年度
Request for Information,索取資料
LeaderBoard,排行榜
Rate With Margin (Company Currency),利率保證金（公司貨幣）
Sync Offline Invoices,同步離線發票
Paid,付費
Program Fee,課程費用
"Replace a particular BOM in all other BOMs where it is used. It will replace the old BOM link, update cost and regenerate ""BOM Explosion Item"" table as per new BOM.
It also updates latest price in all the BOMs.",替換使用所有其他BOM的特定BOM。它將替換舊的BOM鏈接，更新成本，並按照新的BOM重新生成“BOM爆炸項目”表。它還更新了所有BOM中的最新價格。
The following Work Orders were created:,以下工作訂單已創建：
Discharged,出院
Lead Time Date,交貨時間日期
Guardian Name,監護人姓名
Has Print Format,擁有打印格式
Get Started Sections,入門部分
Sanctioned,制裁
Total Contribution Amount: {0},總貢獻金額：{0}
Row #{0}: Please specify Serial No for Item {1},列#{0}：請為項目{1}指定序號
Crop Cycle,作物週期
"For 'Product Bundle' items, Warehouse, Serial No and Batch No will be considered from the 'Packing List' table. If Warehouse and Batch No are same for all packing items for any 'Product Bundle' item, those values can be entered in the main Item table, values will be copied to 'Packing List' table.",對於“產品包”的物品，倉庫，序列號和批號將被從“裝箱單”表考慮。如果倉庫和批次號是相同的任何“產品包”項目的所有包裝物品，這些值可以在主項表中輸入，值將被複製到“裝箱單”表。
From Place,從地方
Net Pay cannnot be negative,淨薪酬不能為負
Supplier Invoice Date cannot be greater than Posting Date,供應商發票的日期不能超過過帳日期更大
Purchase Order Item,採購訂單項目
Agriculture Task,農業任務
Indirect Income,間接收入
Student Attendance Tool,學生考勤工具
Price List (Auto created),價目表（自動創建）
Date Settings,日期設定
Employee Promotion Detail,員工促銷細節
Company Name,公司名稱
Total Message(s),訊息總和（s ）
Purchased,購買
Rename Attribute Value in Item Attribute.,在項目屬性中重命名屬性值。
Additional Discount Percentage,額外折扣百分比
View a list of all the help videos,查看所有幫助影片名單
Soil Texture,土壤紋理
Select account head of the bank where cheque was deposited.,選取支票存入該銀行帳戶的頭。
Allow user to edit Price List Rate in transactions,允許用戶編輯價目表率的交易
Max Qty,最大數量
Print Report Card,打印報告卡
"Row {0}: Invoice {1} is invalid, it might be cancelled / does not exist. \
						Please enter a valid Invoice",行{0}：發票{1}是無效的，它可能會被取消/不存在。 \請輸入有效的發票
Row {0}: Payment against Sales/Purchase Order should always be marked as advance,行{0}：付款方式對銷售/採購訂單應始終被標記為提前
Chemical,化學藥品
Raw Material Cost(Company Currency),原料成本（公司貨幣）
Row # {0}: Rate cannot be greater than the rate used in {1} {2},行＃{0}：速率不能大於{1} {2}中使用的速率
Row # {0}: Rate cannot be greater than the rate used in {1} {2},行＃{0}：速率不能大於{1} {2}中使用的速率
Meter,儀表
Electricity Cost,電力成本
Amount should be greater than zero.,金額應該大於零。
Lab testing datetime cannot be before collection datetime,實驗室測試日期時間不能在收集日期時間之前
Don't send Employee Birthday Reminders,不要送員工生日提醒
Estimated Arrival,預計抵達時間
Inspection Criteria,檢驗標準
Transfered,轉移
BOM Website Item,BOM網站項目
Upload your letter head and logo. (you can edit them later).,上傳你的信頭和標誌。 （您可以在以後對其進行編輯）。
Bill,法案
All Lead (Open),所有鉛（開放）
Row {0}: Qty not available for {4} in warehouse {1} at posting time of the entry ({2} {3}),行{0}：數量不適用於{4}在倉庫{1}在發布條目的時間（{2} {3}）
You can only select a maximum of one option from the list of check boxes.,您只能從復選框列表中選擇最多一個選項。
Get Advances Paid,獲取有償進展
Automatically Create New Batch,自動創建新批
Automatically Create New Batch,自動創建新批
Admission Start Date,入學開始日期
Total Amount in Words,總金額大寫
New Employee,新員工
There was an error. One probable reason could be that you haven't saved the form. <NAME_EMAIL> if the problem persists.,有一個錯誤。一個可能的原因可能是因為您沒有保存的形式。請聯繫*******************如果問題仍然存在。
My Cart,我的購物車
Order Type must be one of {0},訂單類型必須是一個{0}
Next Contact Date,下次聯絡日期
Opening Qty,開放數量
Appointment Reminder,預約提醒
Please enter Account for Change Amount,對於漲跌額請輸入帳號
Student Batch Name,學生批名
Holiday List Name,假日列表名稱
Schedule Course,課程時間表
Applicable on Material Request,適用於材料請求
No Items added to cart,沒有項目已添加到購物車
Expense Claim,報銷
Do you really want to restore this scrapped asset?,難道你真的想恢復這個報廢的資產？
Qty for {0},數量為{0}
Patient Relation,患者關係
Hub Category to Publish,集線器類別發布
"Sales Order {0} has reservation for item {1}, you can,
		only deliver reserved {1} against {0}. Serial No {2} cannot,
		be delivered",銷售訂單{0}對項目{1}有預留，您只能對{0}提供保留的{1}。序列號{2}無法發送
Billing Address GSTIN,帳單地址GSTIN,
Evaluate,評估
Net Hour Rate,淨小時率
Landed Cost Purchase Receipt,到岸成本採購入庫單
Default Terms,默認條款
Criteria,標準
Packing Slip Item,包裝單項目
Cash/Bank Account,現金／銀行會計科目
Please specify a {0},請指定{0}
Removed items with no change in quantity or value.,刪除的項目在數量或價值沒有變化。
Delivery To,交貨給
Variant creation has been queued.,變體創建已經排隊。
The first Leave Approver in the list will be set as the default Leave Approver.,列表中的第一個請假批准者將被設置為默認的批准批准者。
Attribute table is mandatory,屬性表是強制性的
Get Sales Orders,獲取銷售訂單
{0} can not be negative,{0}不能為負數
Connect to Quickbooks,連接到Quickbooks,
Period End Date,期末結束日期
Row {0}: {1} is required to create the Opening {2} Invoices,行{0}：{1}是創建開始{2}發票所必需的
Membership,籍
Total Number of Depreciations,折舊總數
Rate With Margin,利率保證金
Rate With Margin,利率保證金
Is Return (Debit Note),是退貨（借記卡）
Wages,工資
Maintenance Manager Name,維護經理姓名
Urgent,緊急
Please specify a valid Row ID for row {0} in table {1},請指定行{0}在表中的有效行ID {1}
Unable to find variable: ,無法找到變量：
Please select a field to edit from numpad,請選擇要從數字鍵盤編輯的字段
Cannot be a fixed asset item as Stock Ledger is created.,不能成為庫存分類賬創建的固定資產項目。
Admit,承認
Go to the Desktop and start using ERPNext,轉到桌面和開始使用ERPNext,
Pay Remaining,支付剩餘
Manufacturer,生產廠家
Purchase Receipt Item,採購入庫項目
Sales Invoice Payment,銷售發票付款
Quality Inspection Template Name,質量檢驗模板名稱
First Email,第一郵件
Exception Budget Approver Role,例外預算審批人角色
"Once set, this invoice will be on hold till the set date",一旦設置，該發票將被保留至設定的日期
Reserved Warehouse in Sales Order / Finished Goods Warehouse,在銷售訂單/成品倉庫保留倉庫
Selling Amount,銷售金額
Loyalty Amount,忠誠金額
Creation Document No,文檔創建編號
Location Details,位置詳情
Issue,問題
Records,記錄
Scrapped,報廢
Item Defaults,項目默認值
Returns,返回
WIP Warehouse,WIP倉庫
Serial No {0} is under maintenance contract upto {1},序列號{0}在維護合約期間內直到{1}
Organization Name,組織名稱
Show Latest Forum Posts,顯示最新的論壇帖子
Shipping State,運輸狀態
Projected Quantity as Source,預計庫存量的來源
Item must be added using 'Get Items from Purchase Receipts' button,項目必須使用'從採購入庫“按鈕進行新增
Delivery Trip,送貨之旅
A-,一個-
Transfer Type,轉移類型
Sales Expenses,銷售費用
Diagnosis,診斷
Standard Buying,標準採購
Against,針對
Sales Defaults,銷售默認值
Work Order Qty,工作訂單數量
Default Selling Cost Center,預設銷售成本中心
Disc,圓盤
Material Transferred for Subcontract,轉包材料轉讓
Purchase Orders Items Overdue,採購訂單項目逾期
ZIP Code,郵政編碼
Sales Order {0} is {1},銷售訂單{0} {1}
Select interest income account in loan {0},選擇貸款{0}中的利息收入科目
Contact Info,聯絡方式
Making Stock Entries,製作Stock條目
Cannot promote Employee with status Left,無法提升狀態為Left的員工
Net Weight UOM,淨重計量單位
Default Supplier,預設的供應商
Shipping Rule Condition,送貨規則條件
End Date can not be less than Start Date,結束日期不能小於開始日期
Invoice can't be made for zero billing hour,在零計費時間內無法開具發票
Date of Commencement,開始日期
Select company name first.,先選擇公司名稱。
Email sent to {0},電子郵件發送到{0}
Quotations received from Suppliers.,從供應商收到的報價。
Replace BOM and update latest price in all BOMs,更換BOM並更新所有BOM中的最新價格
This is a root supplier group and cannot be edited.,這是一個根源供應商組，無法編輯。
Driver Name,司機姓名
Average Age,平均年齡
Attendance Freeze Date,出勤凍結日期
Attendance Freeze Date,出勤凍結日期
Inward,向內的
List a few of your suppliers. They could be organizations or individuals.,列出一些你的供應商。他們可以是組織或個人。
View All Products,查看所有產品
Minimum Lead Age (Days),最低鉛年齡（天）
Minimum Lead Age (Days),最低鉛年齡（天）
All BOMs,所有的材料明細表
Hotel Rooms of type {0} are unavailable on {1},{0}類型的酒店客房不適用於{1}
Default Currency,預設貨幣
Maximum discount for Item {0} is {1}%,第{0}項的最大折扣為{1}％
From Employee,從員工
Cellphone Number,手機號碼
Monitor Progress,監視進度
Warning: System will not check overbilling since amount for Item {0} in {1} is zero,警告： {0} {1}為零，系統將不檢查超收因為金額項目
Make Difference Entry,使不同入口
Auto Repeat Section,自動重複部分
Transportation,運輸
Invalid Attribute,無效屬性
{0} {1} must be submitted,{0} {1}必須提交
Default Supplier Group,默認供應商組
Quantity must be less than or equal to {0},量必須小於或等於{0}
Application Settings,應用程序設置
Total Characters,總字元數
Row Spacing,行間距
Please select BOM in BOM field for Item {0},請BOM字段中選擇BOM的項目{0}
There isn't any item variant for the selected item,所選項目沒有任何項目變體
C-Form Invoice Detail,C-表 發票詳細資訊
Payment Reconciliation Invoice,付款發票對帳
Contribution %,貢獻％
"As per the Buying Settings if Purchase Order Required == 'YES', then for creating Purchase Invoice, user need to create Purchase Order first for item {0}",根據購買設置，如果需要採購訂單==&#39;是&#39;，那麼為了創建採購發票，用戶需要首先為項目{0}創建採購訂單
HSN-wise-summary of outward supplies,HSN明智的向外供應摘要
Company registration numbers for your reference. Tax numbers etc.,公司註冊號碼，供大家參考。稅務號碼等
To State,國家
Distributor,經銷商
Asset Finance Book,資產融資書
Shopping Cart Shipping Rule,購物車運輸規則
Please set 'Apply Additional Discount On',請設置“收取額外折扣”
Applicable Percent,適用百分比
Ordered Items To Be Billed,預付款的訂購物品
From Range has to be less than To Range,從範圍必須小於要範圍
Global Defaults,全域預設值
Project Collaboration Invitation,項目合作邀請
Action Name,動作名稱
Start Year,開始年份
First 2 digits of GSTIN should match with State number {0},GSTIN的前2位數字應與狀態號{0}匹配
PDC/LC,PDC / LC,
Start date of current invoice's period,當前發票期間內的開始日期
Capacity Planning Error,產能規劃錯誤
Trial Balance for Party,試算表的派對
Consultant,顧問
Parents Teacher Meeting Attendance,家長老師見面會
Finished Item {0} must be entered for Manufacture type entry,完成項目{0}必須為製造類條目進入
Opening Accounting Balance,打開會計平衡
GST Sales Register,消費稅銷售登記冊
Sales Invoice Advance,銷售發票提前
Nothing to request,無需求
Default Return Warehouse,默認退貨倉庫
Select your Domains,選擇您的域名
Shopify Supplier,Shopify供應商
Payment Invoice Items,付款發票項目
Fields will be copied over only at time of creation.,字段將僅在創建時復制。
'Actual Start Date' can not be greater than 'Actual End Date',“實際開始日期”不能大於“實際結束日期'
Management,管理
Payer Settings,付款人設置
No pending Material Requests found to link for the given items.,找不到針對給定項目鏈接的待處理物料請求。
Select company first,首先選擇公司
"This will be appended to the Item Code of the variant. For example, if your abbreviation is ""SM"", and the item code is ""T-SHIRT"", the item code of the variant will be ""T-SHIRT-SM""",這將追加到變異的項目代碼。例如，如果你的英文縮寫為“SM”，而該項目的代碼是“T-SHIRT”，該變種的項目代碼將是“T-SHIRT-SM”
Net Pay (in words) will be visible once you save the Salary Slip.,薪資單一被儲存，淨付款就會被顯示出來。
Is Return,退貨
Start day is greater than end day in task '{0}',開始日期大於任務“{0}”的結束日期
Return / Debit Note,返回/借記注
Price List Country,價目表國家
UOMs,計量單位
{0} valid serial nos for Item {1},{0}項目{1}的有效的序號
Item Code cannot be changed for Serial No.,產品編號不能為序列號改變
UOM Conversion Factor,計量單位換算係數
Please enter Item Code to get Batch Number,請輸入產品代碼來獲得批號
Loyalty Point Entry,忠誠度積分
Default Item Group,預設項目群組
Time In Mins,分鐘時間
Supplier database.,供應商數據庫。
Contract Terms and Conditions,合同條款和條件
You cannot restart a Subscription that is not cancelled.,您無法重新啟動未取消的訂閱。
Balance Sheet,資產負債表
Cost Center For Item with Item Code ',成本中心與項目代碼“項目
Total Parents Teacher Meeting,總計家長教師會議
"Payment Mode is not configured. Please check, whether account has been set on Mode of Payments or on POS Profile.",付款方式未配置。請檢查是否帳戶已就付款方式或POS機配置文件中設置。
Same item cannot be entered multiple times.,同一項目不能輸入多次。
"Further accounts can be made under Groups, but entries can be made against non-Groups",進一步帳戶可以根據組進行，但條目可針對非組進行
Lead,潛在客戶
Payables,應付帳款
Course Intro,課程介紹
MWS Auth Token,MWS Auth Token,
Stock Entry {0} created,庫存輸入{0}創建
You don't have enough Loyalty Points to redeem,您沒有獲得忠誠度積分兌換
Please set associated account in Tax Withholding Category {0} against Company {1},請在針對公司{1}的預扣稅分類{0}中設置關聯帳戶
Row #{0}: Rejected Qty can not be entered in Purchase Return,行＃{0}：駁回採購退貨數量不能進入
Changing Customer Group for the selected Customer is not allowed.,不允許更改所選客戶的客戶組。
Purchase Order Items To Be Billed,欲付款的採購訂單品項
Updating estimated arrival times.,更新預計到達時間。
Enrollment Details,註冊詳情
Cannot set multiple Item Defaults for a company.,無法為公司設置多個項目默認值。
Net Rate,淨費率
Please select a customer,請選擇一個客戶
Purchase Invoice Item,採購發票項目
Stock Ledger Entries and GL Entries are reposted for the selected Purchase Receipts,針對所選的採購入庫單，存貨帳分錄和總帳分錄已經重新登錄。
Assessment Terms,評估條款
Item 1,項目1,
Holiday,節日
Close Issue After Days,關閉問題天后
You need to be a user with System Manager and Item Manager roles to add users to Marketplace.,您需要是具有System Manager和Item Manager角色的用戶才能將用戶添加到Marketplace。
Leave blank if considered for all branches,保持空白如果考慮到全部分支機構
Validity in Days,天數有效
Validity in Days,天數有效
C-form is not applicable for Invoice: {0},C-形式不適用發票：{0}
Name of Consultant,顧問的名字
Unreconciled Payment Details,未核銷付款明細
Member Activity,會員活動
Order Count,訂單數量
Order Count,訂單數量
Current Fiscal Year,當前會計年度
Group same items,組相同的項目
Disable Rounded Total,禁用圓角總
Sync in Progress,同步進行中
Parent Department,家長部門
'Entries' cannot be empty,“分錄”不能是空的
Maintenance Role,維護角色
Duplicate row {0} with same {1},重複的行{0}同{1}
Disable Marketplace,禁用市場
Trial Balance,試算表
Setting up Employees,建立職工
Hotel Reservation User,酒店預訂用戶
Subscription Settings,訂閱設置
Update Auto Repeat Reference,更新自動重複參考
Work Done,工作完成
Please specify at least one attribute in the Attributes table,請指定屬性表中的至少一個屬性
All Students,所有學生
Item {0} must be a non-stock item,項{0}必須是一個非庫存項目
View Ledger,查看總帳
Intervals,間隔
Reconciled Transactions,協調的事務
Linked Location,鏈接位置
"An Item Group exists with same name, please change the item name or rename the item group",具有具有相同名稱的項目群組存在，請更改項目名稱或重新命名該項目群組
Student Mobile No.,學生手機號碼
Rest Of The World,世界其他地區
The Item {0} cannot have Batch,該項目{0}不能有批
Yield UOM,產量UOM,
Budget Variance Report,預算差異報告
Is Item from Hub,是來自Hub的Item,
Get Items from Healthcare Services,從醫療保健服務獲取項目
Row {0}: Activity Type is mandatory.,行{0}：活動類型是強制性的。
Accounting Ledger,會計總帳
Difference Amount,差額
Reverse Charge,反向充電
Timing Detail,時間細節
05-Change in POS,05-更改POS,
Service Detail,服務細節
Item Description,項目說明
Student Sibling,學生兄弟
Supplied Items,提供的物品
Please set an active menu for Restaurant {0},請設置餐館{0}的有效菜單
Qty To Manufacture,製造數量
Maintain same rate throughout purchase cycle,在整個採購週期價格保持一致
Opportunity Item,項目的機會
Student and Guardian Contact Details,學生和監護人聯繫方式
Merge Account,合併科目
Row {0}: For supplier {0} Email Address is required to send email,行{0}：對於供應商{0}的電郵地址發送電子郵件
Temporary Opening,臨時開通
Employee Leave Balance,員工休假餘額
Balance for Account {0} must always be {1},科目{0}的餘額必須始終為{1}
More Info,更多訊息
Valuation Rate required for Item in row {0},行對項目所需的估值速率{0}
Scorecard Actions,記分卡操作
Example: Masters in Computer Science,舉例：碩士計算機科學
Supplier {0} not found in {1},在{1}中找不到供應商{0}
Rejected Warehouse,拒絕倉庫
Against Voucher,對傳票
Default Buying Cost Center,預設採購成本中心
"To get the best out of ERPNext, we recommend that you take some time and watch these help videos.",為得到最好的 ERPNext 教學，我們建議您花一些時間和觀看這些說明影片。
For Default Supplier (optional),對於默認供應商（可選）
Lead Time in days,交貨天期
Accounts Payable Summary,應付帳款摘要
Not authorized to edit frozen Account {0},無權修改凍結帳戶{0}
Get Outstanding Invoices,獲取未付發票
Sales Order {0} is not valid,銷售訂單{0}無效
Warn for new Request for Quotations,警告新的報價請求
Purchase orders help you plan and follow up on your purchases,採購訂單幫助您規劃和跟進您的購買
Lab Test Prescriptions,實驗室測試處方
"The total Issue / Transfer quantity {0} in Material Request {1}  \
							cannot be greater than requested quantity {2} for Item {3}",在材質要求總發行/傳輸量{0} {1} \不能超過請求的數量{2}的項目更大的{3}
"If Shopify not contains a customer in Order, then while syncing Orders, the system will consider default customer for order",如果Shopify不包含訂單中的客戶，則在同步訂單時，系統會考慮默認客戶訂單
Opening Invoice Creation Tool Item,打開發票創建工具項目
Cashier Closing Payments,收銀員結算付款
Employee Number,員工人數
Cancel Invoice After Grace Period,在寬限期後取消發票
Case No(s) already in use. Try from Case No {0},案例編號已在使用中（ S） 。從案例沒有嘗試{0}
% Completed,％已完成
Invoiced Amount (Exculsive Tax),發票金額（Exculsive稅）
Item 2,項目2,
Authorization Endpoint,授權端點
Auto re-order,自動重新排序
Total Achieved,實現總計
Place of Issue,簽發地點
Laboratory Testing Datetime,實驗室測試日期時間
Add Quote,添加報價
UOM coversion factor required for UOM: {0} in Item: {1},所需的計量單位計量單位：丁文因素：{0}項：{1}
Indirect Expenses,間接費用
Row {0}: Qty is mandatory,列#{0}：數量是強制性的
Agriculture,農業
Create Sales Order,創建銷售訂單
Accounting Entry for Asset,資產會計分錄
Block Invoice,阻止發票
Quantity to Make,數量
Sync Master Data,同步主數據
Repair Cost,修理費用
Your Products or Services,您的產品或服務
Failed to login,登錄失敗
Asset {0} created,資產{0}已創建
Special Test Items,特殊測試項目
You need to be a user with System Manager and Item Manager roles to register on Marketplace.,您需要是具有System Manager和Item Manager角色的用戶才能在Marketplace上註冊。
Website Image should be a public file or website URL,網站形象應該是一個公共文件或網站網址
This is a root item group and cannot be edited.,這是個根項目群組，且無法被編輯。
Merge,合併
Purchase Order,採購訂單
Fuel UOM,燃油計量單位
Warehouse Contact Info,倉庫聯絡方式
Write Off Difference Amount,核銷金額差異
Volunteer Name,志願者姓名
Rows with duplicate due dates in other rows were found: {0},發現其他行中具有重複截止日期的行：{0}
Shipping rule not applicable for country {0},運費規則不適用於國家/地區{0}
Foreign Trade Details,外貿詳細
Assessment Plan Status,評估計劃狀態
Serial No Details,序列號詳細資訊
Item Tax Rate,項目稅率
From Party Name,來自黨名
Group Roll Number,組卷編號
Group Roll Number,組卷編號
"For {0}, only credit accounts can be linked against another debit entry",{0}，只有貸方科目可以連接另一個借方分錄
Delivery Note {0} is not submitted,送貨單{0}未提交
Item {0} must be a Sub-contracted Item,項{0}必須是一個小項目簽約
Capital Equipments,資本設備
"Pricing Rule is first selected based on 'Apply On' field, which can be Item, Item Group or Brand.",基於“適用於”欄位是「項目」，「項目群組」或「品牌」，而選擇定價規則。
Please set the Item Code first,請先設定商品代碼
Doc Type,文件類型
Total allocated percentage for sales team should be 100,對於銷售團隊總分配比例應為100,
Billing Interval Count,計費間隔計數
Appointments and Patient Encounters,預約和患者遭遇
Department and Grade,部門和年級
Edit Description,編輯說明
For Supplier,對供應商
Setting Account Type helps in selecting this Account in transactions.,設置會計科目類型有助於在交易中選擇該科目。
Grand Total (Company Currency),總計（公司貨幣）
Create Print Format,創建打印格式
Fee Created,創建費用
Did not find any item called {0},沒有找到所謂的任何項目{0}
Items Filter,物品過濾
Criteria Formula,標準配方
Total Outgoing,出貨總計
"There can only be one Shipping Rule Condition with 0 or blank value for ""To Value""",只能有一個運輸規則條件為0或空值“ To值”
Duration,持續時間
"For an item {0}, quantity must be positive number",對於商品{0}，數量必須是正數
Note: This Cost Center is a Group. Cannot make accounting entries against groups.,注：該成本中心是一個集團。不能讓反對團體的會計分錄。
Compensatory leave request days not in valid holidays,補休請求天不在有效假期
Child warehouse exists for this warehouse. You can not delete this warehouse.,兒童倉庫存在這個倉庫。您不能刪除這個倉庫。
Website Item Groups,網站項目群組
Total (Company Currency),總計（公司貨幣）
Accessable Value,可訪問的價值
Serial number {0} entered more than once,序號{0}多次輸入
Journal Entry,日記帳分錄
From GSTIN,來自GSTIN,
Unclaimed amount,無人認領的金額
{0} items in progress,正在進行{0}項目
Workstation Name,工作站名稱
Grade Code,等級代碼
POS Item Group,POS項目組
Email Digest:,電子郵件摘要：
Alternative item must not be same as item code,替代項目不能與項目代碼相同
BOM {0} does not belong to Item {1},BOM {0}不屬於項目{1}
Target Distribution,目標分佈
06-Finalization of Provisional assessment,06-定期評估
Bank Account No.,銀行賬號
"Scorecard variables can be used, as well as:
{total_score} (the total score from that period),
{period_number} (the number of periods to present day)
",可以使用記分卡變量，以及：{total_score}（該期間的總分數），{period_number}（到當前時間段的數量）
Collapse All,全部收縮
Create Purchase Order,創建採購訂單
Reading 8,閱讀8,
Discharge Note,卸貨說明
Taxes and Charges Calculation,稅費計算
Book Asset Depreciation Entry Automatically,自動存入資產折舊條目
Book Asset Depreciation Entry Automatically,自動存入資產折舊條目
Request for Quotation Supplier,詢價供應商
Registration Message,註冊信息
Prescription Dosage,處方用量
HR Manager,人力資源經理
Please select a Company,請選擇一個公司
Supplier Invoice Date,供應商發票日期
This value is used for pro-rata temporis calculation,該值用於按比例計算
You need to enable Shopping Cart,您需要啟用購物車
Writeoff,註銷
Naming Series Prefix,命名系列前綴
Scoring Criteria,評分標準
Party Account Currency,黨的科目幣種
Total Estimated Distance,總估計距離
BOM Browser,BOM瀏覽器
Overlapping conditions found between:,存在重疊的條件：
Against Journal Entry {0} is already adjusted against some other voucher,對日記條目{0}已經調整一些其他的優惠券
Total Order Value,總訂單價值
Ageing Range 3,老齡範圍3,
POS Closing Voucher Details,POS關閉憑證詳細信息
Shopify Log,Shopify日誌
Check In,報到
No of Visits,沒有訪問量的
Maintenance Schedule {0} exists against {1},針對{1}存在維護計劃{0}
Enrolling student,招生學生
Currency of the Closing Account must be {0},關閉科目的貨幣必須是{0}
Sum of points for all goals should be 100. It is {0},對所有目標點的總和應該是100。{0}
Start and End Dates,開始和結束日期
Contract Template Fulfilment Terms,合同模板履行條款
Delivered Items To Be Billed,交付項目要被收取
Open BOM {0},開放BOM {0}
Warehouse cannot be changed for Serial No.,倉庫不能改變序列號
UOM,UOM,
Utilities,公用事業
Accounting,會計
Purchase Receipt Amount,採購收據金額
Please select batches for batched item ,請為批量選擇批次
Depreciation Schedules,折舊計劃
"Support for public app is deprecated. Please setup private app, for more details refer user manual",對公共應用程序的支持已被棄用。請設置私人應用程序，更多詳細信息請參閱用戶手冊
Following accounts might be selected in GST Settings:,以下帳戶可能在GST設置中選擇：
Projects,專案
Transaction Currency,交易貨幣
From {0} | {1} {2},從{0} | {1} {2}
Some emails are invalid,有些電子郵件無效
Operation Description,操作說明
Cannot change Fiscal Year Start Date and Fiscal Year End Date once the Fiscal Year is saved.,不能更改財政年度開始日期和財政年度結束日期，一旦會計年度被保存。
Shopping Cart,購物車
Avg Daily Outgoing,平均每日傳出
Campaign,競賽
Name and Type,名稱和類型
Contacts and Address,聯繫人和地址
Contact Person,聯絡人
'Expected Start Date' can not be greater than 'Expected End Date',“預計開始日期”不能大於“預計結束日期'
No data for this period,此期間沒有數據
Course End Date,課程結束日期
Planned Quantity,計劃數量
Item Tax Amount,項目稅額
Water Analysis Criteria,水分析標準
Maintain Stock,維護庫存資料
Prefered Email,首選電子郵件
Eligibility and Details,資格和細節
Net Change in Fixed Asset,在固定資產淨變動
Reqd Qty,需要數量
Leave blank if considered for all designations,離開，如果考慮所有指定空白
Charge of type 'Actual' in row {0} cannot be included in Item Rate,類型'實際'行{0}的計費，不能被包含在項目單價
Max: {0},最大數量：{0}
From Datetime,從日期時間
For Company,對於公司
Communication log.,通信日誌。
"Request for Quotation is disabled to access from portal, for more check portal settings.",詢價被禁止訪問門脈，為更多的檢查門戶設置。
Supplier Scorecard Scoring Variable,供應商記分卡評分變量
Buying Amount,購買金額
Shipping Address Name,送貨地址名稱
Terms and Conditions Content,條款及細則內容
There were errors creating Course Schedule,創建課程表時出現錯誤
The first Expense Approver in the list will be set as the default Expense Approver.,列表中的第一個費用審批人將被設置為默認的費用審批人。
cannot be greater than 100,不能大於100,
You need to be a user other than Administrator with System Manager and Item Manager roles to register on Marketplace.,您需要是具有System Manager和Item Manager角色的Administrator以外的用戶才能在Marketplace上註冊。
Item {0} is not a stock Item,項{0}不是缺貨登記
Unscheduled,計劃外
Owned,擁有的
Depends on Leave Without Pay,依賴於無薪休假
"Higher the number, higher the priority",數字越大，優先級越高
Purchase Invoice Trends,購買發票趨勢
Minimum Total Spent,最低總支出
"Row #{0}: The batch {1} has only {2} qty. Please select another batch which has {3} qty available or split the row into multiple rows, to deliver/issue from multiple batches",行＃{0}：批次{1}只有{2}數量。請選擇具有{3}數量的其他批次，或將該行拆分成多個行，以便從多個批次中傳遞/發布
Expiry Duration (in days),到期時間（天）
Price Determination,價格確定
New Department,新部門
Select POS Profile,選擇POS配置文件
Warranty / AMC Status,保修/ AMC狀態
Accounts Browser,帳戶瀏覽器
Referral,推薦
Payment Entry Reference,付款輸入參考
GL Entry,GL報名
Response Options,響應選項
Loading Payment System,加載支付系統
Batch-Wise Balance History,間歇式平衡歷史
Row #{0}: Cannot set Rate if amount is greater than billed amount for Item {1}.,行＃{0}：如果金額大於項目{1}的開帳單金額，則無法設置費率。
Print settings updated in respective print format,打印設置在相應的打印格式更新
Package Code,封裝代碼
Negative Quantity is not allowed,負數量是不允許
"Tax detail table fetched from item master as a string and stored in this field.
Used for Taxes and Charges",從項目主檔獲取的稅務詳細資訊表，成為字串並存儲在這欄位。用於稅賦及費用
Employee cannot report to himself.,員工不能報告自己。
Max Leaves Allowed,允許最大葉子
"If the account is frozen, entries are allowed to restricted users.",如果帳戶被凍結，條目被允許受限制的用戶。
Bank Balance,銀行結餘
Accounting Entry for {0}: {1} can only be made in currency: {2},會計分錄為{0}：{1}只能在貨幣做：{2}
Account Balance,帳戶餘額
Tax Rule for transactions.,稅收規則進行的交易。
Type of document to rename.,的文件類型進行重命名。
{0} {1}: Customer is required against Receivable account {2},{0} {1}：需要客戶對應收帳款{2}
Total Taxes and Charges (Company Currency),總稅費和費用（公司貨幣）
Weather Parameter,天氣參數
Show unclosed fiscal year's P&L balances,顯示未關閉的會計年度的盈虧平衡
Asset Naming Series,資產命名系列
Collection Details,收集細節
Allow Print Before Pay,付款前允許打印
Linked Soil Texture,連接的土壤紋理
Shipping Account,送貨科目
{0} {1}: Account {2} is inactive,{0} {1}科目{2}無效
Make Sales Orders to help you plan your work and deliver on-time,製作銷售訂單，以幫助你計劃你的工作和按時交付
Bank Transaction Entries,銀行交易分錄
Readings,閱讀
Total Additional Costs,總額外費用
No of Interactions,沒有相互作用
Scrap Material Cost(Company Currency),廢料成本（公司貨幣）
Sub Assemblies,子組件
Asset Name,資產名稱
Task Weight,任務重
Loyalty Program Type,忠誠度計劃類型
Stock Manager,庫存管理
Source warehouse is mandatory for row {0},列{0}的來源倉是必要的
The Payment Term at row {0} is possibly a duplicate.,第{0}行的支付條款可能是重複的。
Agriculture (beta),農業（測試版）
Packing Slip,包裝單
Office Rent,辦公室租金
Setup SMS gateway settings,設置短信閘道設置
Common Name,通用名稱
Import Failed!,導入失敗！
Workstation Working Hour,工作站工作時間
Blood Pressure,血壓
Analyst,分析人士
Inventory,庫存
Sales Details,銷售詳細資訊
With Items,隨著項目
Maintenance Team,維修隊
Is Additional Component,是附加組件
In Qty,在數量
Validate Enrolled Course for Students in Student Group,驗證學生組學生入學課程
Expense Claim Rejected,費用索賠被拒絕
Item Attribute,項目屬性
Source Location,來源地點
Institute Name,學院名稱
There can be multiple tiered collection factor based on the total spent. But the conversion factor for redemption will always be same for all the tier.,根據總花費可以有多個分層收集因子。但兌換的兌換係數對於所有等級總是相同的。
Item Variants,項目變體
Services,服務
Select Possible Supplier,選擇潛在供應商
"Select, to make the customer searchable with these fields",選擇，使客戶可以使用這些字段進行搜索
Import Delivery Notes from Shopify on Shipment,在發貨時從Shopify導入交貨單
Show closed,顯示關閉
Asset Category is mandatory for Fixed Asset item,資產類別是強制性的固定資產項目
Fee Validity,費用有效期
No records found in the Payment table,沒有在支付表中找到記錄
This {0} conflicts with {1} for {2} {3},此{0}衝突{1}在{2} {3}
Students HTML,學生HTML,
GST HSN Code,GST HSN代碼
Total Experience,總經驗
Open Projects,打開項目
Packing Slip(s) cancelled,包裝單（ S）已取消
Cash Flow from Investing,從投資現金流
Program Course,課程計劃
Allow Appointments,允許約會
Freight and Forwarding Charges,貨運代理費
Company Tagline for website homepage,公司標語的網站主頁
Item Group Name,項目群組名稱
Taken,拍攝
Date of Leaving,離開日期
For Price List,對於價格表
Executive Search,獵頭
Setting defaults,設置默認值
Auto Opt In (For all customers),自動選擇（適用於所有客戶）
Create Leads,建立潛在客戶
Schedules,時間表
POS Profile is required to use Point-of-Sale,POS配置文件需要使用銷售點
Net Amount,淨額
{0} {1} has not been submitted so the action cannot be completed,"{0} {1} 尚未提交, 因此無法完成操作"
BOM Detail No,BOM表詳細編號
Additional Charges,附加費用
Result Route Field,結果路由字段
Additional Discount Amount (Company Currency),額外的優惠金額（公司貨幣）
Supplier Scorecard,供應商記分卡
Result Datetime,結果日期時間
Support Hour Distribution,支持小時分配
Maintenance Visit,維護訪問
Leaving Certificate Number,畢業證書號碼
"Appointment cancelled, Please review and cancel the invoice {0}",預約已取消，請查看並取消發票{0}
Available Batch Qty at Warehouse,可用的批次數量在倉庫
Is Company Account,是公司帳戶
Landed Cost Help,到岸成本幫助
Select Shipping Address,選擇送貨地址
Expected Hrs,預計的小時數
Memebership Details,Memebership細節
Please input all required Result Value(s),請輸入所有必需的結果值（s）
Accounts Receivable Summary,應收帳款匯總
Linked Invoices,鏈接的發票
Opening Invoices,打開發票
Contract Details,合同細節
Please set User ID field in an Employee record to set Employee Role,請在員工記錄設定員工角色設置用戶ID字段
UOM Name,計量單位名稱
HSN Code,HSN代碼
Contribution Amount,貢獻金額
Shipping Address,送貨地址
This tool helps you to update or fix the quantity and valuation of stock in the system. It is typically used to synchronise the system values and what actually exists in your warehouses.,此工具可幫助您更新或修復系統中的庫存數量和價值。它通常被用於同步系統值和實際存在於您的倉庫。
In Words will be visible once you save the Delivery Note.,送貨單一被儲存，就會顯示出來。
Unverified Webhook Data,未經驗證的Webhook數據
Student {0} - {1} appears Multiple times in row {2} & {3},學生{0}  -  {1}出現連續中多次{2}和{3}
Two-way,雙向
Day to Send,發送日
Manage Sample Collection,管理樣品收集
Ignore Existing Ordered Quantity,忽略現有的訂購數量
Tobacco Past Use,煙草過去使用
Brand Name,商標名稱
Transporter Details,貨運公司細節
Default warehouse is required for selected item,默認倉庫需要選中的項目
Possible Supplier,可能的供應商
Monthly Distribution,月度分佈
Receiver List is empty. Please create Receiver List,收受方列表為空。請創建收受方列表
Healthcare (beta),醫療保健（beta）
Production Plan Sales Order,生產計劃銷售訂單
"No active BOM found for item {0}. Delivery by \
						Serial No cannot be ensured",未找到項{0}的有效BOM。無法確保交貨\串口號
Sales Partner Target,銷售合作夥伴目標
Pricing Rule,定價規則
Duplicate roll number for student {0},學生{0}的重複卷號
Duplicate roll number for student {0},學生{0}的重複卷號
Material Request to Purchase Order,材料要求採購訂單
Row # {0}: Returned Item {1} does not exists in {2} {3},行＃{0}：返回的項目{1}不存在{2} {3}
Bank Accounts,銀行帳戶
Bank Reconciliation Statement,銀行對帳表
Medical Coding,醫學編碼
Lead Name,主導者名稱
POS,POS,
Opening Stock Balance,期初存貨餘額
Capital Work In Progress Account,資本工作進行中的帳戶
Asset Value Adjustment,資產價值調整
{0} must appear only once,{0}必須只出現一次
No Items to pack,無項目包裝
From Value,從價值
Manufacturing Quantity is mandatory,生產數量是必填的
"If checked, the Home page will be the default Item Group for the website",如果選中，主頁將是網站的默認項目組
Reading 4,4閱讀
"Students are at the heart of the system, add all your students",學生在系統的心臟，添加所有的學生
Member ID,會員ID,
Row #{0}: Clearance date {1} cannot be before Cheque Date {2},行＃{0}：清除日期{1}無法支票日期前{2}
Certificate Required,證書要求
Default Holiday List,預設假日表列
Supplier Group,供應商集團
Row {0}: From Time and To Time of {1} is overlapping with {2},行{0}：從時間和結束時間{1}是具有重疊{2}
Stock Liabilities,現貨負債
Supplier Warehouse,供應商倉庫
Contact Mobile No,聯絡手機號碼
Select Company,選擇公司
Material Requests for which Supplier Quotations are not created,尚未建立供應商報價的材料需求
User {0} doesn't have any default POS Profile. Check Default at Row {1} for this User.,用戶{0}沒有任何默認的POS配置文件。檢查此用戶的行{1}處的默認值。
Set 0 for no limit,為不限制設為0,
Primary Address and Contact Detail,主要地址和聯繫人詳情
Resend Payment Email,重新發送付款電子郵件
New task,新任務
Appointment,約定
Make Quotation,請報價
Other Reports,其他報告
Please select at least one domain.,請選擇至少一個域名。
Dependent Task,相關任務
Shopify Tax Account,Shopify稅收帳戶
Conversion factor for default Unit of Measure must be 1 in row {0},預設計量單位的轉換因子必須是1在行{0}
Optimize Route,優化路線
Try planning operations for X days in advance.,嘗試提前X天規劃作業。
"{0} vacancies and {1} budget for {2} already planned for subsidiary companies of {3}. \
				You can only plan for upto {4} vacancies and and budget {5} as per staffing plan {6} for parent company {3}.",已為{3}的子公司計劃{2}的{0}空缺和{1}預算。 \根據母公司{3}的員工計劃{6}，您只能計劃最多{4}個職位空缺和預算{5}。
Please set Default Payroll Payable Account in Company {0},請公司設定默認應付職工薪酬帳戶{0}
Get financial breakup of Taxes and charges data by Amazon ,通過亞馬遜獲取稅收和收費數據的財務分解
Receiver List,收受方列表
Search Item,搜索項目
Payment Amount,付款金額
Healthcare Service Items,醫療服務項目
Net Change in Cash,現金淨變動
Grading Scale,分級量表
Unit of Measure {0} has been entered more than once in Conversion Factor Table,計量單位{0}已經進入不止一次在轉換係數表
Already completed,已經完成
Stock In Hand,庫存在手
"Please add the remaining benefits {0} to the application as \
				pro-rata component",請將剩餘的權益{0}作為\ pro-rata組件添加到應用程序中
Import Successful!,導入成功！
Payment Request already exists {0},付款申請已經存在{0}
Cost of Issued Items,發布項目成本
Hospital,醫院
Quantity must not be more than {0},數量必須不超過{0}
Previous Financial Year is not closed,上一財政年度未關閉
Practitioner Schedule,從業者時間表
Age (Days),時間（天）
Quotation Item,產品報價
Customer POS Id,客戶POS ID,
Account Name,帳戶名稱
From Date cannot be greater than To Date,起始日期不能大於結束日期
Serial No {0} quantity {1} cannot be a fraction,序列號{0}的數量量{1}不能是分數
Please enter Woocommerce Server URL,請輸入Woocommerce服務器網址
Supplier Part Number,供應商零件編號
Conversion rate cannot be 0 or 1,轉化率不能為0或1,
All the mandatory Task for employee creation hasn't been done yet.,所有員工創建的強制性任務尚未完成。
Credit Controller,信用控制器
03-Deficiency in services,03-服務不足
Default Medical Code Standard,默認醫療代碼標準
Purchase Receipt {0} is not submitted,採購入庫單{0}未提交
Default Payable Account,預設應付帳款科目
"Settings for online shopping cart such as shipping rules, price list etc.",設定線上購物車，如航運規則，價格表等
{0}% Billed,{0}％已開立帳單
Reserved Qty,保留數量
Party Account,參與者科目
Human Resources,人力資源
Debit in Company Currency,借記卡在公司貨幣
BOM Item,BOM項目
Make Disbursement Entry,請輸入支付
Row {0}: Advance against Supplier must be debit,行{0}：提前對供應商必須扣除
Default Values,默認值
This is based on logs against this Vehicle. See timeline below for details,這是基於對本車輛的日誌。詳情請參閱以下時間表
Payroll date can not be less than employee's joining date,工資日期不能低於員工的加入日期
{0} {1} created,已創建{0} {1}
"Job Openings for designation {0} already open \
					or hiring completed as per Staffing Plan {1}",指定{0}的職位空缺已根據人員配置計劃{1}已打開或正在招聘
Constipated,大便乾燥
Against Supplier Invoice {0} dated {1},對供應商發票{0}日期{1}
Default Price List,預設價格表
Asset Movement record {0} created,資產運動記錄{0}創建
No items found.,未找到任何項目。
You cannot delete Fiscal Year {0}. Fiscal Year {0} is set as default in Global Settings,您不能刪除會計年度{0}。會計年度{0}設置為默認的全局設置
Equity/Liability Account,庫存/負債科目
A customer with the same name already exists,一個同名的客戶已經存在
Inactive,待用
Total Net Weight,總淨重
Order Confirmation No,訂單確認號
Eligibility For ITC,適用於ITC的資格
Entry Type,條目類型
Customer Credit Balance,客戶信用平衡
Net Change in Accounts Payable,應付帳款淨額變化
Credit limit has been crossed for customer {0} ({1}/{2}),客戶{0}（{1} / {2}）的信用額度已超過
Customer required for 'Customerwise Discount',需要' Customerwise折扣“客戶
Update bank payment dates with journals.,更新與日記帳之銀行付款日期。
Pricing,價錢
Term Details,長期詳情
Cannot enroll more than {0} students for this student group.,不能註冊超過{0}學生該學生群體更多。
Total (Without Tax),總計（不含稅）
Lead Count,鉛計數
Lead Count,鉛計數
Stock Available,現貨供應
Capacity Planning For (Days),產能規劃的範圍（天）
Procurement,採購
None of the items have any change in quantity or value.,沒有一個項目無論在數量或價值的任何變化。
Mandatory field - Program,強制性領域 - 計劃
Mandatory field - Program,強制性領域 - 計劃
Result Component,結果組件
Warranty Claim,保修索賠
Lead Details,潛在客戶詳情
Loan repayment,償還借款
Asset Account,資產科目
End date of current invoice's period,當前發票的期限的最後一天
Applicable For,適用
Technician Name,技術員姓名
"Cannot ensure delivery by Serial No as \
					Item {0} is added with and without Ensure Delivery by \
					Serial No.",無法通過序列號確保交貨，因為\項目{0}是否添加了確保交貨\序列號
Unlink Payment on Cancellation of Invoice,取消鏈接在發票上的取消付款
Current Odometer reading entered should be greater than initial Vehicle Odometer {0},進入當前的里程表讀數應該比最初的車輛里程表更大的{0}
No Show,沒有出現
Shipping Rule Country,航運規則國家
Leave and Attendance,假離和缺勤
Comprehensive Insurance,綜合保險
Loyalty Point: {0},忠誠度積分：{0}
Add Leads,添加潛在客戶
Redemption,贖回
Packed Items,盒裝項目
Tax Withholding Rates,預扣稅率
Warranty Claim against Serial No.,針對序列號保修索賠
'Total','總數'
Collection Tier,收集層
"Advance paid against {0} {1} cannot be greater \
						than Grand Total {2}",推動打擊{0} {1}不能大於付出\超過總計{2}
Medication,藥物治療
Include Non Stock Items,包括非庫存項目
Challenging/Slow,具有挑戰性/慢
Please select item code,請選擇商品代碼
Studying in Same Institute,就讀於同一研究所
Territory Manager,區域經理
To Warehouse (Optional),倉庫（可選）
GST Accounts,GST科目
Paid Amount (Company Currency),支付的金額（公司貨幣）
Additional Discount,更多優惠
Selling Settings,銷售設置
Online Auctions,網上拍賣
Please specify either Quantity or Valuation Rate or both,請註明無論是數量或估價率或兩者
Fulfillment,履行
View in Cart,查看你的購物車
Marketing Expenses,市場推廣開支
Item Shortage Report,商品短缺報告
Can't create standard criteria. Please rename the criteria,無法創建標準條件。請重命名標準
"Weight is mentioned,\nPlease mention ""Weight UOM"" too",重量被提及，請同時註明“重量計量單位”
Material Request used to make this Stock Entry,做此存貨分錄所需之物料需求
Hub Password,集線器密碼
Separate course based Group for every Batch,為每個批次分離基於課程的組
Separate course based Group for every Batch,為每個批次分離基於課程的組
Single unit of an Item.,該產品的一個單元。
Fee Category,收費類別
Next Business Day,下一個營業日
Dosage by time interval,劑量按時間間隔
Section Header,章節標題
Student Fee Collection,學生費徵收
Appointment Duration (mins),預約時間（分鐘）
Make Accounting Entry For Every Stock Movement,為每股份轉移做會計分錄
Please enter valid Financial Year Start and End Dates,請輸入有效的財政年度開始和結束日期
Date Of Retirement,退休日
Sales Person Commission Summary,銷售人員委員會摘要
Additional Salary Component,額外的薪資組件
Transferred,轉入
Doors,門
ERPNext Setup Complete!,ERPNext設定完成！
Collect Fee for Patient Registration,收取病人登記費
Cannot change Attributes after stock transaction. Make a new Item and transfer stock to the new Item,庫存交易後不能更改屬性。創建一個新項目並將庫存轉移到新項目
Weightage,權重
Tax Breakup,稅收分解
Joining Details,加入詳情
Non Profit Member,非盈利會員
{0} {1}: Cost Center is required for 'Profit and Loss' account {2}. Please set up a default Cost Center for the Company.,{0} {1}：需要的損益“科目成本中心{2}。請設置為公司默認的成本中心。
A Customer Group exists with same name please change the Customer name or rename the Customer Group,客戶群組存在相同名稱，請更改客戶名稱或重新命名客戶群組
Area,區
Company Description,公司介紹
Parent Territory,家長領地
Place of Supply,供貨地點
Reading 2,閱讀2,
Material Receipt,收料
Submit/Reconcile Payments,提交/協調付款
Products,產品
Instructor,講師
Select Item (optional),選擇項目（可選）
The Loyalty Program isn't valid for the selected company,忠誠度計劃對所選公司無效
Fee Schedule Student Group,費用計劃學生組
"If this item has variants, then it cannot be selected in sales orders etc.",如果此項目已變種，那麼它不能在銷售訂單等選擇
Next Contact By,下一個聯絡人由
Quantity required for Item {0} in row {1},列{1}項目{0}必須有數量
Warehouse {0} can not be deleted as quantity exists for Item {1},倉庫{0} 不能被刪除因為項目{1}還有庫存
Order Type,訂單類型
Item-wise Sales Register,項目明智的銷售登記
Gross Purchase Amount,總購買金額
Opening Balances,期初餘額
Depreciation Method,折舊方法
Is this Tax included in Basic Rate?,包括在基本速率此稅？
Total Target,總目標
Production Plan Material Request,生產計劃申請材料
Release Date,發布日期
Reconciliation JSON,JSON對賬
Too many columns. Export the report and print it using a spreadsheet application.,過多的列數。請導出報表，並使用試算表程式進行列印。
Batch No,批號
Hub Seller Name,集線器賣家名稱
Employee Advances,員工發展
Allow multiple Sales Orders against a Customer's Purchase Order,允許多個銷售訂單對客戶的採購訂單
Student Group Instructor,學生組教練
Student Group Instructor,學生組教練
Assessment  Mark (Out of 10),評估標記（滿分10分）
Guardian2 Mobile No,Guardian2手機號碼
Main,主頁
Following item {0} is not marked as {1} item. You can enable them as {1} item from its Item master,項目{0}之後未標記為{1}項目。您可以從項目主文件中將它們作為{1}項啟用
Variant,變種
"For an item {0}, quantity must be negative number",對於商品{0}，數量必須是負數
Set prefix for numbering series on your transactions,為你的交易編號序列設置的前綴
Default BOM ({0}) must be active for this item or its template,預設BOM（{0}）必須是活動的這個項目或者其模板
Leave Encashed?,離開兌現？
Opportunity From field is mandatory,機會從字段是強制性的
Variants,變種
Make Purchase Order,製作採購訂單
Send To,發送到
There is not enough leave balance for Leave Type {0},沒有足夠的餘額休假請假類型{0}
Allocated amount,分配量
Contribution to Net Total,貢獻淨合計
Customer's Item Code,客戶的產品編號
Stock Reconciliation,庫存調整
Territory Name,地區名稱
Purchase Orders to Receive,要收貨的採購訂單
Work-in-Progress Warehouse is required before Submit,提交之前，需要填入在製品倉庫
You can only have Plans with the same billing cycle in a Subscription,您只能在訂閱中擁有相同結算週期的計劃
Mapped Data,映射數據
Warehouse and Reference,倉庫及參考
Statutory info and other general information about your Supplier,供應商的法定資訊和其他一般資料
Serial Nos and Batches,序列號和批號
Serial Nos and Batches,序列號和批號
Student Group Strength,學生群體力量
Student Group Strength,學生群體力量
Against Journal Entry {0} does not have any unmatched {1} entry,對日記條目{0}沒有任何無與倫比{1}進入
"Subsidiary companies have already planned for {1} vacancies at a budget of {2}. \
				Staffing Plan for {0} should allocate more vacancies and budget for {3} than planned for its subsidiary companies",子公司已經計劃{2}的預算{1}空缺。 \ {0}的人員配備計劃應為其子公司分配更多空缺和預算{3}
Appraisals,估價
Duplicate Serial No entered for Item {0},重複的序列號輸入的項目{0}
Track Leads by Lead Source.,通過鉛源追踪潛在客戶。
A condition for a Shipping Rule,為運輸規則的條件
Please enter ,請輸入
Maintenance Log,維護日誌
Please set filter based on Item or Warehouse,根據項目或倉庫請設置過濾器
The net weight of this package. (calculated automatically as sum of net weight of items),淨重這個包。 （當項目的淨重量總和自動計算）
Make Inter Company Journal Entry,使公司日記帳分錄
Discount amount cannot be greater than 100%,折扣金額不能大於100％
"Number of new Cost Center, it will be included in the cost center name as a prefix",新成本中心的數量，它將作為前綴包含在成本中心名稱中
To Deliver and Bill,準備交貨及開立發票
Instructors,教師
Credit Amount in Account Currency,在科目幣金額
BOM {0} must be submitted,BOM {0}必須提交
Authorization Control,授權控制
Row #{0}: Rejected Warehouse is mandatory against rejected Item {1},行＃{0}：拒絕倉庫是強制性的反對否決項{1}
"Warehouse {0} is not linked to any account, please mention the account in  the warehouse record or set default inventory account in company {1}.",倉庫{0}未與任何科目關聯，請在倉庫記錄中設定科目，或在公司{1}中設置默認庫存科目。
Manage your orders,管理您的訂單
Actual Time and Cost,實際時間和成本
Material Request of maximum {0} can be made for Item {1} against Sales Order {2},針對銷售訂單{2}的項目{1}，最多可以有 {0} 被完成。
Crop Spacing,作物間距
Course Abbreviation,當然縮寫
Action if Annual Budget Exceeded on PO,年度預算超出採購訂單時採取的行動
Student Leave Application,學生請假申請
Will also apply for variants,同時將申請變種
"Asset cannot be cancelled, as it is already {0}",資產不能被取消，因為它已經是{0}
On,開啟
Bundle items at time of sale.,在銷售時捆綁項目。
Dispatch Settings,發貨設置
Actual Qty,實際數量
References,參考
Reading 10,閱讀10,
Serial nos {0} does not belongs to the location {1},序列號{0}不屬於位置{1}
Barcodes,條形碼
Hub Node,樞紐節點
You have entered duplicate items. Please rectify and try again.,您輸入重複的項目。請糾正，然後再試一次。
Associate,關聯
Asset Movement,資產運動
Work Order {0} must be submitted,必須提交工單{0}
New Cart,新的車
Item {0} is not a serialized Item,項{0}不是一個序列化的項目
Delivery Settings,交貨設置
Fetch Data,獲取數據
Create Receiver List,創建接收器列表
Available-for-use Date should be after purchase date,可供使用的日期應在購買日期之後
Wheels,車輪
To Package No.,以包號
Deferred Revenue Account,遞延收入科目
Material Requests,材料要求
Issue Date,發行日期
Activity Cost,項目成本
Timesheet Detail,詳細時間表
Consumed Qty,消耗的數量
Telecommunications,電信
Billing currency must be equal to either default company's currency or party account currency,帳單貨幣必須等於默認公司的貨幣或科目幣種
Indicates that the package is a part of this delivery (Only Draft),表示該包是這個交付的一部分（僅草案）
Row {0}: Due Date cannot be before posting date,行{0}：到期日期不能在發布日期之前
Make Payment Entry,製作付款分錄
Quantity for Item {0} must be less than {1},項目{0}的數量必須小於{1}
Sales Invoice Trends,銷售發票趨勢
Can refer row only if the charge type is 'On Previous Row Amount' or 'Previous Row Total',可以參考的行只有在充電類型是“在上一行量'或'前行總計”
Delivery Warehouse,交貨倉庫
Tree of financial Cost Centers.,財務成本中心的樹。
Sub Type,子類型
Delivery Document No,交貨證明文件號碼
Ensure Delivery Based on Produced Serial No,確保基於生產的序列號的交貨
Please set 'Gain/Loss Account on Asset Disposal' in Company {0},請公司制定“關於資產處置收益/損失科目”{0}
Get Items From Purchase Receipts,從採購入庫單取得項目
Creation Date,創建日期
Target Location is required for the asset {0},目標位置是資產{0}所必需的
"Selling must be checked, if Applicable For is selected as {0}",銷售必須進行檢查，如果適用於被選擇為{0}
Material Request Date,材料申請日期
Supplier Quotation Item,供應商報價項目
Material Consumption is not set in Manufacturing Settings.,材料消耗未在生產設置中設置。
Visit the forums,訪問論壇
Student Mobile Number,學生手機號碼
Has Variants,有變種
You have already selected items from {0} {1},您已經選擇從項目{0} {1}
Name of the Monthly Distribution,每月分配的名稱
Batch ID is mandatory,批號是必需的
Batch ID is mandatory,批號是必需的
Parent Sales Person,母公司銷售人員
No items to be received are overdue,沒有收到的物品已逾期
The seller and the buyer cannot be the same,賣方和買方不能相同
Collect Progress,收集進度
Select the program first,首先選擇程序
Patient Age,患者年齡
Managing Projects,項目管理
Serial no {0} has been already returned,序列號{0}已被返回
Supplier of Goods or Services.,供應商的商品或服務。
Fiscal Year,財政年度
Planned,計劃
Margin Money,保證金
Budget,預算
Set Open,設置打開
Fixed Asset Item must be a non-stock item.,固定資產項目必須是一個非庫存項目。
"Budget cannot be assigned against {0}, as it's not an Income or Expense account",財政預算案不能對{0}指定的，因為它不是一個收入或支出科目
Max exemption amount for {0} is {1},{0}的最大免除金額為{1}
Achieved,已實現
Application Form Route,申請表路線
Row {0}: Allocated amount {1} must be less than or equals to invoice outstanding amount {2},行{0}：已分配量{1}必須小於或等於發票餘額{2}
In Words will be visible once you save the Sales Invoice.,銷售發票一被儲存，就會顯示出來。
Follow Up,跟進
Is Sales Item,是銷售項目
Item Group Tree,項目群組的樹狀結構
Item {0} is not setup for Serial Nos. Check Item master,項目{0}的序列號未設定，請檢查項目主檔
Maintenance Time,維護時間
Amount to Deliver,量交付
Insurance Start Date,保險開始日期
Same item has been entered multiple times. {0},相同的物品已被多次輸入。 {0}
The Term Start Date cannot be earlier than the Year Start Date of the Academic Year to which the term is linked (Academic Year {}). Please correct the dates and try again.,這個詞開始日期不能超過哪個術語鏈接學年的開學日期較早（學年{}）。請更正日期，然後再試一次。
There were errors.,有錯誤。
Employee {0} has already applied for {1} between {2} and {3} : ,員工{0}已在{2}和{3}之間申請{1}：
Guardian Interests,守護興趣
Update Account Name / Number,更新帳戶名稱/號碼
Multiple fiscal years exist for the date {0}. Please set company in Fiscal Year,多個會計年度的日期{0}存在。請設置公司財年
Instructor Records to be created by,導師記錄由
{0} created,{0}已新增
GST Account,GST帳戶
Against Sales Order,對銷售訂單
Serial No Status,序列號狀態
Outstanding,優秀
Daily Timesheet Summary,每日時間表摘要
"Row {0}: To set {1} periodicity, difference between from and to date \
						must be greater than or equal to {2}","行{0}：設置{1}的週期性，從和到日期\
之間差必須大於或等於{2}"
This is based on stock movement. See {0} for details,這是基於庫存移動。見{0}詳情
Selling,銷售
Amount {0} {1} deducted against {2},金額{0} {1}抵扣{2}
Name and Employee ID,姓名和僱員ID,
Website Item Group,網站項目群組
Duties and Taxes,關稅和稅款
Projects Settings,項目設置
Please enter Reference date,參考日期請輸入
{0} payment entries can not be filtered by {1},{0}付款分錄不能由{1}過濾
Table for Item that will be shown in Web Site,表項，將在網站顯示出來
Supplied Qty,附送數量
Material Request Item,物料需求項目
Please cancel Purchase Receipt {0} first,請先取消購買收據{0}
Tree of Item Groups.,項目群組樹。
Total Produced Qty,總生產數量
Cannot refer row number greater than or equal to current row number for this Charge type,不能引用的行號大於或等於當前行號碼提供給充電式
Item-wise Purchase History,全部項目的購買歷史
Please click on 'Generate Schedule' to fetch Serial No added for Item {0},請點擊“生成表”來獲取序列號增加了對項目{0}
Frozen,凍結的
Vehicle Type,車輛類型
Base Amount (Company Currency),基本金額（公司幣種）
Raw Materials,原料
Installation Time,安裝時間
Accounting Details,會計細節
status html,狀態HTML,
Delete all the Transactions for this Company,刪除所有交易本公司
O Positive,O積極
Investments,投資
Resolution Details,詳細解析
Transaction Type,交易類型
Acceptance Criteria,驗收標準
Please enter Material Requests in the above table,請輸入在上表請求材料
No repayments available for Journal Entry,沒有可用於日記帳分錄的還款
Image List,圖像列表
Attribute Name,屬性名稱
Generate Invoice At Beginning Of Period,在期初生成發票
Show In Website,顯示在網站
Expected Time (in hours),預期時間（以小時計）
Check in (group),檢查（組）
Qty to Order,訂購數量
"The account head under Liability or Equity, in which Profit/Loss will be booked",負債或權益下的科目頭，其中利潤/虧損將被黃牌警告
Another Budget record '{0}' already exists against {1} '{2}' and account '{3}' for fiscal year {4},對於財務年度{4}，{1}&#39;{2}&#39;和科目“{3}”已存在另一個預算記錄“{0}”
Gantt chart of all tasks.,所有任務的甘特圖。
Mins to First Response,分鐘為第一個反應
Margin Type,保證金類型
{0} hours,{0}小時
Default Grading Scale,默認等級規模
Tax Account,稅收科目
Invoice No,發票號碼
Room Name,房間名稱
Prescription Duration,處方時間
Customer Addresses And Contacts,客戶的地址和聯絡方式
Campaign Efficiency,運動效率
Campaign Efficiency,運動效率
Discussion,討論
Transaction ID,事務ID,
Anytime,任何時候
Bank Account No,銀行帳號
Surgical History,手術史
Mapped Header,映射的標題
Resignation Letter Date,辭退信日期
Pricing Rules are further filtered based on quantity.,定價規則進一步過濾基於數量。
Discharge,卸貨
Repeat Customer Revenue,重複客戶收入
Mapped Items,映射項目
IT,它
Chapter,章節
Pair,對
Default account will be automatically updated in POS Invoice when this mode is selected.,選擇此模式後，默認帳戶將在POS發票中自動更新。
Select BOM and Qty for Production,選擇BOM和數量生產
Depreciation Schedule,折舊計劃
Sales Partner Addresses And Contacts,銷售合作夥伴地址和聯繫人
Against Account,針對帳戶
Actual Date,實際日期
Please set the Default Cost Center in {0} company.,請在{0}公司中設置默認成本中心。
Has Batch No,有批號
Shopify Webhook Detail,Shopify Webhook詳細信息
Goods and Services Tax (GST India),商品和服務稅（印度消費稅）
Excise Page Number,消費頁碼
Purchase Date,購買日期
Could not generate Secret,無法生成秘密
Volunteer Type,志願者類型
Personal Details,個人資料
Please set 'Asset Depreciation Cost Center' in Company {0},請設置在公司的資產折舊成本中心“{0}
Maintenance Schedules,保養時間表
Actual End Date (via Timesheet),實際結束日期（通過時間表）
Soil Type,土壤類型
Amount {0} {1} against {2} {3},量{0} {1}對{2} {3}
Quotation Trends,報價趨勢
Item Group not mentioned in item master for item {0},項目{0}之項目主檔未提及之項目群組
GoCardless Mandate,GoCardless任務
Debit To account must be a Receivable account,借方科目必須是應收帳款科目
Shipping Amount,航運量
Period Score,期間得分
Add Customers,添加客戶
Pending Amount,待審核金額
Special,特別
Conversion Factor,轉換因子
Delivered,交付
Vehicle Expenses,車輛費用
Create Lab Test(s) on Sales Invoice Submit,在銷售發票上創建實驗室測試提交
Invoice Details,發票明細
Please enable Google Maps Settings to estimate and optimize routes,請啟用Google地圖設置以估算和優化路線
Show on Website,在網站上顯示
Start on,開始
Hub Category,中心類別
Vehicle Number,車號
Add Letterhead,添加信頭
Self-Driving Vehicle,自駕車
Supplier Scorecard Standing,供應商記分卡站立
Row {0}: Bill of Materials not found for the Item {1},行{0}：材料清單未找到項目{1}
Accounts Receivable,應收帳款
Supplier-Wise Sales Analytics,供應商相關的銷售分析
Availed ITC Central Tax,有效的ITC中央稅收
Company Address Name,公司地址名稱
Use Multi-Level BOM,採用多級物料清單
Include Reconciled Entries,包括對賬項目
"Parent Course (Leave blank, if this isn't part of Parent Course)",家長課程（如果不是家長課程的一部分，請留空）
"Parent Course (Leave blank, if this isn't part of Parent Course)",家長課程（如果不是家長課程的一部分，請留空）
Leave blank if considered for all employee types,保持空白如果考慮到所有的員工類型
Distribute Charges Based On,分銷費基於
Timesheets,時間表
net pay info,淨工資信息
CESS Amount,CESS金額
Enable Sync,啟用同步
Single Transaction Threshold,單一交易閾值
This value is updated in the Default Sales Price List.,該值在默認銷售價格表中更新。
New Expenses,新的費用
PDC/LC Amount,PDC / LC金額
Shareholder,股東
Additional Discount Amount,額外的折扣金額
Get Items from Prescriptions,從Prescriptions獲取物品
Patient Details,患者細節
B Positive,B積極
"Maximum benefit of employee {0} exceeds {1} by the sum {2} of previous claimed\
			amount",員工{0}的最大權益超過{1}，前面聲明的金額\金額為{2}
"Row #{0}: Qty must be 1, as item is a fixed asset. Please use separate row for multiple qty.",行＃{0}：訂購數量必須是1，因為項目是固定資產。請使用單獨的行多數量。
Abbr can not be blank or space,縮寫不能為空白或輸入空白鍵
Patient Medical Record,病人醫療記錄
Group to Non-Group,集團以非組
Sports,體育
Total Actual,實際總計
Student Siblings,學生兄弟姐妹
Subscription Plan Detail,訂閱計劃詳情
Unit,單位
Please specify Company,請註明公司
Customer Acquisition and Loyalty,客戶取得和忠誠度
Maintenance Task,維護任務
Please set B2C Limit in GST Settings.,請在GST設置中設置B2C限制。
Marketplace Settings,市場設置
Warehouse where you are maintaining stock of rejected items,你維護退貨庫存的倉庫
Skip Material Transfer,跳過材料轉移
Skip Material Transfer,跳過材料轉移
Unable to find exchange rate for {0} to {1} for key date {2}. Please create a Currency Exchange record manually,無法為關鍵日期{2}查找{0}到{1}的匯率。請手動創建貨幣兌換記錄
Price List,價格表
{0} is now the default Fiscal Year. Please refresh your browser for the change to take effect.,{0}是現在預設的會計年度。請重新載入您的瀏覽器，以使更改生效。
Expense Claims,報銷
BOM Search,BOM搜索
Total Consumed Material Cost (via Stock Entry),總消耗材料成本（通過庫存輸入）
Subscription Period,訂閱期
To Date cannot be less than From Date,迄今不能少於起始日期
"Publish ""In Stock"" or ""Not in Stock"" on Hub based on stock available in this warehouse.",基於倉庫中的庫存，在Hub上發布“庫存”或“庫存”。
Fuel Type,燃料類型
Please specify currency in Company,請公司指定的貨幣
Wages per hour,時薪
Stock balance in Batch {0} will become negative {1} for Item {2} at Warehouse {3},在批量庫存餘額{0}將成為負{1}的在倉庫項目{2} {3}
Following Material Requests have been raised automatically based on Item's re-order level,下列資料的要求已自動根據項目的重新排序水平的提高
Account {0} is invalid. Account Currency must be {1},科目{0}是無效的。科目貨幣必須是{1}
Is Internal Supplier,是內部供應商
Create User Permission,創建用戶權限
UOM Conversion factor is required in row {0},計量單位換算係數是必需的行{0}
material_request_item,material_request_item,
"Row #{0}: Reference Document Type must be one of Sales Order, Sales Invoice or Journal Entry",行＃{0}：參考文件類型必須是銷售訂單之一，銷售發票或日記帳分錄
1 Loyalty Points = How much base currency?,1忠誠度積分=多少基礎貨幣？
Retain Sample,保留樣品
Row {0}: From Time and To Time is mandatory.,行{0}：從時間和時間是強制性的。
Amount Difference,金額差異
Item Price added for {0} in Price List {1},加入項目價格為{0}價格表{1}
Order Information,訂單信息
Please enter Employee Id of this sales person,請輸入這個銷售人員的員工標識
Classification of Customers by region,客戶按區域分類
In Production,在生產中
Difference Amount must be zero,差量必須是零
Please enter Production Item first,請先輸入生產項目
Calculated Bank Statement balance,計算的銀行對賬單餘額
Normal Test Template,正常測試模板
disabled user,禁用的用戶
Quotation,報價
Cannot set a received RFQ to No Quote,無法將收到的詢價單設置為無報價
Select an account to print in account currency,選擇一個科目以科目貨幣進行打印
Production Analytics,生產Analytics（分析）
This is based on transactions against this Patient. See timeline below for details,這是基於對這個病人的交易。有關詳情，請參閱下面的時間表
Item {0} has already been returned,項{0}已被退回
**Fiscal Year** represents a Financial Year. All accounting entries and other major transactions are tracked against **Fiscal Year**.,**財年**表示財政年度。所有的會計輸入項目和其他重大交易針對**財年**進行追蹤。
Customer / Lead Address,客戶/鉛地址
Supplier Scorecard Setup,供應商記分卡設置
Assessment Plan Name,評估計劃名稱
Work Order Operation,工作訂單操作
Warning: Invalid SSL certificate on attachment {0},警告：附件無效的SSL證書{0}
"Leads help you get business, add all your contacts and more as your leads",信息幫助你的業務，你所有的聯繫人和更添加為您的線索
Actual Operation Time,實際操作時間
Applicable To (User),適用於（用戶）
Applied,應用的
Re-open,重新打開
Qty as per Stock UOM,數量按庫存計量單位
Guardian2 Name,Guardian2名稱
02-Post Sale Discount,02-售後折扣
"Special Characters except ""-"", ""#"", ""."" and ""/"" not allowed in naming series",特殊字符除了“ - ”，“”，“＃”，和“/”未命名序列允許
"Keep Track of Sales Campaigns. Keep track of Leads, Quotations, Sales Order etc from Campaigns to gauge Return on Investment.",追蹤銷售計劃。追踪訊息，報價，銷售訂單等，從競賽來衡量投資報酬。
You can't redeem Loyalty Points having more value than the Grand Total.,您無法兌換價值超過總計的忠誠度積分。
SO Qty,SO數量
The field To Shareholder cannot be blank,“股東”字段不能為空
Calculate Total Score,計算總分
Health Insurance,健康保險
Manufacturing Manager,生產經理
Serial No {0} is under warranty upto {1},序列號{0}在保修期內直到{1}
Minimum Permissible Value,最小允許值
User {0} already exists,用戶{0}已經存在
Shipments,發貨
Total Allocated Amount (Company Currency),總撥款額（公司幣種）
To be delivered to customer,要傳送給客戶
Scrap Material Cost,廢料成本
Serial No {0} does not belong to any Warehouse,序列號{0}不屬於任何倉庫
Email Notification Sent,電子郵件通知已發送
In Words (Company Currency),大寫（Company Currency）
Company is manadatory for company account,公司是公司科目的管理者
"Item Code, warehouse, quantity are required on row",在行上需要項目代碼，倉庫，數量
Supplier,供應商
This is a root department and cannot be edited.,這是根部門，無法編輯。
Show Payment Details,顯示付款詳情
Duration in Days,持續時間天數
Miscellaneous Expenses,雜項開支
Default Company,預設公司
Transactions Annual History,交易年曆
Expense or Difference account is mandatory for Item {0} as it impacts overall stock value,對項目{0}而言， 費用或差異科目是強制必填的，因為它影響整個庫存總值。
Bank Name,銀行名稱
-Above,-以上
Leave the field empty to make purchase orders for all suppliers,將該字段留空以為所有供應商下達採購訂單
Inpatient Visit Charge Item,住院訪問費用項目
Fluid,流體
Note: Email will not be sent to disabled users,注意：電子郵件將不會被發送到被禁用的用戶
Number of Interaction,交互次數
Number of Interaction,交互次數
Item Variant Settings,項目變式設置
Select Company...,選擇公司...
Leave blank if considered for all departments,保持空白如果考慮到全部部門
{0} is mandatory for Item {1},{0}是強制性的項目{1}
"Item {0}: {1} qty produced, ",項目{0}：{1}產生的數量，
From Currency,從貨幣
Weight (In Kilogram),體重（公斤）
"chapters/chapter_name,
leave blank automatically set after saving chapter.",保存章節後自動設置章節/章節名稱。
Please set GST Accounts in GST Settings,請在GST設置中設置GST帳戶
Type of Business,業務類型
"Please select Allocated Amount, Invoice Type and Invoice Number in atleast one row",請ATLEAST一行選擇分配金額，發票類型和發票號碼
Cost of New Purchase,新的採購成本
Sales Order required for Item {0},所需的{0}項目銷售訂單
Grant Description,授予說明
Rate (Company Currency),率（公司貨幣）
Unallocated Amount,未分配金額
Please enable Applicable on Purchase Order and Applicable on Booking Actual Expenses,請啟用適用於採購訂單並適用於預訂實際費用
Cannot find a matching Item. Please select some other value for {0}.,無法找到匹配的項目。請選擇其他值{0}。
Taxes and Charges,稅收和收費
"A Product or a Service that is bought, sold or kept in stock.",產品或服務已購買，出售或持有的庫存。
Cannot select charge type as 'On Previous Row Amount' or 'On Previous Row Total' for first row,不能選擇充電式為'在上一行量'或'在上一行總'的第一行
This covers all scorecards tied to this Setup,這涵蓋了與此安裝程序相關的所有記分卡
Child Item should not be a Product Bundle. Please remove item `{0}` and save,子項不應該是一個產品包。請刪除項目`{0}`和保存
Banking,銀行業
Add Timesheets,添加時間表
Bank Guarantee,銀行擔保
Bank Guarantee,銀行擔保
Transaction Details,交易明細
Please click on 'Generate Schedule' to get schedule,請在“產生排程”點擊以得到排程表
Ordered Quantity,訂購數量
"e.g. ""Build tools for builders""",例如「建設建設者工具“
Grading Scale Intervals,分級刻度間隔
Purchase Defaults,購買默認值
Make Job Card,製作工作卡
"Could not create Credit Note automatically, please uncheck 'Issue Credit Note' and submit again",無法自動創建Credit Note，請取消選中&#39;Issue Credit Note&#39;並再次提交
Profit for the year,年度利潤
{0} {1}: Accounting Entry for {2} can only be made in currency: {3},{0} {1}在{2}會計分錄只能在貨幣言：{3}
In Process,在過程
Itemwise Discount,Itemwise折扣
Tree of financial accounts.,財務賬目的樹。
Reference Document Type,參考文檔類型
Cash Flow Mapping,現金流量映射
{0} against Sales Order {1},{0}針對銷售訂單{1}
Fixed Asset,固定資產
After Date,日期之後
Serialized Inventory,序列化庫存
Invalid {0} for Inter Company Invoice.,Inter公司發票無效的{0}。
Department Analytics,部門分析
Email not found in default contact,在默認聯繫人中找不到電子郵件
Default Billing Rate,默認計費率
{0} Student Groups created.,{0}創建學生組。
{0} Student Groups created.,{0}創建學生組。
Total Billing Amount,總結算金額
Program in the Fee Structure and Student Group {0} are different.,費用結構和學生組{0}中的課程是不同的。
Receivable Account,應收帳款
Valid From Date must be lesser than Valid Upto Date.,有效起始日期必須小於有效起始日期。
Row #{0}: Asset {1} is already {2},行＃{0}：資產{1}已經是{2}
Stock Balance,庫存餘額
Sales Order to Payment,銷售訂單到付款
With Payment of Tax,繳納稅款
TRIPLICATE FOR SUPPLIER,供應商提供服務
New Balance In Base Currency,基礎貨幣的新平衡
This will be day 1 of the crop cycle,這將是作物週期的第一天
Please select correct account,請選擇正確的科目
Weight UOM,重量計量單位
List of available Shareholders with folio numbers,包含folio號碼的可用股東名單
Salary Structure Employee,薪資結構員工
Show Variant Attributes,顯示變體屬性
The payment gateway account in plan {0} is different from the payment gateway account in this payment request,計劃{0}中的支付閘道科目與此付款請求中的支付閘道科目不同
Course Name,課程名
No Tax Withholding data found for the current Fiscal Year.,未找到當前財年的預扣稅數據。
Users who can approve a specific employee's leave applications,用戶可以批准特定員工的休假申請
Office Equipments,辦公設備
Qty,數量
Companies,企業
Scoring Setup,得分設置
Electronics,電子
Debit ({0}),借記卡（{0}）
Allow Same Item Multiple Times,多次允許相同的項目
Raise Material Request when stock reaches re-order level,當庫存到達需重新訂購水平時提高物料需求
Received Date,接收日期
"If you have created a standard template in Sales Taxes and Charges Template, select one and click on the button below.",如果您已經創建了銷售稅和費模板標準模板，選擇一個，然後點擊下面的按鈕。
Basic Amount (Company Currency),基本金額（公司幣種）
Guardians,守護者
Payment Confirmation,付款確認
Prices will not be shown if Price List is not set,價格將不會顯示如果沒有設置價格
Total Incoming Value,總收入值
Debit To is required,借方是必填項
Inpatient Record,住院病歷
"Timesheets help keep track of time, cost and billing for activites done by your team",時間表幫助追踪的時間，費用和結算由你的團隊做activites,
Purchase Price List,採購價格表
Templates of supplier scorecard variables.,供應商記分卡變數模板。
Quality Manager,質量經理
Payment Reconciliation,付款對帳
Please select Incharge Person's name,請選擇Incharge人的名字
Technology,技術
BOM Website Operation,BOM網站運營
outstanding_amount,outstanding_amount,
Supplier Score,供應商分數
Schedule Admission,安排入場
Cumulative Transaction Threshold,累積交易閾值
Total Invoiced Amt,總開票金額
Conversion Rate,兌換率
Product Search,產品搜索
To Time,要時間
Approving Role (above authorized value),批准角色（上述授權值）
Credit To account must be a Payable account,信用科目必須是應付帳款
Insurance End Date,保險終止日期
Please select Student Admission which is mandatory for the paid student applicant,請選擇付費學生申請者必須入學的學生
BOM recursion: {0} cannot be parent or child of {2},BOM遞歸： {0}不能父母或兒童{2}
Budget List,預算清單
Completed Qty,完成數量
"For {0}, only debit accounts can be linked against another credit entry",{0}，只有借方科目可以連接另一個貸方分錄
Allow Overtime,允許加班
"Serialized Item {0} cannot be updated using Stock Reconciliation, please use Stock Entry",序列化項目{0}無法使用庫存調節更新，請使用庫存條目
"Serialized Item {0} cannot be updated using Stock Reconciliation, please use Stock Entry",序列化項目{0}無法使用庫存調節更新，請使用庫存條目
Maximum Samples - {0} can be retained for Batch {1} and Item {2}.,可以為批次{1}和項目{2}保留最大樣本數量{0}。
Add Time Slots,添加時間插槽
{0} Serial Numbers required for Item {1}. You have provided {2}.,{0}產品{1}需要的序號。您已提供{2}。
Current Valuation Rate,目前的估值價格
GoCardless payment gateway settings,GoCardless支付網關設置
Exchange Gain/Loss,兌換收益/損失
Lost Reason,失落的原因
Enable Amazon,啟用亞馬遜
Row #{0}: Account {1} does not belong to company {2},行＃{0}：科目{1}不屬於公司{2}
Sample Size,樣本大小
Please enter Receipt Document,請輸入收據憑證
All items have already been invoiced,所有項目已開具發票
Please specify a valid 'From Case No.',請指定一個有效的“從案號”
Further cost centers can be made under Groups but entries can be made against non-Groups,進一步的成本中心可以根據組進行，但項可以對非組進行
Total allocated leaves are more days than maximum allocation of {0} leave type for employee {1} in the period,在此期間，總分配的離職時間超過員工{1}的最大分配{0}離職類型的天數
Users and Permissions,用戶和權限
Branch,分支機構
Ca/(K+Ca+Mg),的Ca /（K +鈣+鎂）
Fulfillment User,履行用戶
Total Monthly Sales,每月銷售總額
Subscription Plans,訂閱計劃
Weather,天氣
Actual Quantity,實際數量
example: Next Day Shipping,例如：次日發貨
Serial No {0} not found,序列號{0}未找到
Fee Schedule Program,費用計劃計劃
Student Batch,學生批
Make Student,使學生
Min Grade,最小成績
Healthcare Service Unit Type,醫療服務單位類型
You have been invited to collaborate on the project: {0},您已被邀請在項目上進行合作：{0}
Parent Supplier Group,父供應商組
Purchase Orders to Bill,向比爾購買訂單
Accumulated Values in Group Company,集團公司累計價值
Supplier Delivery Note,供應商交貨單
Actual Qty {0} / Waiting Qty {1},實際數量{0} /等待數量{1}
Actual Qty {0} / Waiting Qty {1},實際數量{0} /等待數量{1}
E-commerce GSTIN,電子商務GSTIN,
Bank Clearance Summary,銀行結算摘要
"Create and manage daily, weekly and monthly email digests.",建立和管理每日，每週和每月的電子郵件摘要。
This is based on transactions against this Sales Person. See timeline below for details,這是基於針對此銷售人員的交易。請參閱下面的時間表了解詳情
Current Amount,電流量
Tax Declaration of {0} for period {1} already submitted.,已提交期間{1}的稅務申報{0}。
Leaves has been granted sucessfully,葉子已成功獲得
Fee Structure,費用結構
Costing Amount,成本核算金額
Application Fee,報名費
On Hold,等候接聽
Inter Company Account,公司內帳戶
Import in Bulk,進口散裝
Address & Contacts,地址及聯絡方式
Sender Name,發件人名稱
Agriculture Analysis Criteria,農業分析標準
[Select],[選擇]
Blood Pressure (diastolic),血壓（舒張）
Sent To,發給
Make Sales Invoice,做銷售發票
Softwares,軟件
Next Contact Date cannot be in the past,接下來跟日期不能過去
For Reference Only.,僅供參考。
Select Batch No,選擇批號
Invalid {0}: {1},無效的{0}：{1}
Reference Inv,參考文獻
Advance Amount,提前量
Capacity Planning,產能規劃
Rounding Adjustment (Company Currency,四捨五入調整（公司貨幣）
Policy number,保單號碼
Reference Number,參考號碼
New Workplace,新工作空間
Set as Closed,設置為關閉
No Item with Barcode {0},沒有條碼{0}的品項
Require Result Value,需要結果值
Show a slideshow at the top of the page,顯示幻燈片在頁面頂部
Tax Withholding Rate,稅收預扣稅率
Boms,物料清單
Stores,商店
Projects Manager,項目經理
Delivery Time,交貨時間
Ageing Based On,老齡化基於
Appointment cancelled,預約被取消
End of Life,壽命結束
Include All Assessment Group,包括所有評估小組
Customer Mobile No,客戶手機號碼
Cash Flow Mapping Template Details,現金流量映射模板細節
Track separate Income and Expense for product verticals or divisions.,跟踪獨立收入和支出進行產品垂直或部門。
Item Reorder,項目重新排序
Transfer Material,轉印材料
Send Payment Request,發送付款請求
"Specify the operations, operating cost and give a unique Operation no to your operations.",指定作業、作業成本並給予該作業一個專屬的作業編號。
This document is over limit by {0} {1} for item {4}. Are you making another {3} against the same {2}?,這份文件是超過限制，通過{0} {1}項{4}。你在做另一個{3}對同一{2}？
Please set recurring after saving,請設置保存後復發
Select change amount account,選擇變化量科目
Price List Currency,價格表之貨幣
Allow Negative Stock,允許負庫存
Installation Note,安裝注意事項
Cash Flow from Financing,從融資現金流
Budget Account,預算科目
Verified By,認證機構
"Cannot change company's default currency, because there are existing transactions. Transactions must be cancelled to change the default currency.",不能改變公司的預設貨幣，因為有存在的交易。交易必須取消更改預設貨幣。
Is Income Tax Liability,是所得稅責任
Grade Description,等級說明
Is Invoiced,已開票
Purchase Receipt No,採購入庫單編號
Earnest Money,保證金
 Shipping Bill Number,裝運單編號
Actions performed,已執行的操作
Section Leader,科長
Transport Receipt No,運輸收據編號
Source of Funds (Liabilities),資金來源（負債）
Source and Target Location cannot be same,源和目標位置不能相同
Quantity in row {0} ({1}) must be same as manufactured quantity {2},列{0}的數量（{1}）必須與生產量{2}相同
Employee,僱員
Fixed Deposit Number,定期存款編號
Failure Date,失敗日期
Result Title Field,結果標題字段
Collected Time,收集時間
Sales Monthly History,銷售月曆
Next Due Date,下一個到期日
Select Batch,選擇批次
{0} {1} is fully billed,{0} {1}}已開票
Vital Signs,生命體徵
Payment Deductions or Loss,付款扣除或損失
Soil Analysis Criterias,土壤分析標準
Standard contract terms for Sales or Purchase.,銷售或採購的標準合同條款。
Group by Voucher,集團透過券
Are you sure you want to cancel this appointment?,你確定要取消這個預約嗎？
Hotel Room Pricing Package,酒店房間價格套餐
Sales Pipeline,銷售渠道
Please set default account in Salary Component {0},請薪酬部分設置默認科目{0}
Please select BOM for Item in Row {0},請行選擇BOM為項目{0}
Fetch Subscription Updates,獲取訂閱更新
Account {0} does not match with Company {1} in Mode of Account: {2},帳戶{0}與帳戶模式{2}中的公司{1}不符
Specified BOM {0} does not exist for Item {1},指定BOM {0}的項目不存在{1}
Course: ,課程：
Maintenance Schedule {0} must be cancelled before cancelling this Sales Order,維護時間表{0}必須取消早於取消這個銷售訂單
Applicable for Users,適用於用戶
Expense Claim Approved,報銷批准
Set Advances and Allocate (FIFO),設置進度和分配（FIFO）
No Work Orders created,沒有創建工作訂單
Pharmaceutical,製藥
Cost of Purchased Items,購買的物品成本
Sales Order Required,銷售訂單需求
Become a Seller,成為賣家
Credit To,信貸
Active Leads / Customers,有效訊息/客戶
Leave blank to use the standard Delivery Note format,留空以使用標準的交貨單格式
Maintenance Schedule Detail,維護計劃細節
Warn for new Purchase Orders,警告新的採購訂單
Reading 9,9閱讀
Is Frozen,就是冰凍
Group node warehouse is not allowed to select for transactions,組節點倉庫不允許選擇用於交易
Buying Settings,採購設定
BOM No. for a Finished Good Item,BOM編號為成品產品
No Quote,沒有報價
Post Title Key,帖子標題密鑰
For Job Card,對於工作卡
Prescriptions,處方
Payment Account,付款帳號
Please specify Company to proceed,請註明公司以處理
Net Change in Accounts Receivable,應收帳款淨額變化
Accepted,接受的
Sales Invoices Summary,銷售發票摘要
To Party Name,到黨名
Organization,組織
Organization,組織
BOM Update Tool,BOM更新工具
Student Group Name,學生組名稱
Show exploded view,顯示爆炸視圖
Creating Fees,創造費用
Please make sure you really want to delete all the transactions for this company. Your master data will remain as it is. This action cannot be undone.,請確保你真的要刪除這家公司的所有交易。主數據將保持原樣。這個動作不能撤消。
Search Results,搜索結果
Room Number,房間號
Invalid reference {0} {1},無效的參考{0} {1}
{0} ({1}) cannot be greater than planned quanitity ({2}) in Production Order {3},"{0} ({1})不能大於計劃數量
({2})生產訂單的 {3}"
Shipping Rule Label,送貨規則標籤
Payroll Entry,工資項目
View Fees Records,查看費用記錄
Make Tax Template,使稅收模板
User Forum,用戶論壇
Raw Materials cannot be blank.,原材料不能為空。
Row #{0} (Payment Table): Amount must be negative,行＃{0}（付款表）：金額必須為負數
"Could not update stock, invoice contains drop shipping item.",無法更新庫存，發票包含下降航運項目。
Fulfilment Status,履行狀態
Lab Test Sample,實驗室測試樣品
Allow Rename Attribute Value,允許重命名屬性值
Quick Journal Entry,快速日記帳分錄
You can not change rate if BOM mentioned agianst any item,你不能改變速度，如果BOM中提到反對的任何項目
Invoice Series Prefix,發票系列前綴
Previous Work Experience,以前的工作經驗
Update Account Number / Name,更新帳號/名稱
Response Key List,響應密鑰列表
For Quantity,對於數量
Please enter Planned Qty for Item {0} at row {1},請輸入列{1}的品項{0}的計劃數量
API,API,
Result Preview Field,結果預覽字段
Packing Unit,包裝單位
Trialling,試用
Deferred Revenue,遞延收入
Cash Account will used for Sales Invoice creation,現金科目將用於創建銷售發票
Membership Expiry Date,會員到期日
{0} must be negative in return document,{0}必須返回文檔中負
Minutes to First Response for Issues,分鐘的問題第一個反應
Terms and Conditions1,條款及條件1,
The name of the institute for which you are setting up this system.,該機構的名稱要為其建立這個系統。
"Accounting entry frozen up to this date, nobody can do / modify entry except role specified below.",會計分錄凍結至該日期，除以下指定職位權限外，他人無法修改。
Please save the document before generating maintenance schedule,請在產生維護計畫前儲存文件
Latest price updated in all BOMs,最新價格在所有BOM中更新
Project Status,項目狀態
Check this to disallow fractions. (for Nos),勾選此選項則禁止分數。 （對於NOS）
Naming Series (for Student Applicant),命名系列（面向學生申請人）
Practitioner Service Unit Schedule,從業者服務單位時間表
Transporter Name,貨運公司名稱
Authorized Value,授權值
Show Operations,顯示操作
Minutes to First Response for Opportunity,分鐘的機會第一個反應
Item or Warehouse for row {0} does not match Material Request,行{0}的項目或倉庫不符合物料需求
Unit of Measure,計量單位
Year End Date,年結結束日期
Task Depends On,任務取決於
Opportunity,機會
Default Workstation,預設工作站
Expense Claim Approved Message,報銷批准的訊息
Deductions or Loss,扣除或損失
{0} {1} is closed,{0} {1}關閉
How frequently?,多久？
Total Collected: {0},總計：{0}
Get Current Stock,取得當前庫存資料
Tree of Bill of Materials,物料清單樹狀圖
TDS Computation Summary,TDS計算摘要
Current State,當前狀態
Mark Present,馬克現在
From Shareholder,來自股東
Drug,藥物
Maintenance start date can not be before delivery date for Serial No {0},序號{0}的維護開始日期不能早於交貨日期
Actual End Date,實際結束日期
Is Finance Cost Adjustment,財務成本調整
Operating Cost (Company Currency),營業成本（公司貨幣）
Applicable To (Role),適用於（角色）
Pending Leaves,等待葉子
Replace BOM,更換BOM,
Code {0} already exist,代碼{0}已經存在
Sales orders are not available for production,銷售訂單不可用於生產
Fixed Asset Depreciation Settings,固定資產折舊設置
Will also apply for variants unless overrridden,同時將申請變種，除非overrridden,
Advances,進展
Manufacture against Material Request,對製造材料要求
Assessment Group: ,評估組：
Request for,要求
Approving User cannot be same as user the rule is Applicable To,批准用戶作為用戶的規則適用於不能相同
Basic Rate (as per Stock UOM),基本速率（按庫存計量單位）
No of Requested SMS,無的請求短信
Leave Without Pay does not match with approved Leave Application records,停薪留職不批准請假的記錄相匹配
Please supply the specified items at the best possible rates,請在提供最好的利率規定的項目
Employee Transfer cannot be submitted before Transfer Date ,員工轉移無法在轉移日期前提交
Make Invoice,製作發票
Remaining Balance,保持平衡
Auto close Opportunity after 15 days,15天之後自動關閉商機
Purchase Orders are not allowed for {0} due to a scorecard standing of {1}.,由於{1}的記分卡，{0}不允許採購訂單。
Barcode {0} is not a valid {1} code,條形碼{0}不是有效的{1}代碼
End Year,結束年份
Quot/Lead %,報價/鉛％
Quot/Lead %,報價/鉛％
Contract End Date must be greater than Date of Joining,合同結束日期必須大於加入的日期
Driver,司機
Nutrition Values,營養價值觀
Is billable,是可計費的
A third party distributor / dealer / commission agent / affiliate / reseller who sells the companies products for a commission.,第三方分銷商/經銷商/代理商/分支機構/分銷商誰銷售公司產品的佣金。
{0} against Purchase Order {1},{0}針對採購訂單{1}
Patient Demographics,患者人口統計學
Actual Start Date (via Timesheet),實際開始日期（通過時間表）
This is an example website auto-generated from ERPNext,這是一個由 ERPNext 自動產生的範例網站
Ageing Range 1,老齡範圍1,
Enable Shopify,啟用Shopify,
Total advance amount cannot be greater than total claimed amount,總預付金額不能超過索賠總額
"Standard tax template that can be applied to all Purchase Transactions. This template can contain list of tax heads and also other expense heads like ""Shipping"", ""Insurance"", ""Handling"" etc.

#### Note,

The tax rate you define here will be the standard tax rate for all **Items**. If there are **Items** that have different rates, they must be added in the **Item Tax** table in the **Item** master.

#### Description of Columns,

1. Calculation Type: 
    - This can be on **Net Total** (that is the sum of basic amount).
    - **On Previous Row Total / Amount** (for cumulative taxes or charges). If you select this option, the tax will be applied as a percentage of the previous row (in the tax table) amount or total.
    - **Actual** (as mentioned).
2. Account Head: The Account ledger under which this tax will be booked,
3. Cost Center: If the tax / charge is an income (like shipping) or expense it needs to be booked against a Cost Center.
4. Description: Description of the tax (that will be printed in invoices / quotes).
5. Rate: Tax rate.
6. Amount: Tax amount.
7. Total: Cumulative total to this point.
8. Enter Row: If based on ""Previous Row Total"" you can select the row number which will be taken as a base for this calculation (default is the previous row).
9. Consider Tax or Charge for: In this section you can specify if the tax / charge is only for valuation (not a part of total) or only for total (does not add value to the item) or for both.
10. Add or Deduct: Whether you want to add or deduct the tax.","可應用到所有購買交易稅的標準模板。這個模板可以包含稅收元首和像“送貨”，“保險”還包括其他費用清單的頭，“處理”等

 ####注

您在此處定義的稅率將標準稅率對所有** **的項目。如果有** **物品具有不同的速率，就必須在**項計稅添加**表中的** **項目主。

 ####列

 1的說明。計算類型：
 - 這可以是在淨** **總（即基本量的總和）。
 - **以前的行總計/金額**（對於累計稅費）。如果選擇此選項，稅收將與前行的百分比（在稅率表）量或總被應用。
 - ** **實際（如前所述）。
 2。帳戶負責人：該帳戶下的台帳此稅收將被黃牌警告
 3。成本中心：如果稅/收費收入（如海運）或費用，它需要對一個成本中心預訂。
 4。說明：稅收的說明（將在發票/報價印刷）。
 5。速度：稅率。
 6。金額：稅額。
 7。總計：累積總數達到了這一點。
 8。輸入行：如果基於“前行匯總”，您可以選擇將被視為這種計算基礎（預設值是前行）的行號。
 9。考慮稅收或收費為：在本節中，你可以指定是否稅/費僅用於評估（總不是部分），或只為總（不增加價值的項目），或兩者兼有。
 10。添加或扣除：無論你是想增加或扣除的稅。"
Homepage,主頁
Grant Application Details ,授予申請細節
Original Item,原始項目
Recd Quantity,到貨數量
Fee Records Created - {0},費紀錄創造 -  {0}
Asset Category Account,資產類別的科目
Row #{0} (Payment Table): Amount must be positive,行＃{0}（付款表）：金額必須為正值
Cannot produce more Item {0} than Sales Order quantity {1},無法產生更多的項目{0}不是銷售訂單數量{1}
Select Attribute Values,選擇屬性值
Reason For Issuing document,簽發文件的原因
Stock Entry {0} is not submitted,庫存輸入{0}不提交
Bank / Cash Account,銀行／現金科目
Next Contact By cannot be same as the Lead Email Address,接著聯繫到不能相同鉛郵箱地址
Billing City,結算城市
Manual,手冊
Hide Currency Symbol,隱藏貨幣符號
Sales Opportunities by Source,來源的銷售機會
"e.g. Bank, Cash, Credit Card",例如：銀行，現金，信用卡
Source Name,源名稱
"Normal resting blood pressure in an adult is approximately 120 mmHg systolic, and 80 mmHg diastolic, abbreviated ""120/80 mmHg""",成年人的正常靜息血壓約為收縮期120mmHg，舒張壓80mmHg，縮寫為“120 / 80mmHg”
"Set items shelf life in days, to set expiry based on manufacturing_date plus self life",以天為單位設置貨架保質期，根據manufacturer_date加上自我壽命設置到期日
Credit Note,信用票據
Ignore Employee Time Overlap,忽略員工時間重疊
Service Address,服務地址
Calibration,校準
Procedure Prescription,程序處方
Furnitures and Fixtures,家具及固定裝置
Manufacture,製造
Setup Company,安裝公司
Lab Test Report,實驗室測試報告
Please Delivery Note first,請送貨單第一
Application Date,申請日期
Currency and Price List,貨幣和價格表
Customer / Lead Name,客戶/鉛名稱
Clearance Date not mentioned,清拆日期未提及
Production,生產
Occupation,佔用
For Quantity must be less than quantity {0},對於數量必須小於數量{0}
Row {0}:Start Date must be before End Date,列#{0}：開始日期必須早於結束日期
Planting Area,種植面積
Total(Qty),總計（數量）
Installed Qty,安裝數量
Is Paid,支付
Time at which materials were received,物料收到的時間
Products per Page,每頁產品
Outgoing Rate,傳出率
Billing Status,計費狀態
Report an Issue,報告問題
Utility Expenses,公用事業費用
Row #{0}: Journal Entry {1} does not have account {2} or already matched against another voucher,行＃{0}：日記條目{1}沒有帳戶{2}或已經對另一憑證匹配
Criteria Weight,標準重量
Default Buying Price List,預設採購價格表
Buying Rate,購買率
Row {0}: Enter location for the asset item {1},行{0}：輸入資產項目{1}的位置
About the Company,關於公司
Sales Order Message,銷售訂單訊息
"Set Default Values like Company, Currency, Current Fiscal Year, etc.",設定預設值如公司，貨幣，當前財政年度等
Payment Type,付款類型
Please select a Batch for Item {0}. Unable to find a single batch that fulfills this requirement,請選擇項目{0}的批次。無法找到滿足此要求的單個批次
Please select a Batch for Item {0}. Unable to find a single batch that fulfills this requirement,請選擇項目{0}的批次。無法找到滿足此要求的單個批次
No gain or loss in the exchange rate,匯率沒有收益或損失
Select Employees,選擇僱員
Sales Invoice Series,銷售發票系列
Potential Sales Deal,潛在的銷售交易
Complaints,投訴
Cheque/Reference Date,支票/參考日期
Total Taxes and Charges,總營業稅金及費用
Emergency Contact,緊急聯絡人
Payment Entry,付款輸入
sales-browser,銷售瀏覽器
Ledger,分類帳
Drug Code,藥品代碼
Target  Amount,目標金額
Print Format for Online,在線打印格式
Shopping Cart Settings,購物車設定
Accounting Entries,會計分錄
"If selected Pricing Rule is made for 'Rate', it will overwrite Price List. Pricing Rule rate is the final rate, so no further discount should be applied. Hence, in transactions like Sales Order, Purchase Order etc, it will be fetched in 'Rate' field, rather than 'Price List Rate' field.",如果選定的定價規則是針對“費率”制定的，則會覆蓋價目表。定價規則費率是最終費率，因此不應再應用更多折扣。因此，在諸如銷售訂單，採購訂單等交易中，它將在&#39;費率&#39;字段中取代，而不是在&#39;價格列表率&#39;字段中取出。
Paid Loan,付費貸款
Duplicate Entry. Please check Authorization Rule {0},重複的條目。請檢查授權規則{0}
Reference Due Date,參考到期日
Ref SQ,參考SQ,
Receipt document must be submitted,收到文件必須提交
Received Qty,到貨數量
Serial No / Batch,序列號/批次
Not Paid and Not Delivered,沒有支付，未送達
Parent Item,父項目
Account Type,科目類型
Webhooks Details,Webhooks詳細信息
No time sheets,沒有考勤表
GoCardless Customer,GoCardless客戶
Maintenance Schedule is not generated for all the items. Please click on 'Generate Schedule',維護計畫不會為全部品項生成。請點擊“生成表”
To Produce,以生產
"For row {0} in {1}. To include {2} in Item rate, rows {3} must also be included",對於行{0} {1}。以包括{2}中的檔案速率，行{3}也必須包括
Parent Service Unit,家長服務單位
Make User,使用戶
Identification of the package for the delivery (for print),寄送包裹的識別碼（用於列印）
Reserved Quantity,保留數量
Please enter valid email address,請輸入有效的電子郵件地址
Please enter valid email address,請輸入有效的電子郵件地址
Volunteer Skill,志願者技能
Inter Company Invoice Reference,Inter公司發票參考
Please select an item in the cart,請在購物車中選擇一個項目
Purchase Receipt Items,採購入庫項目
Customizing Forms,自定義表單
Arrear,拖欠
Depreciation Amount during the period,期間折舊額
Is Return (Credit Note),是退貨（信用票據）
Start Job,開始工作
Serial no is required for the asset {0},資產{0}需要序列號
Disabled template must not be default template,殘疾人模板必須不能默認模板
For row {0}: Enter planned qty,對於行{0}：輸入計劃的數量
Income Account,收入科目
Amount in customer's currency,量客戶的貨幣
Delivery,交貨
Current Qty,目前數量
Restaurant Menu,餐廳菜單
Add Suppliers,添加供應商
Help Section,幫助科
Prev,上一頁
Distance UOM,距離UOM,
"Student Batches help you track attendance, assessments and fees for students",學生批幫助您跟踪學生的出勤，評估和費用
Total Allocated Amount,總撥款額
Set default inventory account for perpetual inventory,設置永久庫存的默認庫存科目
"Cannot deliver Serial No {0} of item {1} as it is reserved to \
												fullfill Sales Order {2}",無法提供項目{1}的序列號{0}，因為它保留在\ fullfill銷售訂單{2}中
Material Request Type,材料需求類型
Send Grant Review Email,發送格蘭特回顧郵件
"LocalStorage is full, did not save",localStorage的是滿的，沒救
Row {0}: UOM Conversion Factor is mandatory,行{0}：計量單位轉換係數是必需的
Room Capacity,房間容量
Already record exists for the item {0},已有記錄存在項目{0}
Ref,參考
You will lose records of previously generated invoices. Are you sure you want to restart this subscription?,您將失去先前生成的發票記錄。您確定要重新啟用此訂閱嗎？
Registration Fee,註冊費用
Loyalty Program Collection,忠誠度計劃集
Subcontracted Item,轉包項目
Student {0} does not belong to group {1},學生{0}不屬於組{1}
Voucher #,憑證＃
Purchase Order Message,採購訂單的訊息
Shipping Country,航運國家
Hide Customer's Tax Id from Sales Transactions,從銷售交易隱藏客戶的稅號
Relieving Date,解除日期
Total Quantity,總數（量
"Pricing Rule is made to overwrite Price List / define discount percentage, based on some criteria.",定價規則是由覆蓋價格表/定義折扣百分比，基於某些條件。
Warehouse can only be changed via Stock Entry / Delivery Note / Purchase Receipt,倉庫只能通過存貨分錄/送貨單/採購入庫單來改變
Class / Percentage,類／百分比
Shopify Settings,Shopify設置
Market Place ID,市場ID,
Head of Marketing and Sales,營銷和銷售主管
Income Tax,所得稅
Track Leads by Industry Type.,以行業類型追蹤訊息。
Go to Letterheads,去信頭
Item Supplier,產品供應商
Please enter Item Code to get batch no,請輸入產品編號，以取得批號
Please select a value for {0} quotation_to {1},請選擇一個值{0} quotation_to {1}
No Items selected for transfer,沒有選擇轉移項目
Stock Settings,庫存設定
"Merging is only possible if following properties are same in both records. Is Group, Root Type, Company",合併是唯一可能的，如果以下屬性中均有記載相同。是集團，根型，公司
Electric,電動
% Progress,％進展
Gain/Loss on Asset Disposal,在資產處置收益/損失
"Only the Student Applicant with the status ""Approved"" will be selected in the table below.",下表中將只選擇狀態為“已批准”的學生申請人。
Rates,價格
Account number for account {0} is not available.<br> Please setup your Chart of Accounts correctly.,科目{0}的科目號碼不可用。 <br>請正確設置您的會計科目表。
Depends on Tasks,取決於任務
Manage Customer Group Tree.,管理客戶群組樹。
Result Value,結果值
New Cost Center Name,新的成本中心名稱
Task Completion,任務完成
Not in Stock,沒存貨
Volunteer Skills,志願者技能
HR User,HR用戶
Reference Document Name,參考文件名稱
Taxes and Charges Deducted,稅收和費用扣除
Issues,問題
Loyalty Program Name,忠誠計劃名稱
Status must be one of {0},狀態必須是一個{0}
Reminder to update GSTIN Sent,提醒更新GSTIN發送
Debit To,借方
Restaurant Menu Item,餐廳菜單項
Required only for sample item.,只對樣品項目所需。
Actual Qty After Transaction,交易後實際數量
Pending SO Items For Purchase Request,待處理的SO項目對於採購申請
Student Admissions,學生入學
{0} {1} is disabled,{0} {1}被禁用
Billing Currency,結算貨幣
Extra Large,特大號
Scientific Name,科學名稱
Service Unit Type,服務單位類型
Branch Code,分行代碼
"Reselect, if the chosen contact is edited after save",重新選擇，如果所選聯繫人在保存後被編輯
In print,已出版
Profit and Loss Statement,損益表
Cheque Number,支票號碼
The item referenced by {0} - {1} is already invoiced,{0}  -  {1}引用的項目已開具發票
Sales Browser,銷售瀏覽器
Total Credit,貸方總額
Warning: Another {0} # {1} exists against stock entry {2},警告：另一個{0}＃{1}存在對庫存分錄{2}
Local,當地
Loans and Advances (Assets),貸款及墊款（資產）
Debtors,債務人
Bank Statement Settings,銀行對賬單設置
Customer Settings,客戶設置
Homepage Featured Product,首頁推薦產品
View Orders,查看訂單
Marketplace URL (to hide and update label),市場URL（隱藏和更新標籤）
All Assessment Groups,所有評估組
New Warehouse Name,新倉庫名稱
App Type,應用類型
Total {0} ({1}),總{0}（{1}）
Territory,領土
Please mention no of visits required,請註明無需訪問
Default Valuation Method,預設的估值方法
Fee,費用
Show Cumulative Amount,顯示累計金額
Update in progress. It might take a while.,正在更新。它可能需要一段時間。
Produced Qty,生產數量
Target Warehouse Name,目標倉庫名稱
Planned Start Time,計劃開始時間
Assessment,評定
Allocated,分配
Close Balance Sheet and book Profit or Loss.,關閉資產負債表和賬面利潤或虧損。
Sensitivity Test Items,靈敏度測試項目
Project Update,項目更新
Fees,費用
Specify Exchange Rate to convert one currency into another,指定的匯率將一種貨幣兌換成另一種
Quotation {0} is cancelled,{0}報價被取消
Total Outstanding Amount,未償還總額
Targets,目標
Please register the SIREN number in the company information file,請在公司信息文件中註冊SIREN號碼
Sales Orders to Bill,比爾的銷售訂單
Price List Master,價格表主檔
CESS Account,CESS帳戶
All Sales Transactions can be tagged against multiple **Sales Persons** so that you can set and monitor targets.,所有的銷售交易，可以用來標記針對多個**銷售**的人，這樣你可以設置和監控目標。
Link to Material Request,鏈接到材料請求
Forum Activity,論壇活動
S.O. No.,SO號
Bank Statement Transaction Settings Item,銀行對賬單交易設置項目
Please create Customer from Lead {0},請牽頭建立客戶{0}
Select Patient,選擇患者
Applicable for Countries,適用於國家
Parameter Name,參數名稱
Student Group Name is mandatory in row {0},學生組名稱是強制性的行{0}
Products to be shown on website homepage,在網站首頁中顯示的產品
This is a root customer group and cannot be edited.,ERPNext是一個開源的基於Web的ERP系統，通過網路技術，向私人有限公司提供整合的工具，在一個小的組織管理大多數流程。有關Web註釋，或購買託管，想得到更多資訊，請連結
Action if Accumulated Monthly Budget Exceeded on PO,採購訂單上累計每月預算超出時的操作
Exchange Rate Revaluation,匯率重估
Ignore Pricing Rule,忽略定價規則
Graduate,畢業生
"Shipping Address does not have country, which is required for this Shipping Rule",送貨地址沒有國家，這是此送貨規則所必需的
Excise Entry,海關入境
Warning: Sales Order {0} already exists against Customer's Purchase Order {1},警告：銷售訂單{0}已經存在針對客戶的採購訂單{1}
"Standard Terms and Conditions that can be added to Sales and Purchases.

Examples:

1. Validity of the offer.
1. Payment Terms (In Advance, On Credit, part advance etc).
1. What is extra (or payable by the Customer).
1. Safety / usage warning.
1. Warranty if any.
1. Returns Policy.
1. Terms of shipping, if applicable.
1. Ways of addressing disputes, indemnity, liability, etc.
1. Address and Contact of your Company.","標準條款和可以添加到銷售和購買條件。

例子：

 1。有效性的報價。
 1。付款條款（事先，在信貸，部分提前等）。
 1。什麼是多餘的（或支付的客戶）。
 1。安全/使用警告。
 1。保修（如有）。
 1。退貨政策。
 1。航運條款（如果適用）。
 1。的解決糾紛，賠償，法律責任等
 1的方式。地址和公司聯繫。"
Issue Type,發行類型
Supplier Invoice Details,供應商發票明細
Expense / Difference account ({0}) must be a 'Profit or Loss' account,費用/差異科目（{0}）必須是一個'收益或損失'的科目
Copied From,複製自
Copied From,複製自
Invoice already created for all billing hours,發票已在所有結算時間創建
Name error: {0},名稱錯誤：{0}
Item Details,產品詳細信息
Is Finance Cost,財務成本
Attendance for employee {0} is already marked,員工{0}的考勤已標記
If more than one package of the same type (for print),如果不止一個相同類型的包裹（用於列印）
Please set default customer in Restaurant Settings,請在“餐廳設置”中設置默認客戶
Parent Warehouse,家長倉庫
Chart,圖表
Net Total,總淨值
Default BOM not found for Item {0} and Project {1},項目{0}和項目{1}找不到默認BOM,
Define various loan types,定義不同的貸款類型
Outstanding Amount,未償還的金額
Time(in mins),時間（分鐘）
Working,工作的
Stock Queue (FIFO),庫存序列（先進先出）
Financial Year,財政年度
{0} does not belong to Company {1},{0}不屬於公司{1}
Could not solve criteria score function for {0}. Make sure the formula is valid.,無法解決{0}的標準分數函數。確保公式有效。
Repayment amount {} should be greater than monthly interest amount {},還款金額{}應大於每月的利息金額{}
Out Patient Settings,出患者設置
Round Off,四捨五入
Quantity must be positive,數量必須是正數
Requested Qty,要求數量
The fields From Shareholder and To Shareholder cannot be blank,來自股東和股東的字段不能為空
Cashier Closing,收銀員關閉
Use for Shopping Cart,使用的購物車
Value {0} for Attribute {1} does not exist in the list of valid Item Attribute Values for Item {2},值{0}的屬性{1}不在有效的項目列表中存在的屬性值項{2}
Select Serial Numbers,選擇序列號
Scrap %,廢鋼％
"Charges will be distributed proportionately based on item qty or amount, as per your selection",費用將被分配比例根據項目數量或金額，按您的選擇
Atleast one item should be entered with negative quantity in return document,ATLEAST一個項目應該負數量回報文檔中輸入
"Operation {0} longer than any available working hours in workstation {1}, break down the operation into multiple operations",操作{0}比任何可用的工作時間更長工作站{1}，分解成運行多個操作
Membership Status,成員身份
No Remarks,暫無產品說明
In Maintenance,在維護中
Click this button to pull your Sales Order data from Amazon MWS.,單擊此按鈕可從亞馬遜MWS中提取銷售訂單數據。
Overdue,過期的
Stock Received But Not Billed,庫存接收，但不付款
Root Account must be a group,Root 科目必須是群組科目
Drug Prescription,藥物處方
Repaid/Closed,償還/關閉
Total Projected Qty,預計總數量
Distribution Name,分配名稱
Include UOM,包括UOM,
"Valuation rate not found for the Item {0}, which is required to do accounting entries for {1} {2}. If the item is transacting as a zero valuation rate item in the {1}, please mention that in the {1} Item table. Otherwise, please create an incoming stock transaction for the item or mention valuation rate in the Item record, and then try submiting/cancelling this entry",對於{1} {2}進行會計分錄所需的項目{0}，找不到估值。如果該項目在{1}中作為零估值率項目進行交易，請在{1}項目表中提及。否則，請在項目記錄中創建貨物的進貨庫存交易或提交估值費率，然後嘗試提交/取消此條目
Course Code,課程代碼
Quality Inspection required for Item {0},項目{0}需要品質檢驗
Use POS in Offline Mode,在離線模式下使用POS,
Supplier Variables,供應商變量
{0} is mandatory. Maybe Currency Exchange record is not created for {1} to {2},{0}是強制性的。可能沒有為{1}到{2}創建貨幣兌換記錄
Rate at which customer's currency is converted to company's base currency,客戶貨幣被換算成公司基礎貨幣的匯率
Net Rate (Company Currency),淨利率（公司貨幣）
Manage Territory Tree.,管理領地樹。
Patient Service Unit,病人服務單位
Sales Invoice,銷售發票
Party Balance,黨平衡
Section Subtotal,部分小計
Please select Apply Discount On,請選擇適用的折扣
Sample Retention Warehouse,樣品保留倉庫
Default Receivable Account,預設應收帳款
Deemed Export,被視為出口
Material Transfer for Manufacture,物料轉倉用於製造
Discount Percentage can be applied either against a Price List or for all Price List.,折扣百分比可以應用於單一價目表或所有價目表。
Accounting Entry for Stock,存貨的會計分錄
LabTest Approver,LabTest審批者
You have already assessed for the assessment criteria {}.,您已經評估了評估標準{}。
Work Orders Created: {0},創建的工單：{0}
Sales Team1,銷售團隊1,
Item {0} does not exist,項目{0}不存在
Customer Address,客戶地址
Failed to setup post company fixtures,未能設置公司固定裝置
Default Inventory Account,默認庫存科目
The folio numbers are not matching,作品集編號不匹配
Payment Request for {0},付款申請{0}
Barcode Type,條碼類型
Antibiotic Name,抗生素名稱
Supplier Group master.,供應商組主人。
Occupancy Status,佔用狀況
Apply Additional Discount On,收取額外折扣
Select Type...,選擇類型...
Root Type,root類型
FIFO,FIFO,
Close the POS,關閉POS,
Row # {0}: Cannot return more than {1} for Item {2},行＃{0}：無法返回超過{1}項{2}
Show this slideshow at the top of the page,這顯示在幻燈片頁面頂部
Item UOM,項目計量單位
Tax Amount After Discount Amount (Company Currency),稅額後，優惠金額（公司貨幣）
Target warehouse is mandatory for row {0},目標倉庫是強制性的行{0}
Primary Settings,主要設置
Select Supplier Address,選擇供應商地址
Add Employees,添加員工
Quality Inspection,品質檢驗
Standard Template,標準模板
Warning: Material Requested Qty is less than Minimum Order Qty,警告：物料需求的數量低於最少訂購量
Account {0} is frozen,帳戶{0}被凍結
Legal Entity / Subsidiary with a separate Chart of Accounts belonging to the Organization.,法人/子公司與帳戶的獨立走勢屬於該組織。
Mute Email,靜音電子郵件
"Food, Beverage & Tobacco",食品、飲料＆煙草
Account Number,帳號
Can only make payment against unbilled {0},只能使支付對未付款的{0}
Commission rate cannot be greater than 100,佣金比率不能大於100,
Allocate Advances Automatically (FIFO),自動分配進度（FIFO）
Volunteer,志願者
Subcontract,轉包
Please enter {0} first,請輸入{0}第一
Actual End Time,實際結束時間
Manufacturer Part Number,製造商零件編號
Estimated Time and Cost,估計時間和成本
Bin,箱子
Crop Name,作物名稱
Only users with {0} role can register on Marketplace,只有{0}角色的用戶才能在Marketplace上註冊
No of Sent SMS,沒有發送短信
Appointments and Encounters,約會和遭遇
Healthcare Administrator,醫療管理員
Set a Target,設定目標
Dosage Strength,劑量強度
Inpatient Visit Charge,住院訪問費用
Expense Account,費用科目
Software,軟件
Colour,顏色
Assessment Plan Criteria,評估計劃標準
Expiry date is mandatory for selected item,有效期限對所選項目是強制性的
Prevent Purchase Orders,防止採購訂單
Scheduled,預定
Request for quotation.,詢價。
"Please select Item where ""Is Stock Item"" is ""No"" and ""Is Sales Item"" is ""Yes"" and there is no other Product Bundle",請選擇項，其中“正股項”是“否”和“是銷售物品”是“是”，沒有其他產品捆綁
Select Customer,選擇客戶
Academic,學術的
Personal and Social History,個人和社會史
User {0} created,用戶{0}已創建
Fee Breakup for each student,每名學生的費用分手
Total advance ({0}) against Order {1} cannot be greater than the Grand Total ({2}),總的超前（{0}）對二階{1}不能大於總計（{2}）
Select Monthly Distribution to unevenly distribute targets across months.,選擇按月分佈橫跨幾個月不均勻分佈的目標。
Change Code,更改代碼
Valuation Rate,估值率
Diesel,柴油機
Price List Currency not selected,尚未選擇價格表之貨幣
Availed ITC Cess,採用ITC Cess,
Student Monthly Attendance Sheet,學生每月考勤表
Shipping rule only applicable for Selling,運費規則僅適用於銷售
Depreciation Row {0}: Next Depreciation Date cannot be before Purchase Date,折舊行{0}：下一個折舊日期不能在購買日期之前
Project Start Date,專案開始日期
Rename Log,重命名日誌
Student Group or Course Schedule is mandatory,學生組或課程表是強制性的
Student Group or Course Schedule is mandatory,學生組或課程表是強制性的
Maintain Billing Hours and Working Hours Same on Timesheet,維護發票時間和工作時間的時間表相同
Against Document No,對文件編號
Scrap,廢料
Go to Instructors,去教練
Manage Sales Partners.,管理銷售合作夥伴。
Inspection Type,檢驗類型
Visited yet,已訪問
Warehouses with existing transaction can not be converted to group.,與現有的交易倉庫不能轉換為組。
Result HTML,結果HTML,
How often should project and company be updated based on Sales Transactions.,項目和公司應根據銷售交易多久更新一次。
Add Students,新增學生
Please select {0},請選擇{0}
C-Form No,C-表格編號
Exploded_items,Exploded_items,
Distance,距離
List your products or services that you buy or sell.,列出您所購買或出售的產品或服務。
Storage Temperature,儲存溫度
Researcher,研究員
Program Enrollment Tool Student,計劃註冊學生工具
Start date should be less than end date for task {0},開始日期應該小於任務{0}的結束日期
Consolidated Financial Statement,合併財務報表
Name or Email is mandatory,姓名或電子郵件是強制性
Instructor Log,講師日誌
Clinical Procedure,臨床程序
Delivery Note Series,送貨單系列
Returned Qty,返回的數量
Exit,出口
Root Type is mandatory,root類型是強制性的
Failed to install presets,無法安裝預設
UOM Conversion in Hours,UOM按小時轉換
Signee Details,簽名詳情
"{0} currently has a {1} Supplier Scorecard standing, and RFQs to this supplier should be issued with caution.",{0}目前擁有{1}供應商記分卡，並且謹慎地向該供應商發出詢價。
Non Profit Manager,非營利經理
Total Cost(Company Currency),總成本（公司貨幣）
Serial No {0} created,序列號{0}創建
Company Description for website homepage,公司介紹了網站的首頁
"For the convenience of customers, these codes can be used in print formats like Invoices and Delivery Notes",為方便客戶，這些代碼可以在列印格式，如發票和送貨單使用
Suplier Name,Suplier名稱
Could not retrieve information for {0}.,無法檢索{0}的信息。
Opening Entry Journal,開幕詞報
Fulfilment Terms,履行條款
Time Sheet List,時間表列表
You can enter any date manually,您可以手動輸入任何日期
Result Printed,結果打印
Depreciation Expense Account,折舊費用科目
Is Inter State,是國際
Shift Management,班次管理
Only leaf nodes are allowed in transaction,只有葉節點中允許交易
Total Costing Amount (via Timesheet),總成本金額（通過時間表）
Expense Approver,費用審批
Row {0}: Advance against Customer must be credit,行{0}：提前對客戶必須是信用
Hourly,每小時
Non-Group to Group,非集團集團
ERPNext User,ERPNext用戶
Batch is mandatory in row {0},在{0}行中必須使用批處理
Batch is mandatory in row {0},在{0}行中必須使用批處理
Purchase Receipt Item Supplied,採購入庫項目供應商
Enable Scheduled Synch,啟用預定同步
To Datetime,以日期時間
Logs for maintaining sms delivery status,日誌維護短信發送狀態
Make Payment via Journal Entry,通過日記帳分錄進行付款
Clinical Procedure Template,臨床步驟模板
Inspection Required before Delivery,分娩前檢查所需
Inspection Required before Purchase,購買前檢查所需
Pending Activities,待活動
Create Lab Test,創建實驗室測試
View Chart of Accounts,查看會計科目表
Chapter Member,章會員
Minimum Order Quantity,最小起訂量
Your Organization,你的組織
"Skipping Leave Allocation for the following employees, as Leave Allocation records already exists against them. {0}",跳過以下員工的休假分配，因為已經存在針對他們的休假分配記錄。 {0}
Fees Category,費用類別
Please enter relieving date.,請輸入解除日期。
Amt,AMT,
Notify Employee,通知員工
Enter name of campaign if source of enquiry is campaign,輸入活動的名稱，如果查詢來源是運動
Newspaper Publishers,報紙出版商
Select Fiscal Year,選擇財政年度
Expected Delivery Date should be after Sales Order Date,預計交貨日期應在銷售訂單日期之後
Reorder Level,重新排序級別
Chart Of Accounts Template,會計科目模板
Update stock must be enable for the purchase invoice {0},必須為購買發票{0}啟用更新庫存
Item Price updated for {0} in Price List {1},項目價格更新{0}價格表{1}
Account with child nodes cannot be converted to ledger,有子節點的帳不能轉換到總帳
Accepted Warehouse,收料倉庫
Posting Date,發布日期
One customer can be part of only single Loyalty Program.,一個客戶只能參與一個忠誠度計劃。
Mark Half Day,馬克半天
Sales Team,銷售團隊
Duplicate entry,重複的條目
Enter the name of the Beneficiary before submittting.,在提交之前輸入受益人的姓名。
Get Students,讓學生
Under Warranty,在保修期
[Error],[錯誤]
In Words will be visible once you save the Sales Order.,銷售訂單一被儲存，就會顯示出來。
Employee Birthday,員工生日
Please select Completion Date for Completed Repair,請選擇完成修復的完成日期
Student Batch Attendance Tool,學生考勤批處理工具
Scheduled Upto,計劃的高級
Date of Establishment,成立時間
Venture Capital,創業投資
An academic term with this 'Academic Year' {0} and 'Term Name' {1} already exists. Please modify these entries and try again.,這個“學年”一個學期{0}和“術語名稱”{1}已經存在。請修改這些條目，然後再試一次。
Must be Whole Number,必須是整數
Invoice Copy,發票副本
Serial No {0} does not exist,序列號{0}不存在
Customer Warehouse (Optional),客戶倉庫（可選）
Blanket Order Item,一攬子訂單項目
Invoice Number,發票號碼
Orders,訂單
Leave Approver,休假審批人
Please select a batch,請選擇一個批次
Travel and Expense Claim,旅行和費用索賠
Redemption Cost Center,贖回成本中心
Scope,範圍
Assessment Group Name,評估小組名稱
Material Transferred for Manufacture,轉移至製造的物料
Last Sync Datetime,上次同步日期時間
Receipt Document Type,收據憑證類型
Select Companies,選擇公司
Proposal/Price Quote,提案/報價
Healthcare,衛生保健
Target Detail,目標詳細資訊
Single Variant,單一變種
% of materials billed against this Sales Order,針對這張銷售訂單的已開立帳單的百分比(%)
Mode of Transportation,運輸方式
Period Closing Entry,期末進入
Select Department...,選擇部門...
Cost Center with existing transactions can not be converted to group,與現有的交易成本中心，不能轉化為組
Authorization URL,授權URL,
Amount {0} {1} {2} {3},金額{0} {1} {2} {3}
Depreciation,折舊
The number of shares and the share numbers are inconsistent,股份數量和庫存數量不一致
Supplier(s),供應商（S）
Guardian Student,學生監護人
Credit Limit,信用額度
Avg. Selling Price List Rate,平均。出售價目表率
Payment Entries {0} are un-linked,付款項{0}是聯合國聯
Voucher No,憑證編號
Lead Owner Efficiency,主導效率
Lead Owner Efficiency,主導效率
"You can claim only an amount of {0}, the rest amount {1} should be in the application \
				as pro-rata component",您只能聲明一定數量的{0}，剩餘數量{1}應該在應用程序中作為按比例分量
Customer Type,客戶類型
Recipient Message And Payment Details,收件人郵件和付款細節
Source DocType,源DocType,
Open a new ticket,打開一張新票
Transporter,運輸車
Material Requests {0} created,{0}物料需求已建立
No of People,沒有人
Template of terms or contract.,模板條款或合同。
Address and Contact,地址和聯絡方式
Is Account Payable,為應付帳款
Stock cannot be updated against Purchase Receipt {0},庫存不能對外購入庫單進行更新{0}
Auto close Issue after 7 days,7天之後自動關閉問題
Note: Due / Reference Date exceeds allowed customer credit days by {0} day(s),註：由於/參考日期由{0}天超過了允許客戶的信用天數（S）
Student Applicant,學生申請
Hub Tracked Item,Hub跟踪物品
Accumulated Depreciation Account,累計折舊科目
Discuss ID,討論ID,
Freeze Stock Entries,凍結庫存項目
Boarding Student,寄宿學生
Please enable Applicable on Booking Actual Expenses,請啟用適用於預訂實際費用
Expected Value After Useful Life,期望值使用壽命結束後
Reorder level based on Warehouse,根據倉庫訂貨點水平
Billing Rate,結算利率
Qty to Deliver,數量交付
Amazon will synch data updated after this date,亞馬遜將同步在此日期之後更新的數據
Stock Analytics,庫存分析
Lab Test(s) ,實驗室測試
Against Document Detail No,對文件詳細編號
Deletion is not permitted for country {0},國家{0}不允許刪除
Party Type is mandatory,黨的類型是強制性
Outgoing,發送
Requested For,要求
Against Doctype,針對文檔類型
{0} {1} is cancelled or closed,{0} {1} 被取消或結案
Calculate Depreciation,計算折舊
Track this Delivery Note against any Project,跟踪此送貨單反對任何項目
Net Cash from Investing,從投資淨現金
Work-in-Progress Warehouse,在製品倉庫
Asset {0} must be submitted,資產{0}必須提交
Total Students,學生總數
Attendance Record {0} exists against Student {1},考勤記錄{0}存在針對學生{1}
Reference #{0} dated {1},參考＃ {0}於{1}
Depreciation Eliminated due to disposal of assets,折舊淘汰因處置資產
Work Order Item,工作訂單項目
Item Code,產品編號
Warranty / AMC Details,保修/ AMC詳情
Select students manually for the Activity based Group,為基於活動的組手動選擇學生
User Remark,用戶備註
Optimizing routes.,優化路線。
Cannot create Retention Bonus for left Employees,無法為左員工創建保留獎金
Market Segment,市場分類
Agriculture Manager,農業經理
Paid Amount cannot be greater than total negative outstanding amount {0},支付的金額不能超過總負餘額大於{0}
Variables,變量
Employee Internal Work History,員工內部工作經歷
Closing (Dr),關閉（Dr）
Serial No {0} not in stock,序列號{0}無貨
Tax template for selling transactions.,稅務模板賣出的交易。
Write Off Outstanding Amount,核銷額（億元）
Current Academic Year,當前學年
Current Academic Year,當前學年
Default Stock UOM,預設庫存計量單位
Number of Depreciations Booked,預訂折舊數
Qty Total,數量總計
School/University,學校/大學
Available Qty at Warehouse,有貨數量在倉庫
Billed Amount,帳單金額
(including),（包括）
Double Declining Balance,雙倍餘額遞減
Closed order cannot be cancelled. Unclose to cancel.,關閉的定單不能被取消。 Unclose取消。
Payroll Setup,工資單設置
Synch Products,同步產品
Loyalty Program,忠誠計劃
Father,父親
Support Tickets,支持門票
'Update Stock' cannot be checked for fixed asset sale,"""更新庫存"" 無法檢查固定資產銷售"
Bank Reconciliation,銀行對帳
Get Updates,獲取更新
{0} {1}: Account {2} does not belong to Company {3},{0} {1}科目{2}不屬於公司{3}
Select at least one value from each of the attributes.,從每個屬性中至少選擇一個值。
Material Request {0} is cancelled or stopped,材料需求{0}被取消或停止
Dispatch State,派遣國
Leave Management,離開管理
Groups,組
Group by Account,以科目分群組
Hold Invoice,保留發票
Lower Income,較低的收入
Current Order,當前訂單
Number of serial nos and quantity must be the same,序列號和數量必須相同
Source and target warehouse cannot be same for row {0},列{0}的來源和目標倉庫不可相同
Asset Received But Not Billed,已收到但未收費的資產
"Difference Account must be a Asset/Liability type account, since this Stock Reconciliation is an Opening Entry",差異科目必須是資產/負債類型的科目，因為此庫存調整是一個開帳分錄
Disbursed Amount cannot be greater than Loan Amount {0},支付額不能超過貸款金額較大的{0}
Go to Programs,轉到程序
Purchase Order number required for Item {0},所需物品{0}的採購訂單號
'From Date' must be after 'To Date',“起始日期”必須經過'終止日期'
Batch {0} of Item {1} is disabled.,項目{1}的批處理{0}已禁用。
Select Healthcare Practitioner...,選擇醫療從業者......
Cannot change status as student {0} is linked with student application {1},無法改變地位的學生{0}與學生申請鏈接{1}
Fully Depreciated,已提足折舊
Stock Projected Qty,存貨預計數量
Customer {0} does not belong to project {1},客戶{0}不屬於項目{1}
"Quotations are proposals, bids you have sent to your customers",語錄是建議，你已經發送到你的客戶提高出價
Customer's Purchase Order,客戶採購訂單
Bypass credit check at Sales Order ,在銷售訂單旁通過信用檢查
Employee Onboarding Activity,員工入職活動
Check if it is a hydroponic unit,檢查它是否是水培單位
Serial No and Batch,序列號和批次
From Company,從公司
Sum of Scores of Assessment Criteria needs to be {0}.,評估標準的得分之和必須是{0}。
Please set Number of Depreciations Booked,請設置折舊數預訂
Calculations,計算
Value or Qty,價值或數量
Payment Terms,付款條件
Productions Orders cannot be raised for:,製作訂單不能上調：
Minute,分鐘
Purchase Taxes and Charges,購置稅和費
Meetup Embed HTML,Meetup嵌入HTML,
Insured value,保價值
Go to Suppliers,去供應商
POS Closing Voucher Taxes,POS關閉憑證稅
Qty to Receive,未到貨量
Grading Scale Interval,分級分度值
Discount (%) on Price List Rate with Margin,價格上漲率與貼現率的折扣（％）
Discount (%) on Price List Rate with Margin,價格上漲率與貼現率的折扣（％）
Rate / UOM,費率/ UOM,
All Warehouses,所有倉庫
No {0} found for Inter Company Transactions.,Inter公司沒有找到{0}。
About your Company,關於貴公司
Credit To account must be a Balance Sheet account,貸方科目必須是資產負債表科目
Donor,捐贈者
Disable In Words,禁用詞
Item Code is mandatory because Item is not automatically numbered,產品編號是強制性的，因為項目沒有自動編號
Quotation {0} not of type {1},報價{0}非為{1}類型
Maintenance Schedule Item,維護計劃項目
%  Delivered,％交付
Please set the Email ID for the Student to send the Payment Request,請設置學生的電子郵件ID以發送付款請求
Medical History,醫學史
Bank Overdraft Account,銀行透支戶口
Schedule Name,計劃名稱
Sales Pipeline by Stage,按階段劃分的銷售渠道
Make Salary Slip,製作工資單
For Buying,為了購買
Add All Suppliers,添加所有供應商
Row #{0}: Allocated Amount cannot be greater than outstanding amount.,行＃{0}：分配金額不能大於未結算金額。
Browse BOM,瀏覽BOM,
Secured Loans,抵押貸款
Edit Posting Date and Time,編輯投稿時間
Please set Depreciation related Accounts in Asset Category {0} or Company {1},請設置在資產類別{0}或公司折舊相關科目{1}
Normal Range,普通範圍
Academic Year,學年
Available Selling,可用銷售
Loyalty Point Entry Redemption,忠誠度積分兌換
Opening Balance Equity,期初餘額權益
N,ñ
Remaining,剩餘
GST Details,消費稅細節
This is based on transactions against this Healthcare Practitioner.,這是基於針對此醫療保健從業者的交易。
Email sent to supplier {0},電子郵件發送到供應商{0}
Default Sales Unit of Measure,默認銷售單位
Academic Year: ,學年：
Admission Schedule Date,入學時間表日期
Past Due Date,過去的截止日期
Not allow to set alternative item for the item {0},不允許為項目{0}設置替代項目
Authorized Signatory,授權簽字人
Create Fees,創造費用
Total Purchase Cost (via Purchase Invoice),總購買成本（通過採購發票）
Select Quantity,選擇數量
Loyalty Points,忠誠度積分
Customs Tariff Number,海關稅則號
Patient Appointment,患者預約
Approving Role cannot be same as role the rule is Applicable To,審批角色作為角色的規則適用於不能相同
Unsubscribe from this Email Digest,從該電子郵件摘要退訂
Get Suppliers By,獲得供應商
{0} not found for Item {1},找不到項目{1} {0}
Go to Courses,去課程
Show Inclusive Tax In Print,在打印中顯示包含稅
"Bank Account, From Date and To Date are Mandatory",銀行科目，從日期到日期是強制性的
Message Sent,發送訊息
Account with child nodes cannot be set as ledger,科目與子節點不能被設置為分類帳
Rate at which Price list currency is converted to customer's base currency,價目表貨幣被換算成客戶基礎貨幣的匯率
Net Amount (Company Currency),淨金額（公司貨幣）
Hour Rate,小時率
Item Naming By,產品命名規則
Another Period Closing Entry {0} has been made after {1},另一個期末錄入{0}作出後{1}
Material Transferred for Manufacturing,物料轉倉用於製造
Account {0} does not exists,科目{0}不存在
Select Loyalty Program,選擇忠誠度計劃
Project Type,專案類型
Child Task exists for this Task. You can not delete this Task.,子任務存在這個任務。你不能刪除這個任務。
Either target qty or target amount is mandatory.,無論是數量目標或目標量是強制性的。
Cost of various activities,各種活動的費用
"Setting Events to {0}, since the Employee attached to the below Sales Persons does not have a User ID{1}",設置活動為{0}，因為附連到下面的銷售者的僱員不具有用戶ID {1}
Billing Details,結算明細
Source and target warehouse must be different,源和目標倉庫必須是不同的
Payment Failed. Please check your GoCardless Account for more details,支付失敗。請檢查您的GoCardless帳戶以了解更多詳情
Not allowed to update stock transactions older than {0},不允許更新比{0}舊的庫存交易
Inspection Required,需要檢驗
PR Detail,詳細新聞稿
Enter the Bank Guarantee Number before submittting.,在提交之前輸入銀行保證號碼。
Class,類
Fully Billed,完全開票
Work Order cannot be raised against a Item Template,工作訂單不能針對項目模板產生
Shipping rule only applicable for Buying,運費規則只適用於購買
Cash In Hand,手頭現金
Delivery warehouse required for stock item {0},需要的庫存項目交割倉庫{0}
The gross weight of the package. Usually net weight + packaging material weight. (for print),包裹的總重量。通常為淨重+包裝材料的重量。 （用於列印）
Users with this role are allowed to set frozen accounts and create / modify accounting entries against frozen accounts,具有此角色的用戶可以設置凍結科目，並新增/修改對凍結科目的會計分錄
Cuts,削減
Is Cancelled,被註銷
Group Based On,基於組
Group Based On,基於組
Bill Date,帳單日期
Laboratory SMS Alerts,實驗室短信
"Service Item,Type,frequency and expense amount are required",服務項目，類型，頻率和消費金額要求
"Even if there are multiple Pricing Rules with highest priority, then following internal priorities are applied:",即使有更高優先級的多個定價規則，然後按照內部優先級應用：
Plant Analysis Criteria,植物分析標準
Supplier Details,供應商詳細資訊
Setup Progress,設置進度
From value must be less than to value in row {0},來源值必須小於列{0}的值
Wire Transfer,電匯
Check all,全面檢查
Issued Items Against Work Order,針對工單發布物品
BOM Stock Calculated,BOM庫存計算
Default Income Account,預設收入科目
Unclosed Fiscal Years Profit / Loss (Credit),未關閉的財年利潤/損失（信用）
Time Sheets,考勤表
Change In Item,更改項目
Default Payment Request Message,預設的付款請求訊息
Check this if you want to show in website,勾選本項以顯示在網頁上
Balance ({0}),餘額（{0}）
Redeem Against,兌換
Banking and Payments,銀行和支付
Please enter API Consumer Key,請輸入API使用者密鑰
Welcome to ERPNext,歡迎來到ERPNext,
Lead to Quotation,主導報價
Email Reminders will be sent to all parties with email contacts,電子郵件提醒將通過電子郵件聯繫方式發送給各方
Twice Daily,每天兩次
A Negative,一個負面的
Nothing more to show.,沒有更多的表現。
From Customer,從客戶
A Product,一個產品
Make Fee Schedule,製作費用表
Stock UOM,庫存計量單位
Purchase Order {0} is not submitted,採購訂單{0}未提交
Expenses Included In Asset Valuation,資產評估中包含的費用
Normal reference range for an adult is 16–20 breaths/minute (RCP 2012),成人的正常參考範圍是16-20次呼吸/分鐘（RCP 2012）
Tariff Number,稅則號
Available Qty at WIP Warehouse,在WIP倉庫可用的數量
Projected,預計
Serial No {0} does not belong to Warehouse {1},序列號{0}不屬於倉庫{1}
Note: System will not check over-delivery and over-booking for Item {0} as quantity or amount is 0,注：系統將不檢查過交付和超額預訂的項目{0}的數量或金額為0,
Quotation Message,報價訊息
Opening Date,開幕日期
Please save the patient first,請先保存患者
Attendance has been marked successfully.,出席已成功標記。
GST Vehicle Type,GST車型
Silt Composition (%),粉塵成分（％）
Remark,備註
Avoid Confirmation,避免確認
Rate and Amount,率及金額
Account Type for {0} must be {1},科目類型為{0}必須{1}
Leaves and Holiday,休假及假日
Current Academic Term,當前學術期限
Current Academic Term,當前學術期限
Not Billed,不發單
Both Warehouse must belong to same Company,這兩個倉庫必須屬於同一個公司
Default Leave Policy,默認離開政策
Shop URL,商店網址
Landed Cost Voucher Amount,到岸成本憑證金額
Item Balance (Simple),物品餘額（簡單）
Bills raised by Suppliers.,由供應商提出的帳單。
Write Off Account,核銷帳戶
Get prescribed procedures,獲取規定的程序
Redemption Account,贖回科目
Discount Amount,折扣金額
Return Against Purchase Invoice,回到對採購發票
Warranty Period (in days),保修期限（天數）
Relation with Guardian1,與關係Guardian1,
Please select BOM against item {0},請選擇物料{0}的物料清單
Make Invoices,製作發票
Show Stock Quantity,顯示庫存數量
Net Cash from Operations,從運營的淨現金
Item 4,項目4,
Admission End Date,錄取結束日期
Journal Entry Account,日記帳分錄帳號
Student Group,學生組
Quotation Series,報價系列
"An item exists with same name ({0}), please change the item group name or rename the item",具有相同名稱的項目存在（ {0} ） ，請更改項目群組名或重新命名該項目
Soil Analysis Criteria,土壤分析標準
Please select customer,請選擇客戶
I,一世
Asset Depreciation Cost Center,資產折舊成本中心
Sales Order Date,銷售訂單日期
Delivered Qty,交付數量
Assessment Plan,評估計劃
Reverse Journal Entry,反向日記帳分錄
Customer {0} is created.,客戶{0}已創建。
 Currently no stock available in any warehouse,目前任何倉庫沒有庫存
Payment Period Based On Invoice Date,基於發票日的付款期
No. of print,打印數量
Hotel Room Reservation Item,酒店房間預訂項目
Missing Currency Exchange Rates for {0},缺少貨幣匯率{0}
Examiner,檢查員
Stock Entry,存貨分錄
Payment References,付款參考
"Number of intervals for the interval field e.g if Interval is 'Days' and Billing Interval Count is 3, invoices will be generated every 3 days",間隔字段的間隔數，例如，如果間隔為&#39;天數&#39;並且計費間隔計數為3，則會每3天生成一次發票
Allow Stock Consumption,允許庫存消耗
Insurance Details,保險詳情
Payable,支付
Share Type,分享類型
Debtors ({0}),債務人（{0}）
Margin,餘量
New Customers,新客戶
Appointment {0} and Sales Invoice {1} cancelled,約會{0}和銷售發票{1}已取消
Opportunities by lead source,鉛來源的機會
Clearance Date,清拆日期
"Asset is already exists against the item {0}, you cannot change the has serial no value",資產已針對商品{0}存在，您無法更改已連續編號的值
Assessment Report,評估報告
Gross Purchase Amount is mandatory,總消費金額是強制性
Company name not same,公司名稱不一樣
Party is mandatory,黨是強制性
Topic Name,主題名稱
Atleast one of the Selling or Buying must be selected,至少需選擇銷售或購買
Please select a valid Date,請選擇一個有效的日期
Select the nature of your business.,選擇您的業務的性質。
"Single for results which require only a single input, result UOM and normal value 
<br>
Compound for results which require multiple input fields with corresponding event names, result UOMs and normal values,
<br>
Descriptive for tests which have multiple result components and corresponding result entry fields. 
<br>
Grouped for test templates which are a group of other test templates.
<br>
No Result for tests with no results. Also, no Lab Test is created. e.g.. Sub Tests for Grouped results.",單一的結果只需要一個輸入，結果UOM和正常值<br>對於需要具有相應事件名稱的多個輸入字段的結果的化合物，結果為UOM和正常值<br>具有多個結果組件和相應結果輸入字段的測試的描述。 <br>分組為一組其他測試模板的測試模板。 <br>沒有沒有結果的測試結果。此外，沒有創建實驗室測試。例如。分組測試的子測試。
Row #{0}: Duplicate entry in References {1} {2},行＃{0}：引用{1} {2}中的重複條目
Where manufacturing operations are carried.,生產作業於此進行。
As Examiner,作為考官
Default Expense Claim Payable Account,默認費用索賠應付帳款
Default Duration,默認時長
Source Warehouse,來源倉庫
Installation Date,安裝日期
Row #{0}: Asset {1} does not belong to company {2},行＃{0}：資產{1}不屬於公司{2}
Sales Invoice {0} created,已創建銷售發票{0}
Confirmation Date,確認日期
Check Out,查看
Total Invoiced Amount,發票總金額
Min Qty can not be greater than Max Qty,最小數量不能大於最大數量
Accumulated Depreciation,累計折舊
Standing Name,常務名稱
Customer or Supplier Details,客戶或供應商詳細訊息
Current Asset Value,流動資產價值
A link to all the Locations in which the Crop is growing,指向作物生長的所有位置的鏈接
Lead Owner,主導擁有者
Sales Orders Detail,銷售訂單明細
Requested Quantity,要求的數量
EDU-FEE-.YYYY.-,EDU-收費.YYYY.-
Marital Status,婚姻狀況
Auto Material Request,自動物料需求
API consumer secret,API消費者秘密
Available Batch Qty at From Warehouse,在從倉庫可用的批次數量
Gross Pay - Total Deduction - Loan Repayment,工資總額 - 扣除總額 - 貸款還款
Current BOM and New BOM can not be same,當前BOM和新BOM不能相同
Date Of Retirement must be greater than Date of Joining,日期退休必須大於加入的日期
Multiple Variants,多種變體
Against Income Account,對收入科目
Trial Period Start Date,試用期開始日期
Item {0}: Ordered qty {1} cannot be less than minimum order qty {2} (defined in Item).,項目{0}：有序數量{1}不能低於最低訂貨量{2}（項中定義）。
Certified,認證
Monthly Distribution Percentage,每月分配比例
Territory Targets,境內目標
Ca/Mg,鈣/鎂
Transporter Info,貨運公司資訊
Please set default {0} in Company {1},請設置在默認情況下公司{0} {1}
Starting position from top edge,起價頂邊位置
Same supplier has been entered multiple times,同一個供應商已多次輸入
Gross Profit / Loss,總利潤/虧損
Warehouse wise Item Balance Age and Value,倉庫明智的項目平衡年齡和價值
Purchase Order Item Supplied,採購訂單項目供應商
Company Name cannot be Company,公司名稱不能為公司
Letter Heads for print templates.,信頭的列印模板。
Titles for print templates e.g. Proforma Invoice.,"列印模板的標題, 例如 Proforma Invoice。"
Student Guardian,學生家長
Member Name,成員名字
Valuation type charges can not marked as Inclusive,估值類型罪名不能標記為包容性
Update Stock,庫存更新
Different UOM for items will lead to incorrect (Total) Net Weight value. Make sure that Net Weight of each item is in the same UOM.,不同計量單位的項目會導致不正確的（總）淨重值。確保每個項目的淨重是在同一個計量單位。
BOM Rate,BOM率
"Stopped Work Order cannot be cancelled, Unstop it first to cancel",停止的工作訂單不能取消，先取消它
Journal Entry for Scrap,日記帳分錄報廢
Please pull items from Delivery Note,請送貨單拉項目
Row {0}: select the workstation against the operation {1},行{0}：根據操作{1}選擇工作站
Journal Entries {0} are un-linked,日記條目{0}都是非聯
{0} Number {1} already used in account {2},已在{2}科目中使用的{0}號碼{1}
"Record of all communications of type email, phone, chat, visit, etc.",類型電子郵件，電話，聊天，訪問等所有通信記錄
Supplier Scorecard Scoring Standing,供應商記分卡
Manufacturers used in Items,在項目中使用製造商
Please mention Round Off Cost Center in Company,請提及公司舍入成本中心
Terms,條款
Select Days,選擇天數
Term Name,術語名稱
You cannot edit root node.,您不能編輯根節點。
Purchase Order Required,採購訂單為必要項
Timer,計時器
Item-wise Sales History,項目明智的銷售歷史
Purchase Analytics,採購分析
Delivery Note Item,送貨單項目
Current invoice {0} is missing,當前發票{0}缺失
Task,任務
Reference Row #,參考列#
Batch number is mandatory for Item {0},批號是強制性的項目{0}
This is a root sales person and cannot be edited.,您可以通過選擇備份頻率啟動和\
Number of Days in Fiscal Year,會計年度的天數
Stock Ledger,庫存總帳
Rate: {0},價格：{0}
Exchange Gain / Loss Account,匯兌損益科目
MWS Credentials,MWS憑證
Employee and Attendance,員工考勤
Purpose must be one of {0},目的必須是一個{0}
Community Forum,社區論壇
Actual qty in stock,實際庫存數量
Actual qty in stock,實際庫存數量
"URL for ""All Products""",網址“所有產品”
Send SMS,發送短信
Max Score,最高分數
Width of amount in word,在字量的寬度
Default Letter Head,預設信頭
Get Items from Open Material Requests,從開放狀態的物料需求取得項目
Billable,計費
Standard Selling Rate,標準銷售率
Rate at which this tax is applied,此稅適用的匯率
Section Name,部分名稱
Reorder Qty,再訂購數量
Depreciation Row {0}: Expected value after useful life must be greater than or equal to {1},折舊行{0}：使用壽命後的預期值必須大於或等於{1}
Stock Adjustment Account,庫存調整科目
Write Off,註銷項款
Allow Overlap,允許重疊
"System User (login) ID. If set, it will become default for all HR forms.",系統用戶（登錄）的標識。如果設定，這將成為的所有人力資源的預設形式。
Enter depreciation details,輸入折舊明細
{0}: From {1},{0}：從{1}
Leave application {0} already exists against the student {1},對學生{1}已經存在申請{0}
depends_on,depends_on,
Queued for updating latest price in all Bill of Materials. It may take a few minutes.,排隊更新所有材料清單中的最新價格。可能需要幾分鐘。
Name of new Account. Note: Please don't create accounts for Customers and Suppliers,新帳戶的名稱。注：請不要創建帳戶的客戶和供應商
Display Items In Stock,顯示庫存商品
Country wise default Address Templates,依據國家別啟發式的預設地址模板
Payment Order Reference,付款訂單參考
Appearance,出現
Avg. Buying Price List Rate,平均。買價格表價格
Supplier delivers to Customer,供應商提供給客戶
Member information.,會員信息。
[{0}](#Form/Item/{0}) is out of stock,[{0}]（＃窗體/項目/ {0}）缺貨
Asset Maintenance,資產維護
Sales Payment Summary,銷售付款摘要
Restaurant,餐廳
API consumer key,API消費者密鑰
Due / Reference Date cannot be after {0},由於/參考日期不能後{0}
Data Import and Export,資料輸入和輸出
Account Details,帳戶細節
No students Found,沒有發現學生
Medical Department,醫學系
Supplier Scorecard Scoring Criteria,供應商記分卡評分標準
Invoice Posting Date,發票發布日期
Sell,賣
Rounded Total,整數總計
Slots for {0} are not added to the schedule,{0}的插槽未添加到計劃中
List items that form the package.,形成包列表項。
Not permitted. Please disable the Test Template,不允許。請禁用測試模板
Distance (in km),距離（公里）
Percentage Allocation should be equal to 100%,百分比分配總和應該等於100%
Please select Posting Date before selecting Party,在選擇之前，甲方請選擇發布日期
School House,學校議院
Out of AMC,出資產管理公司
Opportunity Amount,機會金額
Number of Depreciations Booked cannot be greater than Total Number of Depreciations,預訂折舊數不能超過折舊總數更大
Order Confirmation Date,訂單確認日期
Make Maintenance Visit,使維護訪問
Please contact to the user who have Sales Master Manager {0} role,請聯絡，誰擁有碩士學位的銷售經理{0}角色的用戶
Default Cash Account,預設的現金科目
Company (not Customer or Supplier) master.,公司（不是客戶或供應商）的主人。
This is based on the attendance of this Student,這是基於這名學生出席
No Students in,沒有學生
Add more items or open full form,添加更多項目或全開放形式
Delivery Notes {0} must be cancelled before cancelling this Sales Order,送貨單{0}必須先取消才能取消銷貨訂單
Go to Users,轉到用戶
Paid amount + Write Off Amount can not be greater than Grand Total,支付的金額+寫的抵銷金額不能大於總計
{0} is not a valid Batch Number for Item {1},{0}不是對項目的有效批號{1}
Note: There is not enough leave balance for Leave Type {0},注：沒有足夠的休假餘額請假類型{0}
Invalid GSTIN or Enter NA for Unregistered,無效的GSTIN或輸入NA未註冊
Program Enrollment Fee,計劃註冊費
Supplier Items,供應商項目
Opportunity Type,機會型
To Employee,給員工
New Company,新公司
Transactions can only be deleted by the creator of the Company,交易只能由公司的創建者被刪除
Incorrect number of General Ledger Entries found. You might have selected a wrong Account in the transaction.,不正確的數字總帳條目中找到。你可能會在交易中選擇了錯誤的科目。
Prefered Contact Email,首選聯繫郵箱
Cheque Width,支票寬度
Validate Selling Price for Item against Purchase Rate or Valuation Rate,驗證售價反對預訂價或估價RATE項目
Fee Schedule,收費表
Create Chart Of Accounts Based On,基於會計科目表創建
Date of Birth cannot be greater than today.,出生日期不能大於今天。
Stock Ageing,存貨帳齡分析表
Student {0} exist against student applicant {1},學生{0}存在針對學生申請{1}
Rounding Adjustment (Company Currency),四捨五入調整（公司貨幣）
Timesheet,時間表
Batch: ,批量：
Loyalty Program Help,忠誠度計劃幫助
Set as Open,設置為打開
Scanned Cheque,支票掃描
Send automatic emails to Contacts on Submitting transactions.,對提交的交易，自動發送電子郵件給聯絡人。
Total Billable Amount,總結算金額
Credit Limit and Payment Terms,信用額度和付款條款
Collection Rules,收集規則
Item 3,項目3,
Order Entry,訂單輸入
Customer Contact Email,客戶聯絡電子郵件
Item and Warranty Details,項目和保修細節
Chapter Members,章節成員
Contribution (%),貢獻（％）
Note: Payment Entry will not be created since 'Cash or Bank Account' was not specified,注：付款項將不會被創建因為“現金或銀行科目”未指定
Project {0} already exists,項目{0}已經存在
Nursing User,護理用戶
Plant Analysis Criterias,植物分析標準
Serial No {0} does not belong to Batch {1},序列號{0}不屬於批次{1}
Validity period of this quotation has ended.,此報價的有效期已經結束。
Capital Work in Progress,資本工作正在進行中
Allow Stale Exchange Rates,允許陳舊的匯率
Sales Person Name,銷售人員的姓名
Please enter atleast 1 invoice in the table,請在表中輸入至少一筆發票
Add Users,添加用戶
No Lab Test created,沒有創建實驗室測試
Item Group,項目群組
Student Group: ,學生組：
Finance Book Id,金融書籍ID,
Safety Stock,安全庫存
Healthcare Settings,醫療設置
Progress % for a task cannot be more than 100.,為任務進度百分比不能超過100個。
Before reconciliation,調整前
Taxes and Charges Added (Company Currency),稅收和收費上架（公司貨幣）
Item Tax Row {0} must have account of type Tax or Income or Expense or Chargeable,"商品稅行{0}必須有科目類型為""稅"" 或 ""收入"" 或 ""支出"" 或 ""課稅的"""
Partly Billed,天色帳單
Item {0} must be a Fixed Asset Item,項{0}必須是固定資產項目
Make Variants,變種
Default BOM,預設的BOM,
Total Billed Amount (via Sales Invoice),總開票金額（通過銷售發票）
Debit Note Amount,借方票據金額
"There are inconsistencies between the rate, no of shares and the amount calculated",費率，股份數量和計算的金額之間不一致
Please re-type company name to confirm,請確認重新輸入公司名稱
Total Outstanding Amt,總街貨量金額
Printing Settings,列印設定
Advance Account,預付款科目
Include Payment (POS),包括支付（POS）
Total Debit must be equal to Total Credit. The difference is {0},借方總額必須等於貸方總額。差額為{0}
Automotive,汽車
Insurance Company,保險公司
Fixed Asset Account,固定資產科目
From Delivery Note,從送貨單
Members,會員
Student Email Address,學生的電子郵件地址
Hub Warehouse,Hub倉庫
From Time,從時間
Hotel Settings,酒店設置
In Stock: ,有現貨：
Custom Message,自定義訊息
Investment Banking,投資銀行業務
input,輸入
Cash or Bank Account is mandatory for making payment entry,製作付款分錄時，現金或銀行科目是強制性輸入的欄位。
Multiple Tier Program,多層計劃
Student Address,學生地址
Student Address,學生地址
Price List Exchange Rate,價目表匯率
All Supplier Groups,所有供應商組織
Account Number {0} already used in account {1},已在科目{1}中使用的帳號{0}
Booked,預訂
Tasks Created,創建的任務
Rate,單價
Address Name,地址名稱
From BOM,從BOM,
Assessment Code,評估準則
Stock transactions before {0} are frozen,{0}前的庫存交易被凍結
Please click on 'Generate Schedule',請點擊“生成表”
Reference No is mandatory if you entered Reference Date,如果你輸入的參考日期，參考編號是強制性的
Payment Document,付款單據
Error evaluating the criteria formula,評估標準公式時出錯
Date of Joining must be greater than Date of Birth,加入日期必須大於出生日期
Plans,計劃
Bank,銀行
Issue Material,發行材料
Connect Shopify with ERPNext,將Shopify與ERPNext連接
For Warehouse,對於倉庫
Delivery Notes {0} updated,已更新交貨單{0}
Offer Date,到職日期
Quotations,語錄
You are in offline mode. You will not be able to reload until you have network.,您在離線模式。您將無法重新加載，直到你有網絡。
Grant,格蘭特
No Student Groups created.,沒有學生團體創建的。
Serial No,序列號
Please enter Maintaince Details first,請先輸入維護細節
Row #{0}: Expected Delivery Date cannot be before Purchase Order Date,行＃{0}：預計交貨日期不能在採購訂單日期之前
Print Language,打印語言
Total Working Hours,總的工作時間
Customer PO Details,客戶PO詳細信息
Including items for sub assemblies,包括子組件項目
Temporary Opening Account,臨時開戶
Enter value must be positive,輸入值必須為正
Finance Books,財務書籍
All Territories,所有的領土
Please set leave policy for employee {0} in Employee / Grade record,請在員工/成績記錄中為員工{0}設置休假政策
Invalid Blanket Order for the selected Customer and Item,所選客戶和物料的無效總訂單
Add Multiple Tasks,添加多個任務
Items,項目
End Date cannot be before Start Date.,結束日期不能在開始日期之前。
Student is already enrolled.,學生已經註冊。
Year Name,年結名稱
Following items {0} are not marked as {1} item. You can enable them as {1} item from its Item master,以下項{0}未標記為{1}項。您可以從項目主文件中將它們作為{1}項啟用
PDC/LC Ref,PDC / LC參考
Product Bundle Item,產品包項目
Sales Partner Name,銷售合作夥伴名稱
Request for Quotations,索取報價
Maximum Invoice Amount,最大發票額
Normal Test Items,正常測試項目
Company Settings,公司設置
Student Language,學生語言
Customers,顧客
Is Working Capital,是營運資本
Order/Quot %,訂單/報價％
Order/Quot %,訂單/報價％
Record Patient Vitals,記錄患者維生素
Institution,機構
Partially Depreciated,部分貶抑
Opening Time,開放時間
From and To dates required,需要起始和到達日期
Securities & Commodity Exchanges,證券及商品交易所
Default Unit of Measure for Variant '{0}' must be same as in Template '{1}',測度變異的默認單位“{0}”必須是相同模板“{1}”
Calculate Based On,計算的基礎上
Unfulfilled,秕
From Warehouse,從倉庫
No employees for the mentioned criteria,沒有僱員提到的標準
No Items with Bill of Materials to Manufacture,不與物料清單的項目，以製造
Default Customer,默認客戶
Stage Name,藝名
Supervisor Name,主管名稱
Do not confirm if appointment is created for the same day,不要確認是否在同一天創建約會
Program Enrollment Course,課程註冊課程
Program Enrollment Course,課程註冊課程
User {0} is already assigned to Healthcare Practitioner {1},用戶{0}已分配給Healthcare Practitioner {1}
Make Sample Retention Stock Entry,使樣品保留庫存條目
Valuation and Total,估值與總計
Negotiation/Review,談判/評論
Scorecards,記分卡
Expired Batches,過期批次
This will restrict user access to other employee records,這將限制用戶訪問其他員工記錄
Shipping City,航運市
Customize the Notification,自定義通知
Cash Flow from Operations,運營現金流
CGST Amount,CGST金額
Shipping Rule,送貨規則
Spouse,伴侶
Add Test,添加測試
Limited to 12 characters,限12個字符
Print Heading,列印標題
Delivery Trip service tours to customers.,送貨服務遊覽給顧客。
Total cannot be zero,總計不能為零
'Days Since Last Order' must be greater than or equal to zero,“自從最後訂購日”必須大於或等於零
Maximum Permissible Value,最大允許值
Employee Advance,員工晉升
Sensitivity,靈敏度
Sync has been temporarily disabled because maximum retries have been exceeded,暫時禁用了同步，因為已超出最大重試次數
Raw Material,原料
Plants and Machineries,廠房和機械設備
Tax Amount After Discount Amount,稅額折後金額
Inpatient Status,住院狀況
Daily Work Summary Settings,每日工作總結設置
Selected Price List should have buying and selling fields checked.,選定價目表應該有買入和賣出的字段。
Please enter Reqd by Date,請輸入按日期請求
Internal Transfer,內部轉賬
Maintenance Tasks,維護任務
Either target qty or target amount is mandatory,無論是數量目標或目標量是必需的
Please select Posting Date first,請選擇發布日期第一
Opening Date should be before Closing Date,開業日期應該是截止日期之前，
Cost Center with existing transactions can not be converted to ledger,與現有的交易成本中心，不能轉換為總賬
Applicable on booking actual expenses,適用於預訂實際費用
Days for which Holidays are blocked for this department.,天的假期被封鎖這個部門。
Detected Disease,檢測到的疾病
Produced,生產
Repayment Start Date cannot be before Disbursement Date.,還款開始日期不能在付款日期之前。
Item Code for Suppliers,對於供應商產品編號
Raised By (Email),由（電子郵件）提出
General,一般
Last Communication,最後溝通
Last Communication,最後溝通
TDS Payable Monthly,TDS應付月度
Queued for replacing the BOM. It may take a few minutes.,排隊等待更換BOM。可能需要幾分鐘時間。
Cannot deduct when category is for 'Valuation' or 'Valuation and Total',不能抵扣當類別為“估值”或“估值及總'
Serial Nos Required for Serialized Item {0},序列號為必填項序列為{0}
Match Payments with Invoices,付款與發票對照
Bank Entry,銀行分錄
Applicable To (Designation),適用於（指定）
Student Email,學生電子郵件
"Allergies, Medical and Surgical History",過敏，醫療和外科史
Add to Cart,添加到購物車
Group By,集團通過
Enable / disable currencies.,啟用／禁用的貨幣。
Could not submit some Salary Slips,無法提交一些薪資單
Get Entries,獲取條目
Get Material Request,獲取材質要求
Postal Expenses,郵政費用
Sales Summary,銷售摘要
Entertainment & Leisure,娛樂休閒
Item Variant Details,項目變體的詳細信息
Item Serial No,產品序列號
Is a Subscription,是訂閱
Create Employee Records,建立員工檔案
Accounting Statements,會計報表
Hour,小時
Last Sales Invoice,上次銷售發票
Please select Qty against item {0},請選擇項目{0}的數量
New Serial No cannot have Warehouse. Warehouse must be set by Stock Entry or Purchase Receipt,新的序列號不能有倉庫。倉庫必須由存貨分錄或採購入庫單進行設定
Lead Type,主導類型
All these items have already been invoiced,所有這些項目已開具發票
Set New Release Date,設置新的發布日期
Monthly Sales Target,每月銷售目標
Can be approved by {0},可以通過{0}的批准
Hotel Room Type,酒店房間類型
Default Material Request Type,默認材料請求類型
Evaluation Period,評估期
Work Order not created,工作訂單未創建
"An amount of {0} already claimed for the component {1},\
						 set the amount equal or greater than {2}",已為組件{1}申請的金額{0}，設置等於或大於{2}的金額
Shipping Rule Conditions,送貨規則條件
The new BOM after replacement,更換後的新物料清單
Point of Sale,銷售點
Received Amount,收金額
Widow,寡婦
GSTIN Email Sent On,發送GSTIN電子郵件
Pick/Drop by Guardian,由守護者選擇
SWIFT number,SWIFT號碼
Party Name,方名稱
Benefits Applied,應用的好處
Planting UOM,種植UOM,
Tax,稅
Not Marked,未標記
Signed,簽
Opening Invoices Summary,打開發票摘要
Education Manager,教育經理
The minimum length between each plant in the field for optimum growth,每個工廠之間的最小長度為最佳的增長
"Batched Item {0} cannot be updated using Stock Reconciliation, instead use Stock Entry",批量項目{0}無法使用庫存調節更新，而是使用庫存條目
"Batched Item {0} cannot be updated using Stock Reconciliation, instead use Stock Entry",批量項目{0}無法使用庫存調節更新，而是使用庫存條目
Report Date,報告日期
Middle Name,中間名字
Asset Details,資產詳情
Invoices,發票
Type of Sample,樣品類型
Source Document Name,源文檔名稱
Get Raw Materials For Production,獲取生產原料
Job Title,職位
"{0} indicates that {1} will not provide a quotation, but all items \
					have been quoted. Updating the RFQ quote status.",{0}表示{1}不會提供報價，但所有項目都已被引用。更新詢價狀態。
Maximum Samples - {0} have already been retained for Batch {1} and Item {2} in Batch {3}.,批次{1}和批次{3}中的項目{2}已保留最大樣本數量{0}。
Update BOM Cost Automatically,自動更新BOM成本
Test Name,測試名稱
Clinical Procedure Consumable Item,臨床程序消耗品
Create Users,創建用戶
Subscriptions,訂閱
Make Academic Term Mandatory,強制學術期限
Quantity to Manufacture must be greater than 0.,量生產必須大於0。
Calculate Prorated Depreciation Schedule Based on Fiscal Year,根據會計年度計算折舊折舊計劃
Visit report for maintenance call.,訪問報告維修電話。
Update Rate and Availability,更新率和可用性
Percentage you are allowed to receive or deliver more against the quantity ordered. For example: If you have ordered 100 units. and your Allowance is 10% then you are allowed to receive 110 units.,相對於訂單量允許接受或交付的變動百分比額度。例如：如果你下定100個單位量，而你的許可額度是10%，那麼你可以收到最多110個單位量。
Customer Group,客戶群組
Row #{0}: Operation {1} is not completed for {2} qty of finished goods in Work Order # {3}. Please update operation status via Time Logs,行＃{0}：操作{1}未完成{2}工件訂單＃{3}中成品的數量。請通過時間日誌更新操作狀態
New Batch ID (Optional),新批號（可選）
New Batch ID (Optional),新批號（可選）
Expense account is mandatory for item {0},交際費是強制性的項目{0}
Website Description,網站簡介
Net Change in Equity,在淨資產收益變化
Please cancel Purchase Invoice {0} first,請取消採購發票{0}第一
Not permitted. Please disable the Service Unit Type,不允許。請禁用服務單位類型
"Email Address must be unique, already exists for {0}",電子郵件地址必須是唯一的，已經存在了{0}
AMC Expiry Date,AMC到期時間
Receipt,收據
Sales Register,銷售登記
Quotation Lost Reason,報價遺失原因
Transaction reference no {0} dated {1},交易參考編號{0}日{1}
There is nothing to edit.,無內容可供編輯
Form View,表單視圖
Summary for this month and pending activities,本月和待活動總結
Please set Unrealized Exchange Gain/Loss Account in Company {0},請在公司{0}中設置未實現的匯兌收益/損失科目
"Add users to your organization, other than yourself.",將用戶添加到您的組織，而不是您自己。
Customer Group Name,客戶群組名稱
No Customers yet!,還沒有客戶！
Healthcare Service Unit,醫療服務單位
Cash Flow Statement,現金流量表
No material request created,沒有創建重要請求
Please remove this Invoice {0} from C-Form {1},請刪除此發票{0}從C-表格{1}
Against Voucher Type,對憑證類型
Phone (R),電話（R）
Time slots added,添加時隙
Attributes,屬性
Enable Template,啟用模板
Please enter Write Off Account,請輸入核銷科目
Last Order Date,最後訂購日期
Is Payable,應付
B Negative,B負面
Maintenance Status has to be Cancelled or Completed to Submit,維護狀態必須取消或完成提交
US,我們
Add Weekly Holidays,添加每週假期
Hotel Room,旅館房間
Account {0} does not belongs to company {1},科目{0}不屬於公司{1}
Serial Numbers in row {0} does not match with Delivery Note,行{0}中的序列號與交貨單不匹配
"Then Pricing Rules are filtered out based on Customer, Customer Group, Territory, Supplier, Supplier Group, Campaign, Sales Partner etc.",然後根據客戶，客戶組，地區，供應商，供應商組織，市場活動，銷售合作夥伴等篩選定價規則。
Guardian Details,衛詳細
Start Day,開始日
Chassis No,底盤無
Initiated,啟動
Planned Start Date,計劃開始日期
Please select a BOM,請選擇一個物料清單
Availed ITC Integrated Tax,有效的ITC綜合稅收
Blanket Order Rate,一攬子訂單費率
Certification,證明
Clauses and Conditions,條款和條件
Creation Document Type,創建文件類型
View Timesheet,查看時間表
Make Journal Entry,使日記帳分錄
Project-wise data is not available for Quotation,項目明智的數據不適用於報價
End on,結束
Expected End Date,預計結束日期
Budget Amount,預算額
Donor Name,捐助者名稱
Inter Company Journal Entry Reference,Inter公司日記帳分錄參考
Commercial,商業
Alcohol Current Use,酒精當前使用
Student Admission Program,學生入學計劃
Account Paid To,科目付至
Grace Period,寬限期
Alternative Item Name,替代項目名稱
Parent Item {0} must not be a Stock Item,父項{0}不能是庫存產品
All Products or Services.,所有的產品或服務。
Open Quotations,打開報價單
Supplier Address,供應商地址
{0} Budget for Account {1} against {2} {3} is {4}. It will exceed by {5},{0}預算科目{1}對{2} {3}是{4}。這將超過{5}
Out Qty,輸出數量
Series is mandatory,系列是強制性的
Financial Services,金融服務
Student ID,學生卡
For Quantity must be greater than zero,對於數量必須大於零
Types of activities for Time Logs,活動類型的時間記錄
Sales,銷售
Basic Amount,基本金額
Marketplace Error,市場錯誤
Warehouse required for stock Item {0},倉庫需要現貨產品{0}
Make Repayment Entry,進行還款分錄
All Departments,所有部門
Alcohol Past Use,酒精過去使用
Cr,鉻
Problematic/Stuck,問題/卡住
Billing State,計費狀態
Transfer,轉讓
Work Order {0} must be cancelled before cancelling this Sales Order,在取消此銷售訂單之前，必須先取消工單{0}
Fetch exploded BOM (including sub-assemblies),取得爆炸BOM（包括子組件）
Applicable To (Employee),適用於（員工）
Due Date is mandatory,截止日期是強制性的
Increment for Attribute {0} cannot be 0,增量屬性{0}不能為0,
Benefit Type and Amount,福利類型和金額
Rooms Booked,客房預訂
Ends On date cannot be before Next Contact Date.,結束日期不能在下一次聯繫日期之前。
Pay To / Recd From,支付/ 接收
Setup Series,設置系列
To Invoice Date,要發票日期
Contact HTML,聯繫HTML,
Support Portal,支持門戶
Registration fee can not be Zero,註冊費不能為零
Treatment Period,治療期
Result already Submitted,結果已提交
Reserved Warehouse is mandatory for Item {0} in Raw Materials supplied,保留倉庫對於提供的原材料中的項目{0}是強制性的
Inactive Customers,不活躍的客戶
Maximum Age,最大年齡
Please wait 3 days before resending the reminder.,請重新發送提醒之前請等待3天。
Purchase Receipts,採購入庫
How Pricing Rule is applied?,定價規則被如何應用？
Delivery Note No,送貨單號
Message to show,信息顯示
Product Bundle,產品包
Unable to find score starting at {0}. You need to have standing scores covering 0 to 100,無法從{0}開始獲得分數。你需要有0到100的常規分數
Row {0}: Invalid reference {1},行{0}：無效參考{1}
Purchase Taxes and Charges Template,採購稅負和費用模板
Current Invoice Start Date,當前發票開始日期
{0} {1}: Either debit or credit amount is required for {2},{0} {1}：無論是借方或貸方金額需要{2}
Remarks,備註
Hotel Room Amenity,酒店客房舒適
Action if Annual Budget Exceeded on MR,年度預算超出MR的行動
Account Paid From,帳戶支付從
Raw Material Item Code,原料產品編號
Parent Task,父任務
Write Off Based On,核銷的基礎上
Make Lead,使鉛
Show Barcode Field,顯示條形碼域
Send Supplier Emails,發送電子郵件供應商
Auto Created,自動創建
Item Default,項目默認值
Leave Reason,離開原因
Invoice {0} no longer exists,發票{0}不再存在
Guardian Interest,衛利息
Setup default values for POS Invoices,設置POS發票的默認值
Time to send,發送時間
Employee Detail,員工詳細信息
Set warehouse for Procedure {0} ,為過程{0}設置倉庫
Guardian1 Email ID,Guardian1電子郵件ID,
Guardian1 Email ID,Guardian1電子郵件ID,
Test Code,測試代碼
Settings for website homepage,對網站的主頁設置
{0} is on hold till {1},{0}一直保持到{1}
RFQs are not allowed for {0} due to a scorecard standing of {1},由於{1}的記分卡，{0}不允許使用RFQ,
Link Options,鏈接選項
Total Amount {0},總金額{0}
Invalid attribute {0} {1},無效的屬性{0} {1}
Mention if non-standard payable account,如果非標準應付帳款提到
Please select the assessment group other than 'All Assessment Groups',請選擇“所有評估組”以外的評估組
Row {0}: Cost center is required for an item {1},行{0}：項目{1}需要費用中心
Optional,可選的
{0} variants created.,創建了{0}個變體。
Optional. This setting will be used to filter in various transactions.,可選。此設置將被應用於過濾各種交易進行。
Negative Valuation Rate is not allowed,負面評價率是不允許的
Weekly Off,每週關閉
Reload Linked Analysis,重新加載鏈接分析
"For e.g. 2012, 2012-13",對於例如2012、2012-13,
Provisional Profit / Loss (Credit),臨時溢利/（虧損）（信用）
Return Against Sales Invoice,射向銷售發票
Item 5,項目5,
Creation Time,創作時間
Total Revenue,總收入
Other Risk Factors,其他風險因素
Product Bundle Help,產品包幫助
No record found,沒有資料
Cost of Scrapped Asset,報廢資產成本
{0} {1}: Cost Center is mandatory for Item {2},{0} {1}：成本中心是強制性的項目{2}
Get Items from Product Bundle,從產品包取得項目
Straight Line,直線
Project User,項目用戶
Is Advance,為進
Employee Lifecycle,員工生命週期
Please enter 'Is Subcontracted' as Yes or No,請輸入'轉包' YES或NO,
Default Purchase Unit of Measure,默認採購單位
Last Communication Date,最後通訊日期
Last Communication Date,最後通訊日期
Clinical Procedure Item,臨床流程項目
Contact No.,聯絡電話
Payment Entries,付款項
Access token or Shopify URL missing,訪問令牌或Shopify網址丟失
Latitude,緯度
Scrap Warehouse,廢料倉庫
"Warehouse required at Row No {0}, please set default warehouse for the item {1} for the company {2}",在第{0}行，需要倉庫，請為公司{2}的物料{1}設置默認倉庫
Check if material transfer entry is not required,檢查是否不需要材料轉移條目
Check if material transfer entry is not required,檢查是否不需要材料轉移條目
Get Students From,讓學生從
Publish Items on Website,公佈於網頁上的項目
Group your students in batches,一群學生在分批
Authorization Rule,授權規則
Terms and Conditions Details,條款及細則詳情
Specifications,產品規格
Sales Taxes and Charges Template,營業稅金及費用套版
Total (Credit),總（信用）
Apparel & Accessories,服裝及配飾
Could not solve weighted score function. Make sure the formula is valid.,無法解決加權分數函數。確保公式有效。
Purchase Order Items not received on time,未按時收到採購訂單項目
Number of Order,訂購數量
HTML / Banner that will show on the top of product list.,HTML／橫幅，將顯示在產品列表的頂部。
Specify conditions to calculate shipping amount,指定條件來計算運費金額
Institute's Bus,學院的巴士
Role Allowed to Set Frozen Accounts & Edit Frozen Entries,允許設定凍結科目和編輯凍結分錄的角色
Path,路徑
Cannot convert Cost Center to ledger as it has child nodes,不能成本中心轉換為總賬，因為它有子節點
Total Planned Qty,總計劃數量
Opening Value,開度值
Serial #,序列號
Lab Test Template,實驗室測試模板
Sales Account,銷售科目
Total Weight,總重量
Commission on Sales,銷售佣金
"Row #{0}: Asset {1} cannot be submitted, it is already {2}",行＃{0}：資產{1}無法提交，這已經是{2}
Billing Country,結算國家
Expected Delivery Date,預計交貨日期
Restaurant Order Entry,餐廳訂單錄入
Debit and Credit not equal for {0} #{1}. Difference is {2}.,借貸{0}＃不等於{1}。區別是{2}。
Invoice Separately as Consumables,作為耗材單獨發票
Control Action,控制行動
Assign To Name,分配到名稱
Entertainment Expenses,娛樂費用
Make Material Request,製作材料要求
Open Item {0},打開項目{0}
Written Down Value,寫下價值
Sales Invoice {0} must be cancelled before cancelling this Sales Order,銷售發票{0}必須早於此銷售訂單之前取消
Age,年齡
Billing Amount,開票金額
Select Maximum Of 1,選擇最多1個
Invalid quantity specified for item {0}. Quantity should be greater than 0.,為項目指定了無效的數量{0} 。量應大於0 。
Default Employee Advance Account,默認員工高級帳戶
Search Item (Ctrl + i),搜索項目（Ctrl + i）
Account with existing transaction can not be deleted,科目與現有的交易不能被刪除
Last Carbon Check,最後檢查炭
Legal Expenses,法律費用
Please select quantity on row ,請選擇行數量
Make Opening Sales and Purchase Invoices,打開銷售和購買發票
Posting Time,登錄時間
% Amount Billed,（％）金額已開立帳單
Telephone Expenses,電話費
Logo,標誌
No Item with Serial No {0},沒有序號{0}的品項
Open Notifications,打開通知
Difference Amount (Company Currency),差異金額（公司幣種）
Direct Expenses,直接費用
New Customer Revenue,新客戶收入
Travel Expenses,差旅費
Breakdown,展開
Account: {0} with currency: {1} can not be selected,帳號：{0}幣種：{1}不能選擇
Bank Data,銀行數據
Sample Quantity,樣品數量
"Update BOM cost automatically via Scheduler, based on latest valuation rate / price list rate / last purchase rate of raw materials.",根據最新的估值/價格清單率/最近的原材料採購率，通過計劃程序自動更新BOM成本。
Account {0}: Parent account {1} does not belong to company: {2},科目{0}：上層科目{1}不屬於公司：{2}
Successfully deleted all transactions related to this company!,成功刪除與該公司相關的所有交易！
As on Date,隨著對日
Enrollment Date,報名日期
Out Patient SMS Alerts,輸出病人短信
New Academic Year,新學年
Return / Credit Note,返回/信用票據
Auto insert Price List rate if missing,自動插入價目表率，如果丟失
Total Paid Amount,總支付金額
Transferred Qty,轉讓數量
Navigating,導航
Planning,規劃
Signee,簽署人
Issued,發行
Student Activity,學生活動
Supplier Id,供應商編號
Payment Gateway Details,支付網關細節
Quantity should be greater than 0,量應大於0,
Cash Entry,現金分錄
Child nodes can be only created under 'Group' type nodes,子節點可以在&#39;集團&#39;類型的節點上創建
Academic Year Name,學年名稱
{0} not allowed to transact with {1}. Please change the Company.,不允許{0}與{1}進行交易。請更改公司。
Contact Desc,聯絡倒序
Send regular summary reports via Email.,使用電子郵件發送定期匯總報告。
Please set default account in Expense Claim Type {0},請報銷類型設置默認科目{0}
Student Name,學生姓名
Item Manager,項目經理
Payroll Payable,應付職工薪酬
Collection Datetime,收集日期時間
Total Operating Cost,總營運成本
Note: Item {0} entered multiple times,注：項目{0}多次輸入
All Contacts.,所有聯絡人。
Closed Documents,關閉的文件
Manage Appointment Invoice submit and cancel automatically for Patient Encounter,管理約會發票提交並自動取消患者遭遇
Referring Practitioner,轉介醫生
Company Abbreviation,公司縮寫
User {0} does not exist,用戶{0}不存在
Day(s) after invoice date,發票日期後的天數
Date of Commencement should be greater than Date of Incorporation,開始日期應大於公司註冊日期
Signed On,簽名
Party Type,黨的類型
Payment Schedule,付款時間表
Abbreviation,縮寫
Payment Entry already exists,付款項目已存在
Trial Period End Date,試用期結束日期
Not authroized since {0} exceeds limits,不允許因為{0}超出範圍
Asset Status,資產狀態
Over Dimensional Cargo (ODC),超尺寸貨物（ODC）
Hotel Manager,酒店經理
Set Tax Rule for shopping cart,購物車稅收規則設定
Taxes and Charges Added,稅費上架
Depreciation Row {0}: Next Depreciation Date cannot be before Available-for-use Date,折舊行{0}：下一個折舊日期不能在可供使用的日期之前
Sales Funnel,銷售漏斗
Abbreviation is mandatory,縮寫是強制性的
Task Progress,任務進度
Cart,車
Qty to Transfer,轉移數量
Quotes to Leads or Customers.,行情到引線或客戶。
Role Allowed to edit frozen stock,此角色可以編輯凍結的庫存
Territory Target Variance Item Group-Wise,地域內跨項目群組間的目標差異
All Customer Groups,所有客戶群組
Accumulated Monthly,每月累計
{0} is mandatory. Maybe Currency Exchange record is not created for {1} to {2}.,{0}是強制性的。也許外幣兌換記錄為{1}到{2}尚未建立。
Tax Template is mandatory.,稅務模板是強制性的。
Account {0}: Parent account {1} does not exist,科目{0}：上層科目{1}不存在
Period Start Date,期間開始日期
Price List Rate (Company Currency),價格列表費率（公司貨幣）
Products Settings,產品設置
Item Price Stock,項目價格庫存
To make Customer based incentive schemes.,制定基於客戶的激勵計劃。
Test Created,測試創建
Custom Signature in Print,自定義簽名打印
Temporary,臨時
Customer LPO No.,客戶LPO號
Market Place Account Group,市場科目組
Make Payment Entries,付款條目
Courses,培訓班
Percentage Allocation,百分比分配
Secretary,秘書
"If disable, 'In Words' field will not be visible in any transaction",如果禁用“，在詞”字段不會在任何交易可見
This action will stop future billing. Are you sure you want to cancel this subscription?,此操作將停止未來的結算。您確定要取消此訂閱嗎？
Distinct unit of an Item,一個項目的不同的單元
Criteria Name,標準名稱
Please set Company,請設公司
Procedure Created,程序已創建
Buying,採購
Diseases & Fertilizers,疾病與肥料
Employee Records to be created by,員工紀錄的創造者
AB Negative,AB陰性
Apply Discount On,申請折扣
Membership Type,會員類型
Reqd By Date,REQD按日期
Creditors,債權人
Assessment Name,評估名稱
Show PDC in Print,在打印中顯示PDC,
Row # {0}: Serial No is mandatory,行＃{0}：序列號是必需的
Item Wise Tax Detail,項目智者稅制明細
Institute Abbreviation,研究所縮寫
Item-wise Price List Rate,全部項目的價格表
Supplier Quotation,供應商報價
In Words will be visible once you save the Quotation.,報價一被儲存，就會顯示出來。
Quantity ({0}) cannot be a fraction in row {1},數量（{0}）不能是行{1}中的分數
Quantity ({0}) cannot be a fraction in row {1},數量（{0}）不能是行{1}中的分數
Unsigned,無符號
Each Transaction,每筆交易
Barcode {0} already used in Item {1},條碼{0}已經用在項目{1}
Rules for adding shipping costs.,增加運輸成本的規則。
Varaiance ,Varaiance,
Opening Stock,打開庫存
Customer is required,客戶是必需的
Result Date,結果日期
PDC/LC Date,PDC / LC日期
To Receive,接受
Asset Owner,資產所有者
Reason For Putting On Hold,擱置的理由
Personal Email,個人電子郵件
Total Variance,總方差
"If enabled, the system will post accounting entries for inventory automatically.",如果啟用，系統將自動為發布庫存會計分錄。
"in Minutes,
Updated via 'Time Log'","在分
經由“時間日誌”更新"
From Lead,從鉛
Synch Orders,同步訂單
Orders released for production.,發布生產訂單。
Select Fiscal Year...,選擇會計年度...
POS Profile required to make POS Entry,所需的POS資料，使POS進入
"Loyalty Points will be calculated from the spent done (via the Sales Invoice), based on collection factor mentioned.",忠誠度積分將根據所花費的完成量（通過銷售發票）計算得出。
HRA Settings,HRA設置
Standard Selling,標準銷售
Atleast one warehouse is mandatory,至少要有一間倉庫
"Configure Item Fields like UOM, Item Group, Description and No of Hours.",配置項目字段，如UOM，項目組，描述和小時數。
Certification Status,認證狀態
Subscriber Name,訂戶名稱
Mapped Data Type,映射數據類型
Replace,更換
No products found.,找不到產品。
{0} against Sales Invoice {1},{0}針對銷售發票{1}
Laboratory User,實驗室用戶
Project Name,專案名稱
Mention if non-standard receivable account,提到如果不規範應收帳款
If Income or Expense,如果收入或支出
Matching Invoices,匹配發票
Required Items,所需物品
Stock Value Difference,庫存價值差異
Item Row {0}: {1} {2} does not exist in above '{1}' table,項目行{0}：{1} {2}在上面的“{1}”表格中不存在
Human Resource,人力資源
Payment Reconciliation Payment,付款方式付款對賬
Treatment Task,治療任務
Bank Account Details,銀行科目明細
Blanket Order,總訂單
Tax Assets,所得稅資產
Production Order has been {0},生產訂單已經{0}
House rent paid days overlap with {0},房租支付天數與{0}重疊
BOM No,BOM No.
Journal Entry {0} does not have account {1} or already matched against other voucher,日記條目{0}沒有帳號{1}或已經匹配其他憑證
Moving Average,移動平均線
The BOM which will be replaced,這將被替換的物料清單
Electronic Equipments,電子設備
Maintenance Required,需要維護
Leaves must be allocated in multiples of 0.5,休假必須安排成0.5倍的
Operation Cost,運營成本
Identifying Decision Makers,確定決策者
Outstanding Amt,優秀的金額
Set targets Item Group-wise for this Sales Person.,為此銷售人員設定跨項目群組間的目標。
Freeze Stocks Older Than [Days],凍結早於[Days]的庫存
Payment Ordered,付款訂購
Maintenance Team Name,維護組名稱
"If two or more Pricing Rules are found based on the above conditions, Priority is applied. Priority is a number between 0 to 20 while default value is zero (blank). Higher number means it will take precedence if there are multiple Pricing Rules with same conditions.",如果兩個或更多的定價規則是基於上述條件發現，優先級被應用。優先權是一個介於0到20，而預設值是零（空）。數字越大，意味著其將優先考慮是否有與相同條件下多個定價規則。
Customer is mandatory if 'Opportunity From' is selected as Customer,如果選擇“機會來源”作為客戶，則客戶是強制性的
Fiscal Year: {0} does not exists,會計年度：{0}不存在
To Currency,到貨幣
Make BOM,製作BOM,
Selling rate for item {0} is lower than its {1}. Selling rate should be atleast {2},項目{0}的銷售價格低於其{1}。售價應至少為{2}
Selling rate for item {0} is lower than its {1}. Selling rate should be atleast {2},項目{0}的銷售價格低於其{1}。售價應至少為{2}
Taxes,稅
capital goods,資本貨物
Weight Per Unit,每單位重量
Paid and Not Delivered,支付和未送達
Default Cost Center,預設的成本中心
Stock Transactions,庫存交易明細
Budget Accounts,預算科目
Internal Work History,內部工作經歷
Accumulated Depreciation Amount,累計折舊額
Private Equity,私募股權投資
Supplier Scorecard Variable,供應商記分卡變數
Please create purchase receipt or purchase invoice for the item {0},請為項目{0}創建購買收據或購買發票
Due Advance Amount,到期金額
Customer Feedback,客戶反饋
Expense,費用
Score cannot be greater than Maximum Score,分數不能超過最高得分更大
Source Type,來源類型
Customers and Suppliers,客戶和供應商
From Range,從範圍
Set rate of sub-assembly item based on BOM,基於BOM設置子組合項目的速率
Invoiced,已開發票
Syntax error in formula or condition: {0},式或條件語法錯誤：{0}
Daily Work Summary Settings Company,每日工作總結公司的設置
Item {0} ignored since it is not a stock item,項目{0}被忽略，因為它不是一個庫存項目
"To not apply Pricing Rule in a particular transaction, all applicable Pricing Rules should be disabled.",要在一個特定的交易不適用於定價規則，所有適用的定價規則應該被禁用。
Day(s) after the end of the invoice month,發票月份結束後的一天
Parent Assessment Group,家長評估小組
Sales Order Trends,銷售訂單趨勢
The 'From Package No.' field must neither be empty nor it's value less than 1.,“From Package No.”字段不能為空，也不能小於1。
Held On,舉行
Production Item,生產項目
Employee Information,僱員資料
Healthcare Practitioner not available on {0},醫療從業者在{0}上不可用
Additional Cost,額外費用
"Can not filter based on Voucher No, if grouped by Voucher",是凍結的帳戶。要禁止該帳戶創建/編輯事務，你需要有指定的身份
Make Supplier Quotation,讓供應商報價
Incoming,來
Default tax templates for sales and purchase are created.,銷售和採購的默認稅收模板被創建。
Assessment Result record {0} already exists.,評估結果記錄{0}已經存在。
"Example: ABCD.#####. If series is set and Batch No is not mentioned in transactions, then automatic batch number will be created based on this series. If you always want to explicitly mention Batch No for this item, leave this blank. Note: this setting will take priority over the Naming Series Prefix in Stock Settings.",例如：ABCD。#####。如果系列已設置且交易中未提及批號，則將根據此系列創建自動批號。如果您始終想要明確提及此料品的批號，請將此留為空白。注意：此設置將優先於庫存設置中的命名系列前綴。
Materials Required (Exploded),所需材料（分解）
Party User,派對用戶
Please set Company filter blank if Group By is 'Company',如果Group By是“Company”，請設置公司過濾器空白
Posting Date cannot be future date,發布日期不能是未來的日期
Row # {0}: Serial No {1} does not match with {2} {3},行＃{0}：序列號{1}不相匹配{2} {3}
Target Warehouse Address,目標倉庫地址
End Day,結束的一天
Delivery Note Trends,送貨單趨勢
This Week's Summary,本週的總結
In Stock Qty,庫存數量
Calculate Estimated Arrival Times,計算預計到達時間
Account: {0} can only be updated via Stock Transactions,帳號：{0}只能通過庫存的交易進行更新
Get Courses,獲取課程
Webhooks,網絡掛接
Party,黨
Variant Field,變種場
Target Location,目標位置
Delivery Date,交貨日期
Opportunity Date,機會日期
Health Insurance Provider,健康保險提供者
Show Availability Status,顯示可用性狀態
Return Against Purchase Receipt,採購入庫的退貨
Person Responsible,負責人
Request for Quotation Item,詢價項目
To Bill,發票待輸入
% Ordered,% 已訂購
"For Course based Student Group, the Course will be validated for every Student from the enrolled Courses in Program Enrollment.",對於基於課程的學生小組，課程將從入學課程中的每個學生確認。
Avg. Buying Rate,平均。買入價
From No,來自No,
Actual Time in Hours (via Timesheet),實際時間（小時）
History In Company,公司歷史
Customer Primary Address,客戶主要地址
Reference No.,參考編號。
Description/Strength,說明/力量
Create New Payment/Journal Entry,創建新的付款/日記賬分錄
Certification Application,認證申請
Is Company,是公司
Stock Ledger Entry,庫存總帳條目
{0} on Half day Leave on {1},半天{0}離開{1}
Same item has been entered multiple times,同一項目已進入多次
Leave Block List,休假區塊清單
Tax ID,稅號
Item {0} is not setup for Serial Nos. Column must be blank,項目{0}不是設定為序列號，此列必須為空白
Accounts Settings,會計設定
Customer Territory,客戶地區
Sales Orders to Deliver,要交付的銷售訂單
"Number of new Account, it will be included in the account name as a prefix",新帳號的數量，將作為前綴包含在帳號名稱中
Team Member,隊員
No Result to submit,沒有結果提交
Sales Partner and Commission,銷售合作夥伴及佣金
"Total {0} for all items is zero, may be you should change 'Distribute Charges Based On'",共有{0}所有項目為零，可能是你應該“基於分佈式費用”改變
To Discuss,為了討論
{0} units of {1} needed in {2} to complete this transaction.,{0}單位{1}在{2}完成此交易所需。
Forum URL,論壇URL,
Temporary Accounts,臨時科目
Source Location is required for the asset {0},源位置對資產{0}是必需的
BOM Explosion Item,BOM展開項目
Contact List,聯繫人列表
Auditor,核數師
Frequency To Collect Progress,頻率收集進展
{0} items produced,生產{0}項目
Learn More,學到更多
Distance from top edge,從頂邊的距離
Quantity of Items,項目數量
Price List {0} is disabled or does not exist,價格表{0}禁用或不存在
Return,退貨
Disable,關閉
Pending Review,待審核
"Edit in full page for more options like assets, serial nos, batches etc.",在整頁上編輯更多選項，如資產，序列號，批次等。
Maximum Continuous Days Applicable,最大持續天數適用
{0} - {1} is not enrolled in the Batch {2},{0}  -  {1} 未在批次處理中註冊 {2}
"Asset {0} cannot be scrapped, as it is already {1}",資產{0}不能被廢棄，因為它已經是{1}
Cheques Required,需要檢查
Total Expense Claim (via Expense Claim),總費用報銷（通過費用報銷）
Mark Absent,馬克缺席
IGST Amount,IGST金額
Failed to setup company,未能成立公司
Asset Repair,資產修復
Row {0}: Currency of the BOM #{1} should be equal to the selected currency {2},行{0}：BOM＃的貨幣{1}應等於所選貨幣{2}
Exchange Rate,匯率
Additional information regarding the patient,有關患者的其他信息
Sales Order {0} is not submitted,銷售訂單{0}未提交
Tag Line,標語
Fee Component,收費組件
Fleet Management,車隊的管理
Density (if liquid),密度（如果液體）
Total Weightage of all Assessment Criteria must be 100%,所有評估標準的權重總數要達到100％
Last Purchase Rate,最後預訂價
Asset,財富
Task ID,任務ID,
Stock cannot exist for Item {0} since has variants,庫存可以為項目不存在{0}，因為有變種
Mobile,移動
Sales Person-wise Transaction Summary,銷售人員相關的交易匯總
Warehouse {0} does not exist,倉庫{0}不存在
Monthly Distribution Percentages,每月分佈百分比
The selected item cannot have Batch,所選項目不能批
% of materials delivered against this Delivery Note,針對這張送貨單物料已交貨的百分比(%)
Customer Details,客戶詳細資訊
Check if Asset requires Preventive Maintenance or Calibration,檢查資產是否需要預防性維護或校準
Company Abbreviation cannot have more than 5 characters,公司縮寫不能超過5個字符
Reports to,隸屬於
Paid Amount,支付的金額
Explore Sales Cycle,探索銷售週期
Supervisor,監
Retention Stock Entry,保留庫存入場
Available Stock for Packing Items,可用庫存包裝項目
Item Variant,項目變
Work Order Stock Report,工單庫存報表
Auto Repeat Detail,自動重複細節
Assessment Result Tool,評價結果工具
As Supervisor,作為主管
BOM Scrap Item,BOM項目廢料
Submitted orders can not be deleted,提交的訂單不能被刪除
"Account balance already in Debit, you are not allowed to set 'Balance Must Be' as 'Credit'",科目餘額已歸為借方科目，不允許設為貸方
Quality Management,品質管理
Item {0} has been disabled,項{0}已被禁用
Total Billable Amount (via Timesheet),總計費用金額（通過時間表）
Previous Business Day,前一個營業日
Health Insurance No,健康保險No,
Please enter quantity for Item {0},請輸入項目{0}的量
Total Taxable Amount,應納稅總額
Employee External Work History,員工對外工作歷史
Job card {0} created,已創建作業卡{0}
Purchase,採購
Balance Qty,餘額數量
Goals cannot be empty,目標不能為空
Enrolling students,招收學生
Parent Item Group,父項目群組
Appointment Type,預約類型
{0} for {1},{0}for {1}
Valid number of days,有效天數
Restart Subscription,重新啟動訂閱
Linked Plant Analysis,鏈接的工廠分析
Transporter ID,運輸商ID,
Value Proposition,價值主張
Rate at which supplier's currency is converted to company's base currency,供應商貨幣被換算成公司基礎貨幣的匯率
Service End Date,服務結束日期
Row #{0}: Timings conflicts with row {1},行＃{0}：與排時序衝突{1}
Allow Zero Valuation Rate,允許零估值
Allow Zero Valuation Rate,允許零估值
Setup Gateway accounts.,設置閘道科目。
Employment Type,就業類型
Fixed Assets,固定資產
Set Exchange Gain / Loss,設置兌換收益/損失
GST Purchase Register,消費稅購買登記冊
Cash Flow,現金周轉
Combined invoice portion must equal 100%,合併發票部分必須等於100％
Default Expense Account,預設費用科目
CGST Account,CGST科目
Student Email ID,學生的電子郵件ID,
POS Closing Voucher Invoices,POS關閉憑證發票
Sales Tax Template,銷售稅模板
Update Cost Center Number,更新成本中心編號
Select items to save the invoice,選取要保存發票
Encashment Date,兌現日期
Special Test Template,特殊測試模板
Stock Adjustment,庫存調整
Default Activity Cost exists for Activity Type - {0},默認情況下存在作業成本的活動類型 -  {0}
Planned Operating Cost,計劃運營成本
Term Start Date,期限起始日期
List of all share transactions,所有股份交易清單
Is Transporter,是運輸車
Import Sales Invoice from Shopify if Payment is marked,如果付款已標記，則從Shopify導入銷售發票
Opp Count,Opp Count,
Opp Count,Opp Count,
Both Trial Period Start Date and Trial Period End Date must be set,必須設置試用期開始日期和試用期結束日期
Total Payment Amount in Payment Schedule must be equal to Grand / Rounded Total,支付計劃中的總付款金額必須等於大/圓
Plan,計劃
Bank Statement balance as per General Ledger,銀行對賬單餘額按總帳
Customer / Item Name,客戶／品項名稱
"Aggregate group of **Items** into another **Item**. This is useful if you are bundling a certain **Items** into a package and you maintain stock of the packed **Items** and not the aggregate **Item**. 

The package **Item** will have ""Is Stock Item"" as ""No"" and ""Is Sales Item"" as ""Yes"".

For Example: If you are selling Laptops and Backpacks separately and have a special price if the customer buys both, then the Laptop + Backpack will be a new Product Bundle Item.

Note: BOM = Bill of Materials",聚合組** **項目到另一個項目** **的。如果你是捆綁了一定**項目你保持庫存的包裝**項目的**，而不是聚集**項這是一個有用的**到一個包和**。包** **項目將有“是庫存項目”為“否”和“是銷售項目”為“是”。例如：如果你是銷售筆記本電腦和背包分開，並有一個特殊的價格，如果客戶購買兩個，那麼筆記本電腦+背包將是一個新的產品包項目。注：物料BOM =比爾
Serial No is mandatory for Item {0},項目{0}的序列號是強制性的
Attribute,屬性
Please specify from/to range,請從指定/至範圍
Opening {0} Invoice created,打開{0}已創建發票
Under AMC,在AMC,
Item valuation rate is recalculated considering landed cost voucher amount,物品估價率重新計算考慮到岸成本憑證金額
Default settings for selling transactions.,銷售交易的預設設定。
Guardian Of ,守護者
Threshold,閾
Current BOM,當前BOM表
Balance (Dr - Cr),平衡（Dr  -  Cr）
Add Serial No,添加序列號
Available Qty at Source Warehouse,源倉庫可用數量
Warranty,保證
Debit Note Issued,借記發行說明
Filter based on Cost Center is only applicable if Budget Against is selected as Cost Center,基於成本中心的過濾僅適用於選擇Budget Against作為成本中心的情況
"Search by item code, serial number, batch no or barcode",按項目代碼，序列號，批號或條碼進行搜索
Warehouses,倉庫
{0} asset cannot be transferred,{0}資產不得轉讓
Hotel Room Pricing,酒店房間價格
"Can not mark Inpatient Record Discharged, there are Unbilled Invoices {0}",無法標記出院的住院病歷，有未開單的發票{0}
This Item is a Variant of {0} (Template).,此項目是{0}（模板）的變體。
per hour,每小時
Purchasing,購買
Customer LPO,客戶LPO,
"For Batch based Student Group, the Student Batch will be validated for every Student from the Program Enrollment.",對於基於批次的學生組，學生批次將由課程註冊中的每位學生進行驗證。
Warehouse can not be deleted as stock ledger entry exists for this warehouse.,這個倉庫不能被刪除，因為庫存分錄帳尚存在。
Loan,貸款
Report Preference,報告偏好
Volunteer information.,志願者信息。
Project Manager,專案經理
Quoted Item Comparison,項目報價比較
Overlap in scoring between {0} and {1},{0}和{1}之間的得分重疊
Dispatch,調度
Max discount allowed for item: {0} is {1}%,{0}允許的最大折扣：{1}％
Net Asset value as on,淨資產值作為
Produce,生產
Default Taxes and Charges,默認稅費
Receivable,應收帳款
Row #{0}: Not allowed to change Supplier as Purchase Order already exists,行＃{0}：不能更改供應商的採購訂單已經存在
Material Consumption for Manufacture,材料消耗製造
Alternative Item Code,替代項目代碼
Role that is allowed to submit transactions that exceed credit limits set.,此角色是允許提交超過所設定信用額度的交易。
Select Items to Manufacture,選擇項目，以製造
Delivery Stop,交貨停止
"Master data syncing, it might take some time",主數據同步，這可能需要一些時間
Material Issue,發料
Qualification,合格
Item Price,商品價格
Soap & Detergent,肥皂和洗滌劑
Show Items,顯示項目
From Time cannot be greater than To Time.,從時間不能超過結束時間大。
Do you want to notify all the customers by email?,你想通過電子郵件通知所有的客戶？
Billing Interval,計費間隔
Motion Picture & Video,電影和視頻
Ordered,已訂購
Actual start date and actual end date is mandatory,實際開始日期和實際結束日期是強制性的
Row {0}: {1} must be greater than 0,行{0}：{1}必須大於0,
Assessment Criteria Group,評估標準組
Enable Deferred Revenue,啟用延期收入
Opening Accumulated Depreciation must be less than equal to {0},打開累計折舊必須小於等於{0}
Warehouse Name,倉庫名稱
Actual start date must be less than actual end date,實際開始日期必須小於實際結束日期
Please enter Approving Role or Approving User,請輸入核准角色或審批用戶
Write Off Entry,核銷進入
Rate Of Materials Based On,材料成本基於
"If enabled, field Academic Term will be Mandatory in Program Enrollment Tool.",如果啟用，則在學期註冊工具中，字段學術期限將是強制性的。
Support Analtyics,支援分析
Uncheck all,取消所有
Terms and Conditions,條款和條件
Booked Fixed Asset,預訂的固定資產
To Date should be within the Fiscal Year. Assuming To Date = {0},日期應該是在財政年度內。假設終止日期= {0}
"Here you can maintain height, weight, allergies, medical concerns etc",在這裡，你可以保持身高，體重，過敏，醫療問題等
Cannot cancel because submitted Stock Entry {0} exists,不能取消，因為提交庫存輸入{0}存在
Update latest price in all BOMs,更新所有BOM的最新價格
Medical Record,醫療記錄
Vehicle,車輛
In Words,大寫
Enter the name of the bank or lending institution before submittting.,在提交之前輸入銀行或貸款機構的名稱。
Item Groups,項目組
For Production,對於生產
payment_url,payment_url,
Balance In Account Currency,科目貨幣餘額
Please add a Temporary Opening account in Chart of Accounts,請在會計科目表中添加一個臨時開戶科目
Customer Primary Contact,客戶主要聯繫人
View Task,查看任務
Opp/Lead %,Opp / Lead％
Opp/Lead %,Opp / Lead％
Bank Account Info,銀行科目信息
Bank Guarantee Type,銀行擔保類型
Invoice Portion,發票部分
Asset Depreciations and Balances,資產折舊和平衡
Amount {0} {1} transferred from {2} to {3},金額{0} {1}從轉移{2}到{3}
{0} does not have a Healthcare Practitioner Schedule. Add it in Healthcare Practitioner master,{0}沒有醫療從業者時間表。將其添加到Healthcare Practitioner master中
Get Advances Received,取得預先付款
Add/Remove Recipients,添加／刪除收件人
"To set this Fiscal Year as Default, click on 'Set as Default'",要設定這個財政年度為預設值，點擊“設為預設”
Amount of TDS Deducted,扣除TDS的金額
Include Subcontracted Items,包括轉包物料
Shortage Qty,短缺數量
Item variant {0} exists with same attributes,項目變種{0}存在具有相同屬性
Repay from Salary,從工資償還
Requesting payment against {0} {1} for amount {2},請求對付款{0} {1}量{2}
Salary Slip,工資單
Lost Quotation,遺失報價
Student Batches,學生批
Margin Rate or Amount,保證金稅率或稅額
'To Date' is required,“至日期”是必需填寫的
"Generate packing slips for packages to be delivered. Used to notify package number, package contents and its weight.",產生交貨的包裝單。用於通知箱號，內容及重量。
Task weight cannot be negative,任務權重不能為負
Sales Order Item,銷售訂單項目
Convert Item Description to Clean HTML,將項目描述轉換為清理HTML,
Total Interest Amount,利息總額
Warehouses with child nodes cannot be converted to ledger,與子節點倉庫不能轉換為分類賬
Manage cost of operations,管理作業成本
Stale Days,陳舊的日子
"When any of the checked transactions are ""Submitted"", an email pop-up automatically opened to send an email to the associated ""Contact"" in that transaction, with the transaction as an attachment. The user may or may not send the email.",當任何選取的交易都是“已提交”時，郵件會自動自動打開，發送電子郵件到相關的“聯絡人”通知相關交易，並用該交易作為附件。用戶可決定是否發送電子郵件。
Billing Zipcode,計費郵編
Global Settings,全局設置
Assessment Result Detail,評價結果詳細
Employee Education,員工教育
Duplicate item group found in the item group table,在項目組表中找到重複的項目組
It is needed to fetch Item Details.,需要獲取項目細節。
Fertilizer Name,肥料名稱
Account,帳戶
Serial No {0} has already been received,已收到序號{0}
Requested Items To Be Transferred,將要轉倉的需求項目
Action if Accumulated Monthly Budget Exceeded on Actual,累計每月預算超出實際的行動
Presence of a fever (temp &gt; 38.5 °C/101.3 °F or sustained temp &gt; 38 °C/100.4 °F),發燒（溫度&gt; 38.5°C / 101.3°F或持續溫度&gt; 38°C / 100.4°F）
Sales Team Details,銷售團隊詳細
Delete permanently?,永久刪除？
Potential opportunities for selling.,潛在的銷售機會。
Folio no.,Folio no。
Invalid {0},無效的{0}
Email Digest,電子郵件摘要
Billing Address Name,帳單地址名稱
Department Stores,百貨
Item Delivery Date,物品交貨日期
Sales Update Frequency,銷售更新頻率
Material Requested,要求的材料
PIN,銷
Reserved Qty for sub contract,分包合同的保留數量
Patinet Service Unit,Patinet服務單位
Base Change Amount (Company Currency),基地漲跌額（公司幣種）
No accounting entries for the following warehouses,沒有以下的倉庫會計分錄
Save the document first.,首先保存文檔。
Only {0} in stock for item {1},物品{1}的庫存僅為{0}
Chargeable,收費
Change Abbreviation,更改縮寫
Fulfilment Details,履行細節
Activities,活動
No of Months,沒有幾個月
Max Discount (%),最大折讓（％）
Credit Days cannot be a negative number,信用日不能是負數
Service Stop Date,服務停止日期
Last Order Amount,最後訂單金額
e.g Adjustments for:,例如調整：
" {0} Retain Sample is based on batch, please check Has Batch No to retain sample of item","{0} 留樣品是基於批次, 請檢查是否有批次不保留專案的樣品"
Yet to appear,尚未出現
Job Card Item,工作卡項目
Allow Cost Center In Entry of Balance Sheet Account,允許成本中心輸入資產負債表科目
Merge with Existing Account,與現有科目合併
All items have already been transferred for this Work Order.,所有項目已經為此工作單轉移。
Manufacturing User,製造業用戶
Raw Materials Supplied,提供供應商原物料
Payment Plan,付款計劃
Enable purchase of items via the website,通過網站啟用購買項目
Currency of the price list {0} must be {1} or {2},價目表{0}的貨幣必須是{1}或{2}
Subscription Management,訂閱管理
Appraisal Template,評估模板
To Pin Code,要密碼
Ternary Plot,三元劇情
Check this to enable a scheduled Daily synchronization routine via scheduler,選中此選項可通過調度程序啟用計劃的每日同步例程
Item Classification,項目分類
License Number,許可證號
Business Development Manager,業務發展經理
Maintenance Visit Purpose,維護訪問目的
Invoice Patient Registration,發票患者登記
Period,期間
General Ledger,總帳
To Fiscal Year,到財政年度
View Leads,查看訊息
Attribute Value,屬性值
Expected Amount,預期金額
Create Multiple,創建多個
Itemwise Recommended Reorder Level,Itemwise推薦級別重新排序
Employee {0} of grade {1} have no default leave policy,{1}級員工{0}沒有默認離開政策
Please select {0} first,請先選擇{0}
Added {0} users,添加了{0}個用戶
"In the case of multi-tier program, Customers will be auto assigned to the concerned tier as per their spent",在多層程序的情況下，客戶將根據其花費自動分配到相關層
Physician,醫師
Batch {0} of Item {1} has expired.,一批項目的{0} {1}已過期。
"Item Price appears multiple times based on Price List, Supplier/Customer, Currency, Item, UOM, Qty and Dates.",物料價格根據價格表，供應商/客戶，貨幣，物料，UOM，數量和日期多次出現。
{0} ({1}) cannot be greater than planned quantity ({2}) in Work Order {3},{0}（{1}）不能大於計畫數量 {3} 在工作訂單中（{2}）
Name of Applicant,申請人名稱
Time Sheet for manufacturing.,時間表製造。
Subtotal,小計
Cannot change Variant properties after stock transaction. You will have to make a new Item to do this.,庫存交易後不能更改Variant屬性。你將不得不做一個新的項目來做到這一點。
GoCardless SEPA Mandate,GoCardless SEPA授權
Charges,收費
Get Items For Work Order,獲取工作訂單的物品
Warehouse not found in the system,倉庫系統中未找到
Quality Inspection Reading,質量檢驗閱讀
`Freeze Stocks Older Than` should be smaller than %d days.,`凍結庫存早於`應該是少於％d天。
Purchase Tax Template,購置稅模板
Set a sales goal you'd like to achieve for your company.,為您的公司設定您想要實現的銷售目標。
Healthcare Services,醫療服務
Project wise Stock Tracking,項目明智的庫存跟踪
Regional,區域性
Laboratory,實驗室
UOM Category,UOM類別
Actual Qty (at source/target),實際的數量（於 來源/目標）
Ref Code,參考代碼
Customer Group is Required in POS Profile,POS Profile中需要客戶組
Match non-linked Invoices and Payments.,核對非關聯的發票和付款。
POS Settings,POS設置
Place Order,下單
New Purchase Orders,新的採購訂單
Root cannot have a parent cost center,root不能有一個父成本中心
Select Brand...,選擇品牌...
Non Profit (beta),非營利（測試版）
Accumulated Depreciation as on,作為累計折舊
C-Form Applicable,C-表格適用
Operation Time must be greater than 0 for Operation {0},運行時間必須大於0的操作{0}
Post Route String,郵政路線字符串
Warehouse is mandatory,倉庫是強制性的
Failed to create website,無法創建網站
Mg/K,鎂/ K,
UOM Conversion Detail,計量單位換算詳細
Retention Stock Entry already created or Sample Quantity not provided,已創建保留庫存條目或未提供“樣本數量”
Program Abbreviation,計劃縮寫
Production Order cannot be raised against a Item Template,生產訂單不能對一個項目提出的模板
Charges are updated in Purchase Receipt against each item,費用對在採購入庫單內的每個項目更新
Resolved By,議決
Schedule Discharge,附表卸貨
Cheques and Deposits incorrectly cleared,支票及存款不正確清除
Account {0}: You can not assign itself as parent account,科目{0}：你不能指定自己為上層科目
Price List Rate,價格列表費率
Create customer quotes,創建客戶報價
Service Stop Date cannot be after Service End Date,服務停止日期不能在服務結束日期之後
"Show ""In Stock"" or ""Not in Stock"" based on stock available in this warehouse.",基於倉庫內存貨的狀態顯示「有或」或「無貨」。
Bill of Materials (BOM),材料清單（BOM）
Average time taken by the supplier to deliver,採取供應商的平均時間交付
Assessment Result,評價結果
Hours,小時
Expected Start Date,預計開始日期
04-Correction in Invoice,04-發票糾正
Work Order already created for all items with BOM,已經為包含物料清單的所有料品創建工單
Party Details,黨詳細
Variant Details Report,變體詳細信息報告
Setup Progress Action,設置進度動作
Buying Price List,買價格表
Remove item if charges is not applicable to that item,刪除項目，如果收費並不適用於該項目
Cancel Subscription,取消訂閱
Please select Maintenance Status as Completed or remove Completion Date,請選擇維護狀態為已完成或刪除完成日期
Default Payment Terms Template,默認付款條款模板
Transaction currency must be same as Payment Gateway currency,交易貨幣必須與支付網關貨幣
Receive,接受
Quotations: ,語錄：
Partially Fulfilled,部分實現
Fully Completed,全面完成
{0}% Complete,{0}％完成
Educational Qualification,學歷
Operating Costs,運營成本
Currency for {0} must be {1},貨幣{0}必須{1}
Disposal Date,處置日期
"Emails will be sent to all Active Employees of the company at the given hour, if they do not have holiday. Summary of responses will be sent at midnight.",電子郵件將在指定的時間發送給公司的所有在職職工，如果他們沒有假期。回复摘要將在午夜被發送。
Employee Leave Approver,員工請假審批
Row {0}: An Reorder entry already exists for this warehouse {1},行{0}：一個重新排序條目已存在這個倉庫{1}
"Cannot declare as lost, because Quotation has been made.",不能聲明為丟失，因為報價已經取得進展。
CWIP Account,CWIP科目
Tax Withholding rates to be applied on transactions.,稅收預扣稅率適用於交易。
Supplier Scorecard Criteria,供應商記分卡標準
Please select Start Date and End Date for Item {0},請選擇項目{0}的開始日期和結束日期
Course is mandatory in row {0},當然是行強制性{0}
To date cannot be before from date,無效的主名稱
Prevdoc DocType,Prevdoc的DocType,
Section Footer,章節頁腳
Add / Edit Prices,新增 / 編輯價格
Employee Promotion cannot be submitted before Promotion Date ,員工晉升不能在晉升日期前提交
Chart of Cost Centers,成本中心的圖
Number of days after invoice date has elapsed before canceling subscription or marking subscription as unpaid,在取消訂閱或將訂閱標記為未付之前，發票日期之後的天數已過
Sample Collection,樣品收集
Requested Items To Be Ordered,將要採購的需求項目
Price List Name,價格列表名稱
Dispatch Information,發貨信息
Manufacturing,製造
Ordered Items To Be Delivered,未交貨的訂購項目
Income,收入
Industry Type,行業類型
Something went wrong!,出事了！
Transaction Data Mapping,交易數據映射
Sales Invoice {0} has already been submitted,銷售發票{0}已提交
Fiscal Year {0} does not exist,會計年度{0}不存在
Amount (Company Currency),金額（公司貨幣）
Agriculture User,農業用戶
{0} units of {1} needed in {2} on {3} {4} for {5} to complete this transaction.,{0} {1}在需要{2}在{3} {4}：{5}來完成這一交易單位。
Student Category,學生組
Student,學生
Stock quantity to start procedure is not available in the warehouse. Do you want to record a Stock Transfer,倉庫中不提供開始操作的庫存數量。你想記錄庫存轉移嗎？
Shipping Rule Type,運輸規則類型
Go to Rooms,去房間
"Company, Payment Account, From Date and To Date is mandatory",公司，付款帳戶，從日期和日期是強制性的
Budget Detail,預算案詳情
Please enter message before sending,在發送前，請填寫留言
DUPLICATE FOR SUPPLIER,供應商重複
Point-of-Sale Profile,簡介銷售點的
{0} should be a value between 0 and 100,{0}應該是0到100之間的一個值
Unsecured Loans,無抵押貸款
Cost Center Name,成本中心名稱
Scheduled Date,預定日期
Total Paid Amt,數金額金額
Messages greater than 160 characters will be split into multiple messages,大於160個字元的訊息將被分割成多個訊息送出
Received and Accepted,收貨及允收
GST Itemised Sales Register,消費稅商品銷售登記冊
Staffing Plan Details,人員配置計劃詳情
Serial No Service Contract Expiry,序號服務合同到期
You cannot credit and debit same account at the same time,你無法將貸方與借方在同一時間記在同一科目
Adults' pulse rate is anywhere between 50 and 80 beats per minute.,成年人的脈率在每分鐘50到80次之間。
Student Group Creation Tool,學生組創建工具
Variant Based On,基於變異對
Total weightage assigned should be 100%. It is {0},分配的總權重應為100 ％ 。這是{0}
Loyalty Program Tier,忠誠度計劃層
Your Suppliers,您的供應商
Cannot set as Lost as Sales Order is made.,不能設置為失落的銷售訂單而成。
Supplier Part No,供應商部件號
Cannot deduct when category is for 'Valuation' or 'Vaulation and Total',當類是“估值”或“Vaulation和總&#39;不能扣除
Received From,從......收到
Converted,轉換
Has Serial No,有序列號
Date of Issue,發行日期
"As per the Buying Settings if Purchase Reciept Required == 'YES', then for creating Purchase Invoice, user need to create Purchase Receipt first for item {0}",根據購買設置，如果需要購買記錄==“是”，則為了創建採購發票，用戶需要首先為項目{0}創建採購憑證
Row #{0}: Set Supplier for item {1},行＃{0}：設置供應商項目{1}
Default Distance Unit,默認距離單位
Row {0}: Hours value must be greater than zero.,行{0}：小時值必須大於零。
Website Image {0} attached to Item {1} cannot be found,網站圖像{0}附加到物品{1}無法找到
Content Type,內容類型
Assets,資產
Computer,電腦
List this Item in multiple groups on the website.,列出這個項目在網站上多個組。
Current Invoice End Date,當前發票結束日期
Due Date Based On,到期日基於
Please set default customer group and territory in Selling Settings,請在“銷售設置”中設置默認客戶組和領域
Please check Multi Currency option to allow accounts with other currency,請檢查多幣種選項，允許帳戶與其他貨幣
Item: {0} does not exist in the system,項：{0}不存在於系統中
You are not authorized to set Frozen value,您無權設定值凍結
Get Unreconciled Entries,獲取未調節項
No repayments selected for Journal Entry,沒有為日記帳分錄選擇還款
From Invoice Date,從發票日期
Laboratory Settings,實驗室設置
Service Unit,服務單位
Successfully Set Supplier,成功設置供應商
What does it do?,它有什麼作用？
Tasks have been created for managing the {0} disease (on row {1}),為管理{0}疾病創建了任務（在第{1}行）
Byproducts,副產品
To Warehouse,到倉庫
All Student Admissions,所有學生入學
Average Commission Rate,平均佣金比率
No of Shares,股份數目
'Has Serial No' can not be 'Yes' for non-stock item,非庫存項目不能有序號
Select Status,選擇狀態
Attendance can not be marked for future dates,考勤不能標記為未來的日期
Post Description Key,發布說明密鑰
Pricing Rule Help,定價規則說明
Total Amount per Student,學生總數
Sales Stage,銷售階段
Account Head,帳戶頭
HRA Component,HRA組件
Electrical,電子的
Add the rest of your organization as your users. You can also add invite Customers to your portal by adding them from Contacts,添加您的組織的其餘部分用戶。您還可以添加邀請客戶到您的門戶網站通過從聯繫人中添加它們
Total Value Difference (Out - In),總價值差（輸出 - ）
Requested Amount,請求金額
Row {0}: Exchange Rate is mandatory,行{0}：匯率是必須的
User ID not set for Employee {0},用戶ID不為員工設置{0}
Vehicle Value,汽車衡
Detected Diseases,檢測到的疾病
Default Source Warehouse,預設來源倉庫
Customer Code,客戶代碼
Last Completion Date,最後完成日期
Days Since Last Order,天自上次訂購
Debit To account must be a Balance Sheet account,借方帳戶必須是資產負債表科目
Coated,塗
Row {0}: Expected Value After Useful Life must be less than Gross Purchase Amount,行{0}：使用壽命後的預期值必須小於總採購額
GoCardless Settings,GoCardless設置
Certification Validity,認證有效性
Insurance Start date should be less than Insurance End date,保險開始日期應小於保險終止日期
Display Settings,顯示設置
Stock Assets,庫存資產
Active Menu,活動菜單
Target Qty,目標數量
Against Loan: {0},反對貸款：{0}
Checkout Settings,結帳設定
Delivery Note {0} must not be submitted,送貨單{0}不能提交
Sales Invoice Message,銷售發票訊息
Closing Account {0} must be of type Liability / Equity,關閉科目{0}的類型必須是負債/權益
Ordered Qty,訂購數量
Item {0} is disabled,項目{0}無效
Stock Frozen Upto,存貨凍結到...為止
BOM does not contain any stock item,BOM不包含任何庫存項目
Chapter Head,章主管
Month(s) after the end of the invoice month,發票月份結束後的月份
Project activity / task.,專案活動／任務。
Very Coated,非常塗層
Lab result datetime cannot be before testing datetime,實驗結果日期時間不能在測試日期時間之前
Allow user to edit Discount,允許用戶編輯折扣
Get customers from,從中獲取客戶
Include Exploded Items,包含爆炸物品
"Buying must be checked, if Applicable For is selected as {0}",採購必須進行檢查，如果適用於被選擇為{0}
Discount must be less than 100,折扣必須小於100,
Restrict to Countries,限製到國家
Synch Taxes and Charges,同步稅和費用
Write Off Amount (Company Currency),核銷金額（公司貨幣）
Billing Hours,結算時間
Total Sales Amount (via Sales Order),總銷售額（通過銷售訂單）
Default BOM for {0} not found,默認BOM {0}未找到
Row #{0}: Please set reorder quantity,行＃{0}：請設置再訂購數量
Tap items to add them here,點擊項目將其添加到此處
Program Enrollment,招生計劃
To Folio No,對開本No,
Landed Cost Voucher,到岸成本憑證
Please set {0},請設置{0}
{0} - {1} is inactive student,{0}  -  {1}是非活動學生
{0} - {1} is inactive student,{0}  -  {1}是非活動學生
Health Details,健康細節
To create a Payment Request reference document is required,要創建付款請求參考文檔是必需的
To create a Payment Request reference document is required,要創建付款請求參考文檔是必需的
Assessment  Manager,評估經理
Allocate Payment Amount,分配付款金額
Subscription Plan,訂閱計劃
Salary,薪水
Delivery Document Type,交付文件類型
Do not update variants on save,不要在保存時更新變體
Receivables,應收帳款
Lead Source,主導來源
Additional information regarding the customer.,對於客戶的其他訊息。
Reading 5,閱讀5,
"{0} {1} is associated with {2}, but Party Account is {3}","{0} {1} 與 {2} 關聯, 但當事方科目為 {3}"
Bank Header,銀行標題
View Lab Tests,查看實驗室測試
Hub Users,Hub用戶
Y,ÿ
Maintenance Date,維修日期
Rejected Serial No,拒絕序列號
Year start date or end date is overlapping with {0}. To avoid please set company,新年的開始日期或結束日期與{0}重疊。為了避免請將公司
Please mention the Lead Name in Lead {0},請提及潛在客戶名稱{0}
Start date should be less than end date for Item {0},項目{0}的開始日期必須小於結束日期
"Example: ABCD.#####
If series is set and Serial No is not mentioned in transactions, then automatic serial number will be created based on this series. If you always want to explicitly mention Serial Nos for this item. leave this blank.","例如：ABCD ##### 
如果串聯設定並且序列號沒有在交易中提到，然後自動序列號將在此基礎上創建的系列。如果你總是想明確提到序號為這個項目。留空。"
BOM and Manufacturing Quantity are required,BOM和生產量是必需的
Ageing Range 2,老齡範圍2,
Installing presets,安裝預置
No Delivery Note selected for Customer {},沒有為客戶{}選擇送貨單
Select Items based on Delivery Date,根據交付日期選擇項目
Has any past Grant Record,有過去的贈款記錄嗎？
Sales Analytics,銷售分析
Prospects Engaged But Not Converted,展望未成熟
Prospects Engaged But Not Converted,展望未成熟
Manufacturing Settings,製造設定
Setting up Email,設定電子郵件
Guardian1 Mobile No,Guardian1手機號碼
Please enter default currency in Company Master,請在公司主檔輸入預設貨幣
Stock Entry Detail,存貨分錄明細
See all open tickets,查看所有打開的門票
Healthcare Service Unit Tree,醫療服務單位樹
Product,產品
Home Page is Products,首頁是產品頁
Asset Depreciation Ledger,資產減值總帳
For how much spent = 1 Loyalty Point,花費多少= 1忠誠點
Tax Rule Conflicts with {0},稅收規範衝突{0}
New Account Name,新帳號名稱
Raw Materials Supplied Cost,原料供應成本
Settings for Selling Module,設置銷售模塊
Hotel Room Reservation,酒店房間預訂
Customer Service,顧客服務
Thumbnail,縮略圖
No contacts with email IDs found.,找不到與電子郵件ID的聯繫人。
Item Customer Detail,項目客戶詳細
Prompt for Email on Submission of,提示電子郵件的提交
Total allocated leaves are more than days in the period,分配的總葉多天的期限
Linked Soil Analysis,連接的土壤分析
Item {0} must be a stock Item,項{0}必須是一個缺貨登記
Default Work In Progress Warehouse,預設在製品倉庫
"Schedules for {0} overlaps, do you want to proceed after skiping overlaped slots ?",{0}重疊的時間表，是否要在滑動重疊的插槽後繼續？
Default settings for accounting transactions.,會計交易的預設設定。
Grant Leaves,格蘭特葉子
Default Tax Template,默認稅收模板
{0} Students have been enrolled,{0}學生已被註冊
Student Details,學生細節
Stock Qty,庫存數量
Stock Qty,庫存數量
Default Shipping Account,默認運輸科目
Error: Not a valid id?,錯誤：沒有有效的身份證？
Equity,公平
{0} {1}: 'Profit and Loss' type account {2} not allowed in Opening Entry,{0} {1}：“損益”科目類型{2}不允許進入開
Printing Details,印刷詳情
Closing Date,截止日期
Produced Quantity,生產的產品數量
Quantity  that must be bought or sold per UOM,每個UOM必須購買或出售的數量
Engineer,工程師
Max Amount,最大金額
Total Amount Currency,總金額幣種
Search Sub Assemblies,搜索子組件
Item Code required at Row No {0},於列{0}需要產品編號
SGST Account,SGST科目
Go to Items,轉到項目
Partner Type,合作夥伴類型
Actual,實際
Restaurant Manager,餐廳經理
Customerwise Discount,Customerwise折扣
Timesheet for tasks.,時間表的任務。
Against Expense Account,對費用科目
Installation Note {0} has already been submitted,安裝注意{0}已提交
Get Payment Entries,獲取付款項
Against Docname,對Docname,
All Employee (Active),所有員工（活動）
View Now,立即觀看
Woocommerce Server URL,Woocommerce服務器URL,
Re-Order Level,重新排序級別
Shopify Tax/Shipping Title,Shopify稅/運輸標題
Gantt Chart,甘特圖
Cycle Type,循環類型
Applicable Holiday List,適用假期表
Series Updated,系列更新
Report Type is mandatory,報告類型是強制性的
Serial Number Series,序列號系列
Warehouse is mandatory for stock Item {0} in row {1},倉庫是強制性的庫存項目{0}行{1}
Retail & Wholesale,零售及批發
First Responded On,首先作出回應
Cross Listing of Item in multiple groups,在多組項目的交叉上市
Fiscal Year Start Date and Fiscal Year End Date are already set in Fiscal Year {0},會計年度開始日期和財政年度結束日期已經在財政年度設置{0}
Ignore User Time Overlap,忽略用戶時間重疊
Accounting Period,會計期間
Clearance Date updated,間隙更新日期
Batch Identification,批次標識
Successfully Reconciled,不甘心成功
Download PDF,下載PDF,
Planned End Date,計劃的結束日期
Hidden list maintaining the list of contacts linked to Shareholder,隱藏列表維護鏈接到股東的聯繫人列表
Current Exchange Rate,當前匯率
"Sales, Purchase, Accounting Defaults",銷售，採購，會計違約
Donor Type information.,捐助者類型信息。
{0} on Leave on {1},{0}離開{1}
Supplier Detail,供應商詳細
Error in formula or condition: {0},誤差在式或條件：{0}
Invoiced Amount,發票金額
Criteria weights must add up to 100%,標準重量必須達100％
Stock Items,庫存產品
Update Billed Amount in Sales Order,更新銷售訂單中的結算金額
Posting date and posting time is mandatory,登錄日期和登錄時間是必需的
Tax template for buying transactions.,稅務模板購買交易。
Item Prices,產品價格
In Words will be visible once you save the Purchase Order.,採購訂單一被儲存，就會顯示出來。
Add to Holidays,加入假期
Endpoint,端點
Period Closing Voucher,期末券
Review Details,評論細節
The shareholder does not belong to this company,股東不屬於這家公司
Dosage Form,劑型
Price List master.,價格表主檔
Review Date,評論日期
Allow Alternative Item,允許替代項目
Series for Asset Depreciation Entry (Journal Entry),資產折舊條目系列（期刊條目）
Member Since,成員自
Advance Payments,預付款
Please select Healthcare Service,請選擇醫療保健服務
On Net Total,在總淨
Value for Attribute {0} must be within the range of {1} to {2} in the increments of {3} for Item {4},為屬性{0}值必須的範圍內{1}到{2}中的增量{3}為項目{4}
Waitlisted,輪候
Currency can not be changed after making entries using some other currency,貨幣不能使用其他貨幣進行輸入後更改
Round Off Account,四捨五入科目
Administrative Expenses,行政開支
Consulting,諮詢
Based on price list,基於價格表
Parent Customer Group,母客戶群組
Change,更改
Subscription,訂閱
Contact Email,聯絡電郵
Fee Creation Pending,費用創作待定
Asset Category Name,資產類別名稱
This is a root territory and cannot be edited.,集團或Ledger ，借方或貸方，是特等科目
New Sales Person Name,新銷售人員的姓名
Gross Weight UOM,毛重計量單位
Set Details,設置細節
Preventive Maintenance,預防性的維護
Against Sales Invoice,對銷售發票
Please enter serial numbers for serialized item ,請輸入序列號序列號
Reserved Qty for Production,預留數量生產
Leave unchecked if you don't want to consider batch while making course based groups. ,如果您不想在製作基於課程的組時考慮批量，請不要選中。
Leave unchecked if you don't want to consider batch while making course based groups. ,如果您不想在製作基於課程的組時考慮批量，請不要選中。
Frequency of Depreciation (Months),折舊率（月）
Credit Account,信用科目
Landed Cost Item,到岸成本項目
Show zero values,顯示零值
Quantity of item obtained after manufacturing / repacking from given quantities of raw materials,製造/從原材料數量給予重新包裝後獲得的項目數量
Test Group,測試組
Receivable / Payable Account,應收/應付帳款
Against Sales Order Item,對銷售訂單項目
Company Logo,公司標誌
Please specify Attribute Value for attribute {0},請指定屬性值的屬性{0}
Default Warehouse,預設倉庫
Budget cannot be assigned against Group Account {0},不能指定預算給群組帳目{0}
Show Price,顯示價格
Patient Registration,病人登記
Please enter parent cost center,請輸入父成本中心
Print Without Amount,列印表單時不印金額
Depreciation Date,折舊日期
Work Orders in Progress,工作訂單正在進行中
Support Team,支持團隊
Expiry (In Days),到期（天數）
Batch,批量
Query Route String,查詢路由字符串
Update rate as per last purchase,根據上次購買更新率
Donor Type,捐助者類型
Auto repeat document updated,自動重複文件更新
Balance,餘額
Please select the Company,請選擇公司
Seating Capacity,座位數
Lab Test Groups,實驗室測試組
Party Type and Party is mandatory for {0} account,{0}科目的參與方以及類型為必填
Total Expense Claim (via Expense Claim),總費用報銷（通過費用報銷）
GST Summary,消費稅總結
ISO 8601 standard,ISO 8601標準
Debit Note,繳費單
You can only redeem max {0} points in this order.,您只能按此順序兌換最多{0}個積分。
Please enter API Consumer Secret,請輸入API消費者密碼
As per Stock UOM,按庫存計量單位
Not Expired,沒有過期
Achievement,成就
Insurer,保險公司
Source Document Type,源文檔類型
Source Document Type,源文檔類型
Following course schedules were created,按照課程時間表創建
Total Debit,借方總額
Default Finished Goods Warehouse,預設成品倉庫
Please select Patient,請選擇患者
Sales Person,銷售人員
Amenities,設施
Undeposited Funds Account,未存入資金科目
Budget and Cost Center,預算和成本中心
Multiple default mode of payment is not allowed,不允許多種默認付款方式
Loyalty Points Redemption,忠誠積分兌換
Appointment Analytics,預約分析
Blog Subscriber,網誌訂閱者
Alternate Number,備用號碼
Create rules to restrict transactions based on values.,創建規則來限制基於價值的交易。
Cash Flow Mapping Accounts,現金流量映射科目
 Group Roll No,組卷號
Manufacturing Date,生產日期
Fee Creation Failed,費用創作失敗
Create Missing Party,創建失踪派對
Total Budget,預算總額
Leave blank if you make students groups per year,如果您每年製作學生團體，請留空
Leave blank if you make students groups per year,如果您每年製作學生團體，請留空
"Apps using current key won't be able to access, are you sure?",使用當前密鑰的應用程序將無法訪問，您確定嗎？
Total Advance,預付款總計
Change Template Code,更改模板代碼
The Term End Date cannot be earlier than the Term Start Date. Please correct the dates and try again.,該期限結束日期不能超過期限開始日期。請更正日期，然後再試一次。
Quot Count,報價
Quot Count,報價計數
Bank Statement,銀行對帳單
BOM Stock Report,BOM庫存報告
Quantity Difference,數量差異
Basic Rate,基礎匯率
Credit Amount,信貸金額
Signatory Position,簽署的位置
Set as Lost,設為失落
Total Billable Hours,總計費時間
Number of days that the subscriber has to pay invoices generated by this subscription,用戶必須支付此訂閱生成的發票的天數
Payment Receipt Note,付款收貨注意事項
This is based on transactions against this Customer. See timeline below for details,這是基於對這個顧客的交易。詳情請參閱以下時間表
Row {0}: Allocated amount {1} must be less than or equals to Payment Entry amount {2},行{0}：分配金額{1}必須小於或等於輸入付款金額{2}
New Academic Term,新學期
Course wise Assessment Report,課程明智的評估報告
Availed ITC State/UT Tax,有效的ITC州/ UT稅
Tax Rule,稅務規則
Maintain Same Rate Throughout Sales Cycle,保持同樣的速度在整個銷售週期
Please login as another user to register on Marketplace,請以另一個用戶身份登錄以在Marketplace上註冊
Plan time logs outside Workstation Working Hours.,在工作站的工作時間以外計畫時間日誌。
Customers in Queue,在排隊的客戶
Issuing Date,發行日期
Appointment Booked,預約預約
Nationality,國籍
Submit this Work Order for further processing.,提交此工單以進一步處理。
Items To Be Requested,需求項目
Company Info,公司資訊
Select or add new customer,選擇或添加新客戶
Cost center is required to book an expense claim,成本中心需要預訂費用報銷
Application of Funds (Assets),基金中的應用（資產）
Payment Request Type,付款申請類型
Year Start Date,年結開始日期
Employee Name,員工姓名
Restaurant Order Entry Item,餐廳訂單錄入項目
Rounded Total (Company Currency),整數總計（公司貨幣）
Cannot covert to Group because Account Type is selected.,不能轉換到群組科目，因為科目類型選擇的。
{0} {1} has been modified. Please refresh.,{0} {1} 已修改。請更新。
"If unlimited expiry for the Loyalty Points, keep the Expiry Duration empty or 0.",如果忠誠度積分無限期到期，請將到期時間保持為空或0。
Maintenance Team Members,維護團隊成員
Purchase Amount,購買金額
"Cannot deliver Serial No {0} of item {1} as it is reserved \
											to fullfill Sales Order {2}",無法交付項目{1}的序列號{0}，因為它已保留\以滿足銷售訂單{2}
Supplier Quotation {0} created,供應商報價{0}創建
End Year cannot be before Start Year,結束年份不能啟動年前
Packed quantity must equal quantity for Item {0} in row {1},盒裝數量必須等於{1}列品項{0}的數量
Manufactured Qty,生產數量
The shares don't exist with the {0},這些份額不存在於{0}
Sales Partner Type,銷售夥伴類型
Invoice Created,已創建發票
Out of Order,亂序
Accepted Quantity,允收數量
Ignore Workstation Time Overlap,忽略工作站時間重疊
Please set a default Holiday List for Employee {0} or Company {1},請設置一個默認的假日列表為員工{0}或公司{1}
Select Batch Numbers,選擇批號
Bills raised to Customers.,客戶提出的賬單。
Invoice Appointments Automatically,自動發票約會
Project Id,項目編號
Basic Component,基本組件
Row No {0}: Amount cannot be greater than Pending Amount against Expense Claim {1}. Pending Amount is {2},行無{0}：金額不能大於金額之前對報銷{1}。待審核金額為{2}
Medical Administrator,醫療管理員
Schedule,時間表
Parent Account,上層科目
Reading 3,閱讀3,
Source Warehouse Address,來源倉庫地址
Voucher Type,憑證類型
Max Retry Limit,最大重試限制
Price List not found or disabled,價格表未找到或被禁用
Price,價格
Guardian,監護人
All communications including and above this shall be moved into the new Issue,包括及以上的所有通信均應移至新發行中
Item Alternative,項目選擇
Default income accounts to be used if not set in Healthcare Practitioner to book Appointment charges.,如果未在醫生執業者中設置預約費用，則使用默認收入帳戶。
Create missing customer or supplier.,創建缺少的客戶或供應商。
Appraisal {0} created for Employee {1} in the given date range,鑑定{0}為員工在給定日期範圍{1}創建
Expected Discharge,預期解僱
Del,刪除
Campaign Naming By,活動命名由
Current Address Is,當前地址是
Monthly Sales Target (,每月銷售目標（
"Optional. Sets company's default currency, if not specified.",可選。設置公司的默認貨幣，如果沒有指定。
Customer GSTIN,客戶GSTIN,
List of diseases detected on the field. When selected it'll automatically add a list of tasks to deal with the disease ,在現場檢測到的疾病清單。當選擇它會自動添加一個任務清單處理疾病
This is a root healthcare service unit and cannot be edited.,這是根醫療保健服務單位，不能編輯。
Repair Status,維修狀態
Add Sales Partners,添加銷售合作夥伴
Accounting journal entries.,會計日記帳分錄。
Available Qty at From Warehouse,可用數量從倉庫
Please select Employee Record first.,請選擇員工記錄第一。
Account for Change Amount,帳戶漲跌額
Connecting to QuickBooks,連接到QuickBooks,
Total Gain/Loss,總收益/損失
Invalid Company for Inter Company Invoice.,公司發票無效公司。
input service,輸入服務
Row {0}: Party / Account does not match with {1} / {2} in {3} {4},行{0}：甲方/客戶不與匹配{1} / {2} {3} {4}
Maintenance Team Member,維護團隊成員
Course Code: ,課程編號：
Please enter Expense Account,請輸入您的費用科目
Stock,庫存
"Row #{0}: Reference Document Type must be one of Purchase Order, Purchase Invoice or Journal Entry",行＃{0}：參考文件類型必須是採購訂單之一，購買發票或日記帳分錄
Current Address,當前地址
"If item is a variant of another item then description, image, pricing, taxes etc will be set from the template unless explicitly specified",如果項目是另一項目，然後描述，圖像，定價，稅費等會從模板中設定的一個變體，除非明確指定
Purchase / Manufacture Details,採購／製造詳細資訊
Assessment Group,評估小組
Batch Inventory,批量庫存
GST Transporter ID,消費稅轉運ID,
Procedure Name,程序名稱
Contract End Date,合同結束日期
Seller ID,賣家ID,
Track this Sales Order against any Project,跟踪對任何項目這個銷售訂單
Bank Statement Transaction Entry,銀行對賬單交易分錄
Discount and Margin,折扣和保證金
Prescription,處方
Default Deferred Revenue Account,默認遞延收入科目
Second Email,第二封郵件
Action if Annual Budget Exceeded on Actual,年度預算超出實際的行動
Min Qty,最小數量
Planned Qty,計劃數量
Date of Incorporation,註冊成立日期
Total Tax,總稅收
Last Purchase Price,上次購買價格
For Quantity (Manufactured Qty) is mandatory,對於數量（製造數量）是強制性的
Default Target Warehouse,預設目標倉庫
Net Total (Company Currency),總淨值（公司貨幣）
Air,空氣
The Year End Date cannot be earlier than the Year Start Date. Please correct the dates and try again.,年末日期不能超過年度開始日期。請更正日期，然後再試一次。
Purchase Receipt Message,採購入庫單訊息
Scrap Items,廢物品
Actual Start Date,實際開始日期
% of materials delivered against this Sales Order,針對這張銷售訂單的已交貨物料的百分比(%)
Generate Material Requests (MRP) and Work Orders.,生成材料請求（MRP）和工作訂單。
Set default mode of payment,設置默認付款方式
With Operations,加入作業
Post Route Key List,發布路由密鑰列表
Accounting entries have already been made in currency {0} for company {1}. Please select a receivable or payable account with currency {0}.,會計分錄已取得貨幣{0}為公司{1}。請選擇一個應收或應付科目幣種{0}。
Is Existing Asset,是對現有資產
If different than customer address,如果與客戶地址不同
Without Payment of Tax,不繳納稅款
BOM Operation,BOM的操作
On Previous Row Amount,在上一行金額
Has Expiry Date,有過期日期
Transfer Asset,轉讓資產
POS Profile,POS簡介
Phone (Office),電話（辦公室）
"Cannot Submit, Employees left to mark attendance",無法提交，僱員留下來標記出席
Admission,入場
Admissions for {0},招生{0}
"Seasonality for setting budgets, targets etc.",季節性設置預算，目標等。
Variable Name,變量名
"Item {0} is a template, please select one of its variants",項目{0}是一個模板，請選擇它的一個變體
Deferred Expense,遞延費用
Asset Category,資產類別
Advance Paid,提前支付
Overproduction Percentage For Sales Order,銷售訂單超額生產百分比
Item Tax,產品稅
Material to Supplier,材料到供應商
Material Request Planning,物料請求計劃
Excise Invoice,消費稅發票
Treshold {0}% appears more than once,Treshold {0}出現％不止一次
Current Liabilities,流動負債
Timer exceeded the given hours.,計時器超出了指定的小時數
Send mass SMS to your contacts,發送群發短信到您的聯絡人
A Positive,積極的
Program Name,程序名稱
Consider Tax or Charge for,考慮稅收或收費
Driving License Category,駕駛執照類別
Actual Qty is mandatory,實際數量是強制性
"{0} currently has a {1} Supplier Scorecard standing, and Purchase Orders to this supplier should be issued with caution.",{0}目前擁有{1}供應商記分卡，而採購訂單應謹慎提供給供應商。
Asset Maintenance Team,資產維護團隊
Scheduling Tool,調度工具
Item to be manufactured or repacked,產品被製造或重新包裝
Syntax error in condition: {0},條件中的語法錯誤：{0}
Major/Optional Subjects,大/選修課
Please Set Supplier Group in Buying Settings.,請設置供應商組購買設置。
"Total flexible benefit component amount {0} should not be less \
				than max benefits {1}",靈活福利組件總額{0}不應低於最大福利{1}
Drop Ship,直接發運給客戶
Suspended,暫停
"Here you can maintain family details like name and occupation of parent, spouse and children",在這裡，您可以維護家庭的詳細訊息，如父母，配偶和子女的姓名及職業
Term End Date,期限結束日期
Taxes and Charges Deducted (Company Currency),稅收和扣除（公司貨幣）
General Settings,一般設定
From Currency and To Currency cannot be same,原始貨幣和目標貨幣不能相同
Repack,重新包裝
You must Save the form before proceeding,在繼續之前，您必須儲存表單
Please select the Company first,請先選擇公司
Numeric Values,數字值
Attach Logo,附加標誌
Stock Levels,庫存水平
Commission Rate,佣金比率
Successfully created payment entries,成功創建付款條目
Created {0} scorecards for {1} between: ,為{1}創建{0}記分卡：
Make Variant,在Variant,
"Payment Type must be one of Receive, Pay and Internal Transfer",付款方式必須是接收之一，收費和內部轉賬
Cart is Empty,車是空的
"Item {0} has no Serial No. Only serilialized items \
						can have delivery based on Serial No",項目{0}沒有序列號只有serilialized items \可以根據序列號進行交付
Actual Operating Cost,實際運行成本
Cheque/Reference No,支票/參考編號
Clay Loam,粘土Loam,
Root cannot be edited.,root不能被編輯。
Units of Measure,測量的單位
Default Tax Withholding Config,預設稅款預扣配置
Allow Production on Holidays,允許假日生產
Customer's Purchase Order Date,客戶的採購訂單日期
Default Finance Book,默認金融書
Show Public Attachments,顯示公共附件
Edit Publishing Details,編輯發布細節
Package Weight Details,包裝重量詳情
Reservation Time,預訂時間
Payment Gateway Account,網路支付閘道科目
After payment completion redirect user to selected page.,支付完成後重定向用戶選擇的頁面。
Existing Company,現有的公司
Result Emailed,電子郵件結果
"Tax Category has been changed to ""Total"" because all the Items are non-stock items",稅項類別已更改為“合計”，因為所有物品均為非庫存物品
Total Holidays,總假期
Missing email template for dispatch. Please set one in Delivery Settings.,缺少發送的電子郵件模板。請在“傳遞設置”中設置一個。
Mark as Present,標記為現
Indicator Color,指示燈顏色
To Receive and Bill,準備收料及接收發票
Row #{0}: Reqd by Date cannot be before Transaction Date,行號{0}：按日期請求不能在交易日期之前
Featured Products,特色產品
Select Serial No,選擇序列號
Designer,設計師
Terms and Conditions Template,條款及細則範本
Delivery Details,交貨細節
Cost Center is required in row {0} in Taxes table for type {1},成本中心是必需的行{0}稅表型{1}
Program Code,程序代碼
Terms and Conditions Help,條款和條件幫助
Item-wise Purchase Register,項目明智的購買登記
Expiry Date,到期時間
Employee name and designation in print,員工姓名和印刷品名稱
accounts-browser,科目瀏覽器
Please select Category first,請先選擇分類
Project master.,專案主持。
"To allow over-billing or over-ordering, update ""Allowance"" in Stock Settings or the Item.",要允許對賬單或過度訂貨，庫存設置或更新項目“津貼”。
Contract Terms,合同條款
Do not show any symbol like $ etc next to currencies.,不要顯示如$等任何貨幣符號。
Credit Days,信貸天
Please select Patient to get Lab Tests,請選擇患者以獲得實驗室測試
Make Student Batch,讓學生批
Allow Transfer for Manufacture,允許轉移製造
Get Items from BOM,從物料清單取得項目
Lead Time Days,交貨期天
Is Income Tax Expense,是所得稅費用
Your order is out for delivery!,您的訂單已發貨！
Row #{0}: Posting Date must be same as purchase date {1} of asset {2},行＃{0}：過帳日期必須是相同的購買日期{1}資產的{2}
Check this if the Student is residing at the Institute's Hostel.,如果學生住在學院的旅館，請檢查。
Please enter Sales Orders in the above table,請在上表中輸入銷售訂單
Stock Summary,庫存摘要
Transfer an asset from one warehouse to another,從一個倉庫轉移資產到另一
Bill of Materials,材料清單
Row {0}: Party Type and Party is required for Receivable / Payable account {1},行{0}：參與方類型和參與方需要應收/應付科目{1}
Update Items,更新項目
Ref Date,參考日期
Reason for Leaving,離職原因
Operating Cost(Company Currency),營業成本（公司貨幣）
Shelf Life In Days,保質期天數
Is Opening,是開幕
Expense Approvers,費用審批人
Row {0}: Debit entry can not be linked with a {1},行{0}：借方條目不能與{1}連接
Subscription Section,認購科
Account {0} does not exist,科目{0}不存在
Cash,現金
Short biography for website and other publications.,網站和其他出版物的短的傳記。
"It seems that there is an issue with the server's stripe configuration. In case of failure, the amount will get refunded to your account.",看起來服務器的條帶配置存在問題。如果失敗，這筆款項將退還給您的賬戶。
Completed By,完成
What do you need help with?,你有什麼需要幫助的？
Interest,利益
Make ,使
Other Settings,其他設置
Dr,博士
Payment Failed,支付失敗
Portal,門戶
Lft,LFT,
Master,主
Tasks,任務
You can also copy-paste this link in your browser,您也可以複製粘貼此鏈接到瀏覽器
Profile,輪廓
For,對於
Add Comment,添加評論
Mobile Number,手機號碼
End Time,結束時間
Task Name,任務名稱
Restart,重新開始
Pay,付
From Date must be before To Date,起始日期必須早於終點日期
Notes,筆記
Docs Search,Google文檔搜索
Template Name,模板名稱
Payment Gateway Name,支付網關名稱
Running,運行
Looks like someone sent you to an incomplete URL. Please ask them to look into it.,貌似有人送你一個不完整的URL。請讓他們尋找到它。
Payment Gateway,支付網關
Address and Contacts,地址和聯絡方式
Plan Name,計劃名稱
Reject,拒絕
