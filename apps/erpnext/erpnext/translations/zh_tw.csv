"""Customer Provided Item"" cannot be Purchase Item also",客戶提供的物品“也不能是購買項目,
"""Customer Provided Item"" cannot have Valuation Rate",客戶提供的物品“不能有估價率,
"""Is Fixed Asset"" cannot be unchecked, as Asset record exists against the item",“是固定的資產”不能選中，作為資產記錄存在對項目,
'Based On' and 'Group By' can not be same,“根據”和“分組依據”不能相同,
'Days Since Last Order' must be greater than or equal to zero,“自從最後訂購日”必須大於或等於零,
'Entries' cannot be empty,“分錄”不能是空的,
'From Date' is required,&#39;從日期&#39;是必需的,
'From Date' must be after 'To Date',“起始日期”必須經過'終止日期',
'Has Serial No' can not be 'Yes' for non-stock item,非庫存項目不能有序號,
'Opening',“開放”,
'To Case No.' cannot be less than 'From Case No.',“至案件編號”不能少於'從案件編號“,
'To Date' is required,“至日期”是必需填寫的,
'Total','總數',
'Update Stock' can not be checked because items are not delivered via {0},不能勾選`更新庫存'，因為項目未交付{0},
'Update Stock' cannot be checked for fixed asset sale,"""更新庫存"" 無法檢查固定資產銷售",
) for {0},）為{0},
A Customer Group exists with same name please change the Customer name or rename the Customer Group,客戶群組存在相同名稱，請更改客戶名稱或重新命名客戶群組,
A Default Service Level Agreement already exists.,已存在默認服務級別協議。,
A Lead requires either a person's name or an organization's name,領導者需要一個人的姓名或組織的名稱,
A customer with the same name already exists,一個同名的客戶已經存在,
A question must have more than one options,一個問題必須有多個選項,
A qustion must have at least one correct options,Qustion必須至少有一個正確的選項,
A {0} exists between {1} and {2} (,{1}和{2}之間存在{0}（,
API Endpoint,API端點,
API Key,API密鑰,
Abbr can not be blank or space,縮寫不能為空白或輸入空白鍵,
Abbreviation already used for another company,另一家公司已使用此縮寫,
Abbreviation cannot have more than 5 characters,縮寫不能有超過5個字符,
Abbreviation is mandatory,縮寫是強制性的,
About the Company,關於公司,
About your company,關於貴公司,
Absent,缺席,
Academic Term,學期,
Academic Term: ,學術期限：,
Academic Year,學年,
Academic Year: ,學年：,
Accepted + Rejected Qty must be equal to Received quantity for Item {0},品項{0}的允收＋批退的數量必須等於收到量,
Access Token,存取 Token,
Accessable Value,可訪問的價值,
Account,帳戶,
Account Number,帳號,
Account Number {0} already used in account {1},已在帳戶{1}中使用的帳號{0},
Account Pay Only,賬戶只需支付,
Account Type,帳戶類型,
Account Type for {0} must be {1},帳戶類型為{0}必須{1},
"Account balance already in Credit, you are not allowed to set 'Balance Must Be' as 'Debit'",帳戶餘額已歸為信用帳戶，不允許設為借方帳戶,
"Account balance already in Debit, you are not allowed to set 'Balance Must Be' as 'Credit'",帳戶餘額已歸為借方帳戶，不允許設為信用帳戶,
Account number for account {0} is not available.<br> Please setup your Chart of Accounts correctly.,帳戶{0}的帳戶號碼不可用。 <br>請正確設置您的會計科目表。,
Account with child nodes cannot be converted to ledger,有子節點的帳不能轉換到總帳,
Account with child nodes cannot be set as ledger,帳戶與子節點不能被設置為分類帳,
Account with existing transaction can not be converted to group.,帳戶與現有的交易不能被轉換到群組。,
Account with existing transaction can not be deleted,帳戶與現有的交易不能被刪除,
Account with existing transaction cannot be converted to ledger,帳戶與現有的交易不能被轉換為總賬,
Account {0} does not belong to company: {1},帳戶{0}不屬於公司：{1},
Account {0} does not belongs to company {1},帳戶{0}不屬於公司{1},
Account {0} does not exist,帳戶{0}不存在,
Account {0} does not exists,帳戶{0}不存在,
Account {0} does not match with Company {1} in Mode of Account: {2},帳戶{0}與帳戶模式{2}中的公司{1}不符,
Account {0} has been entered multiple times,帳戶{0}已多次輸入,
Account {0} is added in the child company {1},子公司{1}中添加了帳戶{0},
Account {0} is frozen,帳戶{0}被凍結,
Account {0} is invalid. Account Currency must be {1},帳戶{0}是無效的。帳戶貨幣必須是{1},
Account {0}: Parent account {1} can not be a ledger,帳戶{0}：父帳戶{1}不能是總帳,
Account {0}: Parent account {1} does not belong to company: {2},帳戶{0}：父帳戶{1}不屬於公司：{2},
Account {0}: Parent account {1} does not exist,帳戶{0}：父帳戶{1}不存在,
Account {0}: You can not assign itself as parent account,帳戶{0}：你不能指定自己為父帳戶,
Account: {0} can only be updated via Stock Transactions,帳號：{0}只能通過股票的交易進行更新,
Account: {0} with currency: {1} can not be selected,帳號：{0}幣種：{1}不能選擇,
Accountant,會計人員,
Accounting,會計,
Accounting Entry for Asset,資產會計分錄,
Accounting Entry for Stock,存貨的會計分錄,
Accounting Entry for {0}: {1} can only be made in currency: {2},會計分錄為{0}：{1}只能在貨幣做：{2},
Accounting Ledger,會計總帳,
Accounting journal entries.,會計日記帳分錄。,
Accounts,會計,
Accounts Manager,會計經理,
Accounts Payable,應付帳款,
Accounts Payable Summary,應付帳款摘要,
Accounts Receivable,應收帳款,
Accounts Receivable Summary,應收賬款匯總,
Accounts User,會計人員,
Accounts table cannot be blank.,賬表不能為空。,
Accrual Journal Entry for salaries from {0} to {1},從{0}到{1}的薪金的應計日記帳分錄,
Accumulated Depreciation,累計折舊,
Accumulated Depreciation Amount,累計折舊額,
Accumulated Depreciation as on,作為累計折舊,
Accumulated Monthly,每月累計,
Accumulated Values,累積值,
Accumulated Values in Group Company,集團公司累計價值,
Achieved ({}),達到（{}）,
Action,行動,
Action Initialised,行動初始化,
Active,啟用,
Activity Cost exists for Employee {0} against Activity Type - {1},存在活動費用為員工{0}對活動類型 -  {1},
Activity Cost per Employee,每個員工活動費用,
Activity Type,活動類型,
Actual Cost,實際成本,
Actual Delivery Date,實際交貨日期,
Actual Qty,實際數量,
Actual Qty is mandatory,實際數量是強制性,
Actual Qty {0} / Waiting Qty {1},實際數量{0} /等待數量{1},
Actual Qty: Quantity available in the warehouse.,實際數量：存在倉庫內的數量。,
Actual qty in stock,實際庫存數量,
Actual type tax cannot be included in Item rate in row {0},實際類型稅不能被包含在商品率排{0},
Add,新增,
Add / Edit Prices,新增 / 編輯價格,
Add Comment,添加評論,
Add Customers,添加客戶,
Add Employees,添加員工,
Add Item,新增項目,
Add Items,添加項目,
Add Leads,添加潛在客戶,
Add Multiple Tasks,添加多個任務,
Add Sales Partners,添加銷售合作夥伴,
Add Serial No,添加序列號,
Add Students,新增學生,
Add Suppliers,添加供應商,
Add Time Slots,添加時間插槽,
Add Timesheets,添加時間表,
Add Timeslots,添加時代,
Add Users to Marketplace,將用戶添加到市場,
Add cards or custom sections on homepage,在主頁上添加卡片或自定義欄目,
Add more items or open full form,添加更多項目或全開放形式,
Add notes,添加備註,
Add the rest of your organization as your users. You can also add invite Customers to your portal by adding them from Contacts,添加您的組織的其餘部分用戶。您還可以添加邀請客戶到您的門戶網站通過從聯繫人中添加它們,
Add to Details,添加到詳細信息,
Add/Remove Recipients,添加/刪除收件人,
Added to details,添加到細節,
Added {0} users,添加了{0}個用戶,
Additional Salary Component Exists.,額外的薪資組件存在。,
Address Line 2,地址第2行,
Address Name,地址名稱,
Address Title,地址名稱,
Address Type,地址類型,
Administrative Expenses,行政開支,
Administrative Officer,政務主任,
Administrator,管理員,
Admission,入場,
Admission and Enrollment,入學和入學,
Admissions for {0},招生{0},
Admit,承認,
Admitted,錄取,
Advance Amount,提前量,
Advance Payments,預付款,
Advance account currency should be same as company currency {0},預付帳戶貨幣應與公司貨幣{0}相同,
Advance amount cannot be greater than {0} {1},提前量不能大於{0} {1},
Advertising,廣告,
Against,針對,
Against Account,針對帳戶,
Against Journal Entry {0} does not have any unmatched {1} entry,對日記條目{0}沒有任何無與倫比{1}進入,
Against Journal Entry {0} is already adjusted against some other voucher,對日記條目{0}已經調整一些其他的優惠券,
Against Supplier Invoice {0} dated {1},對供應商發票{0}日期{1},
Against Voucher,對傳票,
Against Voucher Type,對憑證類型,
Age,年齡,
Age (Days),時間（天）,
Ageing Based On,老齡化基於,
Ageing Range 1,老齡範圍1,
Ageing Range 2,老齡範圍2,
Ageing Range 3,老齡範圍3,
Agriculture,農業,
Agriculture (beta),農業（測試版）,
All Accounts,所有帳戶,
All Assessment Groups,所有評估組,
All BOMs,所有的材料明細表,
All Contacts.,所有聯絡人。,
All Customer Groups,所有客戶群組,
All Day,一整天,
All Departments,所有部門,
All Healthcare Service Units,所有醫療服務單位,
All Item Groups,所有項目群組,
All Jobs,所有職位,
All Products,所有產品,
All Products or Services.,所有的產品或服務。,
All Student Admissions,所有學生入學,
All Supplier Groups,所有供應商組織,
All Supplier scorecards.,所有供應商記分卡。,
All Territories,所有的領土,
All Warehouses,所有倉庫,
All communications including and above this shall be moved into the new Issue,包括及以上的所有通信均應移至新發行中,
All items have already been transferred for this Work Order.,所有項目已經為此工作單轉移。,
All the mandatory Task for employee creation hasn't been done yet.,所有員工創建的強制性任務尚未完成。,
Allocate Payment Amount,分配付款金額,
Allocated Amount,分配量,
Allocated Leaves,分配的葉子,
Allocating leaves...,分配葉子......,
Already record exists for the item {0},已有記錄存在項目{0},
"Already set default in pos profile {0} for user {1}, kindly disabled default",已經在用戶{1}的pos配置文件{0}中設置了默認值，請禁用默認值,
Alternate Item,替代項目,
Alternative item must not be same as item code,替代項目不能與項目代碼相同,
Amended From,從修訂,
Amount,量,
Amount After Depreciation,折舊金額後,
Amount of Integrated Tax,綜合稅額,
Amount of TDS Deducted,扣除TDS的金額,
Amount should not be less than zero.,金額不應小於零。,
Amount to Bill,帳單數額,
Amount {0} {1} against {2} {3},量{0} {1}對{2} {3},
Amount {0} {1} deducted against {2},金額{0} {1}抵扣{2},
Amount {0} {1} transferred from {2} to {3},金額{0} {1}從轉移{2}到{3},
Amount {0} {1} {2} {3},金額{0} {1} {2} {3},
Amt,AMT,
"An Item Group exists with same name, please change the item name or rename the item group",具有具有相同名稱的項目群組存在，請更改項目名稱或重新命名該項目群組,
An academic term with this 'Academic Year' {0} and 'Term Name' {1} already exists. Please modify these entries and try again.,這個“學年”一個學期{0}和“術語名稱”{1}已經存在。請修改這些條目，然後再試一次。,
An error occurred during the update process,更新過程中發生錯誤,
"An item exists with same name ({0}), please change the item group name or rename the item",具有相同名稱的項目存在（ {0} ） ，請更改項目群組名或重新命名該項目,
Analyst,分析人士,
Annual Billing: {0},年度結算：{0},
Another Budget record '{0}' already exists against {1} '{2}' and account '{3}' for fiscal year {4},對於財務年度{4}，{1}&#39;{2}&#39;和帳戶“{3}”已存在另一個預算記錄“{0}”,
Another Period Closing Entry {0} has been made after {1},另一個期末錄入{0}作出後{1},
Another Sales Person {0} exists with the same Employee id,另外銷售人員{0}存在具有相同員工ID,
Apparel & Accessories,服裝及配飾,
Applicable For,適用,
"Applicable if the company is SpA, SApA or SRL",如果公司是SpA，SApA或SRL，則適用,
Applicable if the company is a limited liability company,適用於公司是有限責任公司的情況,
Applicable if the company is an Individual or a Proprietorship,適用於公司是個人或獨資企業的情況,
Applicant,申請人,
Applicant Type,申請人類型,
Application of Funds (Assets),基金中的應用（資產）,
Application period cannot be across two allocation records,申請期限不能跨越兩個分配記錄,
Application period cannot be outside leave allocation period,申請期間不能請假外分配週期,
Applied,應用的,
Apply Now,現在申請,
Appointment Confirmation,預約確認,
Appointment Duration (mins),預約時間（分鐘）,
Appointment Type,預約類型,
Appointment {0} and Sales Invoice {1} cancelled,約會{0}和銷售發票{1}已取消,
Appointments and Encounters,約會和遭遇,
Appointments and Patient Encounters,預約和患者遭遇,
Appraisal {0} created for Employee {1} in the given date range,鑑定{0}為員工在給定日期範圍{1}創建,
Apprentice,學徒,
Approval Status,審批狀態,
Approval Status must be 'Approved' or 'Rejected',審批狀態必須被“批准”或“拒絕”,
Approve,批准,
Approving Role cannot be same as role the rule is Applicable To,審批角色作為角色的規則適用於不能相同,
Approving User cannot be same as user the rule is Applicable To,批准用戶作為用戶的規則適用於不能相同,
"Apps using current key won't be able to access, are you sure?",使用當前密鑰的應用程序將無法訪問，您確定嗎？,
Are you sure you want to cancel this appointment?,你確定要取消這個預約嗎？,
Arrear,拖欠,
As Examiner,作為考官,
As On Date,隨著對日,
As Supervisor,作為主管,
As per rules 42 & 43 of CGST Rules,根據CGST規則第42和43條,
As per section 17(5),根據第17（5）條,
As per your assigned Salary Structure you cannot apply for benefits,根據您指定的薪資結構，您無法申請福利,
Assessment,評定,
Assessment Criteria,評估標準,
Assessment Group,評估小組,
Assessment Group: ,評估組：,
Assessment Plan,評估計劃,
Assessment Plan Name,評估計劃名稱,
Assessment Report,評估報告,
Assessment Reports,評估報告,
Assessment Result,評價結果,
Assessment Result record {0} already exists.,評估結果記錄{0}已經存在。,
Asset,財富,
Asset Category,資產類別,
Asset Category is mandatory for Fixed Asset item,資產類別是強制性的固定資產項目,
Asset Maintenance,資產維護,
Asset Movement,資產運動,
Asset Movement record {0} created,資產運動記錄{0}創建,
Asset Name,資產名稱,
Asset Received But Not Billed,已收到但未收費的資產,
Asset Value Adjustment,資產價值調整,
"Asset cannot be cancelled, as it is already {0}",資產不能被取消，因為它已經是{0},
Asset scrapped via Journal Entry {0},通過資產日記帳分錄報廢{0},
"Asset {0} cannot be scrapped, as it is already {1}",資產{0}不能被廢棄，因為它已經是{1},
Asset {0} does not belong to company {1},資產{0}不屬於公司{1},
Asset {0} must be submitted,資產{0}必須提交,
Assets,資產,
Assign,指派,
Assign Salary Structure,分配薪資結構,
Assign To,指派給,
Assign to Employees,分配給員工,
Assigning Structures...,分配結構......,
Associate,關聯,
At least one mode of payment is required for POS invoice.,付款中的至少一個模式需要POS發票。,
Atleast one item should be entered with negative quantity in return document,ATLEAST一個項目應該負數量回報文檔中輸入,
Atleast one of the Selling or Buying must be selected,至少需選擇銷售或購買,
Atleast one warehouse is mandatory,至少要有一間倉庫,
Attach Logo,附加標誌,
Attendance,出勤,
Attendance From Date and Attendance To Date is mandatory,考勤起始日期和出席的日期，是強制性的,
Attendance can not be marked for future dates,考勤不能標記為未來的日期,
Attendance date can not be less than employee's joining date,考勤日期不得少於員工的加盟日期,
Attendance for employee {0} is already marked,員工{0}的考勤已標記,
Attendance for employee {0} is already marked for this day,考勤員工{0}已標記為這一天,
Attendance has been marked successfully.,出席已成功標記。,
Attendance not submitted for {0} as it is a Holiday.,由於是假期，因此未出席{0}的考勤。,
Attendance not submitted for {0} as {1} on leave.,在{0}上沒有針對{1}上的考勤出席。,
Attribute table is mandatory,屬性表是強制性的,
Attribute {0} selected multiple times in Attributes Table,屬性{0}多次選擇在屬性表,
Authorized Signatory,授權簽字人,
Auto Material Requests Generated,汽車材料的要求生成,
Auto Repeat,自動重複,
Auto repeat document updated,自動重複文件更新,
Automotive,汽車,
Available,可得到,
Available Leaves,可用的葉子,
Available Qty,可用數量,
Available Selling,可用銷售,
Available for use date is required,需要使用日期,
Available-for-use Date should be after purchase date,可供使用的日期應在購買日期之後,
Average Age,平均年齡,
Avg Daily Outgoing,平均每日傳出,
Avg. Buying Price List Rate,平均。買價格表價格,
Avg. Selling Price List Rate,平均。出售價目表率,
Avg. Selling Rate,平均。賣出價,
BOM Browser,BOM瀏覽器,
BOM No,BOM No.,
BOM Rate,BOM率,
BOM Stock Report,BOM庫存報告,
BOM and Manufacturing Quantity are required,BOM和生產量是必需的,
BOM does not contain any stock item,BOM不包含任何庫存項目,
BOM {0} does not belong to Item {1},BOM {0}不屬於項目{1},
BOM {0} must be active,BOM {0}必須是積極的,
BOM {0} must be submitted,BOM {0}必須提交,
Balance,餘額,
Balance (Dr - Cr),平衡（Dr  -  Cr）,
Balance ({0}),餘額（{0}）,
Balance Qty,餘額數量,
Balance Sheet,資產負債表,
Balance Value,餘額,
Balance for Account {0} must always be {1},帳戶{0}的餘額必須始終為{1},
Bank,銀行,
Bank Account,銀行帳戶,
Bank Accounts,銀行帳戶,
Bank Draft,銀行匯票,
Bank Entries,銀行條目,
Bank Name,銀行名稱,
Bank Overdraft Account,銀行透支戶口,
Bank Reconciliation,銀行對帳,
Bank Reconciliation Statement,銀行對帳表,
Bank Statement,銀行對帳單,
Bank Statement Settings,銀行對賬單設置,
Bank Statement balance as per General Ledger,銀行對賬單餘額按總帳,
Bank account cannot be named as {0},銀行賬戶不能命名為{0},
Bank/Cash transactions against party or for internal transfer,銀行/現金對一方或內部轉讓交易,
Banking,銀行業,
Banking and Payments,銀行和支付,
Barcode {0} already used in Item {1},條碼{0}已經用在項目{1},
Barcode {0} is not a valid {1} code,條形碼{0}不是有效的{1}代碼,
Base,基礎,
Base URL,基本網址,
Based On,基於,
Based On Payment Terms,根據付款條款,
Basic,基本的,
Batch,批量,
Batch Entries,批量條目,
Batch ID is mandatory,批號是必需的,
Batch Inventory,批量庫存,
Batch Name,批名稱,
Batch No,批號,
Batch number is mandatory for Item {0},批號是強制性的項目{0},
Batch {0} of Item {1} has expired.,一批項目的{0} {1}已過期。,
Batch {0} of Item {1} is disabled.,項目{1}的批處理{0}已禁用。,
Batch: ,批量：,
Become a Seller,成為賣家,
Beginner,初學者,
Bill,法案,
Bill Date,帳單日期,
Bill No,帳單號碼,
Bill of Materials,材料清單,
Bill of Materials (BOM),材料清單（BOM）,
Billable Hours,可開票時間,
Billed,計費,
Billed Amount,帳單金額,
Billing,計費,
Billing Address,帳單地址,
Billing Address is same as Shipping Address,帳單地址與送貨地址相同,
Billing Amount,開票金額,
Billing Status,計費狀態,
Billing currency must be equal to either default company's currency or party account currency,帳單貨幣必須等於默認公司的貨幣或帳戶幣種,
Bills raised by Suppliers.,由供應商提出的帳單。,
Bills raised to Customers.,客戶提出的賬單。,
Biotechnology,生物技術,
Black,黑色,
Blanket Orders from Costumers.,來自客戶的毯子訂單。,
Block Invoice,阻止發票,
Boms,物料清單,
Bonus Payment Date cannot be a past date,獎金支付日期不能是過去的日期,
Both Trial Period Start Date and Trial Period End Date must be set,必須設置試用期開始日期和試用期結束日期,
Both Warehouse must belong to same Company,這兩個倉庫必須屬於同一個公司,
Branch,分支機構,
Broadcasting,廣播,
Browse BOM,瀏覽BOM,
Budget Against,反對財政預算案,
Budget List,預算清單,
Budget Variance Report,預算差異報告,
Budget cannot be assigned against Group Account {0},不能指定預算給群組帳目{0},
"Budget cannot be assigned against {0}, as it's not an Income or Expense account",財政預算案不能對{0}指定的，因為它不是一個收入或支出帳戶,
Bundle items at time of sale.,在銷售時捆綁項目。,
Business Development Manager,業務發展經理,
Buy,購買,
Buying,採購,
Buying Amount,購買金額,
Buying Price List,買價格表,
Buying Rate,購買率,
"Buying must be checked, if Applicable For is selected as {0}",採購必須進行檢查，如果適用於被選擇為{0},
Bypass credit check at Sales Order ,在銷售訂單旁通過信用檢查,
C-Form records,C-往績紀錄,
C-form is not applicable for Invoice: {0},C-形式不適用發票：{0},
CESS Amount,CESS金額,
CGST Amount,CGST金額,
CWIP Account,CWIP賬戶,
Calculated Bank Statement balance,計算的銀行對賬單餘額,
Calls,電話,
Campaign,競賽,
Can be approved by {0},可以通過{0}的批准,
"Can not filter based on Account, if grouped by Account",7 。總計：累積總數達到了這一點。,
"Can not filter based on Voucher No, if grouped by Voucher",是冷凍的帳戶。要禁止該帳戶創建/編輯事務，你需要角色,
"Can not mark Inpatient Record Discharged, there are Unbilled Invoices {0}",無法標記出院的住院病歷，有未開單的發票{0},
Can only make payment against unbilled {0},只能使支付對未付款的{0},
Can refer row only if the charge type is 'On Previous Row Amount' or 'Previous Row Total',可以參考的行只有在充電類型是“在上一行量'或'前行總計”,
"Can't change valuation method, as there are transactions against some items which does not have it's own valuation method",不能改變估值方法，因為有一些項目沒有自己的估值方法的交易,
Can't create standard criteria. Please rename the criteria,無法創建標準條件。請重命名標準,
Cancel Material Visit {0} before cancelling this Warranty Claim,材質訪問{0}之前取消此保修索賠取消,
Cancel Material Visits {0} before cancelling this Maintenance Visit,取消取消此保養訪問之前，材質訪問{0},
Cancel Subscription,取消訂閱,
Cancel the journal entry {0} first,首先取消日記條目{0},
"Cannot Submit, Employees left to mark attendance",無法提交，僱員留下來標記出席,
Cannot be a fixed asset item as Stock Ledger is created.,不能成為股票分類賬創建的固定資產項目。,
Cannot cancel because submitted Stock Entry {0} exists,不能取消，因為提交股票輸入{0}存在,
Cannot cancel transaction for Completed Work Order.,無法取消已完成工單的交易。,
Cannot cancel {0} {1} because Serial No {2} does not belong to the warehouse {3},無法取消{0} {1}，因為序列號{2}不屬於倉庫{3},
Cannot change Attributes after stock transaction. Make a new Item and transfer stock to the new Item,股票交易後不能更改屬性。創建一個新項目並將庫存轉移到新項目,
Cannot change Fiscal Year Start Date and Fiscal Year End Date once the Fiscal Year is saved.,不能更改財政年度開始日期和財政年度結束日期，一旦會計年度被保存。,
Cannot change Service Stop Date for item in row {0},無法更改行{0}中項目的服務停止日期,
Cannot change Variant properties after stock transaction. You will have to make a new Item to do this.,股票交易後不能更改Variant屬性。你將不得不做一個新的項目來做到這一點。,
"Cannot change company's default currency, because there are existing transactions. Transactions must be cancelled to change the default currency.",不能改變公司的預設貨幣，因為有存在的交易。交易必須取消更改預設貨幣。,
Cannot change status as student {0} is linked with student application {1},無法改變地位的學生{0}與學生申請鏈接{1},
Cannot convert Cost Center to ledger as it has child nodes,不能成本中心轉換為總賬，因為它有子節點,
Cannot covert to Group because Account Type is selected.,不能隱蔽到組，因為帳戶類型選擇的。,
Cannot create Retention Bonus for left Employees,無法為左員工創建保留獎金,
Cannot create a Delivery Trip from Draft documents.,無法從草稿文檔創建交貨旅行。,
Cannot deactivate or cancel BOM as it is linked with other BOMs,無法關閉或取消BOM，因為它是與其他材料明細表鏈接,
"Cannot declare as lost, because Quotation has been made.",不能聲明為丟失，因為報價已經取得進展。,
Cannot deduct when category is for 'Valuation' or 'Valuation and Total',不能抵扣當類別為“估值”或“估值及總',
Cannot deduct when category is for 'Valuation' or 'Vaulation and Total',當類是“估值”或“Vaulation和總&#39;不能扣除,
"Cannot delete Serial No {0}, as it is used in stock transactions",無法刪除序列號{0}，因為它採用的是現貨交易,
Cannot enroll more than {0} students for this student group.,不能註冊超過{0}學生該學生群體更多。,
Cannot find active Leave Period,找不到有效的休假期,
Cannot produce more Item {0} than Sales Order quantity {1},無法產生更多的項目{0}不是銷售訂單數量{1},
Cannot promote Employee with status Left,無法提升狀態為Left的員工,
Cannot refer row number greater than or equal to current row number for this Charge type,不能引用的行號大於或等於當前行號碼提供給充電式,
Cannot select charge type as 'On Previous Row Amount' or 'On Previous Row Total' for first row,不能選擇充電式為'在上一行量'或'在上一行總'的第一行,
Cannot set as Lost as Sales Order is made.,不能設置為失落的銷售訂單而成。,
Cannot set authorization on basis of Discount for {0},不能在折扣的基礎上設置授權{0},
Cannot set multiple Item Defaults for a company.,無法為公司設置多個項目默認值。,
Cannot set quantity less than delivered quantity,無法設定數量小於交貨數量,
Cannot set quantity less than received quantity,無法設置小於收貨數量的數量,
Cannot set the field <b>{0}</b> for copying in variants,無法將字段<b>{0}設置</b>為在變體中進行複制,
Cannot transfer Employee with status Left,無法轉移狀態為左的員工,
Cannot {0} {1} {2} without any negative outstanding invoice,無法{0} {1} {2}沒有任何負面的優秀發票,
Capital Equipments,資本設備,
Capital Work in Progress,資本工作正在進行中,
Cart,車,
Cart is Empty,車是空的,
Case No(s) already in use. Try from Case No {0},案例編號已在使用中（ S） 。從案例沒有嘗試{0},
Cash,現金,
Cash Flow Statement,現金流量表,
Cash Flow from Financing,從融資現金流,
Cash Flow from Investing,從投資現金流,
Cash Flow from Operations,運營現金流,
Cash In Hand,手頭現金,
Cash or Bank Account is mandatory for making payment entry,製作付款分錄時，現金或銀行帳戶是強制性輸入的欄位。,
Cashier Closing,收銀員關閉,
Casual Leave,休假,
Category,類別,
Category Name,分類名稱,
Central Tax,中央稅,
Certification,證明,
Change Amount,漲跌額,
Change Item Code,更改物料代碼,
Change Release Date,更改發布日期,
Change Template Code,更改模板代碼,
Changing Customer Group for the selected Customer is not allowed.,不允許更改所選客戶的客戶組。,
Chapter,章節,
Chapter information.,章節信息。,
Charge of type 'Actual' in row {0} cannot be included in Item Rate,類型'實際'行{0}的計費，不能被包含在項目單價,
Charges are updated in Purchase Receipt against each item,費用對在採購入庫單內的每個項目更新,
"Charges will be distributed proportionately based on item qty or amount, as per your selection",費用將被分配比例根據項目數量或金額，按您的選擇,
Chart of Cost Centers,成本中心的圖,
Check all,全面檢查,
Checkout,查看,
Chemical,化學藥品,
Cheque,校驗,
Cheque/Reference No,支票/參考編號,
Cheques Required,需要檢查,
Cheques and Deposits incorrectly cleared,支票及存款不正確清除,
Child Task exists for this Task. You can not delete this Task.,子任務存在這個任務。你不能刪除這個任務。,
Child nodes can be only created under 'Group' type nodes,子節點可以在&#39;集團&#39;類型的節點上創建,
Child warehouse exists for this warehouse. You can not delete this warehouse.,兒童倉庫存在這個倉庫。您不能刪除這個倉庫。,
Circular Reference Error,循環引用錯誤,
City,市,
City/Town,市/鎮,
Claimed Amount,聲明金額,
Clear filters,清除過濾器,
Clear values,清晰的價值觀,
Clearance Date,清拆日期,
Clearance Date not mentioned,清拆日期未提及,
Clearance Date updated,間隙更新日期,
Client,客戶,
Client ID,客戶端ID,
Client Secret,客戶秘密,
Clinical Procedure,臨床程序,
Clinical Procedure Template,臨床步驟模板,
Close Balance Sheet and book Profit or Loss.,關閉資產負債表和賬面利潤或虧損。,
Close Loan,關閉貸款,
Close the POS,關閉POS,
Closed,關閉,
Closed order cannot be cancelled. Unclose to cancel.,關閉的定單不能被取消。 Unclose取消。,
Closing (Cr),關閉（Cr）,
Closing (Dr),關閉（Dr）,
Closing (Opening + Total),閉幕（開幕+總計）,
Closing Account {0} must be of type Liability / Equity,關閉帳戶{0}的類型必須是負債/權益,
Closing Balance,結算餘額,
Code,源碼,
Collapse All,全部收縮,
Color,顏色,
Colour,顏色,
Combined invoice portion must equal 100%,合併發票部分必須等於100％,
Commercial,商業,
Commission on Sales,銷售佣金,
Commission rate cannot be greater than 100,佣金比率不能大於100,
Community Forum,社區論壇,
Company (not Customer or Supplier) master.,公司（不是客戶或供應商）的主人。,
Company Abbreviation,公司縮寫,
Company Abbreviation cannot have more than 5 characters,公司縮寫不能超過5個字符,
Company Name,公司名稱,
Company Name cannot be Company,公司名稱不能為公司,
Company currencies of both the companies should match for Inter Company Transactions.,兩家公司的公司貨幣應該符合Inter公司交易。,
Company is manadatory for company account,公司是公司賬戶的管理者,
Company name not same,公司名稱不一樣,
Compensatory Off,補假,
Compensatory leave request days not in valid holidays,補休請求天不在有效假期,
Computer,電腦,
Condition,條件,
Confirmed orders from Customers.,確認客戶的訂單。,
Connect Amazon with ERPNext,將Amazon與ERPNext連接起來,
Connect Shopify with ERPNext,將Shopify與ERPNext連接,
Connect to Quickbooks,連接到Quickbooks,
Connected to QuickBooks,連接到QuickBooks,
Connecting to QuickBooks,連接到QuickBooks,
Consultation,會診,
Consulting,諮詢,
Consumed,消費,
Consumed Amount,消費金額,
Consumed Qty,消耗的數量,
Consumer Products,消費類產品,
Contact,聯絡人,
Contact Details,聯絡方式,
Contact Number,聯繫電話,
Contact Us,聯繫我們,
Content,內容,
Content Masters,內容大師,
Content Type,內容類型,
Continue Configuration,繼續配置,
Contract End Date must be greater than Date of Joining,合同結束日期必須大於加入的日期,
Contribution %,貢獻％,
Contribution Amount,貢獻金額,
Conversion factor for default Unit of Measure must be 1 in row {0},預設計量單位的轉換因子必須是1在行{0},
Conversion rate cannot be 0 or 1,轉化率不能為0或1,
Convert to Group,轉換為集團,
Convert to Non-Group,轉換為非集團,
Cosmetics,化妝品,
Cost Center Number,成本中心編號,
Cost Center and Budgeting,成本中心和預算編制,
Cost Center is required in row {0} in Taxes table for type {1},成本中心是必需的行{0}稅表型{1},
Cost Center with existing transactions can not be converted to group,與現有的交易成本中心，不能轉化為組,
Cost Center with existing transactions can not be converted to ledger,與現有的交易成本中心，不能轉換為總賬,
Cost as on,成本一樣,
Cost of Delivered Items,交付項目成本,
Cost of Goods Sold,銷貨成本,
Cost of Issued Items,發布項目成本,
Cost of New Purchase,新的採購成本,
Cost of Purchased Items,購買的物品成本,
Cost of Scrapped Asset,報廢資產成本,
Cost of Sold Asset,出售資產的成本,
Cost of various activities,各種活動的費用,
"Could not create Credit Note automatically, please uncheck 'Issue Credit Note' and submit again",無法自動創建Credit Note，請取消選中&#39;Issue Credit Note&#39;並再次提交,
Could not generate Secret,無法生成秘密,
Could not retrieve information for {0}.,無法檢索{0}的信息。,
Could not solve criteria score function for {0}. Make sure the formula is valid.,無法解決{0}的標準分數函數。確保公式有效。,
Could not solve weighted score function. Make sure the formula is valid.,無法解決加權分數函數。確保公式有效。,
Could not submit some Salary Slips,無法提交一些薪資單,
"Could not update stock, invoice contains drop shipping item.",無法更新庫存，發票包含下降航運項目。,
Country wise default Address Templates,依據國家別啟發式的預設地址模板,
Course,課程,
Course Code: ,課程編號：,
Course Enrollment {0} does not exists,課程註冊{0}不存在,
Course Schedule,課程表,
Course: ,課程：,
Cr,鉻,
Create,建立,
Create BOM,創建BOM,
Create Delivery Trip,創建送貨之旅,
Create Disbursement Entry,創建支付條目,
Create Employee,創建員工,
Create Employee Records,建立員工檔案,
"Create Employee records to manage leaves, expense claims and payroll",建立員工檔案管理葉，報銷和工資,
Create Fee Schedule,創建收費表,
Create Fees,創造費用,
Create Inter Company Journal Entry,創建國際公司日記帳分錄,
Create Invoice,創建發票,
Create Invoices,創建發票,
Create Job Card,創建工作卡,
Create Journal Entry,創建日記帳分錄,
Create Lead,創造領導力,
Create Leads,建立潛在客戶,
Create Maintenance Visit,創建維護訪問,
Create Material Request,創建物料申請,
Create Multiple,創建多個,
Create Opening Sales and Purchase Invoices,創建開倉銷售和採購發票,
Create Payment Entries,創建付款條目,
Create Payment Entry,創建付款條目,
Create Print Format,創建打印格式,
Create Purchase Order,創建採購訂單,
Create Purchase Orders,創建採購訂單,
Create Quotation,建立報價,
Create Salary Slip,建立工資單,
Create Salary Slips,創建工資單,
Create Sales Invoice,創建銷售發票,
Create Sales Order,創建銷售訂單,
Create Sales Orders to help you plan your work and deliver on-time,創建銷售訂單以幫助您規劃工作並按時交付,
Create Sample Retention Stock Entry,創建樣本保留庫存條目,
Create Student,創建學生,
Create Student Batch,創建學生批處理,
Create Student Groups,創建挺起胸,
Create Supplier Quotation,創建供應商報價,
Create Tax Template,創建稅務模板,
Create Timesheet,創建時間表,
Create User,創建用戶,
Create Users,創建用戶,
Create Variant,創建變體,
Create Variants,創建變體,
"Create and manage daily, weekly and monthly email digests.",建立和管理每日，每週和每月的電子郵件摘要。,
Create customer quotes,創建客戶報價,
Create rules to restrict transactions based on values.,創建規則來限制基於價值的交易。,
Created {0} scorecards for {1} between: ,為{1}創建{0}記分卡：,
Creating Company and Importing Chart of Accounts,創建公司並導入會計科目表,
Creating Fees,創造費用,
Creating Payment Entries......,創建支付條目......,
Creating Salary Slips...,創建工資單......,
Creating student groups,創建學生組,
Creating {0} Invoice,創建{0}發票,
Credit,信用,
Credit Account,信用賬戶,
Credit Balance,貸方餘額,
Credit Days cannot be a negative number,信用日不能是負數,
Credit Limit,信用額度,
Credit Note,信用票據,
Credit Note Amount,信用額度,
Credit Note Issued,信用票據發行,
Credit Note {0} has been created automatically,信用票據{0}已自動創建,
Credit limit has been crossed for customer {0} ({1}/{2}),客戶{0}（{1} / {2}）的信用額度已超過,
Creditors,債權人,
Criteria weights must add up to 100%,標準重量必須達100％,
Crop Cycle,作物週期,
Crops & Lands,農作物和土地,
Currency Exchange must be applicable for Buying or for Selling.,貨幣兌換必須適用於買入或賣出。,
Currency can not be changed after making entries using some other currency,貨幣不能使用其他貨幣進行輸入後更改,
Currency exchange rate master.,貨幣匯率的主人。,
Currency for {0} must be {1},貨幣{0}必須{1},
Currency is required for Price List {0},價格表{0}需填入貨幣種類,
Currency of the Closing Account must be {0},在關閉帳戶的貨幣必須是{0},
Currency of the price list {0} must be {1} or {2},價目表{0}的貨幣必須是{1}或{2},
Currency should be same as Price List Currency: {0},貨幣應與價目表貨幣相同：{0},
Current,當前,
Current Assets,流動資產,
Current BOM and New BOM can not be same,當前BOM和新BOM不能相同,
Current Job Openings,當前職位空缺,
Current Liabilities,流動負債,
Current Qty,目前數量,
Current invoice {0} is missing,當前發票{0}缺失,
Custom HTML,自定義HTML,
Custom?,自定義？,
Customer,客戶,
Customer Addresses And Contacts,客戶的地址和聯絡方式,
Customer Contact,客戶聯絡,
Customer Database.,客戶數據庫。,
Customer Group,客戶群組,
Customer LPO,客戶LPO,
Customer LPO No.,客戶LPO號,
Customer Name,客戶名稱,
Customer POS Id,客戶POS ID,
Customer Service,顧客服務,
Customer and Supplier,客戶和供應商,
Customer is required,客戶是必需的,
Customer isn't enrolled in any Loyalty Program,客戶未加入任何忠誠度計劃,
Customer required for 'Customerwise Discount',需要' Customerwise折扣“客戶,
Customer {0} does not belong to project {1},客戶{0}不屬於項目{1},
Customer {0} is created.,客戶{0}已創建。,
Customers in Queue,在排隊的客戶,
Customize Homepage Sections,自定義主頁部分,
Customizing Forms,自定義表單,
Daily Project Summary for {0},{0}的每日項目摘要,
Daily Work Summary,每日工作總結,
Daily Work Summary Group,日常工作總結小組,
Data Import and Export,資料輸入和輸出,
Data Import and Settings,數據導入和設置,
Database of potential customers.,數據庫的潛在客戶。,
Date Of Retirement must be greater than Date of Joining,日期退休必須大於加入的日期,
Date is repeated,日期重複,
Date of Birth cannot be greater than today.,出生日期不能大於今天。,
Date of Commencement should be greater than Date of Incorporation,開始日期應大於公司註冊日期,
Date of Joining,加入日期,
Date of Joining must be greater than Date of Birth,加入日期必須大於出生日期,
Datetime,日期時間,
Debit ({0}),借記卡（{0}）,
Debit A/C Number,借記A / C號碼,
Debit Account,借方賬戶,
Debit Note,繳費單,
Debit Note Amount,借方票據金額,
Debit Note Issued,借記發行說明,
Debit To is required,借方是必填項,
Debit and Credit not equal for {0} #{1}. Difference is {2}.,借貸{0}＃不等於{1}。區別是{2}。,
Debtors,債務人,
Debtors ({0}),債務人（{0}）,
Default Activity Cost exists for Activity Type - {0},默認情況下存在作業成本的活動類型 -  {0},
Default BOM ({0}) must be active for this item or its template,預設BOM（{0}）必須是活動的這個項目或者其模板,
Default BOM for {0} not found,默認BOM {0}未找到,
Default BOM not found for Item {0} and Project {1},項目{0}和項目{1}找不到默認BOM,
Default Letter Head,預設信頭,
Default Tax Template,默認稅收模板,
Default Unit of Measure for Item {0} cannot be changed directly because you have already made some transaction(s) with another UOM. You will need to create a new Item to use a different Default UOM.,測度項目的默認單位{0}不能直接改變，因為你已經做了一些交易（S）與其他計量單位。您將需要創建一個新的項目，以使用不同的默認計量單位。,
Default Unit of Measure for Variant '{0}' must be same as in Template '{1}',測度變異的默認單位“{0}”必須是相同模板“{1}”,
Default settings for buying transactions.,採購交易的預設設定。,
Default settings for selling transactions.,銷售交易的預設設定。,
Default tax templates for sales and purchase are created.,銷售和採購的默認稅收模板被創建。,
Defaults,預設,
Defense,防禦,
Define Project type.,定義項目類型。,
Define budget for a financial year.,定義預算財政年度。,
Define various loan types,定義不同的貸款類型,
Del,刪除,
Delay in payment (Days),延遲支付（天）,
Delete all the Transactions for this Company,刪除所有交易本公司,
Deletion is not permitted for country {0},國家{0}不允許刪除,
Delivered,交付,
Delivered Amount,交付金額,
Delivered Qty,交付數量,
Delivered: {0},交貨：{0},
Delivery,交貨,
Delivery Date,交貨日期,
Delivery Note,送貨單,
Delivery Note {0} is not submitted,送貨單{0}未提交,
Delivery Note {0} must not be submitted,送貨單{0}不能提交,
Delivery Notes {0} must be cancelled before cancelling this Sales Order,送貨單{0}必須先取消才能取消銷貨訂單,
Delivery Notes {0} updated,已更新交貨單{0},
Delivery Status,交貨狀態,
Delivery Trip,送貨之旅,
Delivery warehouse required for stock item {0},需要的庫存項目交割倉庫{0},
Department,部門,
Department Stores,百貨,
Depreciation,折舊,
Depreciation Amount,折舊額,
Depreciation Amount during the period,期間折舊額,
Depreciation Date,折舊日期,
Depreciation Eliminated due to disposal of assets,折舊淘汰因處置資產,
Depreciation Entry,折舊分錄,
Depreciation Method,折舊方法,
Depreciation Row {0}: Depreciation Start Date is entered as past date,折舊行{0}：折舊開始日期作為過去的日期輸入,
Depreciation Row {0}: Expected value after useful life must be greater than or equal to {1},折舊行{0}：使用壽命後的預期值必須大於或等於{1},
Depreciation Row {0}: Next Depreciation Date cannot be before Available-for-use Date,折舊行{0}：下一個折舊日期不能在可供使用的日期之前,
Depreciation Row {0}: Next Depreciation Date cannot be before Purchase Date,折舊行{0}：下一個折舊日期不能在購買日期之前,
Designer,設計師,
Detailed Reason,詳細原因,
Details,詳細資訊,
Details of Outward Supplies and inward supplies liable to reverse charge,向外供應和向內供應的詳細信息可能被撤銷費用,
Details of the operations carried out.,進行的作業細節。,
Diagnosis,診斷,
Did not find any item called {0},沒有找到所謂的任何項目{0},
Diff Qty,差異數量,
Difference Account,差異帳戶,
"Difference Account must be a Asset/Liability type account, since this Stock Reconciliation is an Opening Entry",差異帳戶必須是資產/負債類型的帳戶，因為此庫存調整是一個開始分錄,
Difference Amount,差額,
Difference Amount must be zero,差量必須是零,
Different UOM for items will lead to incorrect (Total) Net Weight value. Make sure that Net Weight of each item is in the same UOM.,不同計量單位的項目會導致不正確的（總）淨重值。確保每個項目的淨重是在同一個計量單位。,
Direct Expenses,直接費用,
Direct Income,直接收入,
Disable,關閉,
Disabled template must not be default template,殘疾人模板必須不能默認模板,
Disburse Loan,支付貸款,
Disc,圓盤,
Discharge,卸貨,
Discount Percentage can be applied either against a Price List or for all Price List.,折扣百分比可以應用於單一價目表或所有價目表。,
Discount must be less than 100,折扣必須小於100,
Diseases & Fertilizers,疾病與肥料,
Dispatch,調度,
Dispatch Notification,發貨通知,
Dispatch State,派遣國,
Distance,距離,
Distribution,分配,
Distributor,經銷商,
Dividends Paid,支付的股息,
Do you really want to restore this scrapped asset?,難道你真的想恢復這個報廢的資產？,
Do you really want to scrap this asset?,難道你真的想放棄這項資產？,
Do you want to notify all the customers by email?,你想通過電子郵件通知所有的客戶？,
Doc Date,文件日期,
Doc Name,文件名稱,
Doc Type,文件類型,
Docs Search,Google文檔搜索,
Document Name,文件名稱,
Document Status,文檔狀態,
Document Type,文件類型,
Domain,網域,
Done,完成,
Donor,捐贈者,
Donor Type information.,捐助者類型信息。,
Download JSON,下載JSON,
Drop Ship,直接發運給客戶,
Drug,藥物,
Due / Reference Date cannot be after {0},由於/參考日期不能後{0},
Due Date cannot be before Posting / Supplier Invoice Date,截止日期不能在過帳/供應商發票日期之前,
Due Date is mandatory,截止日期是強制性的,
Duplicate Entry. Please check Authorization Rule {0},重複的條目。請檢查授權規則{0},
Duplicate Serial No entered for Item {0},重複的序列號輸入的項目{0},
Duplicate customer group found in the cutomer group table,在CUTOMER組表中找到重複的客戶群,
Duplicate entry,重複的條目,
Duplicate item group found in the item group table,在項目組表中找到重複的項目組,
Duplicate roll number for student {0},學生{0}的重複卷號,
Duplicate row {0} with same {1},重複的行{0}同{1},
Duplicate {0} found in the table,表中找到重複的{0},
Duration in Days,持續時間天數,
Duties and Taxes,關稅和稅款,
E-Invoicing Information Missing,電子發票信息丟失,
ERPNext Settings,ERP下載設置,
Earnest Money,保證金,
Earning,盈利,
Edit,編輯,
Edit Publishing Details,編輯發布細節,
"Edit in full page for more options like assets, serial nos, batches etc.",在整頁上編輯更多選項，如資產，序列號，批次等。,
Either location or employee must be required,必須要求地點或員工,
Either target qty or target amount is mandatory,無論是數量目標或目標量是必需的,
Either target qty or target amount is mandatory.,無論是數量目標或目標量是強制性的。,
Electrical,電子的,
Electronic Equipments,電子設備,
Electronics,電子,
Eligible ITC,符合條件的ITC,
Email Account,電子郵件帳戶,
Email Address,電子郵件地址,
"Email Address must be unique, already exists for {0}",電子郵件地址必須是唯一的，已經存在了{0},
Email Digest: ,電子郵件摘要：,
Email Reminders will be sent to all parties with email contacts,電子郵件提醒將通過電子郵件聯繫方式發送給各方,
Email Sent,郵件發送,
Email Template,電子郵件模板,
Email not found in default contact,在默認聯繫人中找不到電子郵件,
Email sent to {0},電子郵件發送到{0},
Employee,僱員,
Employee A/C Number,員工賬號,
Employee Advances,員工發展,
Employee Benefits,員工福利,
Employee Grade,員工等級,
Employee ID,員工ID,
Employee Lifecycle,員工生命週期,
Employee Name,員工姓名,
Employee Promotion cannot be submitted before Promotion Date ,員工晉升不能在晉升日期前提交,
Employee Referral,員工推薦,
Employee Transfer cannot be submitted before Transfer Date ,員工轉移無法在轉移日期前提交,
Employee cannot report to himself.,員工不能報告自己。,
Employee relieved on {0} must be set as 'Left',員工解除對{0}必須設定為“左”,
Employee {0} already submited an apllication {1} for the payroll period {2},員工{0}已經在工資期間{2}提交了申請{1},
Employee {0} has already applied for {1} between {2} and {3} : ,員工{0}已在{2}和{3}之間申請{1}：,
Employee {0} has no maximum benefit amount,員工{0}沒有最大福利金額,
Employee {0} is not active or does not exist,員工{0}不活躍或不存在,
Employee {0} is on Leave on {1},員工{0}暫停{1},
Employee {0} of grade {1} have no default leave policy,{1}級員工{0}沒有默認離開政策,
Employee {0} on Half day on {1},員工{0}上半天{1},
Enable,啟用,
Enable / disable currencies.,啟用／禁用的貨幣。,
Enabled,啟用,
"Enabling 'Use for Shopping Cart', as Shopping Cart is enabled and there should be at least one Tax Rule for Shopping Cart",作為啟用的購物車已啟用“使用購物車”，而應該有購物車至少有一個稅務規則,
End Date,結束日期,
End Date can not be less than Start Date,結束日期不能小於開始日期,
End Date cannot be before Start Date.,結束日期不能在開始日期之前。,
End Year,結束年份,
End Year cannot be before Start Year,結束年份不能啟動年前,
End on,結束,
End time cannot be before start time,結束時間不能在開始時間之前,
Ends On date cannot be before Next Contact Date.,結束日期不能在下一次聯繫日期之前。,
Engineer,工程師,
Enough Parts to Build,足夠的配件組裝,
Enroll,註冊,
Enrolling student,招生學生,
Enrolling students,招收學生,
Enter depreciation details,輸入折舊明細,
Enter the Bank Guarantee Number before submittting.,在提交之前輸入銀行保證號碼。,
Enter the name of the Beneficiary before submittting.,在提交之前輸入受益人的姓名。,
Enter the name of the bank or lending institution before submittting.,在提交之前輸入銀行或貸款機構的名稱。,
Enter value betweeen {0} and {1},輸入{0}和{1}之間的值,
Entertainment & Leisure,娛樂休閒,
Entertainment Expenses,娛樂費用,
Equity,公平,
Error Log,錯誤日誌,
Error evaluating the criteria formula,評估標準公式時出錯,
Error in formula or condition: {0},誤差在式或條件：{0},
Error: Not a valid id?,錯誤：沒有有效的身份證？,
Estimated Cost,估計成本,
Evaluation,評估,
"Even if there are multiple Pricing Rules with highest priority, then following internal priorities are applied:",即使有更高優先級的多個定價規則，然後按照內部優先級應用：,
Event Location,活動地點,
Event Name,事件名稱,
Exchange Gain/Loss,兌換收益/損失,
Exchange Rate Revaluation master.,匯率重估主數據。,
Exchange Rate must be same as {0} {1} ({2}),匯率必須一致{0} {1}（{2}）,
Excise Invoice,消費稅發票,
Execution,執行,
Executive Search,獵頭,
Expand All,展開全部,
Expected Delivery Date,預計交貨日期,
Expected Delivery Date should be after Sales Order Date,預計交貨日期應在銷售訂單日期之後,
Expected End Date,預計結束日期,
Expected Hrs,預計的小時數,
Expected Start Date,預計開始日期,
Expense,費用,
Expense / Difference account ({0}) must be a 'Profit or Loss' account,費用/差異帳戶（{0}）必須是一個'溢利或虧損的帳戶,
Expense Account,費用帳戶,
Expense Claim,報銷,
Expense Claim for Vehicle Log {0},報銷車輛登錄{0},
Expense Claim {0} already exists for the Vehicle Log,報銷{0}已經存在車輛日誌,
Expense Claims,報銷,
Expense account is mandatory for item {0},交際費是強制性的項目{0},
Expenses,開支,
Expenses Included In Asset Valuation,資產評估中包含的費用,
Expenses Included In Valuation,支出計入估值,
Expired Batches,過期批次,
Expiring On,即將到期,
Expiry (In Days),到期（天數）,
Export E-Invoices,出口電子發票,
Extra Large,特大號,
Fail,失敗,
Failed,失敗,
Failed to create website,無法創建網站,
Failed to install presets,無法安裝預設,
Failed to login,登錄失敗,
Failed to setup company,未能成立公司,
Failed to setup defaults,無法設置默認值,
Failed to setup post company fixtures,未能設置公司固定裝置,
Fax,傳真,
Fee,費用,
Fee Created,創建費用,
Fee Creation Failed,費用創作失敗,
Fee Creation Pending,費用創作待定,
Fee Records Created - {0},費紀錄創造 -  {0},
Feedback,反饋,
Fees,費用,
Fetch Data,獲取數據,
Fetch Subscription Updates,獲取訂閱更新,
Fetch exploded BOM (including sub-assemblies),取得爆炸BOM（包括子組件）,
Fetching records......,獲取記錄......,
Field Name,欄位名稱,
Fieldname,欄位名稱,
Fields,欄位,
Fill the form and save it,填寫表格，並將其保存,
Filter Employees By (Optional),過濾員工（可選）,
"Filter Fields Row #{0}: Fieldname <b>{1}</b> must be of type ""Link"" or ""Table MultiSelect""",篩選字段行＃{0}：字段名<b>{1}</b>必須是“鏈接”或“表格MultiSelect”類型,
Filter Total Zero Qty,過濾器總計零數量,
Finance Book,金融書,
Financial / accounting year.,財務/會計年度。,
Financial Services,金融服務,
Financial Statements,財務報表,
Financial Year,財政年度,
Finish,完,
Finished Good Item Code,成品商品代碼,
Finished Goods,完成品,
Finished Item {0} must be entered for Manufacture type entry,完成項目{0}必須為製造類條目進入,
Finished product quantity <b>{0}</b> and For Quantity <b>{1}</b> cannot be different,成品數量<b>{0}</b>和數量<b>{1}</b>不能不同,
First Name,名字,
"Fiscal Regime is mandatory, kindly set the fiscal regime in the company {0}",財政制度是強制性的，請在公司{0}設定財政制度,
Fiscal Year,財政年度,
Fiscal Year End Date should be one year after Fiscal Year Start Date,會計年度結束日期應為會計年度開始日期後一年,
Fiscal Year Start Date and Fiscal Year End Date are already set in Fiscal Year {0},會計年度開始日期和財政年度結束日期已經在財政年度設置{0},
Fiscal Year Start Date should be one year earlier than Fiscal Year End Date,會計年度開始日期應比會計年度結束日期提前一年,
Fiscal Year {0} does not exist,會計年度{0}不存在,
Fiscal Year {0} is required,會計年度{0}是必需的,
Fiscal Year {0} not found,會計年度{0}未找到,
Fixed Asset,固定資產,
Fixed Asset Item must be a non-stock item.,固定資產項目必須是一個非庫存項目。,
Fixed Assets,固定資產,
Following Material Requests have been raised automatically based on Item's re-order level,下列資料的要求已自動根據項目的重新排序水平的提高,
Following accounts might be selected in GST Settings:,以下帳戶可能在GST設置中選擇：,
Following course schedules were created,按照課程時間表創建,
Following item {0} is not marked as {1} item. You can enable them as {1} item from its Item master,項目{0}之後未標記為{1}項目。您可以從項目主文件中將它們作為{1}項啟用,
Following items {0} are not marked as {1} item. You can enable them as {1} item from its Item master,以下項{0}未標記為{1}項。您可以從項目主文件中將它們作為{1}項啟用,
Food,食物,
"Food, Beverage & Tobacco",食品、飲料＆煙草,
For,對於,
"For 'Product Bundle' items, Warehouse, Serial No and Batch No will be considered from the 'Packing List' table. If Warehouse and Batch No are same for all packing items for any 'Product Bundle' item, those values can be entered in the main Item table, values will be copied to 'Packing List' table.",對於“產品包”的物品，倉庫，序列號和批號將被從“裝箱單”表考慮。如果倉庫和批次號是相同的任何“產品包”項目的所有包裝物品，這些值可以在主項表中輸入，值將被複製到“裝箱單”表。,
For Employee,對於員工,
For Quantity (Manufactured Qty) is mandatory,對於數量（製造數量）是強制性的,
For Supplier,對供應商,
For Warehouse,對於倉庫,
For Warehouse is required before Submit,對於倉庫之前，需要提交,
"For an item {0}, quantity must be negative number",對於商品{0}，數量必須是負數,
"For an item {0}, quantity must be positive number",對於商品{0}，數量必須是正數,
"For job card {0}, you can only make the 'Material Transfer for Manufacture' type stock entry",對於作業卡{0}，您只能進行“製造材料轉移”類型庫存條目,
"For row {0} in {1}. To include {2} in Item rate, rows {3} must also be included",對於行{0} {1}。以包括{2}中的檔案速率，行{3}也必須包括,
For row {0}: Enter Planned Qty,對於行{0}：輸入計劃的數量,
"For {0}, only credit accounts can be linked against another debit entry",{0}，只有貸方帳戶可以連接另一個借方分錄,
"For {0}, only debit accounts can be linked against another credit entry",{0}，只有借方帳戶可以連接另一個貸方分錄,
Forum Activity,論壇活動,
Free item code is not selected,未選擇免費商品代碼,
Freight and Forwarding Charges,貨運代理費,
Frequency,頻率,
From,從,
From Address 1,來自地址1,
From Address 2,來自地址2,
From Currency and To Currency cannot be same,原始貨幣和目標貨幣不能相同,
From Date and To Date lie in different Fiscal Year,從日期和到期日位於不同的財政年度,
From Date cannot be greater than To Date,起始日期不能大於結束日期,
From Date must be before To Date,起始日期必須早於終點日期,
From Date should be within the Fiscal Year. Assuming From Date = {0},從日期應該是在財政年度內。假設起始日期＝{0},
From Date {0} cannot be after employee's relieving Date {1},起始日期{0}不能在員工解除日期之後{1},
From Date {0} cannot be before employee's joining Date {1},起始日期{0}不能在員工加入日期之前{1},
From Datetime,從日期時間,
From Delivery Note,從送貨單,
From Fiscal Year,從財政年度開始,
From GSTIN,來自GSTIN,
From Party Name,來自黨名,
From Pin Code,來自Pin Code,
From Place,從地方,
From Range has to be less than To Range,從範圍必須小於要範圍,
From State,來自州,
From Time,從時間,
From Time Should Be Less Than To Time,從時間應該少於時間,
From Time cannot be greater than To Time.,從時間不能超過結束時間大。,
"From a supplier under composition scheme, Exempt and Nil rated",來自組成計劃下的供應商，免稅和零評級,
From and To dates required,需要起始和到達日期,
From date can not be less than employee's joining date,起始日期不得少於員工的加入日期,
From value must be less than to value in row {0},來源值必須小於列{0}的值,
From {0} | {1} {2},從{0} | {1} {2},
Fuel Price,燃油價格,
Fuel Qty,燃油數量,
Fulfillment,履行,
Full-time,全日制,
Fully Depreciated,已提足折舊,
Furnitures and Fixtures,家具及固定裝置,
"Further accounts can be made under Groups, but entries can be made against non-Groups",進一步帳戶可以根據組進行，但條目可針對非組進行,
Further cost centers can be made under Groups but entries can be made against non-Groups,進一步的成本中心可以根據組進行，但項可以對非組進行,
Further nodes can be only created under 'Group' type nodes,此外節點可以在&#39;集團&#39;類型的節點上創建,
Future dates not allowed,未來的日期不允許,
Gain/Loss on Asset Disposal,在資產處置收益/損失,
Gantt Chart,甘特圖,
Gantt chart of all tasks.,所有任務的甘特圖。,
Gender,性別,
General,一般,
General Ledger,總帳,
Generate Material Requests (MRP) and Work Orders.,生成材料請求（MRP）和工作訂單。,
Get Details From Declaration,從宣言中獲取細節,
Get Employees,獲得員工,
Get Invocies,獲取Invocies,
Get Invoices,獲取發票,
Get Invoices based on Filters,根據過濾器獲取發票,
Get Items from BOM,從物料清單取得項目,
Get Items from Healthcare Services,從醫療保健服務獲取項目,
Get Items from Prescriptions,從Prescriptions獲取物品,
Get Items from Product Bundle,從產品包取得項目,
Get Suppliers,獲取供應商,
Get Suppliers By,獲得供應商,
Get Updates,獲取更新,
Get customers from,從中獲取客戶,
Get from Patient Encounter,從患者遭遇中獲取,
Getting Started,入門,
Global settings for all manufacturing processes.,所有製造過程中的全域設定。,
Go to the Desktop and start using ERPNext,轉到桌面和開始使用ERPNext,
GoCardless SEPA Mandate,GoCardless SEPA授權,
GoCardless payment gateway settings,GoCardless支付網關設置,
Goal and Procedure,目標和程序,
Goals cannot be empty,目標不能為空,
Goods In Transit,貨物正在運送中,
Goods Transferred,貨物轉移,
Goods and Services Tax (GST India),商品和服務稅（印度消費稅）,
Goods are already received against the outward entry {0},已收到針對外向條目{0}的貨物,
Grand Total,累計,
Grant,格蘭特,
Grant Application,授予申請,
Grant Leaves,格蘭特葉子,
Grocery,雜貨,
Gross Pay,工資總額,
Gross Profit %,毛利 ％,
Gross Profit / Loss,總利潤/虧損,
Gross Purchase Amount,總購買金額,
Gross Purchase Amount is mandatory,總消費金額是強制性,
Group by Account,以帳戶分群組,
Group by Party,按黨分組,
Group by Voucher,集團透過券,
Group by Voucher (Consolidated),按憑證分組（合併）,
Group node warehouse is not allowed to select for transactions,組節點倉庫不允許選擇用於交易,
Group to Non-Group,集團以非組,
Group your students in batches,一群學生在分批,
Groups,組,
Guardian1 Email ID,Guardian1電子郵件ID,
Guardian1 Mobile No,Guardian1手機號碼,
Guardian1 Name,Guardian1名稱,
Guardian2 Email ID,Guardian2電子郵件ID,
Guardian2 Mobile No,Guardian2手機號碼,
Guardian2 Name,Guardian2名稱,
Guest,客人,
HR Manager,人力資源經理,
Half Day Date is mandatory,半天日期是強制性的,
Half Day Date should be between From Date and To Date,半天時間應該是從之間的日期和終止日期,
Half Day Date should be in between Work From Date and Work End Date,半天日期應在工作日期和工作結束日期之間,
Half Yearly,每半年,
Half day date should be in between from date and to date,半天的日期應該在從日期到日期之間,
Half-Yearly,每半年一次,
Head of Marketing and Sales,營銷和銷售主管,
Health Care,保健,
Healthcare,衛生保健,
Healthcare (beta),醫療保健（beta）,
Healthcare Practitioner,醫療從業者,
Healthcare Practitioner not available on {0},醫療從業者在{0}上不可用,
Healthcare Practitioner {0} not available on {1},{1}上沒有醫療從業者{0},
Healthcare Service Unit,醫療服務單位,
Healthcare Service Unit Tree,醫療服務單位樹,
Healthcare Service Unit Type,醫療服務單位類型,
Healthcare Services,醫療服務,
Healthcare Settings,醫療設置,
Help Results for,幫助結果,
High Sensitivity,高靈敏度,
Hold,持有,
Hold Invoice,保留發票,
Holiday,節日,
Holiday List,假日列表,
Hotel Rooms of type {0} are unavailable on {1},{0}類型的酒店客房不適用於{1},
Hourly,每小時,
Hours,小時,
House rent paid days overlapping with {0},房屋租金支付天數與{0}重疊,
House rented dates required for exemption calculation,房子租用日期計算免責,
House rented dates should be atleast 15 days apart,出租房屋的日期應至少相隔15天,
How Pricing Rule is applied?,定價規則被如何應用？,
Hub Category,中心類別,
Hub Sync ID,集線器同步ID,
Human Resource,人力資源,
Human Resources,人力資源,
IFSC Code,IFSC代碼,
IGST Amount,IGST金額,
ITC Available (whether in full op part),ITC可用（無論是全部操作部分）,
ITC Reversed,ITC逆轉,
Identifying Decision Makers,確定決策者,
"If Auto Opt In is checked, then the customers will be automatically linked with the concerned Loyalty Program (on save)",如果選中自動選擇，則客戶將自動與相關的忠誠度計劃鏈接（保存時）,
"If multiple Pricing Rules continue to prevail, users are asked to set Priority manually to resolve conflict.",如果有多個定價規則繼續有效，用戶將被要求手動設定優先順序來解決衝突。,
"If selected Pricing Rule is made for 'Rate', it will overwrite Price List. Pricing Rule rate is the final rate, so no further discount should be applied. Hence, in transactions like Sales Order, Purchase Order etc, it will be fetched in 'Rate' field, rather than 'Price List Rate' field.",如果選定的定價規則是針對“費率”制定的，則會覆蓋價目表。定價規則費率是最終費率，因此不應再應用更多折扣。因此，在諸如銷售訂單，採購訂單等交易中，它將在&#39;費率&#39;字段中取代，而不是在&#39;價格列表率&#39;字段中取出。,
"If two or more Pricing Rules are found based on the above conditions, Priority is applied. Priority is a number between 0 to 20 while default value is zero (blank). Higher number means it will take precedence if there are multiple Pricing Rules with same conditions.",如果兩個或更多的定價規則是基於上述條件發現，優先級被應用。優先權是一個介於0到20，而預設值是零（空）。數字越大，意味著其將優先考慮是否有與相同條件下多個定價規則。,
"If unlimited expiry for the Loyalty Points, keep the Expiry Duration empty or 0.",如果忠誠度積分無限期到期，請將到期時間保持為空或0。,
"If you have any questions, please get back to us.",如果您有任何疑問，請再次與我們聯繫。,
Ignore Existing Ordered Qty,忽略現有的訂購數量,
Image,圖像,
Image View,圖像查看,
Import Data,導入數據,
Import Day Book Data,導入日記簿數據,
Import Log,導入日誌,
Import Master Data,導入主數據,
Import in Bulk,進口散裝,
Import of goods,進口貨物,
Import of services,進口服務,
Importing Items and UOMs,導入項目和UOM,
Importing Parties and Addresses,進口締約方和地址,
In Maintenance,在維護中,
In Production,在生產中,
In Qty,在數量,
In Stock Qty,庫存數量,
In Stock: ,有現貨：,
In Value,在數值,
"In the case of multi-tier program, Customers will be auto assigned to the concerned tier as per their spent",在多層程序的情況下，客戶將根據其花費自動分配到相關層,
Inactive,待用,
Incentives,獎勵,
Include Default FB Entries,包括默認工作簿條目,
Include Exploded Items,包含爆炸物品,
Include UOM,包括UOM,
Included in Gross Profit,包含在毛利潤中,
Income,收入,
Income Account,收入帳戶,
Income Tax,所得稅,
Incoming,來,
Incoming Rate,傳入速率,
Incorrect number of General Ledger Entries found. You might have selected a wrong Account in the transaction.,不正確的數字總帳條目中找到。你可能會在交易中選擇了錯誤的帳戶。,
Increment cannot be 0,增量不能為0,
Increment for Attribute {0} cannot be 0,增量屬性{0}不能為0,
Indirect Expenses,間接費用,
Indirect Income,間接收入,
Individual,個人,
Initiated,啟動,
Inpatient Record,住院病歷,
Installation Note,安裝注意事項,
Installation Note {0} has already been submitted,安裝注意{0}已提交,
Installation date cannot be before delivery date for Item {0},品項{0}的安裝日期不能早於交貨日期,
Installing presets,安裝預置,
Institute Abbreviation,研究所縮寫,
Institute Name,學院名稱,
Instructor,講師,
Insufficient Stock,庫存不足,
Insurance Start date should be less than Insurance End date,保險開始日期應小於保險終止日期,
Integrated Tax,綜合稅,
Inter-State Supplies,國家間供應,
Interest Amount,利息金額,
Interests,興趣,
Intern,實習生,
Internet Publishing,互聯網出版,
Intra-State Supplies,國內供應,
Introduction,介紹,
Invalid Attribute,無效屬性,
Invalid Blanket Order for the selected Customer and Item,所選客戶和物料的無效總訂單,
Invalid Company for Inter Company Transaction.,公司間交易的公司無效。,
Invalid GSTIN! A GSTIN must have 15 characters.,GSTIN無效！ GSTIN必須有15個字符。,
Invalid GSTIN! First 2 digits of GSTIN should match with State number {0}.,GSTIN無效！ GSTIN的前2位數字應與州號{0}匹配。,
Invalid GSTIN! The input you've entered doesn't match the format of GSTIN.,GSTIN無效！您輸入的輸入與GSTIN的格式不匹配。,
Invalid Posting Time,發佈時間無效,
Invalid attribute {0} {1},無效的屬性{0} {1},
Invalid quantity specified for item {0}. Quantity should be greater than 0.,為項目指定了無效的數量{0} 。量應大於0 。,
Invalid reference {0} {1},無效的參考{0} {1},
Invalid {0},無效的{0},
Invalid {0} for Inter Company Transaction.,Inter Company Transaction無效{0}。,
Invalid {0}: {1},無效的{0}：{1},
Inventory,庫存,
Investment Banking,投資銀行業務,
Investments,投資,
Invoice,發票,
Invoice Created,已創建發票,
Invoice Discounting,發票貼現,
Invoice Patient Registration,發票患者登記,
Invoice Posting Date,發票發布日期,
Invoice Type,發票類型,
Invoice already created for all billing hours,發票已在所有結算時間創建,
Invoice can't be made for zero billing hour,在零計費時間內無法開具發票,
Invoice {0} no longer exists,發票{0}不再存在,
Invoiced,已開發票,
Invoiced Amount,發票金額,
Invoices,發票,
Invoices for Costumers.,客戶發票。,
Inward supplies from ISD,ISD的內向供應,
Inward supplies liable to reverse charge (other than 1 & 2 above),內向物品可能反向充電（上述1和2除外）,
Is Active,啟用,
Is Default,是預設,
Is Existing Asset,是對現有資產,
Is Frozen,就是冰凍,
Is Group,是集團,
Issue,問題,
Issue Material,發行材料,
Issued,發行,
Issues,問題,
It is needed to fetch Item Details.,需要獲取項目細節。,
Item,項目,
Item 1,項目1,
Item 2,項目2,
Item 3,項目3,
Item 4,項目4,
Item 5,項目5,
Item Cart,項目車,
Item Code,產品編號,
Item Code cannot be changed for Serial No.,產品編號不能為序列號改變,
Item Code required at Row No {0},於列{0}需要產品編號,
Item Description,項目說明,
Item Group,項目群組,
Item Group Tree,項目群組的樹狀結構,
Item Group not mentioned in item master for item {0},項目{0}之項目主檔未提及之項目群組,
Item Name,項目名稱,
Item Price added for {0} in Price List {1},加入項目價格為{0}價格表{1},
"Item Price appears multiple times based on Price List, Supplier/Customer, Currency, Item, UOM, Qty and Dates.",物料價格根據價格表，供應商/客戶，貨幣，物料，UOM，數量和日期多次出現。,
Item Price updated for {0} in Price List {1},項目價格更新{0}價格表{1},
Item Row {0}: {1} {2} does not exist in above '{1}' table,項目行{0}：{1} {2}在上面的“{1}”表格中不存在,
Item Tax Row {0} must have account of type Tax or Income or Expense or Chargeable,商品稅行{0}必須有帳戶類型稅或收入或支出或課稅的,
Item Template,項目模板,
Item Variant Settings,項目變式設置,
Item Variant {0} already exists with same attributes,項目變種{0}已經具有相同屬性的存在,
Item Variants,項目變體,
Item Variants updated,項目變體已更新,
Item has variants.,項目已變種。,
Item must be added using 'Get Items from Purchase Receipts' button,項目必須使用'從採購入庫“按鈕進行新增,
Item valuation rate is recalculated considering landed cost voucher amount,物品估價率重新計算考慮到岸成本憑證金額,
Item variant {0} exists with same attributes,項目變種{0}存在具有相同屬性,
Item {0} does not exist,項目{0}不存在,
Item {0} does not exist in the system or has expired,項目{0}不存在於系統中或已過期,
Item {0} has already been returned,項{0}已被退回,
Item {0} has been disabled,項{0}已被禁用,
Item {0} has reached its end of life on {1},項{0}已達到其壽命結束於{1},
Item {0} ignored since it is not a stock item,項目{0}被忽略，因為它不是一個庫存項目,
"Item {0} is a template, please select one of its variants",項目{0}是一個模板，請選擇它的一個變體,
Item {0} is cancelled,項{0}將被取消,
Item {0} is disabled,項目{0}無效,
Item {0} is not a serialized Item,項{0}不是一個序列化的項目,
Item {0} is not a stock Item,項{0}不是缺貨登記,
Item {0} is not active or end of life has been reached,項目{0}不活躍或生命的盡頭已經達到,
Item {0} is not setup for Serial Nos. Check Item master,項目{0}的序列號未設定，請檢查項目主檔,
Item {0} is not setup for Serial Nos. Column must be blank,項目{0}不是設定為序列號，此列必須為空白,
Item {0} must be a Fixed Asset Item,項{0}必須是固定資產項目,
Item {0} must be a Sub-contracted Item,項{0}必須是一個小項目簽約,
Item {0} must be a non-stock item,項{0}必須是一個非庫存項目,
Item {0} must be a stock Item,項{0}必須是一個缺貨登記,
Item {0} not found,項{0}未找到,
Item {0} not found in 'Raw Materials Supplied' table in Purchase Order {1},項目{0}未發現“原材料提供&#39;表中的採購訂單{1},
Item {0}: Ordered qty {1} cannot be less than minimum order qty {2} (defined in Item).,項目{0}：有序數量{1}不能低於最低訂貨量{2}（項中定義）。,
Item: {0} does not exist in the system,項：{0}不存在於系統中,
Items,項目,
Items Filter,物品過濾,
Items and Pricing,項目和定價,
Items for Raw Material Request,原料要求的項目,
Job Description,職位描述,
Job Offer,工作機會,
Job card {0} created,已創建作業卡{0},
Journal Entries {0} are un-linked,日記條目{0}都是非聯,
Journal Entry,日記帳分錄,
Journal Entry {0} does not have account {1} or already matched against other voucher,日記條目{0}沒有帳號{1}或已經匹配其他憑證,
Key Reports,主要報告,
LMS Activity,LMS活動,
Lab Test,實驗室測試,
Lab Test Report,實驗室測試報告,
Lab Test Sample,實驗室測試樣品,
Lab Test Template,實驗室測試模板,
Lab Test UOM,實驗室測試UOM,
Lab Tests and Vital Signs,實驗室測試和重要標誌,
Lab result datetime cannot be before testing datetime,實驗結果日期時間不能在測試日期時間之前,
Lab testing datetime cannot be before collection datetime,實驗室測試日期時間不能在收集日期時間之前,
Label,標籤,
Laboratory,實驗室,
Language Name,語言名稱,
Last Communication,最後溝通,
Last Communication Date,最後通訊日期,
Last Order Amount,最後訂單金額,
Last Order Date,最後訂購日期,
Last Purchase Price,上次購買價格,
Last Purchase Rate,最後預訂價,
Latest price updated in all BOMs,最新價格在所有BOM中更新,
Lead,潛在客戶,
Lead Count,鉛計數,
Lead Owner,主導擁有者,
Lead Owner cannot be same as the Lead,主導所有人不能等同於主導者,
Lead Time Days,交貨期天,
Lead to Quotation,主導報價,
"Leads help you get business, add all your contacts and more as your leads",信息幫助你的業務，你所有的聯繫人和更添加為您的線索,
Learn,學習,
Leave Approval Notification,留下批准通知,
Leave Blocked,禁假的,
Leave Encashment,離開兌現,
Leave Management,離開管理,
Leave Status Notification,離開狀態通知,
Leave Type,休假類型,
Leave Type is madatory,離開類型是瘋狂的,
Leave Type {0} cannot be allocated since it is leave without pay,休假類型{0}，因為它是停薪留職無法分配,
Leave Type {0} cannot be carry-forwarded,休假類型{0}不能隨身轉發,
Leave Type {0} is not encashable,離開類型{0}不可放置,
Leave Without Pay,無薪假,
Leave and Attendance,假離和缺勤,
Leave application {0} already exists against the student {1},對學生{1}已經存在申請{0},
"Leave cannot be allocated before {0}, as leave balance has already been carry-forwarded in the future leave allocation record {1}",假，不是之前分配{0}，因為休假餘額已經結轉轉發在未來的假期分配記錄{1},
"Leave cannot be applied/cancelled before {0}, as leave balance has already been carry-forwarded in the future leave allocation record {1}",離開不能應用/前{0}取消，因為假平衡已經被搬入轉發在未來休假分配記錄{1},
Leave of type {0} cannot be longer than {1},請假類型{0}不能長於{1},
Leaves,樹葉,
Leaves Allocated Successfully for {0},{0}的排假成功,
Leaves has been granted sucessfully,葉子已成功獲得,
Leaves must be allocated in multiples of 0.5,休假必須安排成0.5倍的,
Leaves per Year,每年葉,
Ledger,分類帳,
Legal Expenses,法律費用,
Letter Head,信頭,
Letter Heads for print templates.,信頭的列印模板。,
Level,級別,
Liability,責任,
License,執照,
Lifecycle,生命週期,
Link to Material Request,鏈接到材料請求,
List of all share transactions,所有股份交易清單,
List of available Shareholders with folio numbers,包含folio號碼的可用股東名單,
Loading Payment System,加載支付系統,
Loan,貸款,
Loan Amount cannot exceed Maximum Loan Amount of {0},貸款額不能超過最高貸款額度{0},
Loan Application,申請貸款,
Loan Management,貸款管理,
Loan Repayment,償還借款,
Loan Start Date and Loan Period are mandatory to save the Invoice Discounting,貸款開始日期和貸款期限是保存發票折扣的必要條件,
Loans (Liabilities),借款（負債）,
Loans and Advances (Assets),貸款及墊款（資產）,
Local,當地,
Log,日誌,
Logs for maintaining sms delivery status,日誌維護短信發送狀態,
Lost,丟失,
Low Sensitivity,低靈敏度,
Lower Income,較低的收入,
Loyalty Amount,忠誠金額,
Loyalty Point Entry,忠誠度積分,
Loyalty Points,忠誠度積分,
"Loyalty Points will be calculated from the spent done (via the Sales Invoice), based on collection factor mentioned.",忠誠度積分將根據所花費的完成量（通過銷售發票）計算得出。,
Loyalty Points: {0},忠誠度積分：{0},
Loyalty Program,忠誠計劃,
Main,主頁,
Maintenance,維護,
Maintenance Log,維護日誌,
Maintenance Manager,維護經理,
Maintenance Schedule,維護計劃,
Maintenance Schedule is not generated for all the items. Please click on 'Generate Schedule',維護計畫不會為全部品項生成。請點擊“生成表”,
Maintenance Schedule {0} exists against {1},針對{1}存在維護計劃{0},
Maintenance Schedule {0} must be cancelled before cancelling this Sales Order,維護時間表{0}必須取消早於取消這個銷售訂單,
Maintenance Status has to be Cancelled or Completed to Submit,維護狀態必須取消或完成提交,
Maintenance User,維護用戶,
Maintenance Visit,維護訪問,
Maintenance Visit {0} must be cancelled before cancelling this Sales Order,維護訪問{0}必須取消這個銷售訂單之前被取消,
Maintenance start date can not be before delivery date for Serial No {0},序號{0}的維護開始日期不能早於交貨日期,
Make,使,
Make project from a template.,從模板創建項目。,
Making Stock Entries,製作Stock條目,
Male,男,
Manage Customer Group Tree.,管理客戶群組樹。,
Manage Sales Partners.,管理銷售合作夥伴。,
Manage Sales Person Tree.,管理銷售人員樹。,
Manage Territory Tree.,管理領地樹。,
Manage your orders,管理您的訂單,
Management,管理,
Manager,經理,
Managing Projects,項目管理,
Managing Subcontracting,管理轉包,
Mandatory,強制性,
Mandatory field - Academic Year,必修課 - 學年,
Mandatory field - Get Students From,強制性領域 - 獲得學生,
Mandatory field - Program,強制性領域 - 計劃,
Manufacture,製造,
Manufacturer,生產廠家,
Manufacturer Part Number,製造商零件編號,
Manufacturing,製造,
Manufacturing Quantity is mandatory,生產數量是必填的,
Mapping,製圖,
Mapping Type,映射類型,
Mark Absent,馬克缺席,
Mark Attendance,出席人數,
Mark Half Day,馬克半天,
Mark Present,馬克現在,
Marketing,市場營銷,
Marketing Expenses,市場推廣開支,
Marketplace,市井,
Marketplace Error,市場錯誤,
Masters,資料主檔,
Match Payments with Invoices,付款與發票對照,
Match non-linked Invoices and Payments.,核對非關聯的發票和付款。,
Material Consumption is not set in Manufacturing Settings.,材料消耗未在生產設置中設置。,
Material Receipt,收料,
Material Request,物料需求,
Material Request Date,材料申請日期,
Material Request No,材料需求編號,
"Material Request not created, as quantity for Raw Materials already available.",材料請求未創建，因為原材料的數量已經可用。,
Material Request of maximum {0} can be made for Item {1} against Sales Order {2},針對銷售訂單{2}的項目{1}，最多可以有 {0} 被完成。,
Material Request to Purchase Order,材料要求採購訂單,
Material Request {0} is cancelled or stopped,材料需求{0}被取消或停止,
Material Request {0} submitted.,材料申請{0}已提交。,
Material Transfer,物料轉倉,
Material Transferred,轉移的材料,
Material to Supplier,材料到供應商,
Max Exemption Amount cannot be greater than maximum exemption amount {0} of Tax Exemption Category {1},最高豁免金額不得超過免稅類別{1}的最高豁免金額{0},
Max benefits should be greater than zero to dispense benefits,最大的好處應該大於零來分配好處,
Max discount allowed for item: {0} is {1}%,{0}允許的最大折扣：{1}％,
Max: {0},最大數量：{0},
Maximum Samples - {0} can be retained for Batch {1} and Item {2}.,可以為批次{1}和項目{2}保留最大樣本數量{0}。,
Maximum Samples - {0} have already been retained for Batch {1} and Item {2} in Batch {3}.,批次{1}和批次{3}中的項目{2}已保留最大樣本數量{0}。,
Maximum amount eligible for the component {0} exceeds {1},符合組件{0}的最高金額超過{1},
Maximum benefit amount of component {0} exceeds {1},組件{0}的最大受益金額超過{1},
Maximum benefit amount of employee {0} exceeds {1},員工{0}的最高福利金額超過{1},
Maximum discount for Item {0} is {1}%,第{0}項的最大折扣為{1}％,
Maximum leave allowed in the leave type {0} is {1},假期類型{0}允許的最大休假是{1},
Medical,醫療,
Medical Code,醫療法,
Medical Code Standard,醫療代碼標準,
Medical Department,醫學系,
Medical Record,醫療記錄,
Medium,介質,
Meeting,會議,
Member Activity,會員活動,
Member ID,會員ID,
Member Name,成員名字,
Member information.,會員信息。,
Membership,籍,
Membership Details,會員資格,
Membership ID,會員ID,
Membership Type,會員類型,
Memebership Details,Memebership細節,
Memebership Type Details,Memebership類型詳細信息,
Merge,合併,
Merge Account,合併帳戶,
Merge with Existing Account,與現有帳戶合併,
"Merging is only possible if following properties are same in both records. Is Group, Root Type, Company",合併是唯一可能的，如果以下屬性中均有記載相同。是集團，根型，公司,
Message Examples,訊息範例,
Message Sent,發送訊息,
Middle Name,中間名字,
Middle Name (Optional),中間名（可選）,
Min Amt can not be greater than Max Amt,Min Amt不能大於Max Amt,
Min Qty can not be greater than Max Qty,最小數量不能大於最大數量,
Minimum Lead Age (Days),最低鉛年齡（天）,
Miscellaneous Expenses,雜項開支,
Missing Currency Exchange Rates for {0},缺少貨幣匯率{0},
Missing email template for dispatch. Please set one in Delivery Settings.,缺少發送的電子郵件模板。請在“傳遞設置”中設置一個。,
"Missing value for Password, API Key or Shopify URL",缺少密碼，API密鑰或Shopify網址的值,
Mode of Transportation,運輸方式,
Mode of payment is required to make a payment,付款方式需要進行付款,
Moderate Sensitivity,中等靈敏度,
Monthly,每月一次,
Monthly Distribution,月度分佈,
Monthly Repayment Amount cannot be greater than Loan Amount,每月還款額不能超過貸款金額較大,
More Information,更多訊息,
More than one selection for {0} not allowed,不允許對{0}進行多次選擇,
Motion Picture & Video,電影和視頻,
Move,舉,
Move Item,移動項目,
Multi Currency,多幣種,
Multiple Item prices.,多個項目的價格。,
Multiple Loyalty Program found for the Customer. Please select manually.,為客戶找到多個忠誠度計劃。請手動選擇。,
"Multiple Price Rules exists with same criteria, please resolve conflict by assigning priority. Price Rules: {0}",海報價格規則，同樣的標準存在，請通過分配優先解決衝突。價格規則：{0},
Multiple Variants,多種變體,
Multiple fiscal years exist for the date {0}. Please set company in Fiscal Year,多個會計年度的日期{0}存在。請設置公司財年,
Music,音樂,
My Account,我的帳戶,
Name error: {0},名稱錯誤：{0},
Name of new Account. Note: Please don't create accounts for Customers and Suppliers,新帳戶的名稱。注：請不要創建帳戶的客戶和供應商,
Name or Email is mandatory,姓名或電子郵件是強制性,
Nature Of Supplies,供應的性質,
Navigating,導航,
Negative Quantity is not allowed,負數量是不允許,
Negative Valuation Rate is not allowed,負面評價率是不允許的,
Negotiation/Review,談判/評論,
Net Asset value as on,淨資產值作為,
Net Cash from Financing,從融資淨現金,
Net Cash from Investing,從投資淨現金,
Net Cash from Operations,從運營的淨現金,
Net Change in Accounts Payable,應付賬款淨額變化,
Net Change in Accounts Receivable,應收賬款淨額變化,
Net Change in Cash,現金淨變動,
Net Change in Equity,在淨資產收益變化,
Net Change in Fixed Asset,在固定資產淨變動,
Net Change in Inventory,在庫存淨變動,
Net ITC Available(A) - (B),淨ITC可用（A） - （B）,
Net Pay,淨收費,
Net Pay cannot be less than 0,淨工資不能低於0,
Net Profit,淨利,
Net Salary Amount,淨工資金額,
Net Total,總淨值,
Net pay cannot be negative,淨工資不能為負,
New Account Name,新帳號名稱,
New BOM,新的物料清單,
New Batch ID (Optional),新批號（可選）,
New Batch Qty,新批量數量,
New Company,新公司,
New Cost Center Name,新的成本中心名稱,
New Customer Revenue,新客戶收入,
New Customers,新客戶,
New Department,新部門,
New Employee,新員工,
New Quality Procedure,新的質量程序,
New Sales Invoice,新的銷售發票,
New Sales Person Name,新銷售人員的姓名,
New Serial No cannot have Warehouse. Warehouse must be set by Stock Entry or Purchase Receipt,新的序列號不能有倉庫。倉庫必須由存貨分錄或採購入庫單進行設定,
New Warehouse Name,新倉庫名稱,
New credit limit is less than current outstanding amount for the customer. Credit limit has to be atleast {0},新的信用額度小於當前餘額為客戶著想。信用額度是ATLEAST {0},
New task,新任務,
New {0} pricing rules are created,創建新的{0}定價規則,
Newsletters,簡訊,
Newspaper Publishers,報紙出版商,
Next,下一個,
Next Contact By cannot be same as the Lead Email Address,接著聯繫到不能相同鉛郵箱地址,
Next Contact Date cannot be in the past,接下來跟日期不能過去,
No Action,沒有行動,
No Customers yet!,還沒有客戶！,
No Data,無數據,
No Delivery Note selected for Customer {},沒有為客戶{}選擇送貨單,
No Employee Found,找不到員工,
No Item with Barcode {0},沒有條碼{0}的品項,
No Item with Serial No {0},沒有序號{0}的品項,
No Items available for transfer,沒有可用於傳輸的項目,
No Items selected for transfer,沒有選擇轉移項目,
No Items to pack,無項目包裝,
No Items with Bill of Materials to Manufacture,不與物料清單的項目，以製造,
No Items with Bill of Materials.,沒有物料清單的物品。,
No Permission,無權限,
No Remarks,暫無產品說明,
No Result to submit,沒有結果提交,
No Salary Structure assigned for Employee {0} on given date {1},給定日期{1}的員工{0}沒有分配薪金結構,
No Staffing Plans found for this Designation,本指定沒有發現人員配備計劃,
No Student Groups created.,沒有學生團體創建的。,
No Students in,沒有學生,
No Tax Withholding data found for the current Fiscal Year.,未找到當前財年的預扣稅數據。,
No Work Orders created,沒有創建工作訂單,
No accounting entries for the following warehouses,沒有以下的倉庫會計分錄,
No active or default Salary Structure found for employee {0} for the given dates,發現員工{0}對於給定的日期沒有活動或默認的薪酬結構,
No contacts with email IDs found.,找不到與電子郵件ID的聯繫人。,
No data for this period,此期間沒有數據,
No description given,沒有給出描述,
No employees for the mentioned criteria,沒有僱員提到的標準,
No gain or loss in the exchange rate,匯率沒有收益或損失,
No items listed,沒有列出項目,
No items to be received are overdue,沒有收到的物品已逾期,
No material request created,沒有創建重要請求,
No more updates,沒有更多的更新,
No of Interactions,沒有相互作用,
No of Shares,股份數目,
No pending Material Requests found to link for the given items.,找不到針對給定項目鏈接的待處理物料請求。,
No products found,沒有找到產品,
No products found.,找不到產品。,
No record found,沒有資料,
No records found in the Invoice table,沒有在發票表中找到記錄,
No records found in the Payment table,沒有在支付表中找到記錄,
No replies from,從沒有回复,
No salary slip found to submit for the above selected criteria OR salary slip already submitted,沒有發現提交上述選定標准或已提交工資單的工資單,
No tasks,沒有任務,
No time sheets,沒有考勤表,
No values,沒有價值,
No {0} found for Inter Company Transactions.,Inter公司沒有找到{0}。,
Non GST Inward Supplies,非消費稅進口供應,
Non Profit,非營利,
Non Profit (beta),非營利（測試版）,
Non-GST outward supplies,非商品及服務稅外向供應,
Non-Group to Group,非集團集團,
None,沒有,
None of the items have any change in quantity or value.,沒有一個項目無論在數量或價值的任何變化。,
Nos,NOS,
Not Available,無法使用,
Not Marked,未標記,
Not Paid and Not Delivered,沒有支付，未送達,
Not Permitted,不允許,
Not Started,未啟動,
Not active,不活躍,
Not allow to set alternative item for the item {0},不允許為項目{0}設置替代項目,
Not allowed to update stock transactions older than {0},不允許更新比{0}舊的庫存交易,
Not authorized to edit frozen Account {0},無權修改凍結帳戶{0},
Not authroized since {0} exceeds limits,不允許因為{0}超出範圍,
Not permitted for {0},不允許{0},
"Not permitted, configure Lab Test Template as required",不允許，根據需要配置實驗室測試模板,
Not permitted. Please disable the Service Unit Type,不允許。請禁用服務單位類型,
Note: Due / Reference Date exceeds allowed customer credit days by {0} day(s),註：由於/參考日期由{0}天超過了允許客戶的信用天數（S）,
Note: Item {0} entered multiple times,注：項目{0}多次輸入,
Note: Payment Entry will not be created since 'Cash or Bank Account' was not specified,注：付款項將不會被創建因為“現金或銀行帳戶”未指定,
Note: System will not check over-delivery and over-booking for Item {0} as quantity or amount is 0,注：系統將不檢查過交付和超額預訂的項目{0}的數量或金額為0,
Note: There is not enough leave balance for Leave Type {0},注：沒有足夠的休假餘額請假類型{0},
Note: This Cost Center is a Group. Cannot make accounting entries against groups.,注：該成本中心是一個集團。不能讓反對團體的會計分錄。,
Note: {0},注意：{0},
Notes,筆記,
Nothing is included in gross,毛不包含任何內容,
Nothing more to show.,沒有更多的表現。,
Nothing to change,沒什麼可改變的,
Notify Customers via Email,通過電子郵件通知客戶,
Number,數,
Number of Depreciations Booked cannot be greater than Total Number of Depreciations,預訂折舊數不能超過折舊總數更大,
Number of Interaction,交互次數,
Number of Order,訂購數量,
"Number of new Account, it will be included in the account name as a prefix",新帳號的數量，將作為前綴包含在帳號名稱中,
"Number of new Cost Center, it will be included in the cost center name as a prefix",新成本中心的數量，它將作為前綴包含在成本中心名稱中,
Number of root accounts cannot be less than 4,root帳戶數不能少於4,
Office Equipments,辦公設備,
Office Maintenance Expenses,Office維護費用,
Office Rent,辦公室租金,
On Hold,等候接聽,
On Net Total,在總淨,
One customer can be part of only single Loyalty Program.,一個客戶只能參與一個忠誠度計劃。,
Online Auctions,網上拍賣,
Only Leave Applications with status 'Approved' and 'Rejected' can be submitted,只留下地位的申請“已批准”和“拒絕”，就可以提交,
"Only the Student Applicant with the status ""Approved"" will be selected in the table below.",下表中將只選擇狀態為“已批准”的學生申請人。,
Only users with {0} role can register on Marketplace,只有{0}角色的用戶才能在Marketplace上註冊,
Open BOM {0},開放BOM {0},
Open Item {0},打開項目{0},
Open Notifications,打開通知,
Open Orders,開放訂單,
Open a new ticket,打開一張新票,
Opening,開盤,
Opening (Cr),開啟（Cr ）,
Opening (Dr),開啟（Dr）,
Opening Accounting Balance,打開會計平衡,
Opening Accumulated Depreciation,打開累計折舊,
Opening Accumulated Depreciation must be less than equal to {0},打開累計折舊必須小於等於{0},
Opening Balance,期初餘額,
Opening Balance Equity,期初餘額權益,
Opening Date and Closing Date should be within same Fiscal Year,開幕日期和截止日期應在同一會計年度,
Opening Date should be before Closing Date,開業日期應該是截止日期之前，,
Opening Entry Journal,開幕詞報,
Opening Invoice Creation Tool,打開發票創建工具,
Opening Invoice Item,打開發票項目,
Opening Invoices,打開發票,
Opening Invoices Summary,打開發票摘要,
Opening Qty,開放數量,
Opening Stock,打開股票,
Opening Stock Balance,期初存貨餘額,
Opening Value,開度值,
Opening {0} Invoice created,打開{0}已創建發票,
Operation,作業,
Operation Time must be greater than 0 for Operation {0},運行時間必須大於0的操作{0},
"Operation {0} longer than any available working hours in workstation {1}, break down the operation into multiple operations",操作{0}比任何可用的工作時間更長工作站{1}，分解成運行多個操作,
Operations,作業,
Opp Count,Opp Count,
Opp/Lead %,Opp / Lead％,
Opportunities,機會,
Opportunities by lead source,鉛來源的機會,
Opportunity,機會,
Opportunity Amount,機會金額,
Optional Holiday List not set for leave period {0},可選假期列表未設置為假期{0},
"Optional. Sets company's default currency, if not specified.",可選。設置公司的默認貨幣，如果沒有指定。,
Optional. This setting will be used to filter in various transactions.,可選。此設置將被應用於過濾各種交易進行。,
Options,選項,
Order Count,訂單數量,
Order Entry,訂單輸入,
Order Value,訂單價值,
Order rescheduled for sync,訂單重新安排同步,
Order/Quot %,訂單/報價％,
Ordered,已訂購,
Ordered Qty,訂購數量,
"Ordered Qty: Quantity ordered for purchase, but not received.",訂購數量：採購的訂單數量，但沒有收到。,
Orders,訂單,
Orders released for production.,發布生產訂單。,
Organization,組織,
Organization Name,組織名稱,
Other Reports,其他報告,
"Other outward supplies(Nil rated,Exempted)",其他外向供應（未評級，豁免）,
Others,其他,
Out Qty,輸出數量,
Out Value,輸出值,
Out of Order,亂序,
Outgoing,發送,
Outstanding,優秀,
Outstanding Amount,未償還的金額,
Outstanding Amt,優秀的金額,
Outstanding Cheques and Deposits to clear,傑出的支票及存款清除,
Outstanding for {0} cannot be less than zero ({1}),傑出的{0}不能小於零（ {1} ）,
Outward taxable supplies(zero rated),外向應稅物資（零評級）,
Overdue,過期的,
Overlap in scoring between {0} and {1},{0}和{1}之間的得分重疊,
Overlapping conditions found between:,存在重疊的條件：,
Owner,業主,
POS,POS,
POS Profile,POS簡介,
POS Profile is required to use Point-of-Sale,POS配置文件需要使用銷售點,
POS Profile required to make POS Entry,所需的POS資料，使POS進入,
POS Settings,POS設置,
Packed quantity must equal quantity for Item {0} in row {1},盒裝數量必須等於{1}列品項{0}的數量,
Packing Slip,包裝單,
Packing Slip(s) cancelled,包裝單（ S）已取消,
Paid,付費,
Paid Amount,支付的金額,
Paid Amount cannot be greater than total negative outstanding amount {0},支付的金額不能超過總負餘額大於{0},
Paid amount + Write Off Amount can not be greater than Grand Total,支付的金額+寫的抵銷金額不能大於總計,
Paid and Not Delivered,支付和未送達,
Parameter,參數,
Parent Item {0} must not be a Stock Item,父項{0}不能是庫存產品,
Parents Teacher Meeting Attendance,家長老師見面會,
Part-time,兼職,
Partially Depreciated,部分貶抑,
Party,黨,
Party Name,方名稱,
Party Type,黨的類型,
Party Type and Party is mandatory for {0} account,{0}帳戶必須使用派對類型和派對,
Party Type is mandatory,黨的類型是強制性,
Party is mandatory,黨是強制性,
Password,密碼,
Password policy for Salary Slips is not set,未設置Salary Slips的密碼策略,
Past Due Date,過去的截止日期,
Patient Appointment,患者預約,
Patient not found,找不到病人,
Pay Remaining,支付剩餘,
Payable,支付,
Payable Account,應付帳款,
Payable Amount,應付金額,
Payment Cancelled. Please check your GoCardless Account for more details,付款已取消。請檢查您的GoCardless帳戶以了解更多詳情,
Payment Confirmation,付款確認,
Payment Days,付款日,
Payment Document,付款單據,
Payment Due Date,付款截止日期,
Payment Entries {0} are un-linked,付款項{0}是聯合國聯,
Payment Entry,付款輸入,
Payment Entry already exists,付款項目已存在,
Payment Entry has been modified after you pulled it. Please pull it again.,付款項被修改，你把它之後。請重新拉。,
Payment Entry is already created,已創建付款輸入,
Payment Failed. Please check your GoCardless Account for more details,支付失敗。請檢查您的GoCardless帳戶以了解更多詳情,
Payment Gateway,支付網關,
"Payment Gateway Account not created, please create one manually.",支付網關帳戶沒有創建，請手動創建一個。,
Payment Gateway Name,支付網關名稱,
Payment Receipt Note,付款收貨注意事項,
Payment Request,付錢請求,
Payment Request for {0},付款申請{0},
Payment Tems,付款條件,
Payment Terms,付款條件,
Payment Terms Template,付款條款模板,
Payment Terms based on conditions,付款條款基於條件,
Payment Type,付款類型,
"Payment Type must be one of Receive, Pay and Internal Transfer",付款方式必須是接收之一，收費和內部轉賬,
Payment against {0} {1} cannot be greater than Outstanding Amount {2},對支付{0} {1}不能大於未償還{2},
Payment of {0} from {1} to {2},從{1}到{2}的{0}付款,
Payment request {0} created,已創建付款請求{0},
Payments,支付,
Payroll,工資表,
Payroll Number,工資號碼,
Payroll Payable,應付職工薪酬,
Payslip,工資單,
Pending Activities,待活動,
Pending Amount,待審核金額,
Pending Leaves,等待葉子,
Pending Qty,待定數量,
Pending Quantity,待定數量,
Pending Review,待審核,
Pending activities for today,今天待定活動,
Pension Funds,養老基金,
Percentage Allocation should be equal to 100%,百分比分配總和應該等於100%,
Period,期間,
Period Closing Entry,期末進入,
Period Closing Voucher,期末券,
Periodicity,週期性,
Personal Details,個人資料,
Pharmaceutical,製藥,
Pharmaceuticals,製藥,
Physician,醫師,
Piecework,計件工作,
Pincode,PIN代碼,
Place Of Supply (State/UT),供應地點（州/ UT）,
Place Order,下單,
Plan Name,計劃名稱,
Plan for maintenance visits.,規劃維護訪問。,
Planned Qty,計劃數量,
"Planned Qty: Quantity, for which, Work Order has been raised, but is pending to be manufactured.",計劃數量：數量，已為此工作訂單提出，但尚待製造。,
Planning,規劃,
Plants and Machineries,廠房和機械設備,
Please Set Supplier Group in Buying Settings.,請設置供應商組購買設置。,
Please add a Temporary Opening account in Chart of Accounts,請在會計科目表中添加一個臨時開戶賬戶,
Please add the account to root level Company - ,請將帳戶添加到根級公司 -,
Please add the remaining benefits {0} to any of the existing component,請將其餘好處{0}添加到任何現有組件,
Please check Multi Currency option to allow accounts with other currency,請檢查多幣種選項，允許帳戶與其他貨幣,
Please click on 'Generate Schedule',請點擊“生成表”,
Please click on 'Generate Schedule' to fetch Serial No added for Item {0},請點擊“生成表”來獲取序列號增加了對項目{0},
Please click on 'Generate Schedule' to get schedule,請在“產生排程”點擊以得到排程表,
Please confirm once you have completed your training,完成培訓後請確認,
Please create purchase receipt or purchase invoice for the item {0},請為項目{0}創建購買收據或購買發票,
Please define grade for Threshold 0%,請定義等級為閾值0％,
Please enable Applicable on Booking Actual Expenses,請啟用適用於預訂實際費用,
Please enable Applicable on Purchase Order and Applicable on Booking Actual Expenses,請啟用適用於採購訂單並適用於預訂實際費用,
Please enable default incoming account before creating Daily Work Summary Group,請在創建日常工作摘要組之前啟用默認傳入帳戶,
Please enable pop-ups,請啟用彈出窗口,
Please enter 'Is Subcontracted' as Yes or No,請輸入'轉包' YES或NO,
Please enter API Consumer Key,請輸入API使用者密鑰,
Please enter API Consumer Secret,請輸入API消費者密碼,
Please enter Account for Change Amount,對於漲跌額請輸入帳號,
Please enter Approving Role or Approving User,請輸入核准角色或審批用戶,
Please enter Cost Center,請輸入成本中心,
Please enter Delivery Date,請輸入交貨日期,
Please enter Employee Id of this sales person,請輸入這個銷售人員的員工標識,
Please enter Expense Account,請輸入您的費用帳戶,
Please enter Item Code to get Batch Number,請輸入產品代碼來獲得批號,
Please enter Item Code to get batch no,請輸入產品編號，以取得批號,
Please enter Item first,請先輸入品項,
Please enter Maintaince Details first,請先輸入維護細節,
Please enter Planned Qty for Item {0} at row {1},請輸入列{1}的品項{0}的計劃數量,
Please enter Preferred Contact Email,請輸入首選電子郵件聯繫,
Please enter Production Item first,請先輸入生產項目,
Please enter Purchase Receipt first,請先輸入採購入庫單,
Please enter Receipt Document,請輸入收據憑證,
Please enter Reference date,參考日期請輸入,
Please enter Repayment Periods,請輸入還款期,
Please enter Reqd by Date,請輸入按日期請求,
Please enter Woocommerce Server URL,請輸入Woocommerce服務器網址,
Please enter Write Off Account,請輸入核銷帳戶,
Please enter atleast 1 invoice in the table,請在表中輸入至少一筆發票,
Please enter company first,請先輸入公司,
Please enter company name first,請先輸入公司名稱,
Please enter default currency in Company Master,請在公司主檔輸入預設貨幣,
Please enter message before sending,在發送前，請填寫留言,
Please enter parent cost center,請輸入父成本中心,
Please enter quantity for Item {0},請輸入項目{0}的量,
Please enter relieving date.,請輸入解除日期。,
Please enter repayment Amount,請輸入還款金額,
Please enter valid Financial Year Start and End Dates,請輸入有效的財政年度開始和結束日期,
Please enter valid email address,請輸入有效的電子郵件地址,
Please enter {0} first,請輸入{0}第一,
Please fill in all the details to generate Assessment Result.,請填寫所有詳細信息以生成評估結果。,
Please identify/create Account (Group) for type - {0},請為類型{0}標識/創建帳戶（組）,
Please identify/create Account (Ledger) for type - {0},請為類型{0}標識/創建帳戶（分類帳）,
Please login as another user to register on Marketplace,請以另一個用戶身份登錄以在Marketplace上註冊,
Please make sure you really want to delete all the transactions for this company. Your master data will remain as it is. This action cannot be undone.,請確保你真的要刪除這家公司的所有交易。主數據將保持原樣。這個動作不能撤消。,
Please mention Basic and HRA component in Company,請在公司中提及基本和HRA組件,
Please mention Round Off Account in Company,請註明舍入賬戶的公司,
Please mention Round Off Cost Center in Company,請提及公司舍入成本中心,
Please mention no of visits required,請註明無需訪問,
Please mention the Lead Name in Lead {0},請提及潛在客戶名稱{0},
Please pull items from Delivery Note,請送貨單拉項目,
Please register the SIREN number in the company information file,請在公司信息文件中註冊SIREN號碼,
Please remove this Invoice {0} from C-Form {1},請刪除此發票{0}從C-表格{1},
Please save the patient first,請先保存患者,
Please save the report again to rebuild or update,請再次保存報告以重建或更新,
"Please select Allocated Amount, Invoice Type and Invoice Number in atleast one row",請ATLEAST一行選擇分配金額，發票類型和發票號碼,
Please select Apply Discount On,請選擇適用的折扣,
Please select BOM against item {0},請選擇物料{0}的物料清單,
Please select BOM for Item in Row {0},請行選擇BOM為項目{0},
Please select BOM in BOM field for Item {0},請BOM字段中選擇BOM的項目{0},
Please select Category first,請先選擇分類,
Please select Charge Type first,請先選擇付款類別,
Please select Company,請選擇公司,
Please select Company and Designation,請選擇公司和指定,
Please select Company and Posting Date to getting entries,請選擇公司和發布日期以獲取條目,
Please select Company first,請首先選擇公司,
Please select Completion Date for Completed Asset Maintenance Log,請選擇已完成資產維護日誌的完成日期,
Please select Completion Date for Completed Repair,請選擇完成修復的完成日期,
Please select Course,請選擇課程,
Please select Drug,請選擇藥物,
Please select Employee,請選擇員工,
Please select Existing Company for creating Chart of Accounts,請選擇現有的公司創建會計科目表,
Please select Healthcare Service,請選擇醫療保健服務,
"Please select Item where ""Is Stock Item"" is ""No"" and ""Is Sales Item"" is ""Yes"" and there is no other Product Bundle",請選擇項，其中“正股項”是“否”和“是銷售物品”是“是”，沒有其他產品捆綁,
Please select Maintenance Status as Completed or remove Completion Date,請選擇維護狀態為已完成或刪除完成日期,
Please select Party Type first,請選擇黨第一型,
Please select Patient,請選擇患者,
Please select Patient to get Lab Tests,請選擇患者以獲得實驗室測試,
Please select Posting Date before selecting Party,在選擇之前，甲方請選擇發布日期,
Please select Posting Date first,請選擇發布日期第一,
Please select Price List,請選擇價格表,
Please select Program,請選擇程序,
Please select Qty against item {0},請選擇項目{0}的數量,
Please select Sample Retention Warehouse in Stock Settings first,請先在庫存設置中選擇樣品保留倉庫,
Please select Start Date and End Date for Item {0},請選擇項目{0}的開始日期和結束日期,
Please select Student Admission which is mandatory for the paid student applicant,請選擇付費學生申請者必須入學的學生,
Please select a BOM,請選擇一個物料清單,
Please select a Batch for Item {0}. Unable to find a single batch that fulfills this requirement,請選擇項目{0}的批次。無法找到滿足此要求的單個批次,
Please select a Company,請選擇一個公司,
Please select a batch,請選擇一個批次,
Please select a csv file,請選擇一個csv文件,
Please select a field to edit from numpad,請選擇要從數字鍵盤編輯的字段,
Please select a table,請選擇一張桌子,
Please select a valid Date,請選擇一個有效的日期,
Please select a value for {0} quotation_to {1},請選擇一個值{0} quotation_to {1},
Please select a warehouse,請選擇一個倉庫,
Please select at least one domain.,請選擇至少一個域名。,
Please select correct account,請選擇正確的帳戶,
Please select date,請選擇日期,
Please select item code,請選擇商品代碼,
Please select month and year,請選擇年份和月份,
Please select prefix first,請先選擇前綴稱號,
Please select the Company,請選擇公司,
Please select the Multiple Tier Program type for more than one collection rules.,請為多個收集規則選擇多層程序類型。,
Please select the assessment group other than 'All Assessment Groups',請選擇“所有評估組”以外的評估組,
Please select the document type first,請先選擇文檔類型,
Please select weekly off day,請選擇每週休息日,
Please select {0},請選擇{0},
Please select {0} first,請先選擇{0},
Please set 'Apply Additional Discount On',請設置“收取額外折扣”,
Please set 'Asset Depreciation Cost Center' in Company {0},請設置在公司的資產折舊成本中心“{0},
Please set 'Gain/Loss Account on Asset Disposal' in Company {0},請公司制定“關於資產處置收益/損失帳戶”{0},
Please set Account in Warehouse {0} or Default Inventory Account in Company {1},請在倉庫{0}中設置帳戶或在公司{1}中設置默認庫存帳戶,
Please set B2C Limit in GST Settings.,請在GST設置中設置B2C限制。,
Please set Company,請設公司,
Please set Company filter blank if Group By is 'Company',如果Group By是“Company”，請設置公司過濾器空白,
Please set Default Payroll Payable Account in Company {0},請公司設定默認應付職工薪酬帳戶{0},
Please set Depreciation related Accounts in Asset Category {0} or Company {1},請設置在資產類別{0}或公司折舊相關帳戶{1},
Please set Email Address,請設置電子郵件地址,
Please set GST Accounts in GST Settings,請在GST設置中設置GST帳戶,
Please set Hotel Room Rate on {},請在{}上設置酒店房價,
Please set Number of Depreciations Booked,請設置折舊數預訂,
Please set Unrealized Exchange Gain/Loss Account in Company {0},請在公司{0}中設置未實現的匯兌收益/損失帳戶,
Please set User ID field in an Employee record to set Employee Role,請在員工記錄設定員工角色設置用戶ID字段,
Please set a default Holiday List for Employee {0} or Company {1},請設置一個默認的假日列表為員工{0}或公司{1},
Please set account in Warehouse {0},請在倉庫{0}中設置帳戶,
Please set an active menu for Restaurant {0},請設置餐館{0}的有效菜單,
Please set associated account in Tax Withholding Category {0} against Company {1},請在針對公司{1}的預扣稅分類{0}中設置關聯帳戶,
Please set at least one row in the Taxes and Charges Table,請在“稅費和收費表”中至少設置一行,
Please set default Cash or Bank account in Mode of Payment {0},請設定現金或銀行帳戶的預設付款方式{0},
Please set default account in Salary Component {0},請薪酬部分設置默認帳戶{0},
Please set default customer in Restaurant Settings,請在“餐廳設置”中設置默認客戶,
Please set default template for Leave Approval Notification in HR Settings.,請在人力資源設置中為離職審批通知設置默認模板。,
Please set default template for Leave Status Notification in HR Settings.,請在人力資源設置中設置離職狀態通知的默認模板。,
Please set default {0} in Company {1},請設置在默認情況下公司{0} {1},
Please set filter based on Item or Warehouse,根據項目或倉庫請設置過濾器,
Please set leave policy for employee {0} in Employee / Grade record,請在員工/成績記錄中為員工{0}設置休假政策,
Please set recurring after saving,請設置保存後復發,
Please set the Company,請設定公司,
Please set the Customer Address,請設置客戶地址,
Please set the Date Of Joining for employee {0},請為員工{0}設置加入日期,
Please set the Default Cost Center in {0} company.,請在{0}公司中設置默認成本中心。,
Please set the Email ID for the Student to send the Payment Request,請設置學生的電子郵件ID以發送付款請求,
Please set the Item Code first,請先設定商品代碼,
Please set the Payment Schedule,請設置付款時間表,
Please set the series to be used.,請設置要使用的系列。,
Please set {0} for address {1},請為地址{1}設置{0},
Please setup Students under Student Groups,請設置學生組的學生,
Please share your feedback to the training by clicking on 'Training Feedback' and then 'New',請通過點擊“培訓反饋”，然後點擊“新建”,
Please specify Company,請註明公司,
Please specify Company to proceed,請註明公司以處理,
Please specify a valid 'From Case No.',請指定一個有效的“從案號”,
Please specify a valid Row ID for row {0} in table {1},請指定行{0}在表中的有效行ID {1},
Please specify at least one attribute in the Attributes table,請指定屬性表中的至少一個屬性,
Please specify currency in Company,請公司指定的貨幣,
Please specify either Quantity or Valuation Rate or both,請註明無論是數量或估價率或兩者,
Please specify from/to range,請從指定/至範圍,
Please supply the specified items at the best possible rates,請在提供最好的利率規定的項目,
Please update your status for this training event,請更新此培訓活動的狀態,
Please wait 3 days before resending the reminder.,請重新發送提醒之前請等待3天。,
Point of Sale,銷售點,
Point-of-Sale,銷售點,
Point-of-Sale Profile,簡介銷售點的,
Portal,門戶,
Portal Settings,門戶網站設置,
Possible Supplier,可能的供應商,
Postal Expenses,郵政費用,
Posting Date,發布日期,
Posting Date cannot be future date,發布日期不能是未來的日期,
Posting Time,登錄時間,
Posting date and posting time is mandatory,登錄日期和登錄時間是必需的,
Posting timestamp must be after {0},登錄時間戳記必須晚於{0},
Potential opportunities for selling.,潛在的銷售機會。,
Practitioner Schedule,從業者時間表,
Preference,偏愛,
Prescribed Procedures,規定程序,
Prescription,處方,
Prescription Dosage,處方用量,
Prescription Duration,處方時間,
Prescriptions,處方,
Present,現在,
Prev,上一頁,
Preview,預覽,
Preview Salary Slip,預覽工資單,
Previous Financial Year is not closed,上一財政年度未關閉,
Price,價格,
Price List,價格表,
Price List Currency not selected,尚未選擇價格表之貨幣,
Price List Rate,價格列表費率,
Price List master.,價格表主檔,
Price List must be applicable for Buying or Selling,價格表必須適用於購買或出售,
Price List {0} is disabled or does not exist,價格表{0}禁用或不存在,
Price or product discount slabs are required,價格或產品折扣板是必需的,
Pricing,價錢,
Pricing Rule,定價規則,
"Pricing Rule is first selected based on 'Apply On' field, which can be Item, Item Group or Brand.",基於“適用於”欄位是「項目」，「項目群組」或「品牌」，而選擇定價規則。,
"Pricing Rule is made to overwrite Price List / define discount percentage, based on some criteria.",定價規則是由覆蓋價格表/定義折扣百分比，基於某些條件。,
Pricing Rule {0} is updated,定價規則{0}已更新,
Pricing Rules are further filtered based on quantity.,定價規則進一步過濾基於數量。,
Primary Address Details,主要地址詳情,
Primary Contact Details,主要聯繫方式,
Print Format,列印格式,
Print Report Card,打印報告卡,
Print Settings,列印設置,
Print and Stationery,印刷品和文具,
Print settings updated in respective print format,打印設置在相應的打印格式更新,
Print taxes with zero amount,打印零金額的稅,
Printing and Branding,印刷和品牌,
Private Equity,私募股權投資,
Privilege Leave,特權休假,
Probation,緩刑,
Probationary Period,試用期,
Process Day Book Data,處理日書數據,
Process Master Data,處理主數據,
Processing Chart of Accounts and Parties,處理會計科目和締約方,
Processing Items and UOMs,處理物品和UOM,
Processing Party Addresses,處理方地址,
Processing Vouchers,處理優惠券,
Procurement,採購,
Produced Qty,生產數量,
Product,產品,
Product Bundle,產品包,
Product Search,產品搜索,
Production,生產,
Production Item,生產項目,
Products,產品,
Profit and Loss,損益,
Profit for the year,年度利潤,
Program in the Fee Structure and Student Group {0} are different.,費用結構和學生組{0}中的課程是不同的。,
Progress % for a task cannot be more than 100.,為任務進度百分比不能超過100個。,
Project Collaboration Invitation,項目合作邀請,
Project Id,項目編號,
Project Manager,專案經理,
Project Name,專案名稱,
Project Start Date,專案開始日期,
Project Status,項目狀態,
Project Summary for {0},{0}的項目摘要,
Project Update.,項目更新。,
Project Value,專案值,
Project activity / task.,專案活動／任務。,
Project master.,專案主持。,
Project-wise data is not available for Quotation,項目明智的數據不適用於報價,
Projected,預計,
Projected Qty,預計數量,
Projected Quantity Formula,預計數量公式,
Projects,專案,
Property,屬性,
Property already added,已添加屬性,
Proposal Writing,提案寫作,
Proposal/Price Quote,提案/報價,
Provisional Profit / Loss (Credit),臨時溢利/（虧損）（信用）,
Publish Items on Website,公佈於網頁上的項目,
Published,已發行,
Purchase,採購,
Purchase Amount,購買金額,
Purchase Date,購買日期,
Purchase Invoice,採購發票,
Purchase Invoice {0} is already submitted,採購發票{0}已經提交,
Purchase Manager,採購經理,
Purchase Master Manager,採購主檔經理,
Purchase Order,採購訂單,
Purchase Order Amount,採購訂單金額,
Purchase Order Amount(Company Currency),採購訂單金額（公司貨幣）,
Purchase Order Date,採購訂單日期,
Purchase Order Items not received on time,未按時收到採購訂單項目,
Purchase Order number required for Item {0},所需物品{0}的採購訂單號,
Purchase Order to Payment,採購訂單到付款,
Purchase Order {0} is not submitted,採購訂單{0}未提交,
Purchase Orders are not allowed for {0} due to a scorecard standing of {1}.,由於{1}的記分卡，{0}不允許採購訂單。,
Purchase Orders given to Suppliers.,購買給供應商的訂單。,
Purchase Price List,採購價格表,
Purchase Receipt,採購入庫單,
Purchase Receipt {0} is not submitted,採購入庫單{0}未提交,
Purchase Tax Template,購置稅模板,
Purchase User,購買用戶,
Purchase orders help you plan and follow up on your purchases,採購訂單幫助您規劃和跟進您的購買,
Purchasing,購買,
Purpose must be one of {0},目的必須是一個{0},
Qty,數量,
Qty To Manufacture,製造數量,
Qty Total,數量總計,
Qty for {0},數量為{0},
Qualification,合格,
Quality,品質,
Quality Action,質量行動,
Quality Goal.,質量目標。,
Quality Inspection,品質檢驗,
Quality Inspection: {0} is not submitted for the item: {1} in row {2},質量檢驗：項目未提交{0}：行{2}中的{1},
Quality Management,品質管理,
Quality Meeting,質量會議,
Quality Procedure,質量程序,
Quality Procedure.,質量程序。,
Quality Review,質量審查,
Quantity,數量,
Quantity for Item {0} must be less than {1},項目{0}的數量必須小於{1},
Quantity in row {0} ({1}) must be same as manufactured quantity {2},列{0}的數量（{1}）必須與生產量{2}相同,
Quantity must be less than or equal to {0},量必須小於或等於{0},
Quantity must not be more than {0},數量必須不超過{0},
Quantity required for Item {0} in row {1},列{1}項目{0}必須有數量,
Quantity should be greater than 0,量應大於0,
Quantity to Make,數量,
Quantity to Manufacture must be greater than 0.,量生產必須大於0。,
Quantity to Produce,生產數量,
Quantity to Produce can not be less than Zero,生產數量不能少於零,
Query Options,查詢選項,
Queued for replacing the BOM. It may take a few minutes.,排隊等待更換BOM。可能需要幾分鐘時間。,
Queued for updating latest price in all Bill of Materials. It may take a few minutes.,排隊更新所有材料清單中的最新價格。可能需要幾分鐘。,
Quick Journal Entry,快速日記帳分錄,
Quot Count,報價計數,
Quot/Lead %,報價/鉛％,
Quotation,報價,
Quotation {0} is cancelled,{0}報價被取消,
Quotation {0} not of type {1},報價{0}非為{1}類型,
Quotations,語錄,
"Quotations are proposals, bids you have sent to your customers",語錄是建議，你已經發送到你的客戶提高出價,
Quotations received from Suppliers.,從供應商收到的報價。,
Quotations: ,語錄：,
Quotes to Leads or Customers.,行情到引線或客戶。,
RFQs are not allowed for {0} due to a scorecard standing of {1},由於{1}的記分卡，{0}不允許使用RFQ,
Range,範圍,
Rate,單價,
Rating,評分,
Raw Material,原料,
Raw Materials,原料,
Raw Materials cannot be blank.,原材料不能為空。,
Re-open,重新打開,
Read blog,閱讀博客,
Read the ERPNext Manual,閱讀ERPNext手冊,
Reading Uploaded File,閱讀上傳的文件,
Real Estate,房地產,
Reason For Putting On Hold,擱置的理由,
Reason for hold: ,暫停原因：,
Receipt,收據,
Receipt document must be submitted,收到文件必須提交,
Receivable,應收賬款,
Receivable Account,應收賬款,
Received On,收到了,
Received Quantity,收到的數量,
Received Stock Entries,收到的股票條目,
Receiver List is empty. Please create Receiver List,收受方列表為空。請創建收受方列表,
Recipients,受助人,
Reconcile,調和,
"Record of all communications of type email, phone, chat, visit, etc.",類型電子郵件，電話，聊天，訪問等所有通信記錄,
Records,記錄,
Redirect URL,重定向網址,
Ref,參考,
Ref Date,參考日期,
Reference,參考,
Reference #{0} dated {1},參考＃ {0}於{1},
Reference Date,參考日期,
Reference Doctype must be one of {0},參考文檔類型必須是一個{0},
Reference Document,參考文獻,
Reference Document Type,參考文檔類型,
Reference No & Reference Date is required for {0},參考號與參考日期須為{0},
Reference No and Reference Date is mandatory for Bank transaction,參考編號和參考日期是強制性的銀行交易,
Reference No is mandatory if you entered Reference Date,如果你輸入的參考日期，參考編號是強制性的,
Reference No.,參考編號。,
Reference Number,參考號碼,
Reference Owner,參考者,
Reference Type,參考類型,
"Reference: {0}, Item Code: {1} and Customer: {2}",參考：{0}，商品編號：{1}和顧客：{2},
References,參考,
Region,區域,
Reject,拒絕,
Rejected,拒絕,
Related,有關,
Relation with Guardian1,與關係Guardian1,
Relation with Guardian2,與關係Guardian2,
Release Date,發布日期,
Reload Linked Analysis,重新加載鏈接分析,
Remaining,剩餘,
Remaining Balance,保持平衡,
Remarks,備註,
Reminder to update GSTIN Sent,提醒更新GSTIN發送,
Remove item if charges is not applicable to that item,刪除項目，如果收費並不適用於該項目,
Removed items with no change in quantity or value.,刪除的項目在數量或價值沒有變化。,
Reopen,重新打開,
Reorder Level,重新排序級別,
Reorder Qty,再訂購數量,
Repeat Customer Revenue,重複客戶收入,
Repeat Customers,回頭客,
Replace BOM and update latest price in all BOMs,更換BOM並更新所有BOM中的最新價格,
Report,報告,
Report Builder,報表生成器,
Report Type,報告類型,
Report Type is mandatory,報告類型是強制性的,
Reports,報告,
Reqd By Date,REQD按日期,
Reqd Qty,需要數量,
Request for Quotation,詢價,
Request for Quotations,索取報價,
Request for Raw Materials,原材料申請,
Request for purchase.,請求您的報價。,
Request for quotation.,詢價。,
Requested Qty,要求數量,
"Requested Qty: Quantity requested for purchase, but not ordered.",要求的數量：數量要求的報價，但沒有下令。,
Requesting Site,請求網站,
Requesting payment against {0} {1} for amount {2},請求對付款{0} {1}量{2},
Requestor,請求者,
Required On,必填,
Required Qty,所需數量,
Required Quantity,所需數量,
Research & Development,研究與發展,
Researcher,研究員,
Resend Payment Email,重新發送付款電子郵件,
Reserve Warehouse,儲備倉庫,
Reserved Qty,保留數量,
Reserved Qty for Production,預留數量生產,
Reserved Qty for Production: Raw materials quantity to make manufacturing items.,生產保留數量：生產製造項目的原材料數量。,
"Reserved Qty: Quantity ordered for sale, but not delivered.",保留數量：訂購數量待出售，但尚未交付。,
Reserved Warehouse is mandatory for Item {0} in Raw Materials supplied,保留倉庫對於提供的原材料中的項目{0}是強制性的,
Reserved for manufacturing,預留製造,
Reserved for sale,保留出售,
Reserved for sub contracting,保留用於分包合同,
Resolve error and upload again.,解決錯誤並再次上傳。,
Responsibilities,職責,
Rest Of The World,世界其他地區,
Restart Subscription,重新啟動訂閱,
Restaurant,餐廳,
Result Date,結果日期,
Result already Submitted,結果已提交,
Resume,恢復,
Retail & Wholesale,零售及批發,
Retail Operations,零售業務,
Retention Stock Entry,保留股票入場,
Retention Stock Entry already created or Sample Quantity not provided,已創建保留庫存條目或未提供“樣本數量”,
Return,退貨,
Return / Credit Note,返回/信用票據,
Return / Debit Note,返回/借記注,
Returns,返回,
Reverse Journal Entry,反向日記帳分錄,
Review Invitation Sent,審核邀請已發送,
Review and Action,審查和行動,
Rooms Booked,客房預訂,
Root Type,root類型,
Root Type is mandatory,root類型是強制性的,
Root cannot be edited.,root不能被編輯。,
Root cannot have a parent cost center,root不能有一個父成本中心,
Round Off,四捨五入,
Rounded Total,整數總計,
Route,路線,
Row # {0}: Batch No must be same as {1} {2},行＃{0}：批號必須與{1} {2},
Row # {0}: Cannot return more than {1} for Item {2},行＃{0}：無法返回超過{1}項{2},
Row # {0}: Rate cannot be greater than the rate used in {1} {2},行＃{0}：速率不能大於{1} {2}中使用的速率,
Row # {0}: Serial No is mandatory,行＃{0}：序列號是必需的,
Row # {0}: Serial No {1} does not match with {2} {3},行＃{0}：序列號{1}不相匹配{2} {3},
Row #{0} (Payment Table): Amount must be negative,行＃{0}（付款表）：金額必須為負數,
Row #{0} (Payment Table): Amount must be positive,行＃{0}（付款表）：金額必須為正值,
Row #{0}: Account {1} does not belong to company {2},行＃{0}：帳戶{1}不屬於公司{2},
Row #{0}: Allocated Amount cannot be greater than outstanding amount.,行＃{0}：分配金額不能大於未結算金額。,
"Row #{0}: Asset {1} cannot be submitted, it is already {2}",行＃{0}：資產{1}無法提交，這已經是{2},
Row #{0}: Cannot set Rate if amount is greater than billed amount for Item {1}.,行＃{0}：如果金額大於項目{1}的開帳單金額，則無法設置費率。,
Row #{0}: Clearance date {1} cannot be before Cheque Date {2},行＃{0}：清除日期{1}無法支票日期前{2},
Row #{0}: Duplicate entry in References {1} {2},行＃{0}：引用{1} {2}中的重複條目,
Row #{0}: Expected Delivery Date cannot be before Purchase Order Date,行＃{0}：預計交貨日期不能在採購訂單日期之前,
Row #{0}: Item added,行＃{0}：已添加項目,
Row #{0}: Journal Entry {1} does not have account {2} or already matched against another voucher,行＃{0}：日記條目{1}沒有帳戶{2}或已經對另一憑證匹配,
Row #{0}: Not allowed to change Supplier as Purchase Order already exists,行＃{0}：不能更改供應商的採購訂單已經存在,
Row #{0}: Please set reorder quantity,行＃{0}：請設置再訂購數量,
Row #{0}: Please specify Serial No for Item {1},列#{0}：請為項目{1}指定序號,
Row #{0}: Qty increased by 1,行＃{0}：數量增加1,
Row #{0}: Rate must be same as {1}: {2} ({3} / {4}) ,行＃{0}：速率必須與{1}：{2}（{3} / {4}）,
Row #{0}: Reference Document Type must be one of Expense Claim or Journal Entry,行＃{0}：參考文檔類型必須是費用索賠或日記帳分錄之一,
"Row #{0}: Reference Document Type must be one of Purchase Order, Purchase Invoice or Journal Entry",行＃{0}：參考文件類型必須是採購訂單之一，購買發票或日記帳分錄,
Row #{0}: Rejected Qty can not be entered in Purchase Return,行＃{0}：駁回採購退貨數量不能進入,
Row #{0}: Rejected Warehouse is mandatory against rejected Item {1},行＃{0}：拒絕倉庫是強制性的反對否決項{1},
Row #{0}: Reqd by Date cannot be before Transaction Date,行號{0}：按日期請求不能在交易日期之前,
Row #{0}: Set Supplier for item {1},行＃{0}：設置供應商項目{1},
Row #{0}: Status must be {1} for Invoice Discounting {2},行＃{0}：發票貼現的狀態必須為{1} {2},
"Row #{0}: The batch {1} has only {2} qty. Please select another batch which has {3} qty available or split the row into multiple rows, to deliver/issue from multiple batches",行＃{0}：批次{1}只有{2}數量。請選擇具有{3}數量的其他批次，或將該行拆分成多個行，以便從多個批次中傳遞/發布,
Row #{0}: Timings conflicts with row {1},行＃{0}：與排時序衝突{1},
Row #{0}: {1} can not be negative for item {2},行＃{0}：{1}不能為負值對項{2},
Row No {0}: Amount cannot be greater than Pending Amount against Expense Claim {1}. Pending Amount is {2},行無{0}：金額不能大於金額之前對報銷{1}。待審核金額為{2},
Row {0} : Operation is required against the raw material item {1},行{0}：對原材料項{1}需要操作,
Row {0}# Allocated amount {1} cannot be greater than unclaimed amount {2},行{0}＃分配的金額{1}不能大於無人認領的金額{2},
Row {0}# Item {1} cannot be transferred more than {2} against Purchase Order {3},對於採購訂單{3}，行{0}＃項目{1}不能超過{2},
Row {0}# Paid Amount cannot be greater than requested advance amount,行{0}＃付費金額不能大於請求的提前金額,
Row {0}: Activity Type is mandatory.,行{0}：活動類型是強制性的。,
Row {0}: Advance against Customer must be credit,行{0}：提前對客戶必須是信用,
Row {0}: Advance against Supplier must be debit,行{0}：提前對供應商必須扣除,
Row {0}: Allocated amount {1} must be less than or equals to Payment Entry amount {2},行{0}：分配金額{1}必須小於或等於輸入付款金額{2},
Row {0}: Allocated amount {1} must be less than or equals to invoice outstanding amount {2},行{0}：已分配量{1}必須小於或等於發票餘額{2},
Row {0}: An Reorder entry already exists for this warehouse {1},行{0}：一個重新排序條目已存在這個倉庫{1},
Row {0}: Bill of Materials not found for the Item {1},行{0}：材料清單未找到項目{1},
Row {0}: Conversion Factor is mandatory,列#{0}：轉換係數是強制性的,
Row {0}: Cost center is required for an item {1},行{0}：項目{1}需要費用中心,
Row {0}: Credit entry can not be linked with a {1},行{0}：信用記錄無法被鏈接的{1},
Row {0}: Currency of the BOM #{1} should be equal to the selected currency {2},行{0}：BOM＃的貨幣{1}應等於所選貨幣{2},
Row {0}: Debit entry can not be linked with a {1},行{0}：借方條目不能與{1}連接,
Row {0}: Depreciation Start Date is required,行{0}：折舊開始日期是必需的,
Row {0}: Enter location for the asset item {1},行{0}：輸入資產項目{1}的位置,
Row {0}: Exchange Rate is mandatory,行{0}：匯率是必須的,
Row {0}: Expected Value After Useful Life must be less than Gross Purchase Amount,行{0}：使用壽命後的預期值必須小於總採購額,
Row {0}: From Time and To Time is mandatory.,行{0}：從時間和時間是強制性的。,
Row {0}: From Time and To Time of {1} is overlapping with {2},行{0}：從時間和結束時間{1}是具有重疊{2},
Row {0}: From time must be less than to time,行{0}：從時間開始必須小於時間,
Row {0}: Hours value must be greater than zero.,行{0}：小時值必須大於零。,
Row {0}: Invalid reference {1},行{0}：無效參考{1},
Row {0}: Party / Account does not match with {1} / {2} in {3} {4},行{0}：甲方/客戶不與匹配{1} / {2} {3} {4},
Row {0}: Party Type and Party is required for Receivable / Payable account {1},行{0}：黨的類型和黨的需要應收/應付帳戶{1},
Row {0}: Payment against Sales/Purchase Order should always be marked as advance,行{0}：付款方式對銷售/採購訂單應始終被標記為提前,
Row {0}: Please check 'Is Advance' against Account {1} if this is an advance entry.,行{0}：請檢查'是推進'對帳戶{1}，如果這是一個進步條目。,
Row {0}: Please set at Tax Exemption Reason in Sales Taxes and Charges,行{0}：請設置銷售稅和費用中的免稅原因,
Row {0}: Please set the Mode of Payment in Payment Schedule,行{0}：請在付款時間表中設置付款方式,
Row {0}: Please set the correct code on Mode of Payment {1},行{0}：請在付款方式{1}上設置正確的代碼,
Row {0}: Qty is mandatory,列#{0}：數量是強制性的,
Row {0}: Quality Inspection rejected for item {1},行{0}：項目{1}的質量檢驗被拒絕,
Row {0}: UOM Conversion Factor is mandatory,行{0}：計量單位轉換係數是必需的,
Row {0}: select the workstation against the operation {1},行{0}：根據操作{1}選擇工作站,
Row {0}: {1} Serial numbers required for Item {2}. You have provided {3}.,行{0}：{1}項目{2}所需的序列號。你已經提供{3}。,
Row {0}: {1} must be greater than 0,行{0}：{1}必須大於0,
Row {0}: {1} {2} does not match with {3},行{0}：{1} {2}與{3}不匹配,
Row {0}:Start Date must be before End Date,列#{0}：開始日期必須早於結束日期,
Rows with duplicate due dates in other rows were found: {0},發現其他行中具有重複截止日期的行：{0},
Rules for adding shipping costs.,增加運輸成本的規則。,
Rules for applying pricing and discount.,規則適用的定價和折扣。,
S.O. No.,SO號,
SGST Amount,SGST金額,
SO Qty,SO數量,
Safety Stock,安全庫存,
Salary,薪水,
Salary Slip ID,工資單編號,
Salary Slip of employee {0} already created for this period,員工的工資單{0}已為這一時期創建,
Salary Slip of employee {0} already created for time sheet {1},員工的工資單{0}已為時間表創建{1},
Salary Slip submitted for period from {0} to {1},從{0}到{1},
Salary Structure Assignment for Employee already exists,員工的薪酬結構分配已經存在,
Salary Structure Missing,薪酬結構缺失,
Salary Structure must be submitted before submission of Tax Ememption Declaration,薪酬結構必須在提交稅務徵收聲明之前提交,
Salary Structure not found for employee {0} and date {1},未找到員工{0}和日期{1}的薪資結構,
Salary Structure should have flexible benefit component(s) to dispense benefit amount,薪酬結構應該有靈活的福利組成來分配福利金額,
"Salary already processed for period between {0} and {1}, Leave application period cannot be between this date range.",工資已經處理了與{0}和{1}，留下申請期之間不能在此日期範圍內的時期。,
Sales,銷售,
Sales Account,銷售帳戶,
Sales Expenses,銷售費用,
Sales Funnel,銷售漏斗,
Sales Invoice,銷售發票,
Sales Invoice {0} has already been submitted,銷售發票{0}已提交,
Sales Invoice {0} must be cancelled before cancelling this Sales Order,銷售發票{0}必須早於此銷售訂單之前取消,
Sales Manager,銷售經理,
Sales Master Manager,銷售主檔經理,
Sales Order,銷售訂單,
Sales Order Item,銷售訂單項目,
Sales Order required for Item {0},所需的{0}項目銷售訂單,
Sales Order to Payment,銷售訂單到付款,
Sales Order {0} is not submitted,銷售訂單{0}未提交,
Sales Order {0} is not valid,銷售訂單{0}無效,
Sales Order {0} is {1},銷售訂單{0} {1},
Sales Orders,銷售訂單,
Sales Partner,銷售合作夥伴,
Sales Pipeline,銷售渠道,
Sales Price List,銷售價格表,
Sales Return,銷貨退回,
Sales Summary,銷售摘要,
Sales Tax Template,銷售稅模板,
Sales Team,銷售團隊,
Sales User,銷售用戶,
Sales and Returns,銷售和退貨,
Sales campaigns.,銷售活動。,
Sales orders are not available for production,銷售訂單不可用於生產,
Salutation,招呼,
Same Company is entered more than once,同一家公司進入不止一次,
Same item cannot be entered multiple times.,同一項目不能輸入多次。,
Same supplier has been entered multiple times,同一個供應商已多次輸入,
Sample,樣品,
Sample Collection,樣品收集,
Sample quantity {0} cannot be more than received quantity {1},採樣數量{0}不能超過接收數量{1},
Sanctioned,制裁,
Sanctioned Amount,制裁金額,
Sanctioned Amount cannot be greater than Claim Amount in Row {0}.,制裁金額不能大於索賠額行{0}。,
Saved,已儲存,
Scan Barcode,掃描條形碼,
Schedule,時間表,
Schedule Admission,安排入場,
Schedule Course,課程時間表,
Schedule Date,排定日期,
Schedule Discharge,附表卸貨,
Scheduled,預定,
Scheduled Upto,計劃的高級,
"Schedules for {0} overlaps, do you want to proceed after skiping overlaped slots ?",{0}重疊的時間表，是否要在滑動重疊的插槽後繼續？,
Score cannot be greater than Maximum Score,分數不能超過最高得分更大,
Score must be less than or equal to 5,得分必須小於或等於5,
Scorecards,記分卡,
Scrapped,報廢,
Search Results,搜索結果,
Search Sub Assemblies,搜索子組件,
"Search by item code, serial number, batch no or barcode",按項目代碼，序列號，批號或條碼進行搜索,
"Seasonality for setting budgets, targets etc.",季節性設置預算，目標等。,
Secret Key,密鑰,
Secretary,秘書,
Section Code,部分代碼,
Secured Loans,抵押貸款,
Securities & Commodity Exchanges,證券及商品交易所,
Securities and Deposits,證券及存款,
See all open tickets,查看所有打開的門票,
See past orders,查看過去的訂單,
See past quotations,查看過去的報價,
Select,選擇,
Select Alternate Item,選擇備用項目,
Select Attribute Values,選擇屬性值,
Select BOM,選擇BOM,
Select BOM and Qty for Production,選擇BOM和數量生產,
"Select BOM, Qty and For Warehouse",選擇BOM，Qty和For Warehouse,
Select Batch,選擇批次,
Select Batch Numbers,選擇批號,
Select Brand...,選擇品牌...,
Select Company,選擇公司,
Select Company...,選擇公司...,
Select Customer,選擇客戶,
Select Days,選擇天數,
Select Default Supplier,選擇默認供應商,
Select DocType,選擇DocType,
Select Fiscal Year...,選擇會計年度...,
Select Item (optional),選擇項目（可選）,
Select Items based on Delivery Date,根據交付日期選擇項目,
Select Items to Manufacture,選擇項目，以製造,
Select Loyalty Program,選擇忠誠度計劃,
Select Patient,選擇患者,
Select Possible Supplier,選擇潛在供應商,
Select Property,選擇屬性,
Select Quantity,選擇數量,
Select Serial Numbers,選擇序列號,
Select Target Warehouse,選擇目標倉庫,
Select Warehouse...,選擇倉庫...,
Select an account to print in account currency,選擇一個賬戶以賬戶貨幣進行打印,
Select an employee to get the employee advance.,選擇一名員工以推進員工。,
Select at least one value from each of the attributes.,從每個屬性中至少選擇一個值。,
Select change amount account,選擇變化量賬戶,
Select company first,首先選擇公司,
Select students manually for the Activity based Group,為基於活動的組手動選擇學生,
Select the customer or supplier.,選擇客戶或供應商。,
Select the nature of your business.,選擇您的業務的性質。,
Select the program first,首先選擇程序,
Select to add Serial Number.,選擇添加序列號。,
Select your Domains,選擇您的域名,
Selected Price List should have buying and selling fields checked.,選定價目表應該有買入和賣出的字段。,
Sell,賣,
Selling,銷售,
Selling Amount,銷售金額,
Selling Price List,賣價格表,
Selling Rate,賣出率,
"Selling must be checked, if Applicable For is selected as {0}",銷售必須進行檢查，如果適用於被選擇為{0},
Send Grant Review Email,發送格蘭特回顧郵件,
Send Now,立即發送,
Send SMS,發送短信,
Send mass SMS to your contacts,發送群發短信到您的聯絡人,
Sensitivity,靈敏度,
Sent,已送出,
Serial No and Batch,序列號和批次,
Serial No is mandatory for Item {0},項目{0}的序列號是強制性的,
Serial No {0} does not belong to Batch {1},序列號{0}不屬於批次{1},
Serial No {0} does not belong to Delivery Note {1},序列號{0}不屬於送貨單{1},
Serial No {0} does not belong to Item {1},序列號{0}不屬於項目{1},
Serial No {0} does not belong to Warehouse {1},序列號{0}不屬於倉庫{1},
Serial No {0} does not belong to any Warehouse,序列號{0}不屬於任何倉庫,
Serial No {0} does not exist,序列號{0}不存在,
Serial No {0} has already been received,已收到序號{0},
Serial No {0} is under maintenance contract upto {1},序列號{0}在維護合約期間內直到{1},
Serial No {0} is under warranty upto {1},序列號{0}在保修期內直到{1},
Serial No {0} not found,序列號{0}未找到,
Serial No {0} not in stock,序列號{0}無貨,
Serial No {0} quantity {1} cannot be a fraction,序列號{0}的數量量{1}不能是分數,
Serial Nos Required for Serialized Item {0},序列號為必填項序列為{0},
Serial Number: {0} is already referenced in Sales Invoice: {1},序號：{0}已在銷售發票中引用：{1},
Serial Numbers,序列號,
Serial Numbers in row {0} does not match with Delivery Note,行{0}中的序列號與交貨單不匹配,
Serial no {0} has been already returned,序列號{0}已被返回,
Serial number {0} entered more than once,序號{0}多次輸入,
Serialized Inventory,序列化庫存,
Series Updated,系列更新,
Series Updated Successfully,系列更新成功,
Series is mandatory,系列是強制性的,
Series {0} already used in {1},系列{0}已經被應用在{1},
Service,服務,
Service Expense,服務費用,
Service Level Agreement,服務水平協議,
Service Level Agreement.,服務水平協議。,
Service Level.,服務水平。,
Service Stop Date cannot be after Service End Date,服務停止日期不能在服務結束日期之後,
Service Stop Date cannot be before Service Start Date,服務停止日期不能早於服務開始日期,
Services,服務,
"Set Default Values like Company, Currency, Current Fiscal Year, etc.",設定預設值如公司，貨幣，當前財政年度等,
Set Details,設置細節,
Set New Release Date,設置新的發布日期,
Set Project and all Tasks to status {0}?,將項目和所有任務設置為狀態{0}？,
Set Status,設置狀態,
Set Tax Rule for shopping cart,購物車稅收規則設定,
Set as Closed,設置為關閉,
Set as Completed,設為已完成,
Set as Default,設為預設,
Set as Lost,設為失落,
Set as Open,設置為打開,
Set default inventory account for perpetual inventory,設置永久庫存的默認庫存帳戶,
Set this if the customer is a Public Administration company.,如果客戶是公共管理公司，請設置此項。,
Set {0} in asset category {1} or company {2},在資產類別{1}或公司{2}中設置{0},
"Setting Events to {0}, since the Employee attached to the below Sales Persons does not have a User ID{1}",設置活動為{0}，因為附連到下面的銷售者的僱員不具有用戶ID {1},
Setting defaults,設置默認值,
Setting up Email,設定電子郵件,
Setting up Email Account,設置電子郵件帳戶,
Setting up Employees,建立職工,
Setting up Taxes,建立稅,
Settings,設定,
"Settings for online shopping cart such as shipping rules, price list etc.",設定線上購物車，如航運規則，價格表等,
Settings for website homepage,對網站的主頁設置,
Settings for website product listing,網站產品列表的設置,
Setup Gateway accounts.,設置網關帳戶。,
Setup SMS gateway settings,設置短信閘道設置,
Setup cheque dimensions for printing,設置檢查尺寸打印,
Setup default values for POS Invoices,設置POS發票的默認值,
Setup mode of POS (Online / Offline),POS（在線/離線）的設置模式,
Setup your Institute in ERPNext,在ERPNext中設置您的研究所,
Share Balance,份額平衡,
Share Ledger,分享Ledger,
Share Management,分享管理,
Share Transfer,股份轉讓,
Share Type,分享類型,
Shareholder,股東,
Shipments,發貨,
Shipping,航運,
Shipping Address,送貨地址,
"Shipping Address does not have country, which is required for this Shipping Rule",送貨地址沒有國家，這是此送貨規則所必需的,
Shipping rule only applicable for Buying,運費規則只適用於購買,
Shipping rule only applicable for Selling,運費規則僅適用於銷售,
Shopify Supplier,Shopify供應商,
Shopping Cart,購物車,
Shopping Cart Settings,購物車設定,
Short Name,簡稱,
Shortage Qty,短缺數量,
Show Completed,顯示已完成,
Show Cumulative Amount,顯示累計金額,
Show Employee,顯示員工,
Show Open,公開顯示,
Show Opening Entries,顯示開場條目,
Show Payment Details,顯示付款詳情,
Show Return Entries,顯示返回條目,
Show Salary Slip,顯示工資單,
Show Variant Attributes,顯示變體屬性,
Show Variants,顯示變體,
Show closed,顯示關閉,
Show exploded view,顯示爆炸視圖,
Show only POS,只顯示POS,
Show unclosed fiscal year's P&L balances,顯示未關閉的會計年度的盈虧平衡,
Show zero values,顯示零值,
Single Variant,單一變種,
Single unit of an Item.,該產品的一個單元。,
"Skipping Leave Allocation for the following employees, as Leave Allocation records already exists against them. {0}",跳過以下員工的休假分配，因為已經存在針對他們的休假分配記錄。 {0},
"Skipping Salary Structure Assignment for the following employees, as Salary Structure Assignment records already exists against them. {0}",跳過下列員工的薪資結構分配，因為已存在針對他們的薪資結構分配記錄。 {0},
Slideshow,連續播放,
Slots for {0} are not added to the schedule,{0}的插槽未添加到計劃中,
Soap & Detergent,肥皂和洗滌劑,
Software,軟件,
Software Developer,軟件開發人員,
Softwares,軟件,
Soil compositions do not add up to 100,土壤成分不超過100,
Some emails are invalid,有些電子郵件無效,
Some information is missing,缺少一些信息,
Something went wrong!,出事了！,
"Sorry, Serial Nos cannot be merged",對不起，序列號無法合併,
Source,資源,
Source Name,源名稱,
Source Warehouse,來源倉庫,
Source and Target Location cannot be same,源和目標位置不能相同,
Source and target warehouse cannot be same for row {0},列{0}的來源和目標倉庫不可相同,
Source and target warehouse must be different,源和目標倉庫必須是不同的,
Source of Funds (Liabilities),資金來源（負債）,
Source warehouse is mandatory for row {0},列{0}的來源倉是必要的,
Specified BOM {0} does not exist for Item {1},指定BOM {0}的項目不存在{1},
Split Batch,拆分批次,
Split Issue,拆分問題,
Sports,體育,
Staffing Plan {0} already exist for designation {1},已存在人員配置計劃{0}以用於指定{1},
Standard,標準,
Standard Buying,標準採購,
Standard Selling,標準銷售,
Standard contract terms for Sales or Purchase.,銷售或採購的標準合同條款。,
Start Date,開始日期,
Start Date of Agreement can't be greater than or equal to End Date.,協議的開始日期不得大於或等於結束日期。,
Start Year,開始年份,
"Start and end dates not in a valid Payroll Period, cannot calculate {0}",開始和結束日期不在有效的工資核算期內，無法計算{0},
"Start and end dates not in a valid Payroll Period, cannot calculate {0}.",開始日期和結束日期不在有效的工資核算期間內，無法計算{0}。,
Start date should be less than end date for Item {0},項目{0}的開始日期必須小於結束日期,
Start date should be less than end date for task {0},開始日期應該小於任務{0}的結束日期,
Start day is greater than end day in task '{0}',開始日期大於任務“{0}”的結束日期,
Start on,開始,
State/UT Tax,州/ UT稅,
Statement of Account,帳戶狀態,
Status must be one of {0},狀態必須是一個{0},
Stock,庫存,
Stock Adjustment,庫存調整,
Stock Analytics,庫存分析,
Stock Assets,庫存資產,
Stock Available,現貨供應,
Stock Balance,庫存餘額,
Stock Entries already created for Work Order ,已為工單創建的庫存條目,
Stock Entry,存貨分錄,
Stock Entry {0} created,股票輸入{0}創建,
Stock Entry {0} is not submitted,股票輸入{0}不提交,
Stock Expenses,庫存費用,
Stock In Hand,庫存在手,
Stock Items,庫存產品,
Stock Ledger,庫存總帳,
Stock Ledger Entries and GL Entries are reposted for the selected Purchase Receipts,針對所選的採購入庫單，存貨帳分錄和總帳分錄已經重新登錄。,
Stock Levels,庫存水平,
Stock Liabilities,現貨負債,
Stock Options,股票期權,
Stock Qty,庫存數量,
Stock Received But Not Billed,庫存接收，但不付款,
Stock Reports,庫存報告,
Stock Summary,股票摘要,
Stock Transactions,庫存交易明細,
Stock UOM,庫存計量單位,
Stock Value,庫存價值,
Stock balance in Batch {0} will become negative {1} for Item {2} at Warehouse {3},在批量庫存餘額{0}將成為負{1}的在倉庫項目{2} {3},
Stock cannot be updated against Delivery Note {0},送貨單{0}不能更新庫存,
Stock cannot be updated against Purchase Receipt {0},股票不能對外購入庫單進行更新{0},
Stock cannot exist for Item {0} since has variants,股票可以為項目不存在{0}，因為有變種,
Stock transactions before {0} are frozen,{0}前的庫存交易被凍結,
Stopped,停止,
"Stopped Work Order cannot be cancelled, Unstop it first to cancel",停止的工作訂單不能取消，先取消它,
Stores,商店,
Structures have been assigned successfully,已成功分配結構,
Student,學生,
Student Activity,學生活動,
Student Address,學生地址,
Student Admissions,學生入學,
Student Attendance,學生出勤,
"Student Batches help you track attendance, assessments and fees for students",學生批幫助您跟踪學生的出勤，評估和費用,
Student Email Address,學生的電子郵件地址,
Student Email ID,學生的電子郵件ID,
Student Group,學生組,
Student Group Strength,學生群體力量,
Student Group is already updated.,學生組已經更新。,
Student Group: ,學生組：,
Student ID,學生卡,
Student ID: ,學生卡：,
Student LMS Activity,學生LMS活動,
Student Mobile No.,學生手機號碼,
Student Name,學生姓名,
Student Name: ,學生姓名：,
Student Report Card,學生報告卡,
Student is already enrolled.,學生已經註冊。,
Student {0} - {1} appears Multiple times in row {2} & {3},學生{0}  -  {1}出現連續中多次{2}和{3},
Student {0} does not belong to group {1},學生{0}不屬於組{1},
Student {0} exist against student applicant {1},學生{0}存在針對學生申請{1},
"Students are at the heart of the system, add all your students",學生在系統的心臟，添加所有的學生,
Sub Assemblies,子組件,
Sub Type,子類型,
Subcontract,轉包,
Subject,主題,
Submit Proof,提交證明,
Submit Salary Slip,提交工資單,
Submit this Work Order for further processing.,提交此工單以進一步處理。,
Submit this to create the Employee record,提交這個來創建員工記錄,
Submitting Salary Slips...,提交工資單......,
Subscription,訂閱,
Subscription Management,訂閱管理,
Subscriptions,訂閱,
Subtotal,小計,
Successfully Reconciled,不甘心成功,
Successfully Set Supplier,成功設置供應商,
Successfully created payment entries,成功創建付款條目,
Successfully deleted all transactions related to this company!,成功刪除與該公司相關的所有交易！,
Sum of Scores of Assessment Criteria needs to be {0}.,評估標準的得分之和必須是{0}。,
Sum of points for all goals should be 100. It is {0},對所有目標點的總和應該是100。{0},
Summary,摘要,
Summary for this month and pending activities,本月和待活動總結,
Summary for this week and pending activities,本週和待活動總結,
Sunday,星期日,
Suplier,Suplier,
Supplier,供應商,
Supplier Group,供應商集團,
Supplier Group master.,供應商組主人。,
Supplier Id,供應商編號,
Supplier Invoice Date cannot be greater than Posting Date,供應商發票的日期不能超過過帳日期更大,
Supplier Invoice No,供應商發票號碼,
Supplier Invoice No exists in Purchase Invoice {0},供應商發票不存在採購發票{0},
Supplier Name,供應商名稱,
Supplier Part No,供應商部件號,
Supplier Quotation,供應商報價,
Supplier Scorecard,供應商記分卡,
Supplier Warehouse mandatory for sub-contracted Purchase Receipt,對於轉包的採購入庫單，供應商倉庫是強制性輸入的。,
Supplier database.,供應商數據庫。,
Supplier {0} not found in {1},在{1}中找不到供應商{0},
Supplier(s),供應商（S）,
Supplies made to UIN holders,供應給UIN持有人的供應品,
Supplies made to Unregistered Persons,向未登記人員提供的物資,
Suppliies made to Composition Taxable Persons,對合成納稅人的補貼,
Supply Type,供應類型,
Support Analytics,支援分析,
Support Settings,支持設置,
Support Tickets,支持門票,
Support queries from customers.,客戶支持查詢。,
Sync has been temporarily disabled because maximum retries have been exceeded,暫時禁用了同步，因為已超出最大重試次數,
Syntax error in condition: {0},條件中的語法錯誤：{0},
Syntax error in formula or condition: {0},式或條件語法錯誤：{0},
System Manager,系統管理器,
Tap items to add them here,點擊項目將其添加到此處,
Target,目標,
Target ({}),目標（{}）,
Target On,目標在,
Target Warehouse,目標倉庫,
Target warehouse is mandatory for row {0},目標倉庫是強制性的行{0},
Task,任務,
Tasks,任務,
Tasks have been created for managing the {0} disease (on row {1}),為管理{0}疾病創建了任務（在第{1}行）,
Tax,稅,
Tax Assets,所得稅資產,
Tax Category,稅種,
Tax Category for overriding tax rates.,最高稅率的稅收類別。,
"Tax Category has been changed to ""Total"" because all the Items are non-stock items",稅項類別已更改為“合計”，因為所有物品均為非庫存物品,
Tax ID,稅號,
Tax Id: ,稅號：,
Tax Rate,稅率,
Tax Rule Conflicts with {0},稅收規範衝突{0},
Tax Rule for transactions.,稅收規則進行的交易。,
Tax Template is mandatory.,稅務模板是強制性的。,
Tax Withholding rates to be applied on transactions.,稅收預扣稅率適用於交易。,
Tax template for buying transactions.,稅務模板購買交易。,
Tax template for item tax rates.,項目稅率的稅收模板。,
Tax template for selling transactions.,稅務模板賣出的交易。,
Taxable Amount,應稅金額,
Taxes,稅,
Team Updates,團隊更新,
Technology,技術,
Telecommunications,電信,
Telephone Expenses,電話費,
Television,電視,
Template Name,模板名稱,
Template of terms or contract.,模板條款或合同。,
Templates of supplier scorecard criteria.,供應商計分卡標準模板。,
Templates of supplier scorecard variables.,供應商記分卡變數模板。,
Templates of supplier standings.,供應商榜單。,
Temporarily on Hold,暫時擱置,
Temporary,臨時,
Temporary Accounts,臨時帳戶,
Temporary Opening,臨時開通,
Terms and Conditions,條款和條件,
Terms and Conditions Template,條款及細則範本,
Territory,領土,
Test,測試,
Thank you,謝謝,
Thank you for your business!,感謝您的業務！,
The 'From Package No.' field must neither be empty nor it's value less than 1.,“From Package No.”字段不能為空，也不能小於1。,
The Brand,品牌,
The Item {0} cannot have Batch,該項目{0}不能有批,
The Loyalty Program isn't valid for the selected company,忠誠度計劃對所選公司無效,
The Payment Term at row {0} is possibly a duplicate.,第{0}行的支付條款可能是重複的。,
The Term End Date cannot be earlier than the Term Start Date. Please correct the dates and try again.,該期限結束日期不能超過期限開始日期。請更正日期，然後再試一次。,
The Term End Date cannot be later than the Year End Date of the Academic Year to which the term is linked (Academic Year {}). Please correct the dates and try again.,該期限結束日期不能晚於學年年終日期到這個詞聯繫在一起（學年{}）。請更正日期，然後再試一次。,
The Term Start Date cannot be earlier than the Year Start Date of the Academic Year to which the term is linked (Academic Year {}). Please correct the dates and try again.,這個詞開始日期不能超過哪個術語鏈接學年的開學日期較早（學年{}）。請更正日期，然後再試一次。,
The Year End Date cannot be earlier than the Year Start Date. Please correct the dates and try again.,年末日期不能超過年度開始日期。請更正日期，然後再試一次。,
The amount of {0} set in this payment request is different from the calculated amount of all payment plans: {1}. Make sure this is correct before submitting the document.,此付款申請中設置的{0}金額與所有付款計劃的計算金額不同：{1}。在提交文檔之前確保這是正確的。,
The day(s) on which you are applying for leave are holidays. You need not apply for leave.,您目前提出休假申請的日期為休息日。您不需要申請休假。,
The field From Shareholder cannot be blank,來自股東的字段不能為空,
The field To Shareholder cannot be blank,“股東”字段不能為空,
The fields From Shareholder and To Shareholder cannot be blank,來自股東和股東的字段不能為空,
The folio numbers are not matching,作品集編號不匹配,
The holiday on {0} is not between From Date and To Date,在{0}這個節日之間沒有從日期和結束日期,
The name of the institute for which you are setting up this system.,該機構的名稱要為其建立這個系統。,
The name of your company for which you are setting up this system.,您的公司要為其設立這個系統的名稱。,
The number of shares and the share numbers are inconsistent,股份數量和股票數量不一致,
The payment gateway account in plan {0} is different from the payment gateway account in this payment request,計劃{0}中的支付網關帳戶與此付款請求中的支付網關帳戶不同,
The selected BOMs are not for the same item,所選的材料清單並不同樣項目,
The selected item cannot have Batch,所選項目不能批,
The seller and the buyer cannot be the same,賣方和買方不能相同,
The shareholder does not belong to this company,股東不屬於這家公司,
The shares already exist,股份已經存在,
The shares don't exist with the {0},這些份額不存在於{0},
"The task has been enqueued as a background job. In case there is any issue on processing in background, the system will add a comment about the error on this Stock Reconciliation and revert to the Draft stage",該任務已被列入後台工作。如果在後台處理有任何問題，系統將在此庫存對帳中添加有關錯誤的註釋，並恢復到草稿階段,
"Then Pricing Rules are filtered out based on Customer, Customer Group, Territory, Supplier, Supplier Type, Campaign, Sales Partner etc.",然後定價規則將被過濾掉基於客戶，客戶群組，領地，供應商，供應商類型，活動，銷售合作夥伴等。,
"There are inconsistencies between the rate, no of shares and the amount calculated",費率，股份數量和計算的金額之間不一致,
There are more holidays than working days this month.,還有比這個月工作日更多的假期。,
There can be multiple tiered collection factor based on the total spent. But the conversion factor for redemption will always be same for all the tier.,根據總花費可以有多個分層收集因子。但兌換的兌換係數對於所有等級總是相同的。,
There can only be 1 Account per Company in {0} {1},只能有每公司1帳戶{0} {1},
"There can only be one Shipping Rule Condition with 0 or blank value for ""To Value""",只能有一個運輸規則條件為0或空值“ To值”,
There is no leave period in between {0} and {1},{0}和{1}之間沒有休假期限,
There is not enough leave balance for Leave Type {0},沒有足夠的餘額休假請假類型{0},
There is nothing to edit.,無內容可供編輯,
There isn't any item variant for the selected item,所選項目沒有任何項目變體,
"There seems to be an issue with the server's GoCardless configuration. Don't worry, in case of failure, the amount will get refunded to your account.",服務器的GoCardless配置似乎存在問題。別擔心，如果失敗，這筆款項將退還給您的帳戶。,
There were errors creating Course Schedule,創建課程表時出現錯誤,
There were errors.,有錯誤。,
This Item is a Template and cannot be used in transactions. Item attributes will be copied over into the variants unless 'No Copy' is set,這個項目是一個模板，並且可以在交易不能使用。項目的屬性將被複製到變型，除非“不複製”設置,
This Item is a Variant of {0} (Template).,此項目是{0}（模板）的變體。,
This Month's Summary,本月總結,
This Week's Summary,本週的總結,
This action will stop future billing. Are you sure you want to cancel this subscription?,此操作將停止未來的結算。您確定要取消此訂閱嗎？,
This covers all scorecards tied to this Setup,這涵蓋了與此安裝程序相關的所有記分卡,
This document is over limit by {0} {1} for item {4}. Are you making another {3} against the same {2}?,這份文件是超過限制，通過{0} {1}項{4}。你在做另一個{3}對同一{2}？,
This is a root account and cannot be edited.,這是一個 root 帳戶，不能被編輯。,
This is a root customer group and cannot be edited.,ERPNext是一個開源的基於Web的ERP系統，通過網路技術，向私人有限公司提供整合的工具，在一個小的組織管理大多數流程。有關Web註釋，或購買託管，想得到更多資訊，請連結,
This is a root department and cannot be edited.,這是根部門，無法編輯。,
This is a root healthcare service unit and cannot be edited.,這是根醫療保健服務單位，不能編輯。,
This is a root item group and cannot be edited.,這是個根項目群組，且無法被編輯。,
This is a root sales person and cannot be edited.,您可以通過選擇備份頻率啟動和\,
This is a root supplier group and cannot be edited.,這是一個根源供應商組，無法編輯。,
This is a root territory and cannot be edited.,集團或Ledger ，借方或貸方，是特等帳戶,
This is an example website auto-generated from ERPNext,這是一個由 ERPNext 自動產生的範例網站,
This is based on logs against this Vehicle. See timeline below for details,這是基於對本車輛的日誌。詳情請參閱以下時間表,
This is based on stock movement. See {0} for details,這是基於庫存移動。見{0}詳情,
This is based on the Time Sheets created against this project,這是基於對這個項目產生的考勤表,
This is based on the attendance of this Employee,這是基於該員工的考勤,
This is based on the attendance of this Student,這是基於這名學生出席,
This is based on transactions against this Customer. See timeline below for details,這是基於對這個顧客的交易。詳情請參閱以下時間表,
This is based on transactions against this Healthcare Practitioner.,這是基於針對此醫療保健從業者的交易。,
This is based on transactions against this Patient. See timeline below for details,這是基於對這個病人的交易。有關詳情，請參閱下面的時間表,
This is based on transactions against this Sales Person. See timeline below for details,這是基於針對此銷售人員的交易。請參閱下面的時間表了解詳情,
This is based on transactions against this Supplier. See timeline below for details,這是基於對這種供應商的交易。詳情請參閱以下時間表,
This will submit Salary Slips and create accrual Journal Entry. Do you want to proceed?,這將提交工資單，並創建權責發生製日記賬分錄。你想繼續嗎？,
This {0} conflicts with {1} for {2} {3},此{0}衝突{1}在{2} {3},
Time Sheet for manufacturing.,時間表製造。,
Time Tracking,時間跟踪,
"Time slot skiped, the slot {0} to {1} overlap exisiting slot {2} to {3}",時隙滑動，時隙{0}到{1}與現有時隙{2}重疊到{3},
Time slots added,添加時隙,
Time(in mins),時間（分鐘）,
Timer,計時器,
Timer exceeded the given hours.,計時器超出了指定的小時數,
Timesheet,時間表,
Timesheet for tasks.,時間表的任務。,
Timesheet {0} is already completed or cancelled,時間表{0}已完成或取消,
Timesheets,時間表,
"Timesheets help keep track of time, cost and billing for activites done by your team",時間表幫助追踪的時間，費用和結算由你的團隊做activites,
Titles for print templates e.g. Proforma Invoice.,"列印模板的標題, 例如 Proforma Invoice。",
To Bill,發票待輸入,
To Date cannot be before From Date,無效的主名稱,
To Date cannot be less than From Date,迄今不能少於起始日期,
To Date must be greater than From Date,到日期必須大於從日期,
To Date should be within the Fiscal Year. Assuming To Date = {0},日期應該是在財政年度內。假設終止日期= {0},
To Datetime,以日期時間,
To Deliver,為了提供,
To Deliver and Bill,準備交貨及開立發票,
To Fiscal Year,到財政年度,
To Party Name,到黨名,
To Pin Code,要密碼,
To Receive,接受,
To Receive and Bill,準備收料及接收發票,
To State,國家,
To Warehouse,到倉庫,
To create a Payment Request reference document is required,要創建付款請求參考文檔是必需的,
To date can not be equal or less than from date,迄今為止不能等於或少於日期,
To date can not be less than from date,迄今為止不能少於起始日期,
To date can not greater than employee's relieving date,迄今為止不能超過員工的免除日期,
"To filter based on Party, select Party Type first",要根據黨的篩選，選擇黨第一類型,
"To get the best out of ERPNext, we recommend that you take some time and watch these help videos.",為得到最好的 ERPNext 教學，我們建議您花一些時間和觀看這些說明影片。,
"To include tax in row {0} in Item rate, taxes in rows {1} must also be included",要包括稅款，行{0}項率，稅收行{1}也必須包括在內,
To make Customer based incentive schemes.,制定基於客戶的激勵計劃。,
"To merge, following properties must be same for both items",若要合併，以下屬性必須為這兩個項目是相同的,
"To not apply Pricing Rule in a particular transaction, all applicable Pricing Rules should be disabled.",要在一個特定的交易不適用於定價規則，所有適用的定價規則應該被禁用。,
"To set this Fiscal Year as Default, click on 'Set as Default'",要設定這個財政年度為預設值，點擊“設為預設”,
To view logs of Loyalty Points assigned to a Customer.,查看分配給客戶的忠誠度積分的日誌。,
To {0},至{0},
To {0} | {1} {2},至{0} | {1} {2},
Toggle Filters,切換過濾器,
Too many columns. Export the report and print it using a spreadsheet application.,過多的列數。請導出報表，並使用試算表程式進行列印。,
Total (Credit),總（信用）,
Total (Without Tax),總計（不含稅）,
Total Absent,共缺席,
Total Achieved,實現總計,
Total Actual,實際總計,
Total Allocated Leaves,總分配的葉子,
Total Amount,總金額,
Total Amount Credited,總金額,
Total Applicable Charges in Purchase Receipt Items table must be same as Total Taxes and Charges,在外購入庫單項目表總的相關費用必須是相同的總稅費,
Total Budget,預算總額,
Total Collected: {0},總計：{0},
Total Commission,佣金總計,
Total Contribution Amount: {0},總貢獻金額：{0},
Total Credit/ Debit Amount should be same as linked Journal Entry,總信用/借方金額應與鏈接的日記帳分錄相同,
Total Debit must be equal to Total Credit. The difference is {0},借方總額必須等於貸方總額。差額為{0},
Total Deduction,扣除總額,
Total Invoiced Amount,發票總金額,
Total Leaves,葉總,
Total Order Considered,總訂貨考慮,
Total Order Value,總訂單價值,
Total Outgoing,出貨總計,
Total Outstanding,總計傑出,
Total Outstanding Amount,未償還總額,
Total Outstanding: {0},總計：{0},
Total Paid Amount,總支付金額,
Total Payment Amount in Payment Schedule must be equal to Grand / Rounded Total,支付計劃中的總付款金額必須等於大/圓,
Total Payments,總付款,
Total Present,總現,
Total Qty,總數量,
Total Quantity,總數（量,
Total Revenue,總收入,
Total Student,學生總數,
Total Target,總目標,
Total Tax,總稅收,
Total Taxable Amount,應納稅總額,
Total Taxable Value,應稅總額,
Total Unpaid: {0},總未付：{0},
Total Variance,總方差,
Total Weightage of all Assessment Criteria must be 100%,所有評估標準的權重總數要達到100％,
Total advance ({0}) against Order {1} cannot be greater than the Grand Total ({2}),總的超前（{0}）對二階{1}不能大於總計（{2}）,
Total advance amount cannot be greater than total claimed amount,總預付金額不能超過索賠總額,
Total advance amount cannot be greater than total sanctioned amount,總預付金額不得超過全部認可金額,
Total allocated leaves are more days than maximum allocation of {0} leave type for employee {1} in the period,在此期間，總分配的離職時間超過員工{1}的最大分配{0}離職類型的天數,
Total allocated leaves are more than days in the period,分配的總葉多天的期限,
Total allocated percentage for sales team should be 100,對於銷售團隊總分配比例應為100,
Total cannot be zero,總計不能為零,
Total contribution percentage should be equal to 100,總貢獻百分比應等於100,
Total flexible benefit component amount {0} should not be less than max benefits {1},總靈活福利金額{0}不應低於最高福利金額{1},
Total hours: {0},總時間：{0},
Total leaves allocated is mandatory for Leave Type {0},為假期類型{0}分配的總分配數是強制性的,
Total working hours should not be greater than max working hours {0},總的工作時間不應超過最高工時更大{0},
Total {0} ({1}),總{0}（{1}）,
"Total {0} for all items is zero, may be you should change 'Distribute Charges Based On'",共有{0}所有項目為零，可能是你應該“基於分佈式費用”改變,
Total(Amt),總（AMT）,
Total(Qty),總計（數量）,
Traceback,追溯,
Track Leads by Lead Source.,通過鉛源追踪潛在客戶。,
Training,訓練,
Training Event,培訓活動,
Training Events,培訓活動,
Training Feedback,培訓反饋,
Training Result,訓練結果,
Transaction Type,交易類型,
Transaction currency must be same as Payment Gateway currency,交易貨幣必須與支付網關貨幣,
Transaction not allowed against stopped Work Order {0},不允許對停止的工單{0}進行交易,
Transaction reference no {0} dated {1},交易參考編號{0}日{1},
Transactions can only be deleted by the creator of the Company,交易只能由公司的創建者被刪除,
Transfer,轉讓,
Transfer Material,轉印材料,
Transfer Type,轉移類型,
Transfer an asset from one warehouse to another,從一個倉庫轉移資產到另一,
Transfered,轉移,
Transferred Quantity,轉移數量,
Transport Receipt Date,運輸收貨日期,
Transport Receipt No,運輸收據編號,
Transportation,運輸,
Transporter ID,運輸商ID,
Transporter Name,貨運公司名稱,
Travel,旅遊,
Travel Expenses,差旅費,
Tree Type,樹類型,
Tree of Bill of Materials,物料清單樹狀圖,
Tree of Item Groups.,項目群組樹。,
Tree of Procedures,程序樹,
Tree of Quality Procedures.,質量樹程序。,
Tree of financial Cost Centers.,財務成本中心的樹。,
Tree of financial accounts.,財務賬目的樹。,
Treshold {0}% appears more than once,Treshold {0}出現％不止一次,
Trial Period End Date Cannot be before Trial Period Start Date,試用期結束日期不能在試用期開始日期之前,
Trialling,試用,
Type of Business,業務類型,
Types of activities for Time Logs,活動類型的時間記錄,
UOM,UOM,
UOM Conversion factor is required in row {0},計量單位換算係數是必需的行{0},
UOM coversion factor required for UOM: {0} in Item: {1},所需的計量單位計量單位：丁文因素：{0}項：{1},
URL,網址,
Unable to find DocType {0},無法找到DocType {0},
Unable to find exchange rate for {0} to {1} for key date {2}. Please create a Currency Exchange record manually,無法為關鍵日期{2}查找{0}到{1}的匯率。請手動創建貨幣兌換記錄,
Unable to find score starting at {0}. You need to have standing scores covering 0 to 100,無法從{0}開始獲得分數。你需要有0到100的常規分數,
Unable to find variable: ,無法找到變量：,
Unblock Invoice,取消屏蔽發票,
Uncheck all,取消所有,
Unclosed Fiscal Years Profit / Loss (Credit),未關閉的財年利潤/損失（信用）,
Unit,單位,
Unit of Measure,計量單位,
Unit of Measure {0} has been entered more than once in Conversion Factor Table,計量單位{0}已經進入不止一次在轉換係數表,
Unsecured Loans,無抵押貸款,
Unsubscribe from this Email Digest,從該電子郵件摘要退訂,
Unsubscribed,退訂,
Unverified Webhook Data,未經驗證的Webhook數據,
Update Account Name / Number,更新帳戶名稱/號碼,
Update Account Number / Name,更新帳號/名稱,
Update Items,更新項目,
Update Response,更新響應,
Update bank payment dates with journals.,更新與日記帳之銀行付款日期。,
Update in progress. It might take a while.,正在更新。它可能需要一段時間。,
Update rate as per last purchase,根據上次購買更新率,
Update stock must be enable for the purchase invoice {0},必須為購買發票{0}啟用更新庫存,
Updating Variants...,更新變體......,
Upload your letter head and logo. (you can edit them later).,上傳你的信頭和標誌。 （您可以在以後對其進行編輯）。,
Used Leaves,使用的葉子,
User,用戶,
User ID,使用者 ID,
User ID not set for Employee {0},用戶ID不為員工設置{0},
User Remark,用戶備註,
User has not applied rule on the invoice {0},用戶未在發票{0}上應用規則,
User {0} already exists,用戶{0}已經存在,
User {0} created,用戶{0}已創建,
User {0} does not exist,用戶{0}不存在,
User {0} doesn't have any default POS Profile. Check Default at Row {1} for this User.,用戶{0}沒有任何默認的POS配置文件。檢查此用戶的行{1}處的默認值。,
User {0} is already assigned to Employee {1},用戶{0}已經被分配給員工{1},
User {0} is already assigned to Healthcare Practitioner {1},用戶{0}已分配給Healthcare Practitioner {1},
Users,用戶,
Utility Expenses,公用事業費用,
Valid From Date must be lesser than Valid Upto Date.,有效起始日期必須小於有效起始日期。,
Valid from and valid upto fields are mandatory for the cumulative,有效且有效的最多字段對於累積是必需的,
Valid from date must be less than valid upto date,從日期開始有效必須低於最新有效期,
Valid till date cannot be before transaction date,有效日期不能在交易日期之前,
Validity,合法性,
Validity period of this quotation has ended.,此報價的有效期已經結束。,
Valuation Rate,估值率,
Valuation Rate is mandatory if Opening Stock entered,估價費用是強制性的，如果打開股票進入,
Valuation type charges can not marked as Inclusive,估值類型罪名不能標記為包容性,
Value Or Qty,價值或數量,
Value Proposition,價值主張,
Value for Attribute {0} must be within the range of {1} to {2} in the increments of {3} for Item {4},為屬性{0}值必須的範圍內{1}到{2}中的增量{3}為項目{4},
Value missing,價值缺失,
Value must be between {0} and {1},值必須介於{0}和{1}之間,
"Values of exempt, nil rated and non-GST inward supplies",豁免，零稅率和非商品及服務稅內向供應的價值,
Variable,變量,
Variance ({}),差異（{}）,
Variant,變種,
Variant Attributes,變量屬性,
Variant Based On cannot be changed,Variant Based On無法更改,
Variant Details Report,變體詳細信息報告,
Variant creation has been queued.,變體創建已經排隊。,
Vehicle Expenses,車輛費用,
Vehicle No,車輛牌照號碼,
Vehicle Type,車輛類型,
Vehicle/Bus Number,車輛/巴士號碼,
Venture Capital,創業投資,
View Chart of Accounts,查看會計科目表,
View Fees Records,查看費用記錄,
View Lab Tests,查看實驗室測試,
View Leads,查看訊息,
View Ledger,查看總帳,
View Now,立即觀看,
View a list of all the help videos,查看所有幫助影片名單,
View in Cart,查看你的購物車,
Visit report for maintenance call.,訪問報告維修電話。,
Visit the forums,訪問論壇,
Vital Signs,生命體徵,
Volunteer,志願者,
Volunteer Type information.,志願者類型信息。,
Volunteer information.,志願者信息。,
Voucher #,憑證＃,
Voucher No,憑證編號,
Voucher Type,憑證類型,
WIP Warehouse,WIP倉庫,
Walk In,走在,
Warehouse can not be deleted as stock ledger entry exists for this warehouse.,這個倉庫不能被刪除，因為庫存分錄帳尚存在。,
Warehouse cannot be changed for Serial No.,倉庫不能改變序列號,
Warehouse is mandatory,倉庫是強制性的,
Warehouse is mandatory for stock Item {0} in row {1},倉庫是強制性的股票項目{0}行{1},
Warehouse not found in the system,倉庫系統中未找到,
"Warehouse required at Row No {0}, please set default warehouse for the item {1} for the company {2}",在第{0}行，需要倉庫，請為公司{2}的物料{1}設置默認倉庫,
Warehouse required for stock Item {0},倉庫需要現貨產品{0},
Warehouse {0} can not be deleted as quantity exists for Item {1},倉庫{0} 不能被刪除因為項目{1}還有庫存,
Warehouse {0} does not belong to company {1},倉庫{0}不屬於公司{1},
Warehouse {0} does not exist,倉庫{0}不存在,
"Warehouse {0} is not linked to any account, please mention the account in  the warehouse record or set default inventory account in company {1}.",倉庫{0}未與任何帳戶關聯，請在倉庫記錄中提及該帳戶，或在公司{1}中設置默認庫存帳戶。,
Warehouses with child nodes cannot be converted to ledger,與子節點倉庫不能轉換為分類賬,
Warehouses with existing transaction can not be converted to group.,與現有的交易倉庫不能轉換為組。,
Warehouses with existing transaction can not be converted to ledger.,與現有的交易倉庫不能轉換到總帳。,
Warning: Another {0} # {1} exists against stock entry {2},警告：另一個{0}＃{1}存在對庫存分錄{2},
Warning: Invalid SSL certificate on attachment {0},警告：附件無效的SSL證書{0},
Warning: Invalid attachment {0},警告：無效的附件{0},
Warning: Leave application contains following block dates,警告：離開包含以下日期區塊的應用程式,
Warning: Material Requested Qty is less than Minimum Order Qty,警告：物料需求的數量低於最少訂購量,
Warning: Sales Order {0} already exists against Customer's Purchase Order {1},警告：銷售訂單{0}已經存在針對客戶的採購訂單{1},
Warning: System will not check overbilling since amount for Item {0} in {1} is zero,警告： {0} {1}為零，系統將不檢查超收因為金額項目,
Warranty,保證,
Warranty Claim,保修索賠,
Warranty Claim against Serial No.,針對序列號保修索賠,
Website,網站,
Website Image should be a public file or website URL,網站形象應該是一個公共文件或網站網址,
Website Image {0} attached to Item {1} cannot be found,網站圖像{0}附加到物品{1}無法找到,
Website Listing,網站列表,
Website Manager,網站管理,
Website Settings,網站設定,
Week,週,
Weekdays,平日,
Weekly,每週,
"Weight is mentioned,\nPlease mention ""Weight UOM"" too",重量被提及，請同時註明“重量計量單位”,
Welcome email sent,歡迎發送電子郵件,
Welcome to ERPNext,歡迎來到ERPNext,
What do you need help with?,你有什麼需要幫助的？,
What does it do?,它有什麼作用？,
Where manufacturing operations are carried.,生產作業於此進行。,
White,白色,
Wire Transfer,電匯,
WooCommerce Products,WooCommerce產品,
Work In Progress,在製品,
Work Order,工作指示,
Work Order already created for all items with BOM,已經為包含物料清單的所有料品創建工單,
Work Order cannot be raised against a Item Template,工作訂單不能針對項目模板產生,
Work Order has been {0},工單已{0},
Work Order not created,工作訂單未創建,
Work Order {0} must be cancelled before cancelling this Sales Order,在取消此銷售訂單之前，必須先取消工單{0},
Work Order {0} must be submitted,必須提交工單{0},
Work Orders Created: {0},創建的工單：{0},
Work-in-Progress Warehouse is required before Submit,提交之前，需要填入在製品倉庫,
Working,工作的,
Working Hours,工作時間,
Workstation is closed on the following dates as per Holiday List: {0},工作站在以下日期關閉按假日列表：{0},
Wrapping up,包起來,
Wrong Password,密碼錯誤,
Year start date or end date is overlapping with {0}. To avoid please set company,新年的開始日期或結束日期與{0}重疊。為了避免請將公司,
You are not authorized to add or update entries before {0},你無權添加或更新{0}之前的條目,
You are not authorized to approve leaves on Block Dates,在限制的日期，您無權批准休假,
You are not authorized to set Frozen value,您無權設定值凍結,
You are not present all day(s) between compensatory leave request days,您在補休請求日之間不是全天,
You can not change rate if BOM mentioned agianst any item,你不能改變速度，如果BOM中提到反對的任何項目,
You can not enter current voucher in 'Against Journal Entry' column,在您不能輸入電流券“對日記帳分錄”專欄,
You can only have Plans with the same billing cycle in a Subscription,您只能在訂閱中擁有相同結算週期的計劃,
You can only redeem max {0} points in this order.,您只能按此順序兌換最多{0}個積分。,
You can only renew if your membership expires within 30 days,如果您的會員資格在30天內到期，您只能續訂,
You can only select a maximum of one option from the list of check boxes.,您只能從復選框列表中選擇最多一個選項。,
You can only submit Leave Encashment for a valid encashment amount,您只能提交離開封存以獲得有效的兌換金額,
You can't redeem Loyalty Points having more value than the Grand Total.,您無法兌換價值超過總計的忠誠度積分。,
You cannot credit and debit same account at the same time,你無法將貸方與借方在同一時間記在同一帳戶,
You cannot delete Fiscal Year {0}. Fiscal Year {0} is set as default in Global Settings,您不能刪除會計年度{0}。會計年度{0}設置為默認的全局設置,
You cannot delete Project Type 'External',您不能刪除項目類型“外部”,
You cannot edit root node.,您不能編輯根節點。,
You cannot restart a Subscription that is not cancelled.,您無法重新啟動未取消的訂閱。,
You don't have enough Loyalty Points to redeem,您沒有獲得忠誠度積分兌換,
You have already assessed for the assessment criteria {}.,您已經評估了評估標準{}。,
You have already selected items from {0} {1},您已經選擇從項目{0} {1},
You have been invited to collaborate on the project: {0},您已被邀請在項目上進行合作：{0},
You have entered duplicate items. Please rectify and try again.,您輸入重複的項目。請糾正，然後再試一次。,
You need to be a user other than Administrator with System Manager and Item Manager roles to register on Marketplace.,您需要是具有System Manager和Item Manager角色的Administrator以外的用戶才能在Marketplace上註冊。,
You need to be a user with System Manager and Item Manager roles to add users to Marketplace.,您需要是具有System Manager和Item Manager角色的用戶才能將用戶添加到Marketplace。,
You need to be a user with System Manager and Item Manager roles to register on Marketplace.,您需要是具有System Manager和Item Manager角色的用戶才能在Marketplace上註冊。,
You need to be logged in to access this page,您需要登錄才能訪問該頁面,
You need to enable Shopping Cart,您需要啟用購物車,
You will lose records of previously generated invoices. Are you sure you want to restart this subscription?,您將失去先前生成的發票記錄。您確定要重新啟用此訂閱嗎？,
Your Organization,你的組織,
Your cart is Empty,您的購物車是空的,
Your email address...,您的電子郵件地址...,
Your order is out for delivery!,您的訂單已發貨！,
Your tickets,你的門票,
ZIP Code,郵政編碼,
[Error],[錯誤],
[{0}](#Form/Item/{0}) is out of stock,[{0}]（＃窗體/項目/ {0}）缺貨,
`Freeze Stocks Older Than` should be smaller than %d days.,`凍結股票早於`應該是少於％d天。,
based_on,基於,
cannot be greater than 100,不能大於100,
disabled user,禁用的用戶,
"e.g. ""Build tools for builders""",例如「建設建設者工具“,
"e.g. ""Primary School"" or ""University""",如“小學”或“大學”,
"e.g. Bank, Cash, Credit Card",例如：銀行，現金，信用卡,
hidden,隱藏,
old_parent,old_parent,
on,開啟,
{0} '{1}' is disabled,{0}“{1}”已停用,
{0} '{1}' not in Fiscal Year {2},{0}“ {1}”不在財政年度{2},
{0} ({1}) cannot be greater than planned quantity ({2}) in Work Order {3},{0}（{1}）不能大於計畫數量 {3} 在工作訂單中（{2}）,
{0} - {1} is inactive student,{0}  -  {1}是非活動學生,
{0} - {1} is not enrolled in the Batch {2},{0}  -  {1} 未在批次處理中註冊 {2},
{0} - {1} is not enrolled in the Course {2},{0}  -  {1} 未在課程中註冊 {2},
{0} Budget for Account {1} against {2} {3} is {4}. It will exceed by {5},{0}預算帳戶{1}對{2} {3}是{4}。這將超過{5},
{0} Request for {1},{0}申請{1},
{0} Result submittted,{0}結果提交,
{0} Serial Numbers required for Item {1}. You have provided {2}.,{0}產品{1}需要的序號。您已提供{2}。,
{0} Student Groups created.,{0}創建學生組。,
{0} Students have been enrolled,{0}學生已被註冊,
{0} against Bill {1} dated {2},{0}針對帳單{1}日期{2},
{0} against Purchase Order {1},{0}針對採購訂單{1},
{0} against Sales Invoice {1},{0}針對銷售發票{1},
{0} against Sales Order {1},{0}針對銷售訂單{1},
{0} already allocated for Employee {1} for period {2} to {3},{0}已分配給員工{1}週期為{2}到{3},
{0} applicable after {1} working days,在{1}個工作日後適用{0},
{0} asset cannot be transferred,{0}資產不得轉讓,
{0} can not be negative,{0}不能為負數,
{0} created,{0}已新增,
"{0} currently has a {1} Supplier Scorecard standing, and Purchase Orders to this supplier should be issued with caution.",{0}目前擁有{1}供應商記分卡，而採購訂單應謹慎提供給供應商。,
"{0} currently has a {1} Supplier Scorecard standing, and RFQs to this supplier should be issued with caution.",{0}目前擁有{1}供應商記分卡，並且謹慎地向該供應商發出詢價。,
{0} does not belong to Company {1},{0}不屬於公司{1},
{0} does not have a Healthcare Practitioner Schedule. Add it in Healthcare Practitioner master,{0}沒有醫療從業者時間表。將其添加到Healthcare Practitioner master中,
{0} entered twice in Item Tax,{0}輸入兩次項目稅,
{0} for {1},{0}for {1},
{0} has fee validity till {1},{0}的費用有效期至{1},
{0} hours,{0}小時,
{0} in row {1},第{1}行中的{0},
{0} is blocked so this transaction cannot proceed,{0}被阻止，所以此事務無法繼續,
{0} is mandatory,{0}是強制性的,
{0} is mandatory for Item {1},{0}是強制性的項目{1},
{0} is mandatory. Maybe Currency Exchange record is not created for {1} to {2}.,{0}是強制性的。也許外幣兌換記錄為{1}到{2}尚未建立。,
{0} is not a stock Item,{0}不是庫存項目,
{0} is not a valid Batch Number for Item {1},{0}不是對項目的有效批號{1},
{0} is not in Optional Holiday List,{0}不在可選節日列表中,
{0} is not in a valid Payroll Period,{0}不在有效的工資核算期間,
{0} is now the default Fiscal Year. Please refresh your browser for the change to take effect.,{0}是現在預設的會計年度。請重新載入您的瀏覽器，以使更改生效。,
{0} is on hold till {1},{0}一直保持到{1},
{0} item found.,找到{0}項。,
{0} items found.,找到{0}個項目。,
{0} items in progress,正在進行{0}項目,
{0} items produced,生產{0}項目,
{0} must appear only once,{0}必須只出現一次,
{0} must be negative in return document,{0}必須返回文檔中負,
{0} must be submitted,必須提交{0},
{0} not allowed to transact with {1}. Please change the Company.,不允許{0}與{1}進行交易。請更改公司。,
{0} not found for item {1},找不到項目{1} {0},
{0} parameter is invalid,{0}參數無效,
{0} payment entries can not be filtered by {1},{0}付款分錄不能由{1}過濾,
{0} should be a value between 0 and 100,{0}應該是0到100之間的一個值,
{0} units of [{1}](#Form/Item/{1}) found in [{2}](#Form/Warehouse/{2}),{0} [{1}]的單位（＃窗體/項目/ {1}）在[{2}]研究發現（＃窗體/倉儲/ {2}）,
{0} units of {1} needed in {2} on {3} {4} for {5} to complete this transaction.,{0} {1}在需要{2}在{3} {4}：{5}來完成這一交易單位。,
{0} units of {1} needed in {2} to complete this transaction.,{0}單位{1}在{2}完成此交易所需。,
{0} valid serial nos for Item {1},{0}項目{1}的有效的序號,
{0} variants created.,創建了{0}個變體。,
{0} {1} created,已創建{0} {1},
{0} {1} has been modified. Please refresh.,{0} {1} 已修改。請更新。,
{0} {1} has not been submitted so the action cannot be completed,"{0} {1} 尚未提交, 因此無法完成操作",
"{0} {1} is associated with {2}, but Party Account is {3}","{0} {1} 與 {2} 關聯, 但當事方帳戶為 {3}",
{0} {1} is cancelled or closed,{0} {1} 被取消或結案,
{0} {1} is cancelled so the action cannot be completed,{0} {1} 被取消，因此無法完成操作,
{0} {1} is closed,{0} {1}關閉,
{0} {1} is disabled,{0} {1}被禁用,
{0} {1} is frozen,{0} {1}被凍結,
{0} {1} is fully billed,{0} {1}}已開票,
{0} {1} is not active,{0} {1}是不活動,
{0} {1} is not associated with {2} {3},{0} {1} 未與 {2} {3} 關聯,
{0} {1} is not present in the parent company,在母公司中不存在{0} {1},
{0} {1} must be submitted,{0} {1}必須提交,
{0} {1} not in any active Fiscal Year.,{0} {1} 不在任何有效的會計年度,
{0} {1} status is {2},{0} {1}的狀態為{2},
{0} {1}: 'Profit and Loss' type account {2} not allowed in Opening Entry,{0} {1}：“損益”帳戶類型{2}不允許進入開,
{0} {1}: Account {2} does not belong to Company {3},{0} {1}帳戶{2}不屬於公司{3},
{0} {1}: Account {2} is inactive,{0} {1}帳戶{2}無效,
{0} {1}: Accounting Entry for {2} can only be made in currency: {3},{0} {1}在{2}會計分錄只能在貨幣言：{3},
{0} {1}: Cost Center is mandatory for Item {2},{0} {1}：成本中心是強制性的項目{2},
{0} {1}: Cost Center is required for 'Profit and Loss' account {2}. Please set up a default Cost Center for the Company.,{0} {1}：需要的損益“賬戶成本中心{2}。請設置為公司默認的成本中心。,
{0} {1}: Cost Center {2} does not belong to Company {3},{0} {1}：成本中心{2}不屬於公司{3},
{0} {1}: Customer is required against Receivable account {2},{0} {1}：需要客戶對應收賬款{2},
{0} {1}: Either debit or credit amount is required for {2},{0} {1}：無論是借方或貸方金額需要{2},
{0} {1}: Supplier is required against Payable account {2},{0} {1}：需要對供應商應付賬款{2},
{0}% Billed,{0}％已開立帳單,
{0}% Delivered,已交付{0}％,
"{0}: Employee email not found, hence email not sent",{0}：未發現員工的電子郵件，因此，電子郵件未發,
{0}: From {0} of type {1},{0}：從{0}類型{1},
{0}: From {1},{0}：從{1},
{0}: {1} not found in Invoice Details table,{0}：在發票明細表中找不到{1},
Assigned To,指派給,
Chat,聊,
Completed By,完成,
Conditions,條件,
County,縣,
Day of Week,星期幾,
"Dear System Manager,",親愛的系統管理器，,
Default Value,預設值,
Email Group,電子郵件組,
Email Settings,電子郵件設定,
Email not sent to {0} (unsubscribed / disabled),電子郵件不會被發送到{0}（退訂/禁用）,
Error Message,錯誤信息,
Fieldtype,FIELDTYPE,
Help Articles,幫助文章,
Images,圖片,
Import,輸入,
Language,語言,
Likes,喜歡,
Merge with existing,合併與現有的,
Office,辦公室,
Orientation,取向,
Parent,親,
Passive,被動,
Payment Failed,支付失敗,
Percent,百分比,
Permanent,常駐,
Personal,個人,
Plant,廠,
Post,文章,
Postal,郵政,
Postal Code,郵政編碼,
Previous,上一筆,
Read Only,只讀,
Recipient,接受者,
Reviews,評測,
Sender,寄件人,
Shop,店,
Subsidiary,副,
There is some problem with the file url: {0},有一些問題與文件的URL：{0},
There were errors while sending email. Please try again.,還有在發送電子郵件是錯誤的。請再試一次。,
Values Changed,價值觀改變了,
or,要么,
Ageing Range 4,老化範圍4,
Allocated amount cannot be greater than unadjusted amount,分配的金額不能大於未調整的金額,
Allocated amount cannot be negative,分配數量不能為負數,
"Difference Account must be a Asset/Liability type account, since this Stock Entry is an Opening Entry",差異賬戶必須是資產/負債類型賬戶，因為此股票分錄是開倉分錄,
Error in some rows,某些行出錯,
Import Successful,導入成功,
Please save first,請先保存,
Price not found for item {0} in price list {1},價格表{1}中的商品{0}找不到價格,
Warehouse Type,倉庫類型,
Budgets,預算,
Bundle Qty,捆綁數量,
Company field is required,公司字段是必填項,
Creating Dimensions...,創建尺寸......,
Duplicate entry against the item code {0} and manufacturer {1},項目代碼{0}和製造商{1}的重複輸入,
Invalid GSTIN! The input you've entered doesn't match the GSTIN format for UIN Holders or Non-Resident OIDAR Service Providers,GSTIN無效！您輸入的輸入與UIN持有人或非居民OIDAR服務提供商的GSTIN格式不符,
Invoice Grand Total,發票總計,
Last carbon check date cannot be a future date,最後的碳檢查日期不能是未來的日期,
Make Stock Entry,進入股票,
Quality Feedback,質量反饋,
Quality Feedback Template,質量反饋模板,
Rules for applying different promotional schemes.,適用不同促銷計劃的規則。,
Shift,轉移,
Show {0},顯示{0},
Target Details,目標細節,
API,API,
Annual,年刊,
Approved,批准,
Change,更改,
Contact Email,聯絡電郵,
Export Type,導出類型,
From Date,從日期,
Group By,通過...分組,
Importing {0} of {1},導入{1}的{0},
Invalid URL,無效的網址,
Landscape,景觀,
Last Sync On,上次同步開啟,
Naming Series,命名系列,
No data to export,沒有要導出的數據,
Print Heading,列印標題,
Scheduler Inactive,調度程序無效,
Scheduler is inactive. Cannot import data.,調度程序處於非活動狀態。無法導入數據。,
Show Document,顯示文件,
Show Traceback,顯示回溯,
Video,視頻,
% Of Grand Total,佔總數的百分比,
'employee_field_value' and 'timestamp' are required.,需要&#39;employee_field_value&#39;和&#39;timestamp&#39;。,
<b>Company</b> is a mandatory filter.,<b>公司</b>是強制性過濾器。,
<b>From Date</b> is a mandatory filter.,<b>“起始日期”</b>是強制性過濾器。,
<b>From Time</b> cannot be later than <b>To Time</b> for {0},{0}的<b>起始時間</b>不能晚於<b>起始時間</b>,
<b>To Date</b> is a mandatory filter.,<b>截止日期</b>是強制性過濾器。,
A new appointment has been created for you with {0},已為您創建一個{0}的新約會,
Account Value,賬戶價值,
Account is mandatory to get payment entries,必須輸入帳戶才能獲得付款條目,
Account is not set for the dashboard chart {0},沒有為儀表板圖表{0}設置帳戶,
Account {0} does not belong to company {1},帳戶{0}不屬於公司{1},
Account {0} does not exists in the dashboard chart {1},帳戶{0}在儀表板圖表{1}中不存在,
Account: <b>{0}</b> is capital Work in progress and can not be updated by Journal Entry,帳戶： <b>{0}</b>是資金正在進行中，日記帳分錄無法更新,
Account: {0} is not permitted under Payment Entry,帳戶：付款條目下不允許{0},
Accounting Dimension <b>{0}</b> is required for 'Balance Sheet' account {1}.,“資產負債表”帳戶{1}需要會計維度<b>{0</b> }。,
Accounting Dimension <b>{0}</b> is required for 'Profit and Loss' account {1}.,“損益”帳戶{1}需要會計維度<b>{0</b> }。,
Accounting Masters,會計大師,
Accounting Period overlaps with {0},會計期間與{0}重疊,
Activity,活動,
Add / Manage Email Accounts.,新增 / 管理電子郵件帳戶。,
Add Child,新增子項目,
Add Loan Security,添加貸款安全,
Add Multiple,添加多個,
Add Participants,添加參與者,
Add your review,添加您的評論,
Add/Edit Coupon Conditions,添加/編輯優惠券條件,
Added to Featured Items,已添加到精選商品,
Added {0} ({1}),添加{0}（{1}）,
Address Line 1,地址第一行,
Admission End Date should be greater than Admission Start Date.,入學結束日期應大於入學開始日期。,
Against Loan,反對貸款,
Against Loan:,反對貸款：,
All bank transactions have been created,已創建所有銀行交易,
All the depreciations has been booked,所有折舊已被預訂,
Allocation Expired!,分配已過期！,
Allow Resetting Service Level Agreement from Support Settings.,允許從支持設置重置服務水平協議。,
Amount of {0} is required for Loan closure,結清貸款需要{0}的金額,
Amount paid cannot be zero,支付的金額不能為零,
Applied Coupon Code,應用的優惠券代碼,
Apply Coupon Code,申請優惠券代碼,
Appointment Booking,預約預約,
"As there are existing transactions against item {0}, you can not change the value of {1}",由於有對項目{0}現有的交易，你不能改變的值{1},
Asset Id,資產編號,
Asset Value,資產值,
Asset Value Adjustment cannot be posted before Asset's purchase date <b>{0}</b>.,資產價值調整不能在資產購買日期<b>{0}</b>之前過賬。,
Asset {0} does not belongs to the custodian {1},資產{0}不屬於託管人{1},
Asset {0} does not belongs to the location {1},資產{0}不屬於位置{1},
At least one of the Applicable Modules should be selected,應選擇至少一個適用模塊,
Atleast one asset has to be selected.,必須選擇至少一項資產。,
Attendance Marked,出勤率明顯,
Attendance has been marked as per employee check-ins,出勤已標記為每個員工簽到,
Authentication Failed,身份驗證失敗,
Automatic Reconciliation,自動對帳,
Available Stock,可用庫存,
"Available quantity is {0}, you need {1}",可用數量為{0}，您需要{1},
BOM 1,物料清單1,
BOM 2,物料清單2,
BOM Comparison Tool,BOM比較工具,
BOM recursion: {0} cannot be child of {1},BOM遞歸：{0}不能是{1}的子代,
BOM recursion: {0} cannot be parent or child of {1},BOM遞歸：{0}不能是{1}的父級或子級,
Back to Home,回到首頁,
Back to Messages,返回訊息,
Bank Data mapper doesn't exist,銀行數據映射器不存在,
Bank Details,銀行明細,
Bank account '{0}' has been synchronized,銀行帳戶“{0}”已同步,
Bank account {0} already exists and could not be created again,銀行帳戶{0}已存在，無法再次創建,
Bank accounts added,銀行賬戶補充說,
Batch no is required for batched item {0},批處理項{0}需要批次否,
Billing Date,結算日期,
Billing Interval Count cannot be less than 1,賬單間隔計數不能小於1,
Blue,藍色,
Book,書,
Book Appointment,預約書,
Brand,牌,
Browse,瀏覽,
Call Connected,呼叫已連接,
Call Disconnected,呼叫已斷開連接,
Call Missed,打電話錯過了,
Call Summary,通話摘要,
Call Summary Saved,通話摘要已保存,
Cannot Calculate Arrival Time as Driver Address is Missing.,由於缺少驅動程序地址，無法計算到達時間。,
Cannot Optimize Route as Driver Address is Missing.,由於缺少驅動程序地址，無法優化路由。,
Cannot complete task {0} as its dependant task {1} are not ccompleted / cancelled.,無法完成任務{0}，因為其相關任務{1}尚未完成/取消。,
Cannot create loan until application is approved,在申請獲得批准之前無法創建貸款,
Cannot find a matching Item. Please select some other value for {0}.,無法找到匹配的項目。請選擇其他值{0}。,
"Cannot overbill for Item {0} in row {1} more than {2}. To allow over-billing, please set allowance in Accounts Settings",第{1}行中的項目{0}的出價不能超過{2}。要允許超額計費，請在“帳戶設置”中設置配額,
"Capacity Planning Error, planned start time can not be same as end time",容量規劃錯誤，計劃的開始時間不能與結束時間相同,
Categories,分類,
Changes in {0},{0}的變化,
Chart,圖表,
Choose a corresponding payment,選擇相應的付款,
Click on the link below to verify your email and confirm the appointment,單擊下面的鏈接以驗證您的電子郵件並確認約會,
Close,關閉,
Communication,通訊,
Compact Item Print,緊湊型項目打印,
Company of asset {0} and purchase document {1} doesn't matches.,資產{0}和購買憑證{1}的公司不匹配。,
Compare BOMs for changes in Raw Materials and Operations,比較原材料和操作中的更改的BOM,
Compare List function takes on list arguments,比較List函數採用列表參數,
Completed Quantity,完成數量,
Connect your Exotel Account to ERPNext and track call logs,將您的Exotel帳戶連接到ERPNext並跟踪通話記錄,
Connect your bank accounts to ERPNext,將您的銀行帳戶連接到ERPNext,
Contact Seller,聯繫賣家,
Continue,繼續,
Couldn't Set Service Level Agreement {0}.,無法設置服務水平協議{0}。,
Country,國家,
Country Code in File does not match with country code set up in the system,文件中的國家/地區代碼與系統中設置的國家/地區代碼不匹配,
Create New Contact,創建新聯繫人,
Create New Lead,創造新的領導者,
Create Pick List,創建選擇列表,
Create Quality Inspection for Item {0},為項目{0}創建質量檢驗,
Creating Accounts...,創建帳戶......,
Creating bank entries...,創建銀行條目......,
Credit limit is already defined for the Company {0},已為公司{0}定義信用額度,
Ctrl + Enter to submit,Ctrl + Enter提交,
Ctrl+Enter to submit,Ctrl + Enter提交,
Currency,貨幣,
Current Status,現狀,
Customer PO,客戶PO,
Customize,客製化,
Daily,日常,
Date Range,日期範圍,
Date of Birth cannot be greater than Joining Date.,出生日期不能大於加入日期。,
Dear,親愛,
Default,預設,
Define coupon codes.,定義優惠券代碼。,
Delayed Days,延遲天數,
Delete,刪除,
Delivered Quantity,交貨數量,
Delivery Notes,送貨單,
Depreciated Amount,折舊額,
Designation,指定,
Difference Value,差異值,
Dimension Filter,尺寸過濾器,
Disabled,不使用,
Disbursement and Repayment,支付和還款,
Distance cannot be greater than 4000 kms,距離不能超過4000公里,
Do you want to submit the material request,您要提交材料申請嗎？,
Doctype,DocType,
Document {0} successfully uncleared,文檔{0}成功未清除,
Download Template,下載模板,
Dr,博士,
Due Date,截止日期,
Duplicate,複製,
Duplicate Project with Tasks,帶有任務的重複項目,
Duplicate project has been created,複製項目已創建,
E-Way Bill JSON can only be generated from a submitted document,e-Way Bill JSON只能從提交的文檔中生成,
E-Way Bill JSON can only be generated from submitted document,e-Way Bill JSON只能從提交的文檔中生成,
E-Way Bill JSON cannot be generated for Sales Return as of now,到目前為止，無法為銷售回報生成電子方式賬單JSON,
ERPNext could not find any matching payment entry,ERPNext找不到任何匹配的付款條目,
Earliest Age,最早年齡,
Edit Details,編輯細節,
Edit Profile,編輯個人資料,
Either GST Transporter ID or Vehicle No is required if Mode of Transport is Road,如果運輸方式為道路，則需要GST運輸車ID或車輛號,
Email,電子郵件,
Email Campaigns,電郵廣告系列,
Employee ID is linked with another instructor,員工ID與另一位講師鏈接,
Employee Tax and Benefits,員工稅和福利,
Employee is required while issuing Asset {0},發放資產{0}時要求員工,
Employee {0} does not belongs to the company {1},員工{0}不屬於公司{1},
Enable Auto Re-Order,啟用自動重新排序,
End Date of Agreement can't be less than today.,協議的結束日期不能低於今天。,
End Time,結束時間,
Energy Point Leaderboard,能源點排行榜,
Enter API key in Google Settings.,在Google設置中輸入API密鑰。,
Enter Supplier,輸入供應商,
Enter Value,輸入值,
Entity Type,實體類型,
Error,錯誤,
Error in Exotel incoming call,Exotel來電錯誤,
Error: {0} is mandatory field,錯誤：{0}是必填字段,
Event Link,活動鏈接,
Exception occurred while reconciling {0},協調{0}時發生異常,
Expected and Discharge dates cannot be less than Admission Schedule date,預計出院日期不得少於入學時間表,
Expire Allocation,過期分配,
Expired,過期,
Export,出口,
Export not allowed. You need {0} role to export.,不允許導出。您需要{0}的角色。,
Failed to add Domain,添加域失敗,
Fetch Items from Warehouse,從倉庫中獲取物品,
Fetching...,正在獲取...,
Field,領域,
Filters,篩選器,
Finding linked payments,查找關聯付款,
Fleet Management,車隊的管理,
Following fields are mandatory to create address:,必須填寫以下字段才能創建地址：,
"For item {0} at row {1}, count of serial numbers does not match with the picked quantity",對於行{1}處的項目{0}，序列號計數與拾取的數量不匹配,
For operation {0}: Quantity ({1}) can not be greter than pending quantity({2}),對於操作{0}：數量（{1}）不能大於掛起的數量（{2}）,
For quantity {0} should not be greater than work order quantity {1},對於數量{0}，不應大於工作訂單數量{1},
Free item not set in the pricing rule {0},未在定價規則{0}中設置免費項目,
From Date and To Date are Mandatory,從日期到日期是強制性的,
From employee is required while receiving Asset {0} to a target location,在接收資產{0}到目標位置時需要從僱員那裡,
Fuel Expense,燃料費用,
Future Payment Amount,未來付款金額,
Future Payment Ref,未來付款參考,
Future Payments,未來付款,
GST HSN Code does not exist for one or more items,一個或多個項目不存在GST HSN代碼,
Generate E-Way Bill JSON,生成電子收費賬單JSON,
Get Items,找項目,
Get Outstanding Documents,獲取優秀文件,
Goal,目標,
Greater Than Amount,大於金額,
Green,綠,
Group,組,
Group By Customer,按客戶分組,
Group By Supplier,按供應商分組,
Group Node,組節點,
Group Warehouses cannot be used in transactions. Please change the value of {0},不能在事務中使用組倉庫。請更改值{0},
Help,幫助,
Help Article,幫助文章,
"Helps you keep tracks of Contracts based on Supplier, Customer and Employee",幫助您根據供應商，客戶和員工記錄合同,
Helps you manage appointments with your leads,幫助您管理潛在客戶的約會,
IBAN is not valid,IBAN無效,
Import Data from CSV / Excel files.,從CSV / Excel文件導入數據。,
In Progress,進行中,
Incoming call from {0},來自{0}的來電,
Incorrect Warehouse,倉庫不正確,
Intermediate,中間,
Invalid Barcode. There is no Item attached to this barcode.,無效的條形碼。該條形碼沒有附件。,
Invalid credentials,無效證件,
Invite as User,邀請成為用戶,
Issue Priority.,問題優先。,
Issue Type.,問題類型。,
"It seems that there is an issue with the server's stripe configuration. In case of failure, the amount will get refunded to your account.",看起來服務器的條帶配置存在問題。如果失敗，這筆款項將退還給您的賬戶。,
Item Reported,項目報告,
Item listing removed,項目清單已刪除,
Item quantity can not be zero,物品數量不能為零,
Item taxes updated,物品稅已更新,
Item {0}: {1} qty produced. ,項目{0}：產生了{1}數量。,
Joining Date can not be greater than Leaving Date,加入日期不能大於離開日期,
Lab Test Item {0} already exist,實驗室測試項目{0}已存在,
Last Issue,最後一期,
Latest Age,後期,
Leave application is linked with leave allocations {0}. Leave application cannot be set as leave without pay,請假申請與請假分配{0}相關聯。請假申請不能設置為無薪休假,
Leaves Taken,葉子採取,
Less Than Amount,少於金額,
Liabilities,負債,
Loading...,載入中...,
Loan Amount exceeds maximum loan amount of {0} as per proposed securities,根據建議的證券，貸款額超過最高貸款額{0},
Loan Applications from customers and employees.,客戶和員工的貸款申請。,
Loan Disbursement,貸款支出,
Loan Processes,貸款流程,
Loan Security,貸款擔保,
Loan Security Pledge,貸款擔保,
Loan Security Pledge Created : {0},已創建的貸款安全承諾：{0},
Loan Security Price,貸款擔保價,
Loan Security Price overlapping with {0},貸款證券價格與{0}重疊,
Loan Security Unpledge,貸款擔保,
Loan Security Value,貸款擔保價值,
Loan Type for interest and penalty rates,利率和罰款率的貸款類型,
Loan amount cannot be greater than {0},貸款金額不能大於{0},
Loan is mandatory,貸款是強制性的,
Loans,貸款,
Loans provided to customers and employees.,提供給客戶和員工的貸款。,
Log Type is required for check-ins falling in the shift: {0}.,簽到班次中需要登錄類型：{0}。,
Looks like someone sent you to an incomplete URL. Please ask them to look into it.,貌似有人送你一個不完整的URL。請讓他們尋找到它。,
Make Journal Entry,使日記帳分錄,
Make Purchase Invoice,做出購買發票,
Manufactured,製成的,
Mark Work From Home,標記在家工作,
Master,主,
Max strength cannot be less than zero.,最大強度不能小於零。,
Maximum attempts for this quiz reached!,達到此測驗的最大嘗試次數！,
Missing Values Required,遺漏必須值,
Mobile No,手機號碼,
Mobile Number,手機號碼,
Name,名稱,
Near you,在你旁邊,
Net Profit/Loss,淨利潤/虧損,
New Expense,新費用,
New Invoice,新發票,
New release date should be in the future,新的發布日期應該是將來的,
Newsletter,新聞,
No Account matched these filters: {},沒有帳戶符合這些過濾條件：{},
No Employee found for the given employee field value. '{}': {},找不到給定員工字段值的員工。 &#39;{}&#39;：{},
No Leaves Allocated to Employee: {0} for Leave Type: {1},休假類型：{1}的未分配給員工的葉子：{0},
No communication found.,沒有找到通訊。,
No correct answer is set for {0},沒有為{0}設置正確答案,
No description,沒有說明,
No issue has been raised by the caller.,調用者沒有提出任何問題。,
No items to publish,沒有要發布的項目,
No outstanding invoices found,沒有找到未完成的發票,
No outstanding invoices found for the {0} {1} which qualify the filters you have specified.,未找到符合您指定的過濾條件的{0} {1}的未結髮票。,
No outstanding invoices require exchange rate revaluation,沒有未結清的發票需要匯率重估,
No reviews yet,還沒有評論,
No views yet,還沒有意見,
Non stock items,非庫存物品,
Not Allowed,不允許,
Not allowed to create accounting dimension for {0},不允許為{0}創建會計維度,
Not permitted. Please disable the Lab Test Template,不允許。請禁用實驗室測試模板,
Note,注釋,
Notes: ,注意事項：,
On Converting Opportunity,轉換機會,
On Purchase Order Submission,提交採購訂單時,
On Sales Order Submission,提交銷售訂單,
On Task Completion,完成任務,
On {0} Creation,在{0}創建時,
Only .csv and .xlsx files are supported currently,目前僅支持.csv和.xlsx文件,
Only expired allocation can be cancelled,只能取消過期分配,
Only users with the {0} role can create backdated leave applications,只有具有{0}角色的用戶才能創建回退的請假申請,
Open,開,
Open Contact,打開聯繫,
Open Lead,開放領導,
Opening and Closing,開幕式和閉幕式,
Operating Cost as per Work Order / BOM,根據工單/物料單的運營成本,
Order Amount,訂單金額,
Page {0} of {1},第{0}頁，共{1}頁,
Paid amount cannot be less than {0},付費金額不能小於{0},
Parent Company must be a group company,母公司必須是集團公司,
Passing Score value should be between 0 and 100,傳球得分值應在0到100之間,
Password policy cannot contain spaces or simultaneous hyphens. The format will be restructured automatically,密碼策略不能包含空格或同時連字符。格式將自動重組,
Patient History,患者病史,
Pause,暫停,
Pay,付,
Payment Document Type,付款單據類型,
Payment Name,付款名稱,
Penalty Amount,罰款金額,
Pending,擱置,
Period based On,期間基於,
Perpetual inventory required for the company {0} to view this report.,公司{0}查看此報告所需的永久清單。,
Phone,電話,
Pick List,選擇列表,
Plaid authentication error,格子認證錯誤,
Plaid public token error,格子公共令牌錯誤,
Plaid transactions sync error,格子交易同步錯誤,
Please check the error log for details about the import errors,有關導入錯誤的詳細信息，請查看錯誤日誌,
Please create <b>DATEV Settings</b> for Company <b>{}</b>.,請為公司<b>{}</b>創建<b>DATEV設置</b> 。,
Please create adjustment Journal Entry for amount {0} ,請為金額{0}創建調整日記帳分錄,
Please do not create more than 500 items at a time,請不要一次創建超過500個項目,
Please enter <b>Difference Account</b> or set default <b>Stock Adjustment Account</b> for company {0},請輸入<b>差異帳戶</b>或為公司{0}設置默認的<b>庫存調整帳戶</b>,
Please enter GSTIN and state for the Company Address {0},請輸入GSTIN並說明公司地址{0},
Please enter Item Code to get item taxes,請輸入商品代碼以獲取商品稅,
Please enter Warehouse and Date,請輸入倉庫和日期,
Please enter the designation,請輸入名稱,
Please login as a Marketplace User to edit this item.,請以市場用戶身份登錄以編輯此項目。,
Please login as a Marketplace User to report this item.,請以市場用戶身份登錄以報告此項目。,
Please select <b>Template Type</b> to download template,請選擇<b>模板類型</b>以下載模板,
Please select Applicant Type first,請先選擇申請人類型,
Please select Customer first,請先選擇客戶,
Please select Item Code first,請先選擇商品代碼,
Please select Loan Type for company {0},請為公司{0}選擇貸款類型,
Please select a Delivery Note,請選擇送貨單,
Please select a Sales Person for item: {0},請為以下項目選擇銷售人員：{0},
Please select another payment method. Stripe does not support transactions in currency '{0}',請選擇其他付款方式。 Stripe不支持貨幣“{0}”的交易,
Please select the customer.,請選擇客戶。,
Please set a Supplier against the Items to be considered in the Purchase Order.,請根據採購訂單中要考慮的項目設置供應商。,
Please set account heads in GST Settings for Compnay {0},請在Compnay {0}的GST設置中設置帳戶首長,
Please set an email id for the Lead {0},請為潛在客戶{0}設置電子郵件ID,
Please set default UOM in Stock Settings,請在“庫存設置”中設置默認的UOM,
Please set filter based on Item or Warehouse due to a large amount of entries.,由於條目很多，請根據物料或倉庫設置過濾器。,
Please set up the Campaign Schedule in the Campaign {0},請在廣告系列{0}中設置廣告系列計劃,
Please set valid GSTIN No. in Company Address for company {0},請在公司地址中為公司{0}設置有效的GSTIN號。,
Please set {0},請設置{0},customer
Please setup a default bank account for company {0},請為公司{0}設置默認銀行帳戶,
Please specify,請註明,
Please specify a {0},請指定一個{0},lead
Pledge Status,質押狀態,
Pledge Time,承諾時間,
Printing,列印,
Priority,優先,
Priority has been changed to {0}.,優先級已更改為{0}。,
Priority {0} has been repeated.,優先級{0}已重複。,
Processing XML Files,處理XML文件,
Project,專案,
Proposed Pledges are mandatory for secured Loans,建議抵押是抵押貸款的強制性要求,
Provide the academic year and set the starting and ending date.,提供學年並設置開始和結束日期。,
Public token is missing for this bank,此銀行缺少公共令牌,
Publish,發布,
Publish 1 Item,發布1項,
Publish Items,發布項目,
Publish More Items,發布更多項目,
Publish Your First Items,發布您的第一個項目,
Publish {0} Items,發布{0}項,
Published Items,發布的項目,
Purchase Invoice cannot be made against an existing asset {0},無法針對現有資產{0}生成採購發票,
Purchase Invoices,購買發票,
Purchase Orders,訂單,
Purchase Receipt doesn't have any Item for which Retain Sample is enabled.,購買收據沒有任何啟用了保留樣本的項目。,
Purchase Return,採購退貨,
Qty of Finished Goods Item,成品數量,
Qty or Amount is mandatroy for loan security,數量或金額是貸款擔保的強制要求,
Quality Inspection required for Item {0} to submit,要提交項目{0}所需的質量檢驗,
Quantity to Manufacture,製造數量,
Quantity to Manufacture can not be zero for the operation {0},操作{0}的製造數量不能為零,
Quarterly,每季,
Queued,排隊,
Quick Entry,快速入門,
Quiz {0} does not exist,測驗{0}不存在,
Quotation Amount,報價金額,
Rate or Discount is required for the price discount.,價格折扣需要Rate或Discount。,
Reconcile Entries,協調條目,
Reconcile this account,核對此帳戶,
Reconciled,已對帳,
Recruitment,招募,
Red,紅,
Refreshing,清爽,
Release date must be in the future,發布日期必須在將來,
Relieving Date must be greater than or equal to Date of Joining,取消日期必須大於或等於加入日期,
Rename,改名,
Rename Not Allowed,重命名不允許,
Repayment Method is mandatory for term loans,定期貸款必須採用還款方法,
Repayment Start Date is mandatory for term loans,定期貸款的還款開始日期是必填項,
Report Item,報告項目,
Report this Item,舉報此項目,
Reserved Qty for Subcontract: Raw materials quantity to make subcontracted items.,分包的預留數量：製造分包項目的原材料數量。,
Reset,重啟,
Reset Service Level Agreement,重置服務水平協議,
Resetting Service Level Agreement.,重置服務水平協議。,
Return amount cannot be greater unclaimed amount,退貨金額不能大於無人認領的金額,
Review,評論,
Room,房間,
Row # ,第＃行,
Row #{0}: Accepted Warehouse and Supplier Warehouse cannot be same,第＃0行：接受倉庫和供應商倉庫不能相同,
Row #{0}: Cannot delete item {1} which has already been billed.,第＃{0}行：無法刪除已計費的項目{1}。,
Row #{0}: Cannot delete item {1} which has already been delivered,第＃0行：無法刪除已經交付的項目{1},
Row #{0}: Cannot delete item {1} which has already been received,第＃0行：無法刪除已收到的項目{1},
Row #{0}: Cannot delete item {1} which has work order assigned to it.,第＃{0}行：無法刪除已為其分配了工作訂單的項目{1}。,
Row #{0}: Cannot delete item {1} which is assigned to customer's purchase order.,第＃{0}行：無法刪除分配給客戶採購訂單的項目{1}。,
Row #{0}: Cannot select Supplier Warehouse while suppling raw materials to subcontractor,第{0}行：在向分包商供應原材料時無法選擇供應商倉庫,
Row #{0}: Cost Center {1} does not belong to company {2},第{0}行：成本中心{1}不屬於公司{2},
Row #{0}: Operation {1} is not completed for {2} qty of finished goods in Work Order {3}. Please update operation status via Job Card {4}.,行＃{0}：對於工作訂單{3}中的{2}數量的成品，未完成操作{1}。請通過工作卡{4}更新操作狀態。,
Row #{0}: Payment document is required to complete the transaction,第{0}行：需要付款憑證才能完成交易,
Row #{0}: Serial No {1} does not belong to Batch {2},行＃{0}：序列號{1}不屬於批次{2},
Row #{0}: Service End Date cannot be before Invoice Posting Date,行＃{0}：服務終止日期不能早於發票過帳日期,
Row #{0}: Service Start Date cannot be greater than Service End Date,行＃{0}：服務開始日期不能大於服務結束日期,
Row #{0}: Service Start and End Date is required for deferred accounting,行＃{0}：延期計費需要服務開始和結束日期,
Row {0}: Invalid Item Tax Template for item {1},第{0}行：項目{1}的項目稅模板無效,
Row {0}: Quantity not available for {4} in warehouse {1} at posting time of the entry ({2} {3}),第{0}行：在輸入條目（{2} {3}）時，倉庫{1}中{4}不可使用的數量,
Row {0}: user has not applied the rule {1} on the item {2},第{0}行：用戶尚未在項目{2}上應用規則{1},
Row {0}:Sibling Date of Birth cannot be greater than today.,第{0}行：同級出生日期不能大於今天。,
Rows Added in {0},{0}中添加的行數,
Rows Removed in {0},在{0}中刪除的行,
Sanctioned Amount limit crossed for {0} {1},越過了{0} {1}的認可金額限制,
Sanctioned Loan Amount already exists for {0} against company {1},{0}對公司{1}的批准貸款額已存在,
Save,儲存,
Save Item,保存項目,
Saved Items,保存的項目,
Search Items ...,搜索項目......,
Search for anything ...,搜索任何東西......,
Search results for,為。。。。尋找結果,
Select All,全選,
Select Difference Account,選擇差異賬戶,
Select a Default Priority.,選擇默認優先級。,
Select a company,選擇一家公司,
Select finance book for the item {0} at row {1},為行{1}中的項{0}選擇財務手冊,
Select only one Priority as Default.,僅選擇一個優先級作為默認值。,
Seller Information,賣家信息,
Send,發送,
Send a message,發送一個消息,
Sending,發出,
Sends Mails to lead or contact based on a Campaign schedule,根據Campaign計劃發送郵件以進行引導或聯繫,
Serial Number Created,序列號已創建,
Serial Numbers Created,序列號已創建,
Serial no(s) required for serialized item {0},序列化項目{0}所需的序列號,
Server Error,服務器錯誤,
Service Level Agreement has been changed to {0}.,服務水平協議已更改為{0}。,
Service Level Agreement was reset.,服務水平協議已重置。,
Service Level Agreement with Entity Type {0} and Entity {1} already exists.,與實體類型{0}和實體{1}的服務水平協議已存在。,
Set,集合,
Set Meta Tags,設置元標記,
Set {0} in company {1},在公司{1}中設置{0},
Setup,設定,
Setup Wizard,設置嚮導,
Shift Management,班次管理,
Show Future Payments,顯示未來付款,
Show Linked Delivery Notes,顯示鏈接的交貨單,
Show Sales Person,顯示銷售人員,
Show Stock Ageing Data,顯示庫存賬齡數據,
Show Warehouse-wise Stock,顯示倉庫庫存,
Something went wrong while evaluating the quiz.,評估測驗時出了點問題。,
Sr,序號,
Start,開始,
Start Date cannot be before the current date,開始日期不能早於當前日期,
Start Time,開始時間,
Status,狀態,
Status must be Cancelled or Completed,狀態必須已取消或已完成,
Stock Balance Report,庫存餘額報告,
Stock Entry has been already created against this Pick List,已經根據此選擇列表創建了股票輸入,
Stock Ledger ID,股票分類帳編號,
Stock Value ({0}) and Account Balance ({1}) are out of sync for account {2} and it's linked warehouses.,庫存值（{0}）和帳戶餘額（{1}）與帳戶{2}及其鏈接的倉庫不同步。,
Student with email {0} does not exist,電子郵件{0}的學生不存在,
Submit Review,提交評論,
Submitted,提交,
Supplier Addresses And Contacts,供應商的地址和聯絡方式,
Synchronize this account,同步此帳戶,
Tag,標籤,
Target Location is required while receiving Asset {0} from an employee,從員工那裡收到資產{0}時需要目標位置,
Target Location is required while transferring Asset {0},轉移資產{0}時需要目標位置,
Target Location or To Employee is required while receiving Asset {0},接收資產{0}時需要“目標位置”或“發給員工”,
Task's {0} End Date cannot be after Project's End Date.,任務的{0}結束日期不能晚於項目的結束日期。,
Task's {0} Start Date cannot be after Project's End Date.,任務的{0}開始日期不能晚於項目的結束日期。,
Tax Account not specified for Shopify Tax {0},沒有為Shopify Tax {0}指定稅務帳戶,
Tax Total,稅收總額,
The Campaign '{0}' already exists for the {1} '{2}',{1}&#39;{2}&#39;廣告系列“{0}”已存在,
The difference between from time and To Time must be a multiple of Appointment,時間與時間之間的差異必須是約會的倍數,
The field Asset Account cannot be blank,字段資產帳戶不能為空,
The field Equity/Liability Account cannot be blank,字段權益/責任帳戶不能為空,
The following serial numbers were created: <br><br> {0},創建了以下序列號： <br><br> {0},
The parent account {0} does not exists in the uploaded template,上級模板中不存在上級帳戶{0},
The question cannot be duplicate,問題不能重複,
The selected payment entry should be linked with a creditor bank transaction,所選付款條目應與債權銀行交易相關聯,
The selected payment entry should be linked with a debtor bank transaction,所選付款條目應與債務人銀行交易掛鉤,
The total allocated amount ({0}) is greated than the paid amount ({1}).,總分配金額（{0}）比付款金額（{1}）更重要。,
There are no vacancies under staffing plan {0},人員編制計劃{0}下沒有職位空缺,
This Service Level Agreement is specific to Customer {0},此服務級別協議特定於客戶{0},
This action will unlink this account from any external service integrating ERPNext with your bank accounts. It cannot be undone. Are you certain ?,此操作將取消此帳戶與將ERPNext與您的銀行帳戶集成的任何外部服務的鏈接。它無法撤消。你確定嗎 ？,
This bank account is already synchronized,此銀行帳戶已同步,
This bank transaction is already fully reconciled,此銀行交易已完全已對帳,
This employee already has a log with the same timestamp.{0},此員工已有一個具有相同時間戳的日誌。{0},
This page keeps track of items you want to buy from sellers.,此頁面會跟踪您要從賣家處購買的商品。,
This page keeps track of your items in which buyers have showed some interest.,此頁面會跟踪您的商品，其中買家已表現出一些興趣。,
Timing,定時,
Title,標題,
"To allow over billing, update ""Over Billing Allowance"" in Accounts Settings or the Item.",要允許超額結算，請在“帳戶設置”或“項目”中更新“超額結算限額”。,
"To allow over receipt / delivery, update ""Over Receipt/Delivery Allowance"" in Stock Settings or the Item.",要允許超過收貨/交貨，請在庫存設置或項目中更新“超過收貨/交貨限額”。,
To date needs to be before from date,迄今為止需要在日期之前,
Total,總計,
Total Early Exits,早期退出總額,
Total Late Entries,總遲到條目,
Total Payment Request amount cannot be greater than {0} amount,總付款請求金額不能大於{0}金額,
Total payments amount can't be greater than {},付款總額不得超過{},
Totals,總計,
Training Event:,培訓活動：,
Transactions already retreived from the statement,已從報表中檢索到的交易,
Transfer Material to Supplier,轉印材料供應商,
Transport Receipt No and Date are mandatory for your chosen Mode of Transport,您選擇的運輸方式必須提供運輸收據號和日期,
Type,類型,
Unable to find Salary Component {0},無法找到薪資組件{0},
Unable to find the time slot in the next {0} days for the operation {1}.,無法找到操作{1}的未來{0}天的時間段。,
Unable to update remote activity,無法更新遠程活動,
Unknown Caller,未知的來電者,
Unlink external integrations,取消外部集成的鏈接,
Unmarked Attendance for days,數天無限制出勤,
Unpublish Item,取消發布項目,
Unreconciled,未調節,
Unsupported GST Category for E-Way Bill JSON generation,用於e-Way Bill JSON生成的不支持的GST類別,
Update,更新資料,
Update Details,更新詳情,
Update Taxes for Items,更新項目稅金,
"Upload a bank statement, link or reconcile a bank account",上傳銀行對帳單，關聯或核對銀行帳戶,
Upload a statement,上傳聲明,
Use a name that is different from previous project name,使用與先前項目名稱不同的名稱,
User {0} is disabled,用戶{0}被禁用,
Users and Permissions,用戶和權限,
Vacancies cannot be lower than the current openings,職位空缺不能低於目前的職位空缺,
Valid From Time must be lesser than Valid Upto Time.,有效起始時間必須小於有效起始時間。,
Valuation Rate required for Item {0} at row {1},第{1}行的第{0}項所需的估價率,
Vehicle Type is required if Mode of Transport is Road,如果運輸方式為道路，則需要車輛類型,
Vendor Name,供應商名稱,
Verify Email,驗證郵件,
View,視圖,
View all issues from {0},查看{0}中的所有問題,
View call log,查看通話記錄,
Warehouse,倉庫,
Warehouse not found against the account {0},在帳戶{0}中找不到倉庫,
Welcome to {0},歡迎{0},
Why do think this Item should be removed?,為什麼要認為這個項目應該刪除？,
Work Order {0}: Job Card not found for the operation {1},工作單{0}：找不到工序{1}的工作卡,
Workday {0} has been repeated.,工作日{0}已重複。,
XML Files Processed,處理的XML文件,
Year,年份,
You,您,
You are not allowed to enroll for this course,您無權註冊此課程,
You are not enrolled in program {0},您尚未加入計劃{0},
You can Feature upto 8 items.,最多可以包含8個項目。,
You can also copy-paste this link in your browser,您也可以複製粘貼此鏈接到瀏覽器,
You can publish upto 200 items.,您最多可以發布200個項目。,
You have to enable auto re-order in Stock Settings to maintain re-order levels.,您必須在庫存設置中啟用自動重新訂購才能維持重新訂購級別。,
You must be a registered supplier to generate e-Way Bill,您必須是註冊供應商才能生成電子方式賬單,
You need to login as a Marketplace User before you can add any reviews.,在添加任何評論之前，您需要以市場用戶身份登錄。,
Your Featured Items,您的特色商品,
Your Items,您的物品,
Your Profile,您的個人資料,
Your rating:,你的評分：,
e-Way Bill already exists for this document,e-Way Bill已存在於本文件中,
{0} Coupon used are {1}. Allowed quantity is exhausted,{0}使用的優惠券是{1}。允許量已耗盡,
{0} Name,{0}名稱,
{0} bank transaction(s) created,創建了{0}銀行交易,
{0} bank transaction(s) created and {1} errors,創建了{0}個銀行交易和{1}個錯誤,
{0} can not be greater than {1},{0}不能大於{1},
{0} conversations,{0}次對話,
{0} is not a company bank account,{0}不是公司銀行帳戶,
{0} is not a group node. Please select a group node as parent cost center,{0}不是組節點。請選擇一個組節點作為父成本中心,
{0} is not the default supplier for any items.,{0}不是任何商品的默認供應商。,
{0} is required,{0}是必需的,
{0}: {1} must be less than {2},{0}：{1}必須小於{2},
{} is an invalid Attendance Status.,{}是無效的出勤狀態。,
{} is required to generate E-Way Bill JSON,生成E-Way Bill JSON需要{},
"Invalid lost reason {0}, please create a new lost reason",無效的丟失原因{0}，請創建一個新的丟失原因,
Profit This Year,今年獲利,
Total Expense,總費用,
Total Expense This Year,今年總費用,
Total Income,總收入,
Total Income This Year,今年總收入,
Barcode,條碼,
Bold,膽大,
Center,中央,
Clear,明確,
Comment,評論,
Comments,評論,
Download,下載,
Left,左,
Link,鏈接,
Reference Name,參考名稱,
Refresh,重新載入,
Time,時間,
Actual,實際,
Add to Cart,添加到購物車,
Days Since Last Order,自上次訂購以來的天數,
In Stock,庫存,
Loan Amount is mandatory,貸款金額是強制性的,
No students Found,找不到學生,
Not in Stock,沒存貨,
Please select a Customer,請選擇一位客戶,
Printed On,印在,
Received From,從......收到,
Sales Person,銷售人員,
To date cannot be before From date,無效的主名稱,
Write Off,註銷項款,
{0} Created,{0}已新增,
Email Id,電子郵件ID,
No,無,
Reference Doctype,DocType參照,
User Id,用戶身份,
Yes,是的,
Actual ,實際,
Add to cart,添加到購物車,
Budget,預算,
Chart of Accounts,會計科目表,
Customer database.,客戶數據庫。,
Days Since Last order,自上次訂購以來的天數,
Download as JSON,下載為JSON,
End date can not be less than start date,結束日期不能小於開始日期,
For Default Supplier (Optional),對於默認供應商（可選）,
From date cannot be greater than To date,起始日期不能大於結束日期,
Group by,集團通過,
In stock,有現貨,
Item name,項目名稱,
Loan amount is mandatory,貸款金額是強制性的,
Minimum Qty,最低數量,
More details,更多詳情,
Nature of Supplies,供應的性質,
No Items found.,未找到任何項目。,
No employee found,無發現任何員工,
No students found,沒有發現學生,
Not in stock,沒存貨,
Not permitted,不允許,
Open Issues ,開放式問題,
Open Projects ,打開項目,
Open To Do ,開做,
Operation Id,操作編號,
Partially ordered,部分訂購,
Please select company first,請首先選擇公司,
Please select patient,請選擇患者,
Projected qty,預計數量,
Sales person,銷售人員,
Serial No {0} Created,序列號{0}創建,
Source Location is required for the Asset {0},源位置對資產{0}是必需的,
Tax Id,稅號,
To Time,要時間,
To date cannot be before from date,截止日期不能早於起始日期,
Total Taxable value,應稅總額,
Upcoming Calendar Events ,即將到來的日曆事件,
Value or Qty,價值或數量,
Variant of,變種,
Write off,註銷項款,
hours,小時,
received from,從......收到,
to,至,
Failed to setup defaults for country {0}. <NAME_EMAIL>,無法設置國家/地區{0}的默認設置。請聯繫*******************,
Row #{0}: Item {1} is not a Serialized/Batched Item. It cannot have a Serial No/Batch No against it.,行＃{0}：項目{1}不是序列化/批量項目。它不能有序列號/批號。,
Please set {0},請設置{0},
Please set {0},請設置{0},supplier
Please setup Instructor Naming System in Education > Education Settings,請在“教育”&gt;“教育設置”中設置教師命名系統,
Please set Naming Series for {0} via Setup > Settings > Naming Series,請通過設置&gt;設置&gt;命名系列為{0}設置命名系列,
UOM Conversion factor ({0} -> {1}) not found for item: {2},找不到項目{2}的UOM轉換因子（{0}-&gt; {1}）,
Item Code > Item Group > Brand,物料代碼&gt;物料組&gt;品牌,
Customer > Customer Group > Territory,客戶&gt;客戶組&gt;地區,
Supplier > Supplier Type,供應商&gt;供應商類型,
Please setup Employee Naming System in Human Resource > HR Settings,請在人力資源&gt;人力資源設置中設置員工命名系統,
Please setup numbering series for Attendance via Setup > Numbering Series,請通過“設置”&gt;“編號序列”為出勤設置編號序列,
The value of {0} differs between Items {1} and {2},{0}的值在項目{1}和{2}之間有所不同,
Auto Fetch,自動提取,
Fetch Serial Numbers based on FIFO,根據FIFO獲取序列號,
"Outward taxable supplies(other than zero rated, nil rated and exempted)",向外應稅供應（零稅率，零稅率和免稅額除外）,
"To allow different rates, disable the {0} checkbox in {1}.",要允許不同的速率，請禁用{1}中的{0}複選框。,
Current Odometer Value should be greater than Last Odometer Value {0},當前里程表的值應大於上一次里程表的值{0},
No additional expenses has been added,沒有增加額外的費用,
Asset{} {assets_link} created for {},為{}創建的資產{} {assets_link},
Row {}: Asset Naming Series is mandatory for the auto creation for item {},第{}行：對於項目{}的自動創建，必須使用資產命名系列,
Assets not created for {0}. You will have to create asset manually.,未為{0}創建資產。您將必須手動創建資產。,
{0} {1} has accounting entries in currency {2} for company {3}. Please select a receivable or payable account with currency {2}.,{0} {1}擁有公司{3}的幣種為{2}的會計分錄。請選擇幣種為{2}的應收或應付帳戶。,
Invalid Account,無效賬戶,
Purchase Order Required,採購訂單為必要項,
Purchase Receipt Required,需要採購入庫單,
Account Missing,帳戶遺失,
Requested,已要求,
Invalid Account Currency,無效的帳戶幣種,
"Row {0}: The item {1}, quantity must be positive number",第{0}行：項目{1}，數量必須為正數,
"Please set {0} for Batched Item {1}, which is used to set {2} on Submit.",請為批處理項目{1}設置{0}，該項目用於在提交時設置{2}。,
Variant Item,變項,
BOM 1 {0} and BOM 2 {1} should not be same,BOM 1 {0}和BOM 2 {1}不應相同,
Note: Item {0} added multiple times,注意：項目{0}被多次添加,
YouTube,的YouTube,
Vimeo,Vimeo,
Publish Date,發布日期,
Duration,持續時間,
Advanced Settings,高級設置,
Path,路徑,
Components,組件,
Verified By,認證機構,
Invalid naming series (. missing) for {0},{0}的無效命名系列（。丟失）,
Filter Based On,過濾依據,
Manufacturer Part Number <b>{0}</b> is invalid,製造商零件編號<b>{0}</b>無效,
Invalid Part Number,無效的零件號,
Select atleast one Social Media from Share on.,從“共享”中選擇至少一個社交媒體。,
Invalid Scheduled Time,無效的計劃時間,
Length Must be less than 280.,長度必須小於280。,
Error while POSTING {0},發布{0}時出錯,
"Session not valid, Do you want to login?",會話無效，您要登錄嗎？,
Session Active,會話進行中,
Session Not Active. Save doc to login.,會話無效。保存文檔以登錄。,
Error! Failed to get request token.,錯誤！無法獲取請求令牌。,
Invalid {0} or {1},無效的{0}或{1},
Error! Failed to get access token.,錯誤！無法獲取訪問令牌。,
Invalid Consumer Key or Consumer Secret Key,無效的消費者密鑰或消費者秘密密鑰,
Your Session will be expire in ,您的會話將在過期,
Session is expired. Save doc to login.,會話已過期。保存文檔以登錄。,
Error While Uploading Image,上傳圖片時出錯,
You Didn't have permission to access this API,您無權訪問此API,
Valid Upto date cannot be before Valid From date,有效截止日期不能早於有效起始日期,
Valid From date not in Fiscal Year {0},有效起始日期不在會計年度{0}中,
Valid Upto date not in Fiscal Year {0},有效最新日期不在會計年度{0}中,
Group Roll No,組卷編號,
Maintain Same Rate Throughout Sales Cycle,保持同樣的速度在整個銷售週期,
"Row {1}: Quantity ({0}) cannot be a fraction. To allow this, disable '{2}' in UOM {3}.",第{1}行：數量（{0}）不能為小數。為此，請在UOM {3}中禁用“ {2}”。,
Must be Whole Number,必須是整數,
Please setup Razorpay Plan ID,請設置Razorpay計劃ID,
Contact Creation Failed,聯繫人創建失敗,
{0} already exists for employee {1} and period {2},員工{1}和期間{2}已存在{0},
Leaves Allocated,葉子分配,
Leaves Expired,葉子過期,
Leave Without Pay does not match with approved {} records,帶薪休假與批准的{}記錄不匹配,
Income Tax Slab not set in Salary Structure Assignment: {0},未在薪金結構分配中設置所得稅表：{0},
Income Tax Slab: {0} is disabled,所得稅計劃：{0}已禁用,
Income Tax Slab must be effective on or before Payroll Period Start Date: {0},所得稅計劃必須在薪資期限開始日期或之前生效：{0},
No leave record found for employee {0} on {1},在{1}上沒有找到員工{0}的請假記錄,
Row {0}: {1} is required in the expenses table to book an expense claim.,費用表中的行{0}：{1}用於預訂費用索賠。,
Set the default account for the {0} {1},設置{0} {1}的默認帳戶,
Income Tax Slab,所得稅表,
Row #{0}: Cannot set amount or formula for Salary Component {1} with Variable Based On Taxable Salary,行＃{0}：無法為應稅薪金變量設置薪金成分{1}的金額或公式,
Row #{}: {} of {} should be {}. Please modify the account or select a different account.,第{{}行：{}的{}應該是{}。請修改帳戶或選擇其他帳戶。,
Row #{}: Please asign task to a member.,第{}行：請將任務分配給成員。,
Process Failed,處理失敗,
Tally Migration Error,理貨遷移錯誤,
Please set Warehouse in Woocommerce Settings,請在Woocommerce設置中設置Warehouse,
Row {0}: Delivery Warehouse ({1}) and Customer Warehouse ({2}) can not be same,第{0}行：交貨倉庫（{1}）和客戶倉庫（{2}）不能相同,
Row {0}: Due Date in the Payment Terms table cannot be before Posting Date,第{0}行：“付款條款”表中的到期日期不能早於過帳日期,
Cannot find {} for item {}. Please set the same in Item Master or Stock Settings.,找不到項目{}的{}。請在“物料主數據”或“庫存設置”中進行相同設置。,
Row #{0}: The batch {1} has already expired.,行＃{0}：批次{1}已過期。,
Start Year and End Year are mandatory,開始年和結束年是強制性的,
GL Entry,GL報名,
Cannot allocate more than {0} against payment term {1},付款期{1}不能分配超過{0},
The root account {0} must be a group,根帳戶{0}必須是一個組,
Shipping rule not applicable for country {0} in Shipping Address,送貨規則不適用於送貨地址中的國家/地區{0},
Get Payments from,從中獲取付款,
Set Shipping Address or Billing Address,設置送貨地址或帳單地址,
Consultation Setup,諮詢設置,
Fee Validity,費用有效期,
Laboratory Setup,實驗室設置,
Dosage Form,劑型,
Records and History,記錄和歷史,
Patient Medical Record,病人醫療記錄,
Rehabilitation,復原,
Exercise Type,鍛煉類型,
Exercise Difficulty Level,運動難度等級,
Therapy Type,治療類型,
Therapy Plan,治療計劃,
Therapy Session,治療會議,
Motor Assessment Scale,運動評估量表,
[Important] [ERPNext] Auto Reorder Errors,[重要] [ERPNext]自動重新排序錯誤,
"Regards,",問候，,
The following {0} were created: {1},已創建以下{0}：{1},
Work Orders,工作訂單,
The {0} {1} created sucessfully,{0} {1}成功創建,
Work Order cannot be created for following reason: <br> {0},由於以下原因，無法創建工作訂單：<br> {0},
Add items in the Item Locations table,在“項目位置”表中添加項目,
Update Current Stock,更新當前庫存,
"{0} Retain Sample is based on batch, please check Has Batch No to retain sample of item",{0}保留樣品基於批次，請檢查是否具有批次號以保留項目樣品,
Currently no stock available in any warehouse,目前在任何倉庫中都沒有庫存,
BOM Qty,BOM數量,
Time logs are required for {0} {1},{0} {1}需要時間日誌,
Total Completed Qty,完成總數量,
Qty to Manufacture,製造數量,
Repay From Salary can be selected only for term loans,只能為定期貸款選擇“從工資還款”,
No valid Loan Security Price found for {0},找不到{0}的有效貸款擔保價格,
Loan Account and Payment Account cannot be same,貸款帳戶和付款帳戶不能相同,
Loan Security Pledge can only be created for secured loans,只能為有抵押貸款創建貸款安全承諾,
Social Media Campaigns,社交媒體活動,
From Date can not be greater than To Date,起始日期不能大於截止日期,
Please set a Customer linked to the Patient,請設置與患者鏈接的客戶,
Customer Not Found,找不到客戶,
Please Configure Clinical Procedure Consumable Item in ,請在以下位置配置臨床程序消耗品,
Out Patient Consulting Charge Item,出患者諮詢費用項目,
Inpatient Visit Charge Item,住院訪問費用項目,
OP Consulting Charge,OP諮詢費,
Inpatient Visit Charge,住院訪問費用,
Appointment Status,預約狀態,
Test: ,測試：,
Collection Details: ,集合詳細信息：,
Select Therapy Type,選擇治療類型,
{0} sessions completed,{0}個會話已完成,
{0} session completed,{0}個會話已完成,
Therapy Sessions,治療會議,
Add Exercise Step,添加運動步驟,
Edit Exercise Step,編輯運動步驟,
Patient Appointments,預約病人,
Item with Item Code {0} already exists,物料代碼為{0}的物料已存在,
Registration Fee cannot be negative or zero,註冊費不能為負或零,
Configure a service Item for {0},為{0}配置服務項目,
Temperature: ,溫度：,
Pulse: ,脈衝：,
Respiratory Rate: ,呼吸頻率：,
BP: ,血壓：,
BMI: ,體重指數：,
Check Availability,檢查可用性,
Please select Patient first,請先選擇患者,
Please select a Mode of Payment first,請先選擇付款方式,
Please set the Paid Amount first,請先設置付款金額,
Not Therapies Prescribed,沒有規定的療法,
There are no Therapies prescribed for Patient {0},沒有為患者{0}規定任何療法,
Appointment date and Healthcare Practitioner are Mandatory,預約日期和醫療從業者為必填項,
No Prescribed Procedures found for the selected Patient,未找到所選患者的處方程序,
Please select a Patient first,請先選擇一個病人,
There are no procedure prescribed for ,沒有規定的程序,
Prescribed Therapies,訂明療法,
Appointment overlaps with ,約會與,
{0} has appointment scheduled with {1} at {2} having {3} minute(s) duration.,{0}已與{1}安排了{3}分鐘的{2}約會。,
Appointments Overlapping,約會重疊,
Consulting Charges: {0},諮詢費用：{0},
Appointment Cancelled. Please review and cancel the invoice {0},預約已取消。請查看並取消發票{0},
Appointment Cancelled.,預約已取消。,
Fee Validity {0} updated.,費用有效期{0}已更新。,
Practitioner Schedule Not Found,找不到醫生時間表,
{0} does not have a Healthcare Practitioner Schedule. Add it in Healthcare Practitioner,{0}沒有醫療保健從業人員時間表。將其添加到醫療保健從業者中,
Healthcare Service Units,醫療服務單位,
Complete and Consume,完成並消耗,
Complete {0} and Consume Stock?,完成{0}並消耗庫存嗎？,
Complete {0}?,完成{0}嗎？,
Stock quantity to start the Procedure is not available in the Warehouse {0}. Do you want to record a Stock Entry?,倉庫{0}中沒有啟動該過程的庫存數量。您是否要記錄庫存條目？,
{0} as on {1},{0}與{1}相同,
Clinical Procedure ({0}):,臨床程序（{0}）：,
Please set Customer in Patient {0},請在“患者{0}”中設置“客戶”,
Item {0} is not active,項目{0}無效,
Therapy Plan {0} created successfully.,治療計劃{0}已成功創建。,
Symptoms: ,症狀：,
No Symptoms,無症狀,
Diagnosis: ,診斷：,
No Diagnosis,沒有診斷,
Drug(s) Prescribed.,處方藥。,
Test(s) Prescribed.,規定的測試。,
Procedure(s) Prescribed.,規定的程序。,
Counts Completed: {0},已完成的計數：{0},
Patient Assessment,患者評估,
Assessments,評估,
Heads (or groups) against which Accounting Entries are made and balances are maintained.,頭（或組）針對其會計分錄是由和平衡得以維持。,
Account Name,帳戶名稱,
Inter Company Account,Inter公司帳戶,
Parent Account,父帳戶,
Setting Account Type helps in selecting this Account in transactions.,設置帳戶類型有助於在交易中選擇該帳戶。,
Chargeable,收費,
Rate at which this tax is applied,此稅適用的匯率,
Frozen,凍結的,
"If the account is frozen, entries are allowed to restricted users.",如果帳戶被凍結，條目被允許受限制的用戶。,
Balance must be,餘額必須,
Lft,左腳,
Old Parent,老家長,
Include in gross,包括在總金額中,
Auditor,核數師,
Accounting Dimension,會計維度,
Dimension Name,尺寸名稱,
Dimension Defaults,尺寸默認值,
Accounting Dimension Detail,會計維度明細,
Default Dimension,默認尺寸,
Mandatory For Balance Sheet,資產負債表必備,
Mandatory For Profit and Loss Account,對於損益賬戶必須提供,
Accounting Period,會計期間,
Period Name,期間名稱,
Closed Documents,關閉的文件,
Accounts Settings,帳戶設定,
Settings for Accounts,設置帳戶,
Make Accounting Entry For Every Stock Movement,為每股份轉移做會計分錄,
Users with this role are allowed to set frozen accounts and create / modify accounting entries against frozen accounts,具有此角色的用戶可以設置凍結帳戶，並新增/修改對凍結帳戶的會計分錄,
Determine Address Tax Category From,確定地址稅類別,
Over Billing Allowance (%),超過結算津貼（％）,
Credit Controller,信用控制器,
Check Supplier Invoice Number Uniqueness,檢查供應商發票編號唯一性,
Make Payment via Journal Entry,通過日記帳分錄進行付款,
Unlink Payment on Cancellation of Invoice,取消鏈接在發票上的取消付款,
Book Asset Depreciation Entry Automatically,自動存入資產折舊條目,
Automatically Add Taxes and Charges from Item Tax Template,從項目稅模板自動添加稅費,
Automatically Fetch Payment Terms,自動獲取付款條款,
Show Payment Schedule in Print,在打印中顯示付款時間表,
Currency Exchange Settings,貨幣兌換設置,
Allow Stale Exchange Rates,允許陳舊的匯率,
Stale Days,陳舊的日子,
Report Settings,報告設置,
Use Custom Cash Flow Format,使用自定義現金流量格式,
Allowed To Transact With,允許與,
SWIFT number,環球銀行金融電信協會代碼,
Branch Code,分行代碼,
Address and Contact,地址和聯絡方式,
Contact HTML,聯繫HTML,
Data Import Configuration,數據導入配置,
Bank Transaction Mapping,銀行交易映射,
Plaid Access Token,格子訪問令牌,
Company Account,公司帳號,
Account Subtype,帳戶子類型,
Is Default Account,是默認帳戶,
Is Company Account,是公司帳戶,
Party Details,黨詳細,
Account Details,帳戶細節,
IBAN,伊班,
Bank Account No,銀行帳號,
Integration Details,集成細節,
Integration ID,整合編號,
Change this date manually to setup the next synchronization start date,手動更改此日期以設置下一個同步開始日期,
Bank Account Subtype,銀行帳戶子類型,
Bank Account Type,銀行賬戶類型,
Bank Guarantee,銀行擔保,
Bank Guarantee Type,銀行擔保類型,
Reference Document Name,參考文件名稱,
Validity in Days,天數有效,
Bank Account Info,銀行賬戶信息,
Clauses and Conditions,條款和條件,
Other Details,其他詳情,
Bank Guarantee Number,銀行擔保編號,
Margin Money,保證金,
Charges Incurred,收費發生,
Fixed Deposit Number,定期存款編號,
Account Currency,賬戶幣種,
Select the Bank Account to reconcile.,選擇要對帳的銀行帳戶。,
Include Reconciled Entries,包括對賬項目,
Get Payment Entries,獲取付款項,
Payment Entries,付款項,
Update Clearance Date,更新日期間隙,
Bank Reconciliation Detail,銀行對帳詳細,
Cheque Number,支票號碼,
Cheque Date,檢查日期,
Statement Header Mapping,聲明標題映射,
Statement Headers,聲明標題,
Transaction Data Mapping,交易數據映射,
Mapped Items,映射項目,
Bank Statement Settings Item,銀行對賬單設置項目,
Mapped Header,映射的標題,
Bank Header,銀行標題,
Bank Statement Transaction Entry,銀行對賬單交易分錄,
Bank Transaction Entries,銀行交易分錄,
Match Transaction to Invoices,將交易與發票匹配,
Create New Payment/Journal Entry,創建新的付款/日記賬分錄,
Submit/Reconcile Payments,提交/協調付款,
Matching Invoices,匹配發票,
Payment Invoice Items,付款發票項目,
Reconciled Transactions,協調的事務,
Bank Statement Transaction Invoice Item,銀行對賬單交易發票項目,
Payment Description,付款說明,
Invoice Date,發票日期,
invoice,發票,
Bank Statement Transaction Payment Item,銀行對賬單交易付款項目,
outstanding_amount,outstanding_amount,
Payment Reference,付款憑據,
Bank Statement Transaction Settings Item,銀行對賬單交易設置項目,
Bank Data,銀行數據,
Mapped Data Type,映射數據類型,
Mapped Data,映射數據,
Bank Transaction,銀行交易,
Transaction ID,事務ID,
Unallocated Amount,未分配金額,
Field in Bank Transaction,銀行交易中的字段,
Column in Bank File,銀行文件中的列,
Bank Transaction Payments,銀行交易付款,
Control Action,控制行動,
Applicable on Material Request,適用於材料請求,
Action if Annual Budget Exceeded on MR,年度預算超出MR的行動,
Ignore,忽視,
Action if Accumulated Monthly Budget Exceeded on MR,如果累計每月預算超過MR，則採取行動,
Applicable on Purchase Order,適用於採購訂單,
Action if Annual Budget Exceeded on PO,年度預算超出採購訂單時採取的行動,
Action if Accumulated Monthly Budget Exceeded on PO,採購訂單上累計每月預算超出時的操作,
Applicable on booking actual expenses,適用於預訂實際費用,
Action if Annual Budget Exceeded on Actual,年度預算超出實際的行動,
Action if Accumulated Monthly Budget Exceeded on Actual,累計每月預算超出實際的行動,
Budget Accounts,預算科目,
Budget Account,預算科目,
Budget Amount,預算額,
C-Form,C表格,
C-Form No,C-表格編號,
Received Date,接收日期,
Quarter,25美分硬幣,
I,一世,
II,II,
III,三級,
C-Form Invoice Detail,C-表 發票詳細資訊,
Invoice No,發票號碼,
Cash Flow Mapper,現金流量映射器,
Section Name,部分名稱,
Section Header,章節標題,
Section Leader,科長,
e.g Adjustments for:,例如調整：,
Section Subtotal,部分小計,
Section Footer,章節頁腳,
Cash Flow Mapping,現金流量映射,
Select Maximum Of 1,選擇最多1個,
Is Finance Cost,財務成本,
Is Working Capital,是營運資本,
Is Finance Cost Adjustment,財務成本調整,
Is Income Tax Liability,是所得稅責任,
Is Income Tax Expense,是所得稅費用,
Cash Flow Mapping Accounts,現金流量映射賬戶,
account,帳戶,
Cash Flow Mapping Template,現金流量映射模板,
Cash Flow Mapping Template Details,現金流量映射模板細節,
Net Amount,淨額,
Cashier Closing Payments,收銀員結算付款,
Chart of Accounts Importer,會計科目表,
Import Chart of Accounts from a csv file,從csv文件導入科目表,
Attach custom Chart of Accounts file,附加自定義會計科目表文件,
Chart Preview,圖表預覽,
Chart Tree,圖表樹,
Cheque Print Template,檢查打印模板,
Has Print Format,擁有打印格式,
Primary Settings,主要設置,
Cheque Size,檢查尺寸,
Starting position from top edge,起價頂邊位置,
Cheque Width,支票寬度,
Cheque Height,檢查高度,
Scanned Cheque,支票掃描,
Is Account Payable,為應付賬款,
Distance from top edge,從頂邊的距離,
Distance from left edge,從左側邊緣的距離,
Message to show,信息顯示,
Date Settings,日期設定,
Starting location from left edge,從左邊起始位置,
Payer Settings,付款人設置,
Width of amount in word,在字量的寬度,
Line spacing for amount in words,字數金額的行距,
Amount In Figure,量圖,
Signatory Position,簽署的位置,
Closed Document,封閉文件,
Track separate Income and Expense for product verticals or divisions.,跟踪獨立收入和支出進行產品垂直或部門。,
Cost Center Name,成本中心名稱,
Parent Cost Center,父成本中心,
lft,LFT,
rgt,gt,
Coupon Code,優惠券代碼,
Coupon Name,優惠券名稱,
Coupon Type,優惠券類型,
Promotional,促銷性,
Gift Card,禮物卡,
unique e.g. SAVE20  To be used to get discount,獨特的，例如SAVE20用於獲得折扣,
Used,用過的,
Coupon Description,優惠券說明,
Discounted Invoice,特價發票,
Debit to,借給,
Exchange Rate Revaluation,匯率重估,
Get Entries,獲取條目,
Exchange Rate Revaluation Account,匯率重估賬戶,
Total Gain/Loss,總收益/損失,
Balance In Account Currency,賬戶貨幣餘額,
Current Exchange Rate,當前匯率,
Balance In Base Currency,平衡基礎貨幣,
New Exchange Rate,新匯率,
New Balance In Base Currency,基礎貨幣的新平衡,
Gain/Loss,收益/損失,
**Fiscal Year** represents a Financial Year. All accounting entries and other major transactions are tracked against **Fiscal Year**.,**財年**表示財政年度。所有的會計輸入項目和其他重大交易針對**財年**進行追蹤。,
Year Name,年份名稱,
"For e.g. 2012, 2012-13",對於例如2012、2012-13,
Year Start Date,年結開始日期,
Year End Date,年份結束日期,
Companies,企業,
Auto Created,自動創建,
Stock User,庫存用戶,
Fiscal Year Company,會計年度公司,
Debit Amount,借方金額,
Credit Amount,信貸金額,
Debit Amount in Account Currency,在賬戶幣種借記金額,
Credit Amount in Account Currency,在賬戶幣金額,
Voucher Detail No,券詳細說明暫無,
Is Opening,是開幕,
Is Advance,為進,
To Rename,重命名,
GST Account,GST帳戶,
CGST Account,CGST賬戶,
SGST Account,SGST賬戶,
IGST Account,IGST帳戶,
CESS Account,CESS帳戶,
Loan Start Date,貸款開始日期,
Loan Period (Days),貸款期限（天）,
Loan End Date,貸款結束日期,
Bank Charges,銀行收費,
Short Term Loan Account,短期貸款賬戶,
Bank Charges Account,銀行手續費賬戶,
Accounts Receivable Credit Account,應收帳款信用帳戶,
Accounts Receivable Discounted Account,應收帳款折扣帳戶,
Accounts Receivable Unpaid Account,應收帳款未付帳戶,
Item Tax Template,物品稅模板,
Tax Rates,稅率,
Item Tax Template Detail,項目稅模板詳細信息,
Entry Type,條目類型,
Inter Company Journal Entry,Inter公司日記帳分錄,
Bank Entry,銀行分錄,
Cash Entry,現金分錄,
Credit Card Entry,信用卡進入,
Contra Entry,魂斗羅進入,
Excise Entry,海關入境,
Write Off Entry,核銷進入,
Opening Entry,開放報名,
ACC-JV-.YYYY.-,ACC-合資-.YYYY.-,
Accounting Entries,會計分錄,
Total Debit,借方總額,
Total Credit,貸方總額,
Difference (Dr - Cr),差異（Dr - Cr）,
Make Difference Entry,使不同入口,
Total Amount Currency,總金額幣種,
Total Amount in Words,總金額大寫,
Remark,備註,
Paid Loan,付費貸款,
Inter Company Journal Entry Reference,Inter公司日記帳分錄參考,
Write Off Based On,核銷的基礎上,
Get Outstanding Invoices,獲取未付發票,
Write Off Amount,沖銷金額,
Printing Settings,列印設定,
Pay To / Recd From,支付/ 接收,
Payment Order,付款單,
Subscription Section,認購科,
Journal Entry Account,日記帳分錄帳號,
Account Balance,帳戶餘額,
Party Balance,黨平衡,
Accounting Dimensions,會計維度,
If Income or Expense,如果收入或支出,
Exchange Rate,匯率,
Debit in Company Currency,借記卡在公司貨幣,
Credit in Company Currency,信用在公司貨幣,
Payroll Entry,工資項目,
Employee Advance,員工晉升,
Reference Due Date,參考到期日,
Loyalty Program Tier,忠誠度計劃層,
Redeem Against,兌換,
Expiry Date,到期時間,
Loyalty Point Entry Redemption,忠誠度積分兌換,
Redemption Date,贖回日期,
Redeemed Points,兌換積分,
Loyalty Program Name,忠誠計劃名稱,
Loyalty Program Type,忠誠度計劃類型,
Single Tier Program,單層計劃,
Multiple Tier Program,多層計劃,
Customer Territory,客戶地區,
Auto Opt In (For all customers),自動選擇（適用於所有客戶）,
Collection Tier,收集層,
Collection Rules,收集規則,
Redemption,贖回,
Conversion Factor,轉換因子,
1 Loyalty Points = How much base currency?,1忠誠度積分=多少基礎貨幣？,
Expiry Duration (in days),到期時間（天）,
Help Section,幫助科,
Loyalty Program Help,忠誠度計劃幫助,
Loyalty Program Collection,忠誠度計劃集,
Tier Name,等級名稱,
Minimum Total Spent,最低總支出,
For how much spent = 1 Loyalty Point,花費多少= 1忠誠點,
Mode of Payment Account,支付帳戶模式,
Default Account,預設帳戶,
Default account will be automatically updated in POS Invoice when this mode is selected.,選擇此模式後，默認帳戶將在POS發票中自動更新。,
**Monthly Distribution** helps you distribute the Budget/Target across months if you have seasonality in your business.,**月度分配**幫助你分配預算/目標跨越幾個月，如果你在你的業務有季節性。,
Distribution Name,分配名稱,
Name of the Monthly Distribution,每月分配的名稱,
Monthly Distribution Percentages,每月分佈百分比,
Monthly Distribution Percentage,每月分配比例,
Percentage Allocation,百分比分配,
Create Missing Party,創建失踪派對,
Create missing customer or supplier.,創建缺少的客戶或供應商。,
Opening Invoice Creation Tool Item,打開發票創建工具項目,
Temporary Opening Account,臨時開戶,
Party Account,黨的帳戶,
Type of Payment,付款類型,
Receive,接受,
Internal Transfer,內部轉賬,
Payment Order Status,付款訂單狀態,
Payment Ordered,付款訂購,
Payment From / To,付款自/至,
Company Bank Account,公司銀行賬戶,
Party Bank Account,黨銀行賬戶,
Account Paid From,帳戶支付從,
Account Paid To,賬戶付至,
Paid Amount (Company Currency),支付的金額（公司貨幣）,
Received Amount,收金額,
Received Amount (Company Currency),收到的款項（公司幣種）,
Get Outstanding Invoice,獲得優秀發票,
Payment References,付款參考,
Writeoff,註銷,
Total Allocated Amount,總撥款額,
Total Allocated Amount (Company Currency),總撥款額（公司幣種）,
Set Exchange Gain / Loss,設置兌換收益/損失,
Difference Amount (Company Currency),差異金額（公司幣種）,
Write Off Difference Amount,核銷金額差異,
Deductions or Loss,扣除或損失,
Payment Deductions or Loss,付款扣除或損失,
Cheque/Reference Date,支票/參考日期,
Payment Entry Deduction,輸入付款扣除,
Payment Entry Reference,付款輸入參考,
Allocated,分配,
Payment Gateway Account,網路支付閘道帳戶,
Payment Account,付款帳號,
Default Payment Request Message,預設的付款請求訊息,
Payment Order Type,付款訂單類型,
Payment Order Reference,付款訂單參考,
Bank Account Details,銀行賬戶明細,
Payment Reconciliation,付款對帳,
Receivable / Payable Account,應收/應付賬款,
Bank / Cash Account,銀行／現金帳戶,
From Invoice Date,從發票日期,
To Invoice Date,要發票日期,
Minimum Invoice Amount,最小發票金額,
Maximum Invoice Amount,最大發票額,
System will fetch all the entries if limit value is zero.,如果限制值為零，系統將獲取所有條目。,
Get Unreconciled Entries,獲取未調節項,
Unreconciled Payment Details,未核銷付款明細,
Invoice/Journal Entry Details,發票/日記帳分錄詳細資訊,
Payment Reconciliation Invoice,付款發票對帳,
Invoice Number,發票號碼,
Payment Reconciliation Payment,付款方式付款對賬,
Reference Row,參考行,
Allocated amount,分配量,
Payment Request Type,付款申請類型,
Inward,向內的,
Transaction Details,交易明細,
Amount in customer's currency,量客戶的貨幣,
Is a Subscription,是訂閱,
Transaction Currency,交易貨幣,
Subscription Plans,訂閱計劃,
SWIFT Number,SWIFT號碼,
Recipient Message And Payment Details,收件人郵件和付款細節,
Make Sales Invoice,做銷售發票,
Mute Email,靜音電子郵件,
payment_url,payment_url,
Payment Gateway Details,支付網關細節,
Payment Schedule,付款時間表,
Invoice Portion,發票部分,
Payment Amount,付款金額,
Payment Term Name,付款條款名稱,
Due Date Based On,到期日基於,
Day(s) after invoice date,發票日期後的天數,
Day(s) after the end of the invoice month,發票月份結束後的一天,
Month(s) after the end of the invoice month,發票月份結束後的月份,
Credit Days,信貸天,
Credit Months,信貸月份,
Allocate Payment Based On Payment Terms,根據付款條件分配付款,
"If this checkbox is checked, paid amount will be splitted and allocated as per the amounts in payment schedule against each payment term",如果選中此復選框，則將按照每個付款期限的付款時間表中的金額來拆分和分配付款金額,
Payment Terms Template Detail,付款條款模板細節,
Closing Fiscal Year,截止會計年度,
Closing Account Head,關閉帳戶頭,
"The account head under Liability or Equity, in which Profit/Loss will be booked",負債或權益下的帳戶頭，其中利潤/虧損將被黃牌警告,
POS Customer Group,POS客戶群,
POS Field,POS場,
POS Item Group,POS項目組,
Update Stock,庫存更新,
Ignore Pricing Rule,忽略定價規則,
Applicable for Users,適用於用戶,
Sales Invoice Payment,銷售發票付款,
Item Groups,項目組,
Only show Items from these Item Groups,僅顯示這些項目組中的項目,
Customer Groups,客戶群,
Only show Customer of these Customer Groups,僅顯示這些客戶組的客戶,
Write Off Account,核銷帳戶,
Write Off Cost Center,沖銷成本中心,
Account for Change Amount,帳戶漲跌額,
Taxes and Charges,稅收和收費,
Apply Discount On,申請折扣,
POS Profile User,POS配置文件用戶,
Apply On,適用於,
Price or Product Discount,價格或產品折扣,
Apply Rule On Item Code,在物品代碼上應用規則,
Apply Rule On Item Group,在項目組上應用規則,
Apply Rule On Brand,在品牌上應用規則,
Mixed Conditions,混合條件,
Conditions will be applied on all the selected items combined. ,條件將適用於所有選定項目的組合。,
Is Cumulative,是累積的,
Coupon Code Based,基於優惠券代碼,
Discount on Other Item,其他商品折扣,
Apply Rule On Other,在其他方面適用規則,
Party Information,黨的信息,
Quantity and Amount,數量和金額,
Min Qty,最小數量,
Max Qty,最大數量,
Min Amt,敏安,
Max Amt,馬克斯·阿姆特,
Period Settings,期間設置,
Margin,餘量,
Margin Type,保證金類型,
Margin Rate or Amount,保證金稅率或稅額,
Price Discount Scheme,價格折扣計劃,
Rate or Discount,價格或折扣,
Discount Percentage,折扣率,
Discount Amount,折扣金額,
For Price List,對於價格表,
Product Discount Scheme,產品折扣計劃,
Same Item,相同的項目,
Free Item,免費物品,
Threshold for Suggestion,建議的門檻,
System will notify to increase or decrease quantity or amount ,系統將通知增加或減少數量或金額,
"Higher the number, higher the priority",數字越大，優先級越高,
Apply Multiple Pricing Rules,應用多個定價規則,
Apply Discount on Rate,應用折扣率,
Validate Applied Rule,驗證應用規則,
Rule Description,規則說明,
Pricing Rule Help,定價規則說明,
Promotional Scheme Id,促銷計劃ID,
Promotional Scheme,促銷計劃,
Pricing Rule Brand,定價規則品牌,
Pricing Rule Detail,定價規則細節,
Child Docname,兒童醫生名稱,
Rule Applied,適用規則,
Pricing Rule Item Code,定價規則項目代碼,
Pricing Rule Item Group,定價規則項目組,
Price Discount Slabs,價格折扣板,
Promotional Scheme Price Discount,促銷計劃價格折扣,
Product Discount Slabs,產品折扣板,
Promotional Scheme Product Discount,促銷計劃產品折扣,
Min Amount,最低金額,
Max Amount,最大金額,
Discount Type,折扣類型,
Tax Withholding Category,預扣稅類別,
Edit Posting Date and Time,編輯投稿時間,
Is Paid,支付,
Is Return (Debit Note),是退貨（借記卡）,
Apply Tax Withholding Amount,申請預扣稅金額,
Accounting Dimensions ,會計維度,
Supplier Invoice Details,供應商發票明細,
Supplier Invoice Date,供應商發票日期,
Return Against Purchase Invoice,回到對採購發票,
Select Supplier Address,選擇供應商地址,
Contact Person,聯絡人,
Select Shipping Address,選擇送貨地址,
Currency and Price List,貨幣和價格表,
Price List Currency,價格表之貨幣,
Price List Exchange Rate,價目表匯率,
Set Accepted Warehouse,設置接受的倉庫,
Rejected Warehouse,拒絕倉庫,
Warehouse where you are maintaining stock of rejected items,你維護退貨庫存的倉庫,
Raw Materials Supplied,提供供應商原物料,
Supplier Warehouse,供應商倉庫,
Pricing Rules,定價規則,
Supplied Items,提供的物品,
Total (Company Currency),總計（公司貨幣）,
Net Total (Company Currency),總淨值（公司貨幣）,
Total Net Weight,總淨重,
Shipping Rule,送貨規則,
Purchase Taxes and Charges Template,採購稅負和費用模板,
Purchase Taxes and Charges,購置稅和費,
Tax Breakup,稅收分解,
Taxes and Charges Calculation,稅費計算,
Taxes and Charges Added (Company Currency),稅收和收費上架（公司貨幣）,
Taxes and Charges Deducted (Company Currency),稅收和扣除（公司貨幣）,
Total Taxes and Charges (Company Currency),總稅費和費用（公司貨幣）,
Taxes and Charges Added,稅費上架,
Taxes and Charges Deducted,稅收和費用扣除,
Total Taxes and Charges,總營業稅金及費用,
Additional Discount,更多優惠,
Apply Additional Discount On,收取額外折扣,
Additional Discount Amount (Company Currency),額外的優惠金額（公司貨幣）,
Additional Discount Percentage,額外折扣率,
Additional Discount Amount,額外折扣金額,
Grand Total (Company Currency),總計（公司貨幣）,
Rounding Adjustment (Company Currency),四捨五入調整（公司貨幣）,
Rounded Total (Company Currency),整數總計（公司貨幣）,
In Words (Company Currency),大寫（Company Currency）,
Rounding Adjustment,舍入調整,
In Words,大寫,
Total Advance,預付款總計,
Disable Rounded Total,禁用圓角總,
Cash/Bank Account,現金／銀行帳戶,
Write Off Amount (Company Currency),核銷金額（公司貨幣）,
Set Advances and Allocate (FIFO),設置進度和分配（FIFO）,
Get Advances Paid,獲取有償進展,
Advances,進展,
Terms,條款,
Terms and Conditions1,條款及條件1,
Group same items,組相同的項目,
Print Language,打印語言,
"Once set, this invoice will be on hold till the set date",一旦設置，該發票將被保留至設定的日期,
Credit To,信貸,
Party Account Currency,黨的賬戶幣種,
Against Expense Account,對費用帳戶,
Inter Company Invoice Reference,Inter公司發票參考,
Is Internal Supplier,是內部供應商,
Start date of current invoice's period,當前發票期間內的開始日期,
End date of current invoice's period,當前發票的期限的最後一天,
Update Auto Repeat Reference,更新自動重複參考,
Purchase Invoice Advance,購買發票提前,
Purchase Invoice Item,採購發票項目,
Quantity and Rate,數量和速率,
Received Qty,到貨數量,
Accepted Qty,接受數量,
Rejected Qty,被拒絕的數量,
UOM Conversion Factor,計量單位換算係數,
Discount on Price List Rate (%),折扣價目表率（％）,
Price List Rate (Company Currency),價格列表費率（公司貨幣）,
Rate ,單價,
Rate (Company Currency),率（公司貨幣）,
Amount (Company Currency),金額（公司貨幣）,
Is Free Item,是免費物品,
Net Rate,淨費率,
Net Rate (Company Currency),淨利率（公司貨幣）,
Net Amount (Company Currency),淨金額（公司貨幣）,
Item Tax Amount Included in Value,物品稅金額包含在價值中,
Landed Cost Voucher Amount,到岸成本憑證金額,
Raw Materials Supplied Cost,原料供應成本,
Accepted Warehouse,收料倉庫,
Serial No,序列號,
Rejected Serial No,拒絕序列號,
Expense Head,總支出,
Is Fixed Asset,是固定的資產,
Asset Location,資產位置,
Deferred Expense,遞延費用,
Deferred Expense Account,遞延費用帳戶,
Service Stop Date,服務停止日期,
Enable Deferred Expense,啟用延期費用,
Service Start Date,服務開始日期,
Service End Date,服務結束日期,
Allow Zero Valuation Rate,允許零估值,
Item Tax Rate,項目稅率,
Tax detail table fetched from item master as a string and stored in this field.\nUsed for Taxes and Charges,從項目主檔獲取的稅務詳細資訊表，成為字串並存儲在這欄位。用於稅賦及費用,
Purchase Order Item,採購訂單項目,
Purchase Receipt Detail,採購收貨明細,
Item Weight Details,項目重量細節,
Weight Per Unit,每單位重量,
Total Weight,總重量,
Weight UOM,重量計量單位,
Page Break,分頁符,
Consider Tax or Charge for,考慮稅收或收費,
Valuation and Total,估值與總計,
Valuation,計價,
Add or Deduct,加或減,
On Previous Row Amount,在上一行金額,
On Previous Row Total,在上一行共,
On Item Quantity,關於物品數量,
Reference Row #,參考列#,
Is this Tax included in Basic Rate?,包括在基本速率此稅？,
"If checked, the tax amount will be considered as already included in the Print Rate / Print Amount",如果選中，稅額將被視為已包含在列印速率/列印數量,
Account Head,帳戶頭,
Tax Amount After Discount Amount,稅額折後金額,
Item Wise Tax Detail ,明智的稅項明細,
"Standard tax template that can be applied to all Purchase Transactions. This template can contain list of tax heads and also other expense heads like ""Shipping"", ""Insurance"", ""Handling"" etc.\n\n#### Note\n\nThe tax rate you define here will be the standard tax rate for all **Items**. If there are **Items** that have different rates, they must be added in the **Item Tax** table in the **Item** master.\n\n#### Description of Columns\n\n1. Calculation Type: \n    - This can be on **Net Total** (that is the sum of basic amount).\n    - **On Previous Row Total / Amount** (for cumulative taxes or charges). If you select this option, the tax will be applied as a percentage of the previous row (in the tax table) amount or total.\n    - **Actual** (as mentioned).\n2. Account Head: The Account ledger under which this tax will be booked\n3. Cost Center: If the tax / charge is an income (like shipping) or expense it needs to be booked against a Cost Center.\n4. Description: Description of the tax (that will be printed in invoices / quotes).\n5. Rate: Tax rate.\n6. Amount: Tax amount.\n7. Total: Cumulative total to this point.\n8. Enter Row: If based on ""Previous Row Total"" you can select the row number which will be taken as a base for this calculation (default is the previous row).\n9. Consider Tax or Charge for: In this section you can specify if the tax / charge is only for valuation (not a part of total) or only for total (does not add value to the item) or for both.\n10. Add or Deduct: Whether you want to add or deduct the tax.",可應用到所有購買交易稅的標準模板。這個模板可以包含稅收元首和像“送貨”，“保險”還包括其他費用清單的頭，“處理”等\n\n ####注\n\n您在此處定義的稅率將標準稅率對所有** **的項目。如果有** **物品具有不同的速率，就必須在**項計稅添加**表中的** **項目主。\n\n ####列\n\n 1的說明。計算類型：\n - 這可以是在淨** **總（即基本量的總和）。\n - **以前的行總計/金額**（對於累計稅費）。如果選擇此選項，稅收將與前行的百分比（在稅率表）量或總被應用。\n - ** **實際（如前所述）。\n 2。帳戶負責人：該帳戶下的台帳此稅收將被黃牌警告\n 3。成本中心：如果稅/收費收入（如海運）或費用，它需要對一個成本中心預訂。\n 4。說明：稅收的說明（將在發票/報價印刷）。\n 5。速度：稅率。\n 6。金額：稅額。\n 7。總計：累積總數達到了這一點。\n 8。輸入行：如果基於“前行匯總”，您可以選擇將被視為這種計算基礎（預設值是前行）的行號。\n 9。考慮稅收或收費為：在本節中，你可以指定是否稅/費僅用於評估（總不是部分），或只為總（不增加價值的項目），或兩者兼有。\n 10。添加或扣除：無論你是想增加或扣除的稅。,
Salary Component Account,薪金部分賬戶,
Default Bank / Cash account will be automatically updated in Salary Journal Entry when this mode is selected.,默認銀行/現金帳戶時，會選擇此模式可以自動在工資日記條目更新。,
Include Payment (POS),包括支付（POS）,
Offline POS Name,離線POS名稱,
Is Return (Credit Note),是退貨（信用票據）,
Return Against Sales Invoice,射向銷售發票,
Update Billed Amount in Sales Order,更新銷售訂單中的結算金額,
Customer PO Details,客戶PO詳細信息,
Customer's Purchase Order,客戶採購訂單,
Customer's Purchase Order Date,客戶的採購訂單日期,
Customer Address,客戶地址,
Shipping Address Name,送貨地址名稱,
Company Address Name,公司地址名稱,
Rate at which Customer Currency is converted to customer's base currency,公司貨幣被換算成客戶基礎貨幣的匯率,
Rate at which Price list currency is converted to customer's base currency,價目表貨幣被換算成客戶基礎貨幣的匯率,
Set Source Warehouse,設置源倉庫,
Packing List,包裝清單,
Packed Items,盒裝項目,
Product Bundle Help,產品包幫助,
Time Sheet List,時間表列表,
Time Sheets,考勤表,
Total Billing Amount,總結算金額,
Sales Taxes and Charges Template,營業稅金及費用套版,
Sales Taxes and Charges,銷售稅金及費用,
Loyalty Points Redemption,忠誠積分兌換,
Redeem Loyalty Points,兌換忠誠度積分,
Redemption Account,贖回賬戶,
Redemption Cost Center,贖回成本中心,
In Words will be visible once you save the Sales Invoice.,銷售發票一被儲存，就會顯示出來。,
Allocate Advances Automatically (FIFO),自動分配進度（FIFO）,
Get Advances Received,取得預先付款,
Base Change Amount (Company Currency),基地漲跌額（公司幣種）,
Write Off Outstanding Amount,核銷額（億元）,
Terms and Conditions Details,條款及細則詳情,
Is Internal Customer,是內部客戶,
Unpaid and Discounted,無償和折扣,
Accounting Details,會計細節,
Debit To,借方,
Is Opening Entry,是開放登錄,
C-Form Applicable,C-表格適用,
Commission Rate (%),佣金比率（％）,
Sales Team1,銷售團隊1,
Against Income Account,對收入帳戶,
Sales Invoice Advance,銷售發票提前,
Advance amount,提前量,
Sales Invoice Item,銷售發票項目,
Customer's Item Code,客戶的產品編號,
Brand Name,商標名稱,
Qty as per Stock UOM,數量按庫存計量單位,
Discount and Margin,折扣和保證金,
Rate With Margin,利率保證金,
Discount (%) on Price List Rate with Margin,價格上漲率與貼現率的折扣（％）,
Rate With Margin (Company Currency),利率保證金（公司貨幣）,
Delivered By Supplier,交付供應商,
Deferred Revenue,遞延收入,
Deferred Revenue Account,遞延收入帳戶,
Enable Deferred Revenue,啟用延期收入,
Stock Details,庫存詳細訊息,
Customer Warehouse (Optional),客戶倉庫（可選）,
Available Batch Qty at Warehouse,可用的批次數量在倉庫,
Available Qty at Warehouse,有貨數量在倉庫,
Delivery Note Item,送貨單項目,
Base Amount (Company Currency),基本金額（公司幣種）,
Sales Invoice Timesheet,銷售發票時間表,
Time Sheet,時間表,
Billing Hours,結算時間,
Timesheet Detail,詳細時間表,
Tax Amount After Discount Amount (Company Currency),稅額後，優惠金額（公司貨幣）,
Item Wise Tax Detail,項目智者稅制明細,
Parenttype,Parenttype,
"Standard tax template that can be applied to all Sales Transactions. This template can contain list of tax heads and also other expense / income heads like ""Shipping"", ""Insurance"", ""Handling"" etc.\n\n#### Note\n\nThe tax rate you define here will be the standard tax rate for all **Items**. If there are **Items** that have different rates, they must be added in the **Item Tax** table in the **Item** master.\n\n#### Description of Columns\n\n1. Calculation Type: \n    - This can be on **Net Total** (that is the sum of basic amount).\n    - **On Previous Row Total / Amount** (for cumulative taxes or charges). If you select this option, the tax will be applied as a percentage of the previous row (in the tax table) amount or total.\n    - **Actual** (as mentioned).\n2. Account Head: The Account ledger under which this tax will be booked\n3. Cost Center: If the tax / charge is an income (like shipping) or expense it needs to be booked against a Cost Center.\n4. Description: Description of the tax (that will be printed in invoices / quotes).\n5. Rate: Tax rate.\n6. Amount: Tax amount.\n7. Total: Cumulative total to this point.\n8. Enter Row: If based on ""Previous Row Total"" you can select the row number which will be taken as a base for this calculation (default is the previous row).\n9. Is this Tax included in Basic Rate?: If you check this, it means that this tax will not be shown below the item table, but will be included in the Basic Rate in your main item table. This is useful where you want give a flat price (inclusive of all taxes) price to customers.",可應用到所有銷售交易稅的標準模板。這個模板可以包含稅收元首和像“送貨”，“保險”還包括其他費用/收入頭列表中，“處理”等\n\n ####注\n\n稅率您定義，這裡將是標準稅率對所有** **的項目。如果有** **物品具有不同的速率，就必須在**項計稅添加**表中的** **項目主。\n\n ####列\n\n 1的說明。計算類型：\n - 這可以是在淨** **總（即基本量的總和）。\n - **以前的行總計/金額**（對於累計稅費）。如果選擇此選項，稅收將與前行的百分比（在稅率表）量或總被應用。\n - ** **實際（如前所述）。\n 2。帳戶負責人：該帳戶下的台帳此稅收將被黃牌警告\n 3。成本中心：如果稅/收費收入（如海運）或費用，它需要對一個成本中心預訂。\n 4。說明：稅收的說明（將在發票/報價印刷）。\n 5。速度：稅率。\n 6。金額：稅額。\n 7。總計：累積總數達到了這一點。\n 8。輸入行：如果基於“前行匯總”，您可以選擇將被視為這種計算基礎（預設值是前行）的行號。\n 9。這是含稅的基本速率？：如果你檢查這一點，就意味著這個稅不會顯示在項目表中，但在你的主項表將被納入基本速率。你想要給一個單位的價格（包括所有稅費）的價格為顧客這是非常有用的。,
* Will be calculated in the transaction.,*將被計算在該交易。,
From No,來自No,
To No,到沒有,
Is Company,是公司,
Current State,當前狀態,
Purchased,購買,
From Shareholder,來自股東,
From Folio No,來自Folio No,
To Shareholder,給股東,
To Folio No,對開本No,
Equity/Liability Account,股票/負債賬戶,
Asset Account,資產賬戶,
(including),（包括）,
Folio no.,Folio no。,
Address and Contacts,地址和聯繫方式,
Contact List,聯繫人列表,
Hidden list maintaining the list of contacts linked to Shareholder,隱藏列表維護鏈接到股東的聯繫人列表,
Specify conditions to calculate shipping amount,指定條件來計算運費金額,
Shipping Rule Label,送貨規則標籤,
example: Next Day Shipping,例如：次日發貨,
Shipping Rule Type,運輸規則類型,
Shipping Account,送貨帳戶,
Calculate Based On,計算的基礎上,
Net Weight,淨重,
Shipping Amount,航運量,
Shipping Rule Conditions,送貨規則條件,
Restrict to Countries,限製到國家,
Valid for Countries,有效的國家,
Shipping Rule Condition,送貨規則條件,
A condition for a Shipping Rule,為運輸規則的條件,
From Value,從價值,
To Value,重視價值,
Shipping Rule Country,航運規則國家,
Subscription Period,訂閱期,
Subscription Start Date,訂閱開始日期,
Trial Period Start Date,試用期開始日期,
Trial Period End Date,試用期結束日期,
Current Invoice Start Date,當前發票開始日期,
Current Invoice End Date,當前發票結束日期,
Days Until Due,截止天數,
Number of days that the subscriber has to pay invoices generated by this subscription,用戶必須支付此訂閱生成的發票的天數,
Cancel At End Of Period,期末取消,
Generate Invoice At Beginning Of Period,在期初生成發票,
Plans,計劃,
Discounts,打折,
Additional DIscount Percentage,額外折扣百分比,
Additional DIscount Amount,額外的折扣金額,
Subscription Invoice,訂閱發票,
Subscription Plan,訂閱計劃,
Billing Interval,計費間隔,
Billing Interval Count,計費間隔計數,
"Number of intervals for the interval field e.g if Interval is 'Days' and Billing Interval Count is 3, invoices will be generated every 3 days",間隔字段的間隔數，例如，如果間隔為&#39;天數&#39;並且計費間隔計數為3，則會每3天生成一次發票,
Payment Plan,付款計劃,
Subscription Plan Detail,訂閱計劃詳情,
Plan,計劃,
Subscription Settings,訂閱設置,
Grace Period,寬限期,
Number of days after invoice date has elapsed before canceling subscription or marking subscription as unpaid,在取消訂閱或將訂閱標記為未付之前，發票日期之後的天數已過,
Tax Rule,稅務規則,
Tax Type,稅收類型,
Use for Shopping Cart,使用的購物車,
Billing City,結算城市,
Billing County,開票縣,
Billing State,計費狀態,
Billing Zipcode,計費郵編,
Billing Country,結算國家,
Shipping City,航運市,
Shipping County,航運縣,
Shipping State,運輸狀態,
Shipping Zipcode,運輸郵編,
Shipping Country,航運國家,
Tax Withholding Account,扣繳稅款賬戶,
Tax Withholding Rates,預扣稅率,
Rates,價格,
Tax Withholding Rate,稅收預扣稅率,
Single Transaction Threshold,單一交易閾值,
Cumulative Transaction Threshold,累積交易閾值,
Agriculture Analysis Criteria,農業分析標準,
Linked Doctype,鏈接的文檔類型,
Soil Texture,土壤紋理,
Weather,天氣,
Agriculture Manager,農業經理,
Agriculture User,農業用戶,
Agriculture Task,農業任務,
Task Name,任務名稱,
Start Day,開始日,
End Day,結束的一天,
Holiday Management,假期管理,
Previous Business Day,前一個營業日,
Next Business Day,下一個營業日,
Urgent,緊急,
Crop Name,作物名稱,
Scientific Name,科學名稱,
"You can define all the tasks which need to carried out for this crop here. The day field is used to mention the day on which the task needs to be carried out, 1 being the 1st day, etc.. ",你可以在這裡定義所有需要進行的作業。日場是用來提及任務需要執行的日子，1日是第一天等。,
Crop Spacing,作物間距,
Crop Spacing UOM,裁剪間隔UOM,
Row Spacing,行間距,
Row Spacing UOM,行距單位,
Biennial,雙年展,
Planting UOM,種植UOM,
Planting Area,種植面積,
Yield UOM,產量UOM,
Produced Items,生產物品,
Produce,生產,
Byproducts,副產品,
Linked Location,鏈接位置,
A link to all the Locations in which the Crop is growing,指向作物生長的所有位置的鏈接,
This will be day 1 of the crop cycle,這將是作物週期的第一天,
ISO 8601 standard,ISO 8601標準,
Cycle Type,循環類型,
The minimum length between each plant in the field for optimum growth,每個工廠之間的最小長度為最佳的增長,
The minimum distance between rows of plants for optimum growth,植株之間的最小距離，以獲得最佳生長,
Detected Diseases,檢測到的疾病,
List of diseases detected on the field. When selected it'll automatically add a list of tasks to deal with the disease ,在現場檢測到的疾病清單。當選擇它會自動添加一個任務清單處理疾病,
Detected Disease,檢測到的疾病,
LInked Analysis,LInked分析,
Tasks Created,創建的任務,
Common Name,通用名稱,
Treatment Task,治療任務,
Treatment Period,治療期,
Fertilizer Name,肥料名稱,
Density (if liquid),密度（如果液體）,
Linked Plant Analysis,鏈接的工廠分析,
Linked Soil Analysis,連接的土壤分析,
Linked Soil Texture,連接的土壤紋理,
Collection Datetime,收集日期時間,
Laboratory Testing Datetime,實驗室測試日期時間,
Result Datetime,結果日期時間,
Plant Analysis Criterias,植物分析標準,
Plant Analysis Criteria,植物分析標準,
Minimum Permissible Value,最小允許值,
Maximum Permissible Value,最大允許值,
Ca/K,鈣/ K,
Ca/Mg,鈣/鎂,
Mg/K,鎂/ K,
(Ca+Mg)/K,（鈣+鎂）/ K,
Ca/(K+Ca+Mg),的Ca /（K +鈣+鎂）,
Soil Analysis Criterias,土壤分析標準,
Soil Analysis Criteria,土壤分析標準,
Soil Type,土壤類型,
Loamy Sand,壤土沙,
Sandy Loam,桑迪壤土,
Silt Loam,淤泥壤土,
Clay Loam,粘土Loam,
Silty Clay Loam,粉質粘土壤土,
Sandy Clay,桑迪·克萊,
Silty Clay,粉質粘土,
Silt Composition (%),粉塵成分（％）,
Ternary Plot,三元劇情,
Soil Texture Criteria,土壤質地標準,
Type of Sample,樣品類型,
Collection Temperature ,收集溫度,
Storage Temperature,儲存溫度,
Appearance,出現,
Person Responsible,負責人,
Water Analysis Criteria,水分析標準,
Weather Parameter,天氣參數,
Asset Owner,資產所有者,
Asset Owner Company,資產所有者公司,
Disposal Date,處置日期,
Journal Entry for Scrap,日記帳分錄報廢,
Available-for-use Date,可用日期,
Calculate Depreciation,計算折舊,
Allow Monthly Depreciation,允許每月折舊,
Number of Depreciations Booked,預訂折舊數,
Finance Books,財務書籍,
Straight Line,直線,
Double Declining Balance,雙倍餘額遞減,
Manual,手冊,
Value After Depreciation,折舊後,
Total Number of Depreciations,折舊總數,
Frequency of Depreciation (Months),折舊率（月）,
Next Depreciation Date,接下來折舊日期,
Depreciation Schedule,折舊計劃,
Depreciation Schedules,折舊計劃,
Insurance details,保險詳情,
Policy number,保單號碼,
Insurer,保險公司,
Insured value,保價值,
Insurance Start Date,保險開始日期,
Insurance End Date,保險終止日期,
Comprehensive Insurance,綜合保險,
Maintenance Required,需要維護,
Check if Asset requires Preventive Maintenance or Calibration,檢查資產是否需要預防性維護或校準,
Booked Fixed Asset,預訂的固定資產,
Purchase Receipt Amount,採購收據金額,
Default Finance Book,默認金融書,
Quality Manager,質量經理,
Asset Category Name,資產類別名稱,
Depreciation Options,折舊選項,
Enable Capital Work in Progress Accounting,啟用資本在建會計,
Finance Book Detail,財務圖書細節,
Asset Category Account,資產類別的帳戶,
Fixed Asset Account,固定資產帳戶,
Accumulated Depreciation Account,累計折舊科目,
Depreciation Expense Account,折舊費用帳戶,
Capital Work In Progress Account,資本工作進行中的帳戶,
Asset Finance Book,資產融資書,
Written Down Value,寫下價值,
Expected Value After Useful Life,期望值使用壽命結束後,
Rate of Depreciation,折舊率,
Maintenance Team,維修隊,
Maintenance Manager Name,維護經理姓名,
Maintenance Tasks,維護任務,
Manufacturing User,製造業用戶,
Asset Maintenance Log,資產維護日誌,
Maintenance Type,維護類型,
Maintenance Status,維修狀態,
Planned,計劃,
Has Certificate ,有證書,
Certificate,證書,
Actions performed,已執行的操作,
Asset Maintenance Task,資產維護任務,
Maintenance Task,維護任務,
Preventive Maintenance,預防性的維護,
Calibration,校準,
2 Yearly,2年,
Certificate Required,證書要求,
Assign to Name,分配給名稱,
Next Due Date,下一個到期日,
Last Completion Date,最後完成日期,
Asset Maintenance Team,資產維護團隊,
Maintenance Team Name,維護組名稱,
Maintenance Team Members,維護團隊成員,
Stock Manager,庫存管理,
Asset Movement Item,資產變動項目,
Source Location,來源地點,
From Employee,從員工,
Target Location,目標位置,
To Employee,給員工,
Asset Repair,資產修復,
Failure Date,失敗日期,
Assign To Name,分配到名稱,
Repair Status,維修狀態,
Error Description,錯誤說明,
Downtime,停機,
Repair Cost,修理費用,
Manufacturing Manager,生產經理,
Current Asset Value,流動資產價值,
New Asset Value,新資產價值,
Make Depreciation Entry,計提折舊進入,
Finance Book Id,金融書籍ID,
Location Name,地點名稱,
Parent Location,父位置,
Check if it is a hydroponic unit,檢查它是否是水培單位,
Location Details,位置詳情,
Latitude,緯度,
Longitude,經度,
Area,區,
Area UOM,區域UOM,
Tree Details,樹詳細信息,
Maintenance Team Member,維護團隊成員,
Team Member,隊員,
Maintenance Role,維護角色,
Buying Settings,採購設定,
Settings for Buying Module,設置購買模塊,
Supplier Naming By,供應商命名,
Default Supplier Group,默認供應商組,
Default Buying Price List,預設採購價格表,
Backflush Raw Materials of Subcontract Based On,基於CRM的分包合同反向原材料,
Material Transferred for Subcontract,轉包材料轉讓,
Over Transfer Allowance (%),超過轉移津貼（％）,
Percentage you are allowed to transfer more against the quantity ordered. For example: If you have ordered 100 units. and your Allowance is 10% then you are allowed to transfer 110 units.,允許您根據訂購數量轉移更多的百分比。例如：如果您訂購了100個單位。你的津貼是10％，那麼你可以轉讓110個單位。,
PUR-ORD-.YYYY.-,採購訂單-.YYYY.-,
Get Items from Open Material Requests,從開放狀態的物料需求取得項目,
Fetch items based on Default Supplier.,根據默認供應商獲取項目。,
Required By,需求來自,
Order Confirmation No,訂單確認號,
Order Confirmation Date,訂單確認日期,
Customer Mobile No,客戶手機號碼,
Customer Contact Email,客戶聯絡電子郵件,
Set Target Warehouse,設置目標倉庫,
Sets 'Warehouse' in each row of the Items table.,在“項目”表的每一行中設置“倉庫”。,
Supply Raw Materials,供應原料,
Purchase Order Pricing Rule,採購訂單定價規則,
Set Reserve Warehouse,設置儲備倉庫,
In Words will be visible once you save the Purchase Order.,採購訂單一被儲存，就會顯示出來。,
Advance Paid,提前支付,
% Billed,％已開立帳單,
% Received,％ 已收,
Ref SQ,參考SQ,
Inter Company Order Reference,公司間訂單參考,
Supplier Part Number,供應商零件編號,
Billed Amt,已結算額,
Warehouse and Reference,倉庫及參考,
To be delivered to customer,要傳送給客戶,
Material Request Item,物料需求項目,
Supplier Quotation Item,供應商報價項目,
Against Blanket Order,反對一攬子訂單,
Blanket Order,總訂單,
Blanket Order Rate,一攬子訂單費率,
Returned Qty,返回的數量,
Purchase Order Item Supplied,採購訂單項目供應商,
BOM Detail No,BOM表詳細編號,
Stock Uom,庫存計量單位,
Raw Material Item Code,原料產品編號,
Supplied Qty,附送數量,
Purchase Receipt Item Supplied,採購入庫項目供應商,
Current Stock,當前庫存,
For individual supplier,對於個別供應商,
Link to Material Requests,鏈接到物料請求,
Message for Supplier,消息供應商,
Request for Quotation Item,詢價項目,
Required Date,所需時間,
Request for Quotation Supplier,詢價供應商,
Send Email,發送電子郵件,
Quote Status,報價狀態,
Download PDF,下載PDF,
Supplier of Goods or Services.,供應商的商品或服務。,
Name and Type,名稱和類型,
Default Bank Account,預設銀行帳戶,
Is Transporter,是運輸車,
Supplier Type,供應商類型,
Allow Purchase Invoice Creation Without Purchase Order,允許創建沒有採購訂單的採購發票,
Allow Purchase Invoice Creation Without Purchase Receipt,允許在沒有收貨的情況下創建採購發票,
Warn RFQs,警告詢價單,
Warn POs,警告採購訂單,
Prevent RFQs,防止詢價,
Prevent POs,防止採購訂單,
Billing Currency,結算貨幣,
Default Payment Terms Template,默認付款條款模板,
Block Supplier,塊供應商,
Hold Type,保持類型,
Leave blank if the Supplier is blocked indefinitely,如果供應商被無限期封鎖，請留空,
Default Payable Accounts,預設應付帳款,
Mention if non-standard payable account,如果非標準應付賬款提到,
Default Tax Withholding Config,預設稅款預扣配置,
Supplier Details,供應商詳細資訊,
Statutory info and other general information about your Supplier,供應商的法定資訊和其他一般資料,
Supplier Address,供應商地址,
Link to material requests,鏈接到材料請求,
Rounding Adjustment (Company Currency,四捨五入調整（公司貨幣）,
Auto Repeat Section,自動重複部分,
Is Subcontracted,轉包,
Lead Time in days,交貨天期,
Supplier Score,供應商分數,
Indicator Color,指示燈顏色,
Evaluation Period,評估期,
Per Week,每個星期,
Scoring Setup,得分設置,
Weighting Function,加權函數,
"Scorecard variables can be used, as well as:\n{total_score} (the total score from that period),\n{period_number} (the number of periods to present day)\n",可以使用記分卡變量，以及：{total_score}（該期間的總分數），{period_number}（到當前時間段的數量）,
Scoring Standings,計分榜,
Criteria Setup,條件設置,
Load All Criteria,加載所有標準,
Scoring Criteria,評分標準,
Scorecard Actions,記分卡操作,
Warn for new Request for Quotations,警告新的報價請求,
Warn for new Purchase Orders,警告新的採購訂單,
Notify Supplier,通知供應商,
Notify Employee,通知員工,
Supplier Scorecard Criteria,供應商記分卡標準,
Criteria Name,標準名稱,
Max Score,最高分數,
Criteria Formula,標準配方,
Criteria Weight,標準重量,
Supplier Scorecard Period,供應商記分卡期,
Period Score,期間得分,
Calculations,計算,
Criteria,標準,
Variables,變量,
Supplier Scorecard Setup,供應商記分卡設置,
Supplier Scorecard Scoring Criteria,供應商記分卡評分標準,
Supplier Scorecard Scoring Standing,供應商記分卡,
Standing Name,常務名稱,
Yellow,黃色,
Min Grade,最小成績,
Max Grade,最高等級,
Warn Purchase Orders,警告採購訂單,
Prevent Purchase Orders,防止採購訂單,
Employee ,僱員,
Supplier Scorecard Scoring Variable,供應商記分卡評分變量,
Variable Name,變量名,
Parameter Name,參數名稱,
Supplier Scorecard Standing,供應商記分卡站立,
Supplier Scorecard Variable,供應商記分卡變數,
Call Log,通話記錄,
Received By,收到者,
Caller Information,來電者信息,
Contact Name,聯絡人姓名,
Lead ,鉛,
Lead Name,主導者名稱,
Ringing,鈴聲,
Missed,錯過,
Call Duration in seconds,呼叫持續時間（秒）,
Recording URL,錄製網址,
Communication Medium,通信介質,
Communication Medium Type,通信媒體類型,
Voice,語音,
Catch All,全部抓住,
"If there is no assigned timeslot, then communication will be handled by this group",如果沒有分配的時間段，則該組將處理通信,
Timeslots,時隙,
Communication Medium Timeslot,通信媒體時隙,
Employee Group,員工組,
Appointment,約定,
Scheduled Time,計劃的時間,
Unverified,未驗證,
Customer Details,客戶詳細資訊,
Phone Number,電話號碼,
Skype ID,Skype帳號,
Linked Documents,鏈接文件,
Appointment With,預約,
Calendar Event,日曆活動,
Appointment Booking Settings,預約預約設置,
Enable Appointment Scheduling,啟用約會計劃,
Agent Details,代理商詳細信息,
Number of Concurrent Appointments,並發預約數,
Appointment Details,預約詳情,
Appointment Duration (In Minutes),預約時間（以分鐘為單位）,
Notify Via Email,通過電子郵件通知,
Notify customer and agent via email on the day of the appointment.,在約會當天通過電子郵件通知客戶和代理商。,
Number of days appointments can be booked in advance,可以提前預約的天數,
Success Settings,成功設定,
Success Redirect URL,成功重定向網址,
"Leave blank for home.\nThis is relative to site URL, for example ""about"" will redirect to ""https://yoursitename.com/about""",留空在家。這是相對於網站URL的，例如“ about”將重定向到“ https://yoursitename.com/about”,
Appointment Booking Slots,預約訂位,
Day Of Week,星期幾,
From Time ,從時間,
Campaign Email Schedule,Campaign電子郵件計劃,
Send After (days),發送後（天）,
Signed,簽,
Party User,派對用戶,
Unsigned,無符號,
Fulfilment Status,履行狀態,
N/A,不適用,
Unfulfilled,秕,
Partially Fulfilled,部分實現,
Fulfilled,達到,
Lapsed,失效,
Signee Details,簽名詳情,
Signee,簽署人,
Signed On,簽名,
Contract Details,合同細節,
Contract Template,合約範本,
Contract Terms,合同條款,
Fulfilment Details,履行細節,
Fulfilment Deadline,履行期限,
Fulfilment Terms,履行條款,
Contract Fulfilment Checklist,合同履行清單,
Contract Terms and Conditions,合同條款和條件,
Fulfilment Terms and Conditions,履行條款和條件,
Contract Template Fulfilment Terms,合同模板履行條款,
Email Campaign,電郵廣告系列,
Email Campaign For ,電子郵件活動,
Lead is an Organization,領導是一個組織,
Person Name,人姓名,
Lost Quotation,遺失報價,
Interested,有興趣,
Converted,轉換,
Do Not Contact,不要聯絡,
From Customer,從客戶,
Campaign Name,活動名稱,
Follow Up,跟進,
Next Contact By,下一個聯絡人由,
Next Contact Date,下次聯絡日期,
Ends On,結束於,
Address & Contact,地址及聯絡方式,
Mobile No.,手機號碼,
Lead Type,主導類型,
Channel Partner,渠道合作夥伴,
Consultant,顧問,
Market Segment,市場分類,
Industry,行業,
Request Type,請求類型,
Product Enquiry,產品查詢,
Request for Information,索取資料,
Suggestions,建議,
Blog Subscriber,網誌訂閱者,
LinkedIn Settings,LinkedIn設置,
Company ID,公司編號,
OAuth Credentials,OAuth憑證,
Consumer Key,消費者密鑰,
Consumer Secret,消費者的秘密,
User Details,使用者詳細資料,
Session Status,會話狀態,
Lost Reason Detail,丟失的原因細節,
Opportunity Lost Reason,機會失去理智,
Potential Sales Deal,潛在的銷售交易,
Opportunity From,機會從,
Customer / Lead Name,客戶/鉛名稱,
Opportunity Type,機會型,
Converted By,轉換依據,
Sales Stage,銷售階段,
Lost Reason,失落的原因,
Expected Closing Date,預計截止日期,
To Discuss,為了討論,
With Items,隨著項目,
Probability (%),機率（％）,
Contact Info,聯絡方式,
Customer / Lead Address,客戶/鉛地址,
Contact Mobile No,聯絡手機號碼,
Enter name of campaign if source of enquiry is campaign,輸入活動的名稱，如果查詢來源是運動,
Opportunity Date,機會日期,
Opportunity Item,項目的機會,
Basic Rate,基礎匯率,
Stage Name,藝名,
Social Media Post,社交媒體帖子,
Post Status,發布狀態,
Posted,發表於,
LinkedIn,領英,
LinkedIn Post Id,LinkedIn郵政編號,
Tweet,鳴叫,
Twitter Settings,Twitter設置,
API Secret Key,API密鑰,
Term Name,術語名稱,
Term Start Date,期限起始日期,
Term End Date,期限結束日期,
Academics User,學術界用戶,
Academic Year Name,學年名稱,
LMS User,LMS用戶,
Assessment Criteria Group,評估標準組,
Assessment Group Name,評估小組名稱,
Parent Assessment Group,家長評估小組,
Assessment Name,評估名稱,
Grading Scale,分級量表,
Examiner,檢查員,
Examiner Name,考官名稱,
Supervisor,監,
Supervisor Name,主管名稱,
Evaluate,評估,
Maximum Assessment Score,最大考核評分,
Assessment Plan Criteria,評估計劃標準,
Maximum Score,最高分數,
Result,成績,
Total Score,總得分,
Grade,年級,
Assessment Result Detail,評價結果詳細,
Assessment Result Tool,評價結果工具,
Result HTML,結果HTML,
Content Activity,內容活動,
Last Activity ,上次活動,
Content Question,內容問題,
Question Link,問題鏈接,
Course Name,課程名,
Topics,話題,
Default Grading Scale,默認等級規模,
Education Manager,教育經理,
Course Activity,課程活動,
Course Enrollment,課程報名,
Activity Date,活動日期,
Course Assessment Criteria,課程評價標準,
Weightage,權重,
Course Content,課程內容,
Quiz,測驗,
Program Enrollment,招生計劃,
Enrollment Date,報名日期,
Instructor Name,導師姓名,
Course Scheduling Tool,排課工具,
Course Start Date,課程開始日期,
To TIme,要時間,
Course End Date,課程結束日期,
Course Topic,課程主題,
Topic,話題,
Topic Name,主題名稱,
Education Settings,教育設置,
Current Academic Year,當前學年,
Current Academic Term,當前學術期限,
Attendance Freeze Date,出勤凍結日期,
Validate Batch for Students in Student Group,驗證學生組學生的批次,
"For Batch based Student Group, the Student Batch will be validated for every Student from the Program Enrollment.",對於基於批次的學生組，學生批次將由課程註冊中的每位學生進行驗證。,
Validate Enrolled Course for Students in Student Group,驗證學生組學生入學課程,
"For Course based Student Group, the Course will be validated for every Student from the enrolled Courses in Program Enrollment.",對於基於課程的學生小組，課程將從入學課程中的每個學生確認。,
Make Academic Term Mandatory,強制學術期限,
"If enabled, field Academic Term will be Mandatory in Program Enrollment Tool.",如果啟用，則在學期註冊工具中，字段學術期限將是強制性的。,
Skip User creation for new Student,跳過為新學生創建的用戶,
"By default, a new User is created for every new Student. If enabled, no new User will be created when a new Student is created.",默認情況下，為每個新學生創建一個新用戶。如果啟用，則在創建新學生時將不會創建新用戶。,
Instructor Records to be created by,導師記錄由,
Employee Number,員工人數,
Fee Category,收費類別,
Fee Component,收費組件,
Fees Category,費用類別,
Fee Schedule,收費表,
Fee Structure,費用結構,
Fee Creation Status,費用創建狀態,
In Process,在過程,
Send Payment Request Email,發送付款請求電子郵件,
Student Category,學生組,
Fee Breakup for each student,每名學生的費用分手,
Total Amount per Student,學生總數,
Institution,機構,
Fee Schedule Program,費用計劃計劃,
Student Batch,學生批,
Total Students,學生總數,
Fee Schedule Student Group,費用計劃學生組,
EDU-FEE-.YYYY.-,EDU-收費.YYYY.-,
Send Payment Request,發送付款請求,
Student Details,學生細節,
Student Email,學生電子郵件,
Grading Scale Name,分級標準名稱,
Grading Scale Intervals,分級刻度間隔,
Intervals,間隔,
Grading Scale Interval,分級分度值,
Grade Code,等級代碼,
Threshold,閾,
Grade Description,等級說明,
Guardian,監護人,
Guardian Name,監護人姓名,
Alternate Number,備用號碼,
Occupation,佔用,
Guardian Of ,守護者,
Students,學生們,
Guardian Interests,守護興趣,
Guardian Interest,衛利息,
Interest,利益,
Guardian Student,學生監護人,
Instructor Log,講師日誌,
Other details,其他詳細資訊,
Option,選項,
Is Correct,是正確的,
Program Name,程序名稱,
Program Abbreviation,計劃縮寫,
Courses,培訓班,
Is Published,已發布,
Allow Self Enroll,允許自我註冊,
Is Featured,精選,
Intro Video,介紹視頻,
Program Course,課程計劃,
School House,學校議院,
Boarding Student,寄宿學生,
Check this if the Student is residing at the Institute's Hostel.,如果學生住在學院的旅館，請檢查。,
Institute's Bus,學院的巴士,
Self-Driving Vehicle,自駕車,
Pick/Drop by Guardian,由守護者選擇,
Enrolled courses,入學課程,
Program Enrollment Course,課程註冊課程,
Program Enrollment Fee,計劃註冊費,
Program Enrollment Tool,計劃註冊工具,
Get Students From,讓學生從,
Student Applicant,學生申請,
Get Students,讓學生,
Enrollment Details,註冊詳情,
New Student Batch,新學生批次,
Enroll Students,招收學生,
New Academic Year,新學年,
New Academic Term,新學期,
Program Enrollment Tool Student,計劃註冊學生工具,
Student Batch Name,學生批名,
Program Fee,課程費用,
Question,題,
Single Correct Answer,單一正確答案,
Multiple Correct Answer,多個正確的答案,
Quiz Configuration,測驗配置,
Passing Score,合格分數,
Score out of 100,滿分100分,
Max Attempts,Max嘗試,
Enter 0 to waive limit,輸入0以放棄限制,
Grading Basis,評分基礎,
Latest Highest Score,最新最高分,
Latest Attempt,最新嘗試,
Quiz Activity,測驗活動,
Enrollment,註冊,
Pass,通過,
Quiz Question,測驗問題,
Quiz Result,測驗結果,
Selected Option,選擇的選項,
Correct,正確,
Wrong,錯誤,
Room Name,房間名稱,
Room Number,房間號,
Seating Capacity,座位數,
House Name,房子的名字,
Student Mobile Number,學生手機號碼,
Joining Date,入職日期,
A-,一個-,
Nationality,國籍,
Home Address,家庭地址,
Guardian Details,衛詳細,
Guardians,守護者,
Sibling Details,兄弟姐妹詳情,
Exit,出口,
Date of Leaving,離開日期,
Leaving Certificate Number,畢業證書號碼,
Reason For Leaving,離開的原因,
Student Admission,學生入學,
Admission Start Date,入學開始日期,
Admission End Date,錄取結束日期,
Publish on website,發布在網站上,
Eligibility and Details,資格和細節,
Student Admission Program,學生入學計劃,
Minimum Age,最低年齡,
Maximum Age,最大年齡,
Application Fee,報名費,
Naming Series (for Student Applicant),命名系列（面向學生申請人）,
LMS Only,僅限LMS,
Application Status,應用現狀,
Application Date,申請日期,
Student Attendance Tool,學生考勤工具,
Group Based On,分組依據,
Students HTML,學生HTML,
Group Based on,基於組,
Student Group Name,學生組名稱,
Set 0 for no limit,為不限制設為0,
Instructors,教師,
Student Group Creation Tool,學生組創建工具,
Leave blank if you make students groups per year,如果您每年製作學生團體，請留空,
Get Courses,獲取課程,
Separate course based Group for every Batch,為每個批次分離基於課程的組,
Leave unchecked if you don't want to consider batch while making course based groups. ,如果您不想在製作基於課程的組時考慮批量，請不要選中。,
Student Group Creation Tool Course,學生組創建工具課程,
Course Code,課程代碼,
Student Group Instructor,學生組教練,
Student Group Student,學生組學生,
Group Roll Number,組卷編號,
Student Guardian,學生家長,
Relation,關係,
Mother,母親,
Father,父親,
Student Language,學生語言,
Student Leave Application,學生請假申請,
Mark as Present,標記為現,
Student Log,學生登錄,
Academic,學術的,
Achievement,成就,
Student Report Generation Tool,學生報告生成工具,
Include All Assessment Group,包括所有評估小組,
Show Marks,顯示標記,
Add letterhead,添加信頭,
Total Parents Teacher Meeting,總計家長教師會議,
Attended by Parents,由父母出席,
Assessment Terms,評估條款,
Student Sibling,學生兄弟,
Studying in Same Institute,就讀於同一研究所,
NO,沒有,
Student Siblings,學生兄弟姐妹,
Topic Content,主題內容,
Amazon MWS Settings,亞馬遜MWS設置,
Enable Amazon,啟用亞馬遜,
MWS Credentials,MWS憑證,
Seller ID,賣家ID,
AWS Access Key ID,AWS訪問密鑰ID,
MWS Auth Token,MWS Auth Token,
Market Place ID,市場ID,
AE,自動曝光,
AU,非盟,
CA,CA,
DE,德,
IT,它,
UK,聯合王國,
US,我們,
Customer Type,客戶類型,
Market Place Account Group,市場賬戶組,
After Date,日期之後,
Amazon will synch data updated after this date,亞馬遜將同步在此日期之後更新的數據,
Sync Taxes and Charges,同步稅費,
Get financial breakup of Taxes and charges data by Amazon ,通過亞馬遜獲取稅收和收費數據的財務分解,
Sync Products,同步產品,
Always sync your products from Amazon MWS before synching the Orders details,同步訂單詳細信息之前，請始終從Amazon MWS同步您的產品,
Sync Orders,同步訂單,
Click this button to pull your Sales Order data from Amazon MWS.,單擊此按鈕可從亞馬遜MWS中提取銷售訂單數據。,
Enable Scheduled Sync,啟用預定同步,
Check this to enable a scheduled Daily synchronization routine via scheduler,選中此選項可通過調度程序啟用計劃的每日同步例程,
Max Retry Limit,最大重試限制,
Exotel Settings,Exotel設置,
Account SID,帳戶SID,
GoCardless Mandate,GoCardless任務,
Mandate,任務,
GoCardless Customer,GoCardless客戶,
GoCardless Settings,GoCardless設置,
Plaid Settings,格子設置,
Synchronize all accounts every hour,每小時同步所有帳戶,
Plaid Client ID,格子客戶端ID,
Plaid Secret,格子的秘密,
Plaid Environment,格子環境,
development,發展,
production,生產,
QuickBooks Migrator,QuickBooks遷移器,
Application Settings,應用程序設置,
Token Endpoint,令牌端點,
Scope,範圍,
Authorization Settings,授權設置,
Authorization Endpoint,授權端點,
Authorization URL,授權URL,
Company Settings,公司設置,
Default Shipping Account,默認運輸帳戶,
Default Warehouse,預設倉庫,
Default Cost Center,預設的成本中心,
Undeposited Funds Account,未存入資金賬戶,
Shopify Log,Shopify日誌,
Request Data,請求數據,
Shopify Settings,Shopify設置,
status html,狀態HTML,
Enable Shopify,啟用Shopify,
App Type,應用類型,
Last Sync Datetime,上次同步日期時間,
Shop URL,商店網址,
Webhooks Details,Webhooks詳細信息,
Webhooks,網絡掛接,
Customer Settings,客戶設置,
Default Customer,默認客戶,
Customer Group will set to selected group while syncing customers from Shopify,客戶組將在同步Shopify客戶的同時設置為選定的組,
For Company,對於公司,
Cash Account will used for Sales Invoice creation,現金帳戶將用於創建銷售發票,
Update Price from Shopify To ERPNext Price List,將Shopify更新到ERPNext價目表,
Default Warehouse to to create Sales Order and Delivery Note,默認倉庫到創建銷售訂單和交貨單,
Sales Order Series,銷售訂單系列,
Import Delivery Notes from Shopify on Shipment,在發貨時從Shopify導入交貨單,
Delivery Note Series,送貨單系列,
Import Sales Invoice from Shopify if Payment is marked,如果付款已標記，則從Shopify導入銷售發票,
Sales Invoice Series,銷售發票系列,
Shopify Tax Account,Shopify稅收帳戶,
Shopify Tax/Shipping Title,Shopify稅/運輸標題,
ERPNext Account,ERPNext帳戶,
Shopify Webhook Detail,Shopify Webhook詳細信息,
Tally Migration,理貨遷移,
Master Data,主要的數據,
"Data exported from Tally that consists of the Chart of Accounts, Customers, Suppliers, Addresses, Items and UOMs",從Tally導出的數據包括科目表，客戶，供應商，地址，物料和UOM,
Is Master Data Processed,主數據是否已處理,
Is Master Data Imported,是否導入主數據,
Tally Creditors Account,理貨債權人賬戶,
Creditors Account set in Tally,Tally中設置的債權人帳戶,
Tally Debtors Account,理貨債務人賬戶,
Debtors Account set in Tally,負債表中設置的債務人帳戶,
Tally Company,理貨公司,
Company Name as per Imported Tally Data,根據導入的理貨數據的公司名稱,
Default UOM,默認UOM,
UOM in case unspecified in imported data,如果未在導入的數據中指定UOM,
Your Company set in ERPNext,您的公司在ERPNext中設置,
Processed Files,已處理的文件,
Parties,派對,
UOMs,計量單位,
Vouchers,優惠券,
Round Off Account,四捨五入賬戶,
Day Book Data,日簿數據,
Day Book Data exported from Tally that consists of all historic transactions,從Tally導出的包含所有歷史交易的日簿數據,
Is Day Book Data Processed,是否處理了日記簿數據,
Is Day Book Data Imported,是否導入了日記簿數據,
Woocommerce Settings,Woocommerce設置,
Enable Sync,啟用同步,
Woocommerce Server URL,Woocommerce服務器URL,
API consumer key,API消費者密鑰,
API consumer secret,API消費者秘密,
Tax Account,稅收帳戶,
Freight and Forwarding Account,貨運和轉運帳戶,
Creation User,創作用戶,
"The user that will be used to create Customers, Items and Sales Orders. This user should have the relevant permissions.",將用於創建客戶，項目和銷售訂單的用戶。該用戶應具有相關權限。,
"This warehouse will be used to create Sales Orders. The fallback warehouse is ""Stores"".",該倉庫將用於創建銷售訂單。後備倉庫是“商店”。,
"The fallback series is ""SO-WOO-"".",後備系列是“SO-WOO-”。,
This company will be used to create Sales Orders.,該公司將用於創建銷售訂單。,
Delivery After (Days),交貨後（天）,
This is the default offset (days) for the Delivery Date in Sales Orders. The fallback offset is 7 days from the order placement date.,這是銷售訂單中交貨日期的默認偏移量（天）。後備偏移量是從下單日期算起的7天。,
"This is the default UOM used for items and Sales orders. The fallback UOM is ""Nos"".",這是用於商品和銷售訂單的默認UOM。後備UOM是“不”。,
Endpoints,端點,
Endpoint,端點,
Antibiotic Name,抗生素名稱,
Healthcare Administrator,醫療管理員,
Laboratory User,實驗室用戶,
Is Inpatient,住院嗎,
Default Duration (In Minutes),默認持續時間（以分鐘為單位）,
Body Part,身體的一部分,
Body Part Link,身體部位鏈接,
Procedure Prescription,程序處方,
Service Unit,服務單位,
Consumables,消耗品,
Consume Stock,消費股票,
Invoice Consumables Separately,發票耗材分開,
Consumption Invoiced,消費發票,
Consumable Total Amount,耗材總量,
Consumption Details,消費明細,
Nursing User,護理用戶,
Clinical Procedure Item,臨床流程項目,
Invoice Separately as Consumables,作為耗材單獨發票,
Transfer Qty,轉移數量,
Actual Qty (at source/target),實際的數量（於 來源/目標）,
Is Billable,是可計費的,
Allow Stock Consumption,允許庫存消耗,
Sample UOM,樣本單位,
Collection Details,收集細節,
Change In Item,項目變更,
Codification Table,編纂表,
Complaints,投訴,
Dosage Strength,劑量強度,
Strength,強度,
Drug Prescription,藥物處方,
Drug Name / Description,藥物名稱/說明,
Dosage,劑量,
Dosage by Time Interval,劑量按時間間隔,
Interval,間隔,
Interval UOM,間隔UOM,
Hour,小時,
Update Schedule,更新時間表,
Difficulty Level,難度級別,
Counts Target,計算目標,
Counts Completed,計數完成,
Assistance Level,協助等級,
Active Assist,主動輔助,
Exercise Name,練習名稱,
Body Parts,身體部位,
Exercise Instructions,練習說明,
Exercise Video,運動視頻,
Exercise Steps,鍛煉步驟,
Steps,腳步,
Steps Table,步驟表,
Exercise Type Step,運動類型步驟,
Max number of visit,最大訪問次數,
Visited yet,已訪問,
Reference Appointments,參考預約,
Fee Validity Reference,費用有效期參考,
Basic Details,基本細節,
Mobile,移動,
Phone (R),電話（R）,
Phone (Office),電話（辦公室）,
Employee and User Details,員工和用戶詳細信息,
Hospital,醫院,
Appointments,約會,
Practitioner Schedules,從業者時間表,
Charges,收費,
Out Patient Consulting Charge,門診諮詢費,
Default Currency,預設貨幣,
Healthcare Schedule Time Slot,醫療保健計劃時間槽,
Parent Service Unit,家長服務單位,
Service Unit Type,服務單位類型,
Allow Appointments,允許約會,
Allow Overlap,允許重疊,
Inpatient Occupancy,住院人數,
Occupancy Status,佔用狀況,
Occupied,佔據,
Item Details,產品詳細信息,
UOM Conversion in Hours,UOM按小時轉換,
Rate / UOM,費率/ UOM,
Change in Item,更改項目,
Out Patient Settings,出患者設置,
Patient Name By,病人姓名,
Link Customer to Patient,將客戶與患者聯繫起來,
"If checked, a customer will be created, mapped to Patient.\nPatient Invoices will be created against this Customer. You can also select existing Customer while creating Patient.",如果選中，將創建一個客戶，映射到患者。將針對該客戶創建病人發票。您也可以在創建患者時選擇現有客戶。,
Default Medical Code Standard,默認醫療代碼標準,
Collect Fee for Patient Registration,收取病人登記費,
Checking this will create new Patients with a Disabled status by default and will only be enabled after invoicing the Registration Fee.,選中此選項將默認創建具有“禁用”狀態的新患者，並且僅在開具註冊費發票後才能啟用。,
Registration Fee,註冊費用,
Automate Appointment Invoicing,自動預約發票,
Manage Appointment Invoice submit and cancel automatically for Patient Encounter,管理約會發票提交並自動取消患者遭遇,
Enable Free Follow-ups,啟用免費跟進,
Number of Patient Encounters in Valid Days,有效天數中的患者人數,
The number of free follow ups (Patient Encounters in valid days) allowed,允許的免費跟進次數（患者在有效期內遇到）,
Valid Number of Days,有效天數,
Time period (Valid number of days) for free consultations,免費諮詢的時間段（有效天數）,
Default Healthcare Service Items,默認醫療服務項目,
"You can configure default Items for billing consultation charges, procedure consumption items and inpatient visits",您可以為帳單諮詢費用，程序消耗項目和住院訪問配置默認項目,
Clinical Procedure Consumable Item,臨床程序消耗品,
Default Accounts,默認賬戶,
Default income accounts to be used if not set in Healthcare Practitioner to book Appointment charges.,如果未在醫生執業者中設置預約費用，則使用默認收入帳戶。,
Default receivable accounts to be used to book Appointment charges.,默認的應收帳款將用於預訂約會費用。,
Out Patient SMS Alerts,輸出病人短信,
Patient Registration,病人登記,
Registration Message,註冊信息,
Confirmation Message,確認訊息,
Avoid Confirmation,避免確認,
Do not confirm if appointment is created for the same day,不要確認是否在同一天創建約會,
Appointment Reminder,預約提醒,
Reminder Message,提醒訊息,
Remind Before,提醒之前,
Laboratory Settings,實驗室設置,
Create Lab Test(s) on Sales Invoice Submission,在銷售發票提交上創建實驗室測試,
Checking this will create Lab Test(s) specified in the Sales Invoice on submission.,選中此復選框將創建提交時在銷售發票中指定的實驗室測試。,
Create Sample Collection document for Lab Test,創建樣本收集文檔以進行實驗室測試,
Checking this will create a Sample Collection document  every time you create a Lab Test,每次創建實驗室測試時，選中此項都會創建一個“樣品收集”文檔,
Employee name and designation in print,員工姓名和印刷品名稱,
Check this if you want the Name and Designation of the Employee associated with the User who submits the document to be printed in the Lab Test Report.,如果您希望與提交文檔的用戶相關聯的員工姓名和職務名稱被打印在實驗室測試報告中。,
Do not print or email Lab Tests without Approval,未經批准請勿打印或通過電子郵件發送實驗室測試,
Checking this will restrict printing and emailing of Lab Test documents unless they have the status as Approved.,除非狀態為“已批准”，否則選中此選項將限制打印和通過電子郵件發送實驗室測試文檔。,
Custom Signature in Print,自定義簽名打印,
Laboratory SMS Alerts,實驗室短信,
Result Printed Message,結果打印消息,
Result Emailed Message,結果通過電子郵件發送,
Check In,報到,
Check Out,查看,
A Positive,積極的,
A Negative,一個負面的,
AB Positive,AB積極,
AB Negative,AB陰性,
B Positive,B積極,
B Negative,B負面,
O Positive,O積極,
O Negative,O負面,
Admission Scheduled,入學時間表,
Discharge Scheduled,出院預定,
Discharged,出院,
Admission Schedule Date,入學時間表日期,
Admitted Datetime,承認日期時間,
Expected Discharge,預期解僱,
Lab Prescription,實驗室處方,
Lab Test Name,實驗室測試名稱,
Test Created,測試創建,
Sample ID,樣品編號,
Lab Technician,實驗室技術員,
Report Preference,報告偏好,
Test Name,測試名稱,
Test Template,測試模板,
Test Group,測試組,
Custom Result,自定義結果,
LabTest Approver,LabTest審批者,
Add Test,添加測試,
Normal Range,普通範圍,
Result Format,結果格式,
Single,單,
Compound,複合,
Descriptive,描述性,
Grouped,分組,
No Result,沒有結果,
This value is updated in the Default Sales Price List.,該值在默認銷售價格表中更新。,
Lab Routine,實驗室常規,
Result Value,結果值,
Require Result Value,需要結果值,
Normal Test Template,正常測試模板,
Patient Demographics,患者人口統計學,
Middle Name (optional),中間名（可選）,
Inpatient Status,住院狀況,
"If ""Link Customer to Patient"" is checked in Healthcare Settings and an existing Customer is not selected then, a Customer will be created for this Patient for recording transactions in Accounts module.",如果在“醫療保健設置”中選中了“將客戶鏈接到患者”，並且未選擇現有客戶，則將為此患者創建一個客戶以在“帳戶”模塊中記錄交易。,
Personal and Social History,個人和社會史,
Marital Status,婚姻狀況,
Divorced,離婚,
Widow,寡婦,
Patient Relation,患者關係,
"Allergies, Medical and Surgical History",過敏，醫療和外科史,
Allergies,過敏,
Medication,藥物治療,
Medical History,醫學史,
Surgical History,手術史,
Risk Factors,風險因素,
Occupational Hazards and Environmental Factors,職業危害與環境因素,
Other Risk Factors,其他風險因素,
Patient Details,患者細節,
Additional information regarding the patient,有關患者的其他信息,
Patient Age,患者年齡,
Get Prescribed Clinical Procedures,獲取規定的臨床程序,
Therapy,治療,
Get Prescribed Therapies,獲取處方療法,
Appointment Datetime,約會日期時間,
Duration (In Minutes),持續時間（以分鐘為單位）,
Reference Sales Invoice,參考銷售發票,
More Info,更多訊息,
Referring Practitioner,轉介醫生,
Reminded,提醒,
Assessment Template,評估模板,
Assessment Datetime,評估日期時間,
Assessment Description,評估說明,
Assessment Sheet,評估表,
Total Score Obtained,獲得總分,
Scale Max,最大規模,
Patient Assessment Detail,患者評估詳情,
Assessment Parameter,評估參數,
Patient Assessment Parameter,患者評估參數,
Patient Assessment Sheet,患者評估表,
Patient Assessment Template,患者評估模板,
Assessment Parameters,評估參數,
Parameters,參數,
Assessment Scale,評估量表,
Scale Minimum,最小規模,
Scale Maximum,最大規模,
Encounter Date,相遇日期,
Encounter Time,遇到時間,
Symptoms,病徵,
In print,已出版,
Medical Coding,醫學編碼,
Therapies,療法,
Review Details,評論細節,
Patient Encounter Diagnosis,病人遭遇診斷,
Patient Encounter Symptom,病人遭遇症狀,
Attach Medical Record,附加病歷,
Reference DocType,參考文檔類型,
Spouse,伴侶,
Schedule Details,時間表詳情,
Schedule Name,計劃名稱,
Time Slots,時隙,
Practitioner Service Unit Schedule,從業者服務單位時間表,
Procedure Name,程序名稱,
Appointment Booked,預約預約,
Procedure Created,程序已創建,
Collected By,收集者,
Particulars,細節,
Result Component,結果組件,
Therapy Plan Details,治療計劃詳情,
Total Sessions,總會議,
Total Sessions Completed,總會議完成,
Therapy Plan Detail,治療計劃詳情,
No of Sessions,會話數,
Sessions Completed,會議完成,
Tele,遠程,
Exercises,練習題,
Therapy For,療法,
Add Exercises,添加練習,
Body Temperature,體溫,
Presence of a fever (temp &gt; 38.5 °C/101.3 °F or sustained temp &gt; 38 °C/100.4 °F),發燒（溫度&gt; 38.5°C / 101.3°F或持續溫度&gt; 38°C / 100.4°F）,
Heart Rate / Pulse,心率/脈搏,
Adults' pulse rate is anywhere between 50 and 80 beats per minute.,成年人的脈率在每分鐘50到80次之間。,
Respiratory rate,呼吸頻率,
Normal reference range for an adult is 16–20 breaths/minute (RCP 2012),成人的正常參考範圍是16-20次呼吸/分鐘（RCP 2012）,
Coated,塗,
Very Coated,非常塗層,
Furry,毛茸茸的,
Cuts,削減,
Bloated,脹,
Fluid,流體,
Constipated,大便乾燥,
Very Hyper,非常超,
One Sided,單面,
Blood Pressure (systolic),血壓（收縮期）,
Blood Pressure (diastolic),血壓（舒張）,
Blood Pressure,血壓,
"Normal resting blood pressure in an adult is approximately 120 mmHg systolic, and 80 mmHg diastolic, abbreviated ""120/80 mmHg""",成年人的正常靜息血壓約為收縮期120mmHg，舒張壓80mmHg，縮寫為“120 / 80mmHg”,
Nutrition Values,營養價值觀,
Height (In Meter),高度（米）,
Weight (In Kilogram),體重（公斤）,
BMI,體重指數,
Hotel Room,旅館房間,
Hotel Room Type,酒店房間類型,
Hotel Manager,酒店經理,
Hotel Room Amenity,酒店客房舒適,
Billable,計費,
Hotel Room Package,酒店客房套餐,
Amenities,設施,
Hotel Room Pricing,酒店房間價格,
Hotel Room Pricing Item,酒店房間定價項目,
Hotel Room Pricing Package,酒店房間價格套餐,
Hotel Room Reservation,酒店房間預訂,
Late Checkin,延遲入住,
Booked,預訂,
Hotel Reservation User,酒店預訂用戶,
Hotel Room Reservation Item,酒店房間預訂項目,
Hotel Settings,酒店設置,
Default Taxes and Charges,默認稅費,
Default Invoice Naming Series,默認發票命名系列,
Additional Salary,額外的薪水,
HR,人力資源,
Salary Component,薪金部分,
Overwrite Salary Structure Amount,覆蓋薪資結構金額,
Deduct Full Tax on Selected Payroll Date,在選定的工資日期扣除全額稅,
Payroll Date,工資日期,
Date on which this component is applied,應用此組件的日期,
Salary Slip,工資單,
Salary Component Type,薪資組件類型,
HR User,HR用戶,
Appointment Letter,預約信,
Job Applicant,求職者,
Applicant Name,申請人名稱,
Appointment Date,約會日期,
Appointment Letter Template,預約信模板,
Body,身體,
Closing Notes,結束語,
Appointment Letter content,預約信內容,
Appraisal,評價,
Appraisal Template,評估模板,
For Employee Name,對於員工姓名,
Goals,目標,
Total Score (Out of 5),總分（滿分5分）,
"Any other remarks, noteworthy effort that should go in the records.",任何其他言論，值得一提的努力，應該在記錄中。,
Appraisal Goal,考核目標,
Key Responsibility Area,關鍵責任區,
Weightage (%),權重（％）,
Score Earned,得分,
Appraisal Template Title,評估模板標題,
Appraisal Template Goal,考核目標模板,
Key Performance Area,關鍵績效區,
Leave Application,休假申請,
Attendance Date,出勤日期,
Attendance Request,出席請求,
Late Entry,遲入,
Explanation,說明,
Compensatory Leave Request,補償請假,
Leave Allocation,排假,
Worked On Holiday,在度假工作,
Work From Date,從日期開始工作,
Work End Date,工作結束日期,
Email Sent To,電子郵件發送至,
Select Users,選擇用戶,
Send Emails At,發送電子郵件在,
Reminder,提醒,
Daily Work Summary Group User,日常工作摘要組用戶,
email,電子郵件,
Parent Department,家長部門,
Leave Block List,休假區塊清單,
Days for which Holidays are blocked for this department.,天的假期被封鎖這個部門。,
Leave Approver,休假審批人,
Expense Approver,費用審批,
Department Approver,部門批准人,
Approver,審批人,
Required Skills,必備技能,
Skills,技能專長,
Designation Skill,指定技能,
Driver,司機,
Suspended,暫停,
Transporter,運輸車,
Applicable for external driver,適用於外部驅動器,
Cellphone Number,手機號碼,
License Details,許可證詳情,
License Number,許可證號,
Issuing Date,發行日期,
Driving License Categories,駕駛執照類別,
Driving License Category,駕駛執照類別,
Fleet Manager,車隊經理,
Driver licence class,駕駛執照等級,
HR-EMP-,人力資源管理計劃,
Employment Type,就業類型,
Emergency Contact,緊急聯絡人,
Emergency Contact Name,緊急聯絡名字,
Emergency Phone,緊急電話,
ERPNext User,ERPNext用戶,
"System User (login) ID. If set, it will become default for all HR forms.",系統用戶（登錄）的標識。如果設定，這將成為的所有人力資源的預設形式。,
Create User Permission,創建用戶權限,
This will restrict user access to other employee records,這將限制用戶訪問其他員工記錄,
Joining Details,加入詳情,
Offer Date,到職日期,
Confirmation Date,確認日期,
Contract End Date,合同結束日期,
Notice (days),通知（天）,
Date Of Retirement,退休日,
Department and Grade,部門和年級,
Reports to,隸屬於,
Attendance and Leave Details,出勤和離職詳情,
Leave Policy,離開政策,
Attendance Device ID (Biometric/RF tag ID),考勤設備ID（生物識別/ RF標籤ID）,
Applicable Holiday List,適用假期表,
Default Shift,默認Shift,
Salary Details,薪資明細,
Salary Mode,薪酬模式,
Bank A/C No.,銀行A/C No.,
Health Insurance,健康保險,
Health Insurance Provider,健康保險提供者,
Health Insurance No,健康保險No,
Prefered Email,首選電子郵件,
Personal Email,個人電子郵件,
Permanent Address Is,永久地址是,
Rented,已租借,
Owned,擁有的,
Prefered Contact Email,首選聯繫郵箱,
Company Email,企業郵箱,
Provide Email Address registered in company,提供公司註冊郵箱地址,
Current Address Is,當前地址是,
Current Address,當前地址,
Personal Bio,個人生物,
Bio / Cover Letter,生物/求職信,
Short biography for website and other publications.,網站和其他出版物的短的傳記。,
Passport Number,護照號碼,
Date of Issue,發行日期,
Place of Issue,簽發地點,
Widowed,寡,
"Here you can maintain family details like name and occupation of parent, spouse and children",在這裡，您可以維護家庭的詳細訊息，如父母，配偶和子女的姓名及職業,
Health Details,健康細節,
"Here you can maintain height, weight, allergies, medical concerns etc",在這裡，你可以保持身高，體重，過敏，醫療問題等,
Educational Qualification,學歷,
Previous Work Experience,以前的工作經驗,
External Work History,外部工作經歷,
History In Company,公司歷史,
Internal Work History,內部工作經歷,
Resignation Letter Date,辭退信日期,
Relieving Date,解除日期,
Reason for Leaving,離職原因,
Leave Encashed?,離開兌現？,
Encashment Date,兌現日期,
New Workplace,新工作空間,
Returned Amount,退貨金額,
Claimed,聲稱,
Advance Account,預付帳戶,
Employee Attendance Tool,員工考勤工具,
Unmarked Attendance,無標記考勤,
Employees HTML,員工HTML,
Marked Attendance,明顯考勤,
Marked Attendance HTML,顯著的考勤HTML,
Employee Benefit Application,員工福利申請,
Max Benefits (Yearly),最大利益（每年）,
Remaining Benefits (Yearly),剩餘福利（每年）,
Payroll Period,工資期,
Benefits Applied,應用的好處,
Dispensed Amount (Pro-rated),分配金額（按比例分配）,
Employee Benefit Application Detail,員工福利申請明細,
Earning Component,收入組件,
Pay Against Benefit Claim,支付利益索賠,
Max Benefit Amount,最大福利金額,
Employee Benefit Claim,員工福利索賠,
Claim Date,索賠日期,
Benefit Type and Amount,福利類型和金額,
Claim Benefit For,索賠利益,
Max Amount Eligible,最高金額合格,
Expense Proof,費用證明,
Employee Boarding Activity,員工寄宿活動,
Activity Name,活動名稱,
Task Weight,任務重,
Required for Employee Creation,員工創建需要,
Applicable in the case of Employee Onboarding,適用於員工入職的情況,
Employee Checkin,員工簽到,
Log Type,日誌類型,
OUT,出,
Location / Device ID,位置/設備ID,
Skip Auto Attendance,跳過自動出勤,
Shift Start,轉移開始,
Shift End,轉移結束,
Shift Actual Start,切換實際開始,
Shift Actual End,轉移實際結束,
Employee Education,員工教育,
School/University,學校/大學,
Graduate,畢業生,
Under Graduate,根據研究生,
Year of Passing,路過的一年,
Class / Percentage,類／百分比,
Major/Optional Subjects,大/選修課,
Employee External Work History,員工對外工作歷史,
Total Experience,總經驗,
Default Leave Policy,默認離開政策,
Default Salary Structure,默認工資結構,
Employee Group Table,員工組表,
ERPNext User ID,ERPNext用戶ID,
Employee Health Insurance,員工健康保險,
Health Insurance Name,健康保險名稱,
Employee Incentive,員工激勵,
Incentive Amount,激勵金額,
Employee Internal Work History,員工內部工作經歷,
Employee Onboarding,員工入職,
Notify users by email,通過電子郵件通知用戶,
Employee Onboarding Template,員工入職模板,
Activities,活動,
Employee Onboarding Activity,員工入職活動,
Employee Other Income,員工其他收入,
Employee Promotion,員工晉升,
Promotion Date,促銷日期,
Employee Promotion Details,員工促銷詳情,
Employee Promotion Detail,員工促銷細節,
Employee Property History,員工財產歷史,
Employee Separation,員工分離,
Employee Separation Template,員工分離模板,
Exit Interview Summary,退出面試摘要,
Employee Skill,員工技能,
Proficiency,熟練程度,
Evaluation Date,評估日期,
Employee Skill Map,員工技能圖,
Employee Skills,員工技能,
Trainings,培訓,
Employee Tax Exemption Category,員工免稅類別,
Max Exemption Amount,最高免稅額,
Employee Tax Exemption Declaration,僱員免稅聲明,
Declarations,聲明,
Total Declared Amount,申報總金額,
Total Exemption Amount,免稅總額,
Employee Tax Exemption Declaration Category,員工免稅申報類別,
Exemption Sub Category,豁免子類別,
Exemption Category,豁免類別,
Maximum Exempted Amount,最高豁免金額,
Declared Amount,申報金額,
Employee Tax Exemption Proof Submission,員工免稅證明提交,
Tax Exemption Proofs,免稅證明,
Total Actual Amount,實際總金額,
Employee Tax Exemption Proof Submission Detail,員工免稅證明提交細節,
Maximum Exemption Amount,最高免稅額,
Type of Proof,證明類型,
Actual Amount,實際金額,
Employee Tax Exemption Sub Category,員工免稅子類別,
Tax Exemption Category,免稅類別,
Employee Training,員工培訓,
Training Date,培訓日期,
Employee Transfer,員工轉移,
Transfer Date,轉移日期,
Employee Transfer Details,員工轉移詳情,
Employee Transfer Detail,員工轉移詳情,
Re-allocate Leaves,重新分配葉子,
Create New Employee Id,創建新的員工ID,
New Employee ID,新員工ID,
Employee Transfer Property,員工轉移財產,
Expense Taxes and Charges,費用稅和費用,
Total Sanctioned Amount,總被制裁金額,
Total Advance Amount,總預付金額,
Total Claimed Amount,總索賠額,
Total Amount Reimbursed,報銷金額合計,
Vehicle Log,車輛登錄,
Employees Email Id,員工的電子郵件ID,
More Details,更多細節,
Expense Claim Account,報銷賬戶,
Expense Claim Advance,費用索賠預付款,
Unclaimed amount,無人認領的金額,
Expense Claim Detail,報銷詳情,
Expense Date,犧牲日期,
Expense Claim Type,費用報銷型,
Holiday List Name,假日列表名稱,
Total Holidays,總假期,
Add Weekly Holidays,添加每週假期,
Weekly Off,每週關閉,
Add to Holidays,加入假期,
Clear Table,清除表,
HR Settings,人力資源設置,
Employee Settings,員工設置,
Retirement Age,退休年齡,
Enter retirement age in years,在年內進入退休年齡,
Expense Approver Mandatory In Expense Claim,費用審批人必須在費用索賠中,
Payroll Settings,薪資設置,
Leave,離開,
Max working hours against Timesheet,最大工作時間針對時間表,
Include holidays in Total no. of Working Days,包括節假日的總數。工作日,
"If checked, Total no. of Working Days will include holidays, and this will reduce the value of Salary Per Day",如果選中，則總數。工作日將包括節假日，這將縮短每天的工資的價值,
"If checked, hides and disables Rounded Total field in Salary Slips",如果選中，則隱藏並禁用“工資單”中的“舍入總計”字段,
The fraction of daily wages to be paid for half-day attendance,半天出勤應支付的每日工資的比例,
Email Salary Slip to Employee,電子郵件工資單給員工,
Emails salary slip to employee based on preferred email selected in Employee,電子郵件工資單員工根據員工選擇首選的電子郵件,
Encrypt Salary Slips in Emails,加密電子郵件中的工資單,
"The salary slip emailed to the employee will be password protected, the password will be generated based on the password policy.",通過電子郵件發送給員工的工資單將受密碼保護，密碼將根據密碼策略生成。,
Password Policy,密碼政策,
<b>Example:</b> SAL-{first_name}-{date_of_birth.year} <br>This will generate a password like SAL-Jane-1972,<b>示例：</b> SAL- {first_name}  -  {date_of_birth.year} <br>這將生成一個像SAL-Jane-1972的密碼,
Leave Settings,保留設置,
Leave Approval Notification Template,留下批准通知模板,
Leave Status Notification Template,離開狀態通知模板,
Role Allowed to Create Backdated Leave Application,允許創建回退休假申請的角色,
Leave Approver Mandatory In Leave Application,在離職申請中允許Approver為強制性,
Show Leaves Of All Department Members In Calendar,在日曆中顯示所有部門成員的葉子,
Auto Leave Encashment,自動離開兌現,
Hiring Settings,招聘設置,
Check Vacancies On Job Offer Creation,檢查創造就業機會的職位空缺,
Identification Document Type,識別文件類型,
Effective from,從生效,
Allow Tax Exemption,允許免稅,
"If enabled, Tax Exemption Declaration will be considered for income tax calculation.",如果啟用，免稅聲明將被考慮用於所得稅計算。,
Standard Tax Exemption Amount,標準免稅額,
Taxable Salary Slabs,應稅薪金板塊,
Taxes and Charges on Income Tax,所得稅稅費,
Other Taxes and Charges,其他稅費,
Income Tax Slab Other Charges,所得稅表其他費用,
Min Taxable Income,最低應稅收入,
Max Taxable Income,最高應稅收入,
Applicant for a Job,申請人作業,
Accepted,接受的,
Job Opening,開放職位,
Cover Letter,求職信,
Resume Attachment,簡歷附,
Job Applicant Source,求職者來源,
Applicant Email Address,申請人電子郵件地址,
Awaiting Response,正在等待回應,
Job Offer Terms,招聘條款,
Select Terms and Conditions,選擇條款和條件,
Printing Details,印刷詳情,
Job Offer Term,招聘條件,
Offer Term,要約期限,
Value / Description,值/說明,
Description of a Job Opening,一個空缺職位的說明,
Job Title,職位,
Staffing Plan,人員配備計劃,
Planned number of Positions,計劃的職位數量,
"Job profile, qualifications required etc.",所需的工作概況，學歷等。,
New Leaves Allocated,新的排假,
Add unused leaves from previous allocations,從以前的分配添加未使用的休假,
Unused leaves,未使用的葉子,
Total Leaves Allocated,已安排的休假總計,
Total Leaves Encashed,總葉子被掩飾,
Leave Period,休假期間,
Carry Forwarded Leaves,進行轉發葉,
Apply / Approve Leaves,申請/審批葉,
Leave Balance Before Application,離開平衡應用前,
Total Leave Days,總休假天數,
Leave Approver Name,離開批准人姓名,
Follow via Email,透過電子郵件追蹤,
Block Holidays on important days.,重要的日子中封鎖假期。,
Leave Block List Name,休假區塊清單名稱,
Applies to Company,適用於公司,
"If not checked, the list will have to be added to each Department where it has to be applied.",如果未選取，則該列表將被加到每個應被應用到的部門。,
Block Days,封鎖天數,
Stop users from making Leave Applications on following days.,停止用戶在下面日期提出休假申請。,
Leave Block List Dates,休假區塊清單日期表,
Allow Users,允許用戶,
Allow the following users to approve Leave Applications for block days.,允許以下用戶批准許可申請的區塊天。,
Leave Block List Allowed,准許的休假區塊清單,
Leave Block List Allow,休假區塊清單准許,
Allow User,允許用戶,
Leave Block List Date,休假區塊清單日期表,
Block Date,封鎖日期,
Leave Control Panel,離開控制面板,
Select Employees,選擇僱員,
Employment Type (optional),就業類型（可選）,
Branch (optional),分支（可選）,
Department (optional),部門（可選）,
Designation (optional),指定（可選）,
Employee Grade (optional),員工等級（可選）,
Employee (optional),員工（可選）,
Allocate Leaves,分配葉子,
Carry Forward,發揚,
Please select Carry Forward if you also want to include previous fiscal year's balance leaves to this fiscal year,請選擇結轉，如果你還需要包括上一會計年度的資產負債葉本財年,
New Leaves Allocated (In Days),新的排假（天）,
Leave Balance,保持平衡,
Encashable days,可以忍受的日子,
Encashment Amount,填充量,
Leave Ledger Entry,離開分類帳條目,
Transaction Name,交易名稱,
Is Carry Forward,是弘揚,
Is Expired,已過期,
Is Leave Without Pay,是無薪休假,
Holiday List for Optional Leave,可選假期的假期列表,
Leave Allocations,離開分配,
Leave Policy Details,退出政策詳情,
Leave Policy Detail,退出政策細節,
Annual Allocation,年度分配,
Leave Type Name,休假類型名稱,
Max Leaves Allowed,允許最大葉子,
Applicable After (Working Days),適用於（工作日）,
Maximum Continuous Days Applicable,最大持續天數適用,
Is Optional Leave,是可選的休假,
Allow Negative Balance,允許負平衡,
Include holidays within leaves as leaves,休假中包含節日做休假,
Is Compensatory,是有補償的,
Maximum Carry Forwarded Leaves,最大攜帶轉發葉,
Expire Carry Forwarded Leaves (Days),過期攜帶轉發葉子（天）,
Calculated in days,以天計算,
Encashment,兌現,
Allow Encashment,允許封裝,
Encashment Threshold Days,封存閾值天數,
Earned Leave,獲得休假,
Is Earned Leave,獲得休假,
Earned Leave Frequency,獲得休假頻率,
Rounding,四捨五入,
Payroll Employee Detail,薪資員工詳細信息,
Payroll Frequency,工資頻率,
Bimonthly,雙月刊,
Employees,僱員,
Number Of Employees,在職員工人數,
Employee Details,員工詳細信息,
Validate Attendance,驗證出席,
Salary Slip Based on Timesheet,基於時間表工資單,
Select Payroll Period,選擇工資期,
Deduct Tax For Unclaimed Employee Benefits,扣除未領取僱員福利的稅,
Deduct Tax For Unsubmitted Tax Exemption Proof,扣除未提交免稅證明的稅額,
Select Payment Account to make Bank Entry,選擇付款賬戶，使銀行進入,
Salary Slips Created,工資單創建,
Salary Slips Submitted,提交工資單,
Payroll Periods,工資期間,
Payroll Period Date,工資期間日期,
Purpose of Travel,旅行目的,
Retention Bonus,保留獎金,
Bonus Payment Date,獎金支付日期,
Bonus Amount,獎金金額,
Abbr,縮寫,
Depends on Payment Days,取決於付款日,
Is Tax Applicable,是否適用稅務？,
Variable Based On Taxable Salary,基於應納稅工資的變量,
Exempted from Income Tax,免除所得稅,
Round to the Nearest Integer,舍入到最近的整數,
Statistical Component,統計組成部分,
"If selected, the value specified or calculated in this component will not contribute to the earnings or deductions. However, it's value can be referenced by other components that can be added or deducted. ",如果選擇此項，則在此組件中指定或計算的值不會對收入或扣除貢獻。但是，它的值可以被添加或扣除的其他組件引用。,
Do Not Include in Total,不包括在總計中,
Flexible Benefits,靈活的好處,
Is Flexible Benefit,是靈活的好處,
Max Benefit Amount (Yearly),最大福利金額（每年）,
Only Tax Impact (Cannot Claim But Part of Taxable Income),只有稅收影響（不能索取但應稅收入的一部分）,
Create Separate Payment Entry Against Benefit Claim,針對福利申請創建單獨的付款條目,
Condition and Formula,條件和公式,
Amount based on formula,量基於式,
Formula,式,
Salary Detail,薪酬詳細,
Component,零件,
Do not include in total,不包括在內,
Default Amount,預設數量,
Additional Amount,額外金額,
Tax on flexible benefit,對靈活福利徵稅,
Tax on additional salary,額外工資稅,
Salary Structure,薪酬結構,
Salary Slip Timesheet,工資單時間表,
Total Working Hours,總的工作時間,
Hour Rate,小時率,
Bank Account No.,銀行賬號,
Earning & Deduction,收益與扣除,
Earnings,收益,
Deductions,扣除,
Loan repayment,償還借款,
Employee Loan,員工貸款,
Total Principal Amount,本金總額,
Total Interest Amount,利息總額,
Total Loan Repayment,總貸款還款,
net pay info,淨工資信息,
Gross Pay - Total Deduction - Loan Repayment,工資總額 - 扣除總額 - 貸款還款,
Total in words,總計大寫,
Net Pay (in words) will be visible once you save the Salary Slip.,薪資單一被儲存，淨付款就會被顯示出來。,
Salary Component for timesheet based payroll.,薪酬部分基於時間表工資。,
Leave Encashment Amount Per Day,每天離開沖泡量,
Max Benefits (Amount),最大收益（金額）,
Salary breakup based on Earning and Deduction.,工資分手基於盈利和演繹。,
Total Earning,總盈利,
Salary Structure Assignment,薪酬結構分配,
Shift Assignment,班次分配,
Shift Type,班次類型,
Shift Request,移位請求,
Enable Auto Attendance,啟用自動出勤,
Mark attendance based on 'Employee Checkin' for Employees assigned to this shift.,根據分配給此班次的員工的“員工簽到”標記出勤率。,
Auto Attendance Settings,自動出勤設置,
Determine Check-in and Check-out,確定登記入住和退房,
Alternating entries as IN and OUT during the same shift,在同一班次期間交替輸入IN和OUT,
Strictly based on Log Type in Employee Checkin,嚴格基於員工簽入中的日誌類型,
Working Hours Calculation Based On,基於的工時計算,
First Check-in and Last Check-out,首次入住和最後退房,
Every Valid Check-in and Check-out,每次有效的入住和退房,
Begin check-in before shift start time (in minutes),在班次開始時間（以分鐘為單位）開始辦理登機手續,
The time before the shift start time during which Employee Check-in is considered for attendance.,在考慮員工入住的班次開始時間之前的時間。,
Allow check-out after shift end time (in minutes),允許在班次結束後退房（以分鐘為單位）,
Time after the end of shift during which check-out is considered for attendance.,輪班結束後的時間，在此期間考慮退房。,
Working Hours Threshold for Half Day,半天的工作時間門檻,
Working hours below which Half Day is marked. (Zero to disable),工作時間低於標記的半天。 （零禁用）,
Working Hours Threshold for Absent,缺勤的工作時間門檻,
Working hours below which Absent is marked. (Zero to disable),缺席的工作時間標記為缺席。 （零禁用）,
Process Attendance After,過程出勤,
Attendance will be marked automatically only after this date.,只有在此日期之後才會自動標記出勤率。,
Last Sync of Checkin,上次簽到同步,
Last Known Successful Sync of Employee Checkin. Reset this only if you are sure that all Logs are synced from all the locations. Please don't modify this if you are unsure.,員工簽到的最後一次成功同步。僅當您確定從所有位置同步所有日誌時才重置此項。如果您不確定，請不要修改此項。,
Grace Period Settings For Auto Attendance,自動出勤的寬限期設置,
Enable Entry Grace Period,啟用條目寬限期,
Late Entry Grace Period,延遲入境寬限期,
The time after the shift start time when check-in is considered as late (in minutes).,在辦理登機手續的班次開始時間之後的時間被視為遲到（以分鐘為單位）。,
Enable Exit Grace Period,啟用退出寬限期,
Early Exit Grace Period,提前退出寬限期,
The time before the shift end time when check-out is considered as early (in minutes).,退房結束時間之前的時間被視為提前（以分鐘為單位）。,
Skill Name,技能名稱,
Staffing Plan Details,人員配置計劃詳情,
Staffing Plan Detail,人員配置計劃詳情,
Total Estimated Budget,預計總預算,
Vacancies,職位空缺,
Estimated Cost Per Position,估計的每位成本,
Total Estimated Cost,預計總成本,
Current Count,當前計數,
Current Openings,當前空缺,
Number Of Positions,職位數,
Taxable Salary Slab,應納稅薪金平台,
From Amount,從金額,
To Amount,金額,
Percent Deduction,扣除百分比,
Training Program,培訓計劃,
Event Status,事件狀態,
Has Certificate,有證書,
Seminar,研討會,
Theory,理論,
Workshop,作坊,
Conference,會議,
Exam,考試,
Internet,互聯網,
Self-Study,自習,
Advance,提前,
Trainer Name,培訓師姓名,
Trainer Email,教練電子郵件,
Attendees,與會者,
Employee Emails,員工電子郵件,
Training Event Employee,培訓活動的員工,
Invited,邀請,
Feedback Submitted,提交反饋,
Optional,可選的,
Training Result Employee,訓練結果員工,
Travel Itinerary,旅遊行程,
Travel From,旅行從,
Travel To,前往,
Mode of Travel,旅行模式,
Flight,飛行,
Train,培養,
Taxi,計程車,
Rented Car,租車,
Vegetarian,素,
Non-Vegetarian,非素食主義者,
Gluten Free,不含麩質,
Non Diary,非日記,
Travel Advance Required,需要旅行預付款,
Departure Datetime,離開日期時間,
Arrival Datetime,到達日期時間,
Preferred Area for Lodging,住宿的首選地區,
Check-out Date,離開日期,
Travel Request,旅行要求,
Travel Type,旅行類型,
Domestic,國內,
International,國際,
Travel Funding,旅行資助,
Require Full Funding,需要全額資助,
Fully Sponsored,完全贊助,
"Partially Sponsored, Require Partial Funding",部分贊助，需要部分資金,
Copy of Invitation/Announcement,邀請/公告的副本,
"Details of Sponsor (Name, Location)",贊助商詳情（名稱，地點）,
Identification Document Number,身份證明文件號碼,
Any other details,任何其他細節,
Costing Details,成本計算詳情,
Event Details,活動詳情,
Name of Organizer,主辦單位名稱,
Address of Organizer,主辦單位地址,
Travel Request Costing,旅行請求成本計算,
Expense Type,費用類型,
Sponsored Amount,贊助金額,
Funded Amount,資助金額,
Upload Attendance,上傳考勤,
Attendance From Date,出勤日期,
Attendance To Date,出席會議日期,
Get Template,獲取模板,
Import Attendance,進口出席,
Upload HTML,上傳HTML,
Vehicle,車輛,
Odometer Value (Last),里程表值（最後）,
Acquisition Date,採集日期,
Chassis No,底盤無,
Vehicle Value,汽車衡,
Insurance Details,保險詳情,
Insurance Company,保險公司,
Policy No,保單編號,
Additional Details,額外細節,
Fuel Type,燃料類型,
Diesel,柴油機,
Natural Gas,天然氣,
Electric,電動,
Fuel UOM,燃油計量單位,
Last Carbon Check,最後檢查炭,
Wheels,車輪,
Doors,門,
HR-VLOG-.YYYY.-,HR-VLOG-.YYYY.-,
Odometer Reading,里程表讀數,
Current Odometer value ,當前里程表值,
last Odometer Value ,上一個里程表值,
Refuelling Details,加油詳情,
Invoice Ref,發票編號,
Service Details,服務細節,
Service Detail,服務細節,
Vehicle Service,汽車服務,
Service Item,服務項目,
Brake Oil,剎車油,
Brake Pad,剎車片,
Clutch Plate,離合器壓盤,
Engine Oil,機油,
Oil Change,換油,
Inspection,檢查,
Hub Tracked Item,Hub跟踪物品,
Hub Node,樞紐節點,
Image List,圖像列表,
Item Manager,項目經理,
Hub User,中心用戶,
Hub Password,集線器密碼,
Hub Users,Hub用戶,
Marketplace Settings,市場設置,
Disable Marketplace,禁用市場,
Marketplace URL (to hide and update label),市場URL（隱藏和更新標籤）,
Registered,註冊,
Sync in Progress,同步進行中,
Hub Seller Name,集線器賣家名稱,
Custom Data,自定義數據,
Member,會員,
Partially Disbursed,部分支付,
Loan Closure Requested,請求關閉貸款,
Repay From Salary,從工資償還,
Loan Details,貸款詳情,
Loan Type,貸款類型,
Loan Amount,貸款額度,
Is Secured Loan,有抵押貸款,
Rate of Interest (%) / Year,利率（％）/年,
Disbursement Date,付款日期,
Disbursed Amount,支付額,
Is Term Loan,是定期貸款,
Repayment Method,還款方式,
Repay Fixed Amount per Period,償還每期固定金額,
Repay Over Number of Periods,償還期的超過數,
Repayment Period in Months,在月還款期,
Monthly Repayment Amount,每月還款額,
Repayment Start Date,還款開始日期,
Loan Security Details,貸款安全明細,
Maximum Loan Value,最高貸款額,
Account Info,帳戶信息,
Loan Account,貸款帳戶,
Interest Income Account,利息收入賬戶,
Penalty Income Account,罰款收入帳戶,
Repayment Schedule,還款計劃,
Total Payable Amount,合計應付額,
Total Principal Paid,本金合計,
Total Interest Payable,合計應付利息,
Total Amount Paid,總金額支付,
Loan Manager,貸款經理,
Loan Info,貸款信息,
Proposed Pledges,擬議認捐,
Maximum Loan Amount,最高貸款額度,
Repayment Info,還款信息,
Total Payable Interest,合計應付利息,
Against Loan ,反對貸款,
Loan Interest Accrual,貸款利息計提,
Amounts,金額,
Payable Principal Amount,應付本金,
Paid Interest Amount,已付利息金額,
Process Loan Interest Accrual,流程貸款利息計提,
Repayment Schedule Name,還款時間表名稱,
Loan Closure,貸款結清,
Payment Details,付款詳情,
Interest Payable,應付利息,
Amount Paid,已支付的款項,
Repayment Details,還款明細,
Loan Repayment Detail,貸款還款明細,
Loan Security Name,貸款證券名稱,
Unit Of Measure,測量單位,
Loan Security Code,貸款安全守則,
Loan Security Type,貸款擔保類型,
Haircut %,理髮％,
Loan  Details,貸款明細,
Unpledged,無抵押,
Securities,有價證券,
Total Security Value,總安全價值,
Loan Security Shortfall,貸款安全缺口,
Loan ,貸款,
Shortfall Time,短缺時間,
America/New_York,美國/紐約,
Shortfall Amount,不足額,
Security Value ,安全價值,
Process Loan Security Shortfall,流程貸款安全漏洞,
Loan To Value Ratio,貸款價值比,
Unpledge Time,未承諾時間,
Loan Name,貸款名稱,
Rate of Interest (%) Yearly,利率（％）每年,
Penalty Interest Rate (%) Per Day,每日罰息（％）,
Penalty Interest Rate is levied on the pending interest amount on a daily basis in case of delayed repayment ,如果延遲還款，則每日對未付利息徵收罰款利率,
Grace Period in Days,天寬限期,
No. of days from due date until which penalty won't be charged in case of delay in loan repayment,從到期日起算的天數，直到延遲償還貸款不收取罰款,
Pledge,保證,
Post Haircut Amount,剪髮數量,
Process Type,工藝類型,
Update Time,更新時間,
Proposed Pledge,建議的質押,
Total Payment,總付款,
Balance Loan Amount,平衡貸款額,
Is Accrued,應計,
Salary Slip Loan,工資單貸款,
Loan Repayment Entry,貸款還款錄入,
Sanctioned Loan Amount,認可貸款額,
Sanctioned Amount Limit,批准的金額限制,
Unpledge,不承諾,
Haircut,理髮,
Generate Schedule,生成時間表,
Schedules,時間表,
Maintenance Schedule Detail,維護計劃細節,
Scheduled Date,預定日期,
Actual Date,實際日期,
Maintenance Schedule Item,維護計劃項目,
Random,隨機,
No of Visits,沒有訪問量的,
Maintenance Date,維修日期,
Maintenance Time,維護時間,
Completion Status,完成狀態,
Fully Completed,全面完成,
Unscheduled,計劃外,
Breakdown,展開,
Purposes,目的,
Customer Feedback,客戶反饋,
Maintenance Visit Purpose,維護訪問目的,
Work Done,工作完成,
Against Document No,對文件編號,
Against Document Detail No,對文件詳細編號,
Order Type,訂單類型,
Blanket Order Item,一攬子訂單項目,
Ordered Quantity,訂購數量,
Item to be manufactured or repacked,產品被製造或重新包裝,
Quantity of item obtained after manufacturing / repacking from given quantities of raw materials,製造/從原材料數量給予重新包裝後獲得的項目數量,
Set rate of sub-assembly item based on BOM,基於BOM設置子組合項目的速率,
Allow Alternative Item,允許替代項目,
Item UOM,項目計量單位,
Conversion Rate,兌換率,
Rate Of Materials Based On,材料成本基於,
With Operations,加入作業,
Manage cost of operations,管理作業成本,
Transfer Material Against,轉移材料,
Materials,用料,
Quality Inspection Required,需要質量檢查,
Quality Inspection Template,質量檢驗模板,
Scrap,廢料,
Scrap Items,廢物品,
Operating Cost,營業成本,
Scrap Material Cost,廢料成本,
Operating Cost (Company Currency),營業成本（公司貨幣）,
Raw Material Cost (Company Currency),原材料成本（公司貨幣）,
Scrap Material Cost(Company Currency),廢料成本（公司貨幣）,
Total Cost,總成本,
Total Cost (Company Currency),總成本（公司貨幣）,
Materials Required (Exploded),所需材料（分解）,
Show in Website,在網站上顯示,
Item Image (if not slideshow),產品圖片（如果不是幻燈片）,
Thumbnail,縮略圖,
Website Specifications,網站規格,
Show Items,顯示項目,
Show Operations,顯示操作,
Website Description,網站簡介,
BOM Explosion Item,BOM展開項目,
Qty Consumed Per Unit,數量消耗每單位,
Include Item In Manufacturing,包括製造業中的項目,
BOM Item,BOM項目,
Item operation,項目操作,
Rate & Amount,價格和金額,
Basic Rate (Company Currency),基礎匯率（公司貨幣）,
Scrap %,廢鋼％,
Original Item,原始項目,
BOM Operation,BOM的操作,
Operation Time ,運作時間,
In minutes,在幾分鐘內,
Base Hour Rate(Company Currency),基數小時率（公司貨幣）,
Operating Cost(Company Currency),營業成本（公司貨幣）,
BOM Scrap Item,BOM項目廢料,
Basic Amount (Company Currency),基本金額（公司幣種）,
BOM Update Tool,BOM更新工具,
"Replace a particular BOM in all other BOMs where it is used. It will replace the old BOM link, update cost and regenerate ""BOM Explosion Item"" table as per new BOM.\nIt also updates latest price in all the BOMs.",替換使用所有其他BOM的特定BOM。它將替換舊的BOM鏈接，更新成本，並按照新的BOM重新生成“BOM爆炸項目”表。它還更新了所有BOM中的最新價格。,
Replace BOM,更換BOM,
Current BOM,當前BOM表,
The BOM which will be replaced,這將被替換的物料清單,
The new BOM after replacement,更換後的新物料清單,
Replace,更換,
Update latest price in all BOMs,更新所有BOM的最新價格,
BOM Website Item,BOM網站項目,
BOM Website Operation,BOM網站運營,
Operation Time,操作時間,
Timing Detail,時間細節,
Time Logs,時間日誌,
Total Time in Mins,分鐘總時間,
Operation ID,操作編號,
Transferred Qty,轉讓數量,
Job Started,工作開始了,
Started Time,開始時間,
Current Time,當前時間,
Job Card Item,工作卡項目,
Job Card Time Log,工作卡時間日誌,
Time In Mins,分鐘時間,
Completed Qty,完成數量,
Manufacturing Settings,製造設定,
Allow Multiple Material Consumption,允許多種材料消耗,
Backflush Raw Materials Based On,倒沖原物料基於,
Material Transferred for Manufacture,轉移至製造的物料,
Capacity Planning,產能規劃,
Disable Capacity Planning,禁用容量規劃,
Allow Overtime,允許加班,
Allow Production on Holidays,允許假日生產,
Capacity Planning For (Days),產能規劃的範圍（天）,
Default Warehouses for Production,默認生產倉庫,
Default Work In Progress Warehouse,預設在製品倉庫,
Default Finished Goods Warehouse,預設成品倉庫,
Default Scrap Warehouse,默認廢料倉庫,
Overproduction Percentage For Sales Order,銷售訂單超額生產百分比,
Overproduction Percentage For Work Order,工作訂單的生產率過高百分比,
Other Settings,其他設置,
Update BOM Cost Automatically,自動更新BOM成本,
Material Request Plan Item,材料申請計劃項目,
Material Request Type,材料需求類型,
Material Issue,發料,
Customer Provided,客戶提供,
Minimum Order Quantity,最小起訂量,
Default Workstation,預設工作站,
Production Plan,生產計劃,
Get Items From,取得項目來源,
Get Sales Orders,獲取銷售訂單,
Material Request Detail,材料請求詳情,
Get Material Request,獲取材質要求,
Material Requests,材料要求,
Get Items For Work Order,獲取工作訂單的物品,
Material Request Planning,物料請求計劃,
Include Non Stock Items,包括非庫存項目,
Include Subcontracted Items,包括轉包物料,
Ignore Existing Projected Quantity,忽略現有的預計數量,
"To know more about projected quantity, <a href=""https://erpnext.com/docs/user/manual/en/stock/projected-quantity"" style=""text-decoration: underline;"" target=""_blank"">click here</a>.","要了解有關預計數量的更多信息， <a href=""https://erpnext.com/docs/user/manual/en/stock/projected-quantity"" style=""text-decoration: underline;"" target=""_blank"">請單擊此處</a> 。",
Download Required Materials,下載所需資料,
Get Raw Materials For Production,獲取生產原料,
Total Planned Qty,總計劃數量,
Total Produced Qty,總生產數量,
Material Requested,要求的材料,
Production Plan Item,生產計劃項目,
Make Work Order for Sub Assembly Items,製作子裝配件的工作訂單,
"If enabled, system will create the work order for the exploded items against which BOM is available.",如果啟用，系統將為BOM可用的爆炸項目創建工作訂單。,
Planned Start Date,計劃開始日期,
Quantity and Description,數量和描述,
material_request_item,material_request_item,
Product Bundle Item,產品包項目,
Production Plan Material Request,生產計劃申請材料,
Production Plan Sales Order,生產計劃銷售訂單,
Sales Order Date,銷售訂單日期,
Routing Name,路由名稱,
Item To Manufacture,產品製造,
Material Transferred for Manufacturing,物料轉倉用於製造,
Manufactured Qty,生產數量,
Use Multi-Level BOM,採用多級物料清單,
Plan material for sub-assemblies,計劃材料為子組件,
Skip Material Transfer to WIP Warehouse,跳過物料轉移到WIP倉庫,
Check if material transfer entry is not required,檢查是否不需要材料轉移條目,
Backflush Raw Materials From Work-in-Progress Warehouse,從在製品庫中反沖原料,
Update Consumed Material Cost In Project,更新項目中的消耗材料成本,
Warehouses,倉庫,
This is a location where raw materials are available.,這是可獲取原材料的地方。,
Work-in-Progress Warehouse,在製品倉庫,
This is a location where operations are executed.,這是執行操作的位置。,
This is a location where final product stored.,這是存放最終產品的位置。,
Scrap Warehouse,廢料倉庫,
This is a location where scraped materials are stored.,這是存放刮擦材料的位置。,
Required Items,所需物品,
Actual Start Date,實際開始日期,
Planned End Date,計劃的結束日期,
Actual End Date,實際結束日期,
Operation Cost,運營成本,
Planned Operating Cost,計劃運營成本,
Actual Operating Cost,實際運行成本,
Additional Operating Cost,額外的運營成本,
Total Operating Cost,總營運成本,
Manufacture against Material Request,對製造材料要求,
Work Order Item,工作訂單項目,
Available Qty at Source Warehouse,源倉庫可用數量,
Available Qty at WIP Warehouse,在WIP倉庫可用的數量,
Work Order Operation,工作訂單操作,
Operation Description,操作說明,
Operation completed for how many finished goods?,操作完成多少成品？,
Work in Progress,在製品,
Estimated Time and Cost,估計時間和成本,
Planned Start Time,計劃開始時間,
Planned End Time,計劃結束時間,
in Minutes,在幾分鐘內,
Actual Time and Cost,實際時間和成本,
Actual Start Time,實際開始時間,
Actual End Time,實際結束時間,
Updated via 'Time Log',經由“時間日誌”更新,
Actual Operation Time,實際操作時間,
in Minutes\nUpdated via 'Time Log',在分\n經由“時間日誌”更新,
(Hour Rate / 60) * Actual Operation Time,（工時率/ 60）*實際操作時間,
Workstation Name,工作站名稱,
Production Capacity,生產能力,
Operating Costs,運營成本,
Electricity Cost,電力成本,
per hour,每小時,
Consumable Cost,消耗成本,
Wages,工資,
Wages per hour,時薪,
Net Hour Rate,淨小時率,
Workstation Working Hour,工作站工作時間,
Certification Application,認證申請,
Name of Applicant,申請人名稱,
Certification Status,認證狀態,
Yet to appear,尚未出現,
Certified,認證,
Not Certified,未認證,
Certified Consultant,認證顧問,
Name of Consultant,顧問的名字,
Certification Validity,認證有效性,
Discuss ID,討論ID,
Non Profit Manager,非營利經理,
Chapter Head,章主管,
Meetup Embed HTML,Meetup嵌入HTML,
chapters/chapter_name\nleave blank automatically set after saving chapter.,保存章節後自動設置章節/章節名稱。,
Chapter Members,章節成員,
Members,會員,
Chapter Member,章會員,
Website URL,網站網址,
Leave Reason,離開原因,
Donor Name,捐助者名稱,
Donor Type,捐助者類型,
Withdrawn,撤消,
Grant Application Details ,授予申請細節,
Grant Description,授予說明,
Requested Amount,請求金額,
Has any past Grant Record,有過去的贈款記錄嗎？,
Show on Website,在網站上顯示,
Assessment  Mark (Out of 10),評估標記（滿分10分）,
Assessment  Manager,評估經理,
Email Notification Sent,電子郵件通知已發送,
Membership Expiry Date,會員到期日,
Razorpay Details,Razorpay詳細信息,
Subscription ID,訂閱編號,
Customer ID,顧客ID,
Subscription Activated,訂閱已激活,
Subscription Start ,訂閱開始,
Subscription End,訂閱結束,
Non Profit Member,非盈利會員,
Membership Status,成員身份,
Member Since,成員自,
Payment ID,付款編號,
Membership Settings,會員設置,
Enable RazorPay For Memberships,為會員啟用RazorPay,
RazorPay Settings,RazorPay設置,
Billing Cycle,結算週期,
Billing Frequency,帳單頻率,
"The number of billing cycles for which the customer should be charged. For example, if a customer is buying a 1-year membership that should be billed on a monthly basis, this value should be 12.",應向客戶收費的計費周期數。例如，如果客戶購買的1年會員資格應按月計費，則此值應為12。,
Razorpay Plan ID,Razorpay計劃編號,
Volunteer Name,志願者姓名,
Volunteer Type,志願者類型,
Weekends,週末,
Availability Timeslot,可用時間段,
Evening,晚間,
Anytime,任何時候,
Volunteer Skills,志願者技能,
Volunteer Skill,志願者技能,
Homepage,主頁,
Hero Section Based On,基於英雄的英雄部分,
Homepage Section,主頁部分,
Hero Section,英雄區,
Tag Line,標語,
Company Tagline for website homepage,公司標語的網站主頁,
Company Description for website homepage,公司介紹了網站的首頁,
Homepage Slideshow,主頁幻燈片,
"URL for ""All Products""",網址“所有產品”,
Products to be shown on website homepage,在網站首頁中顯示的產品,
Homepage Featured Product,首頁推薦產品,
route,路線,
Section Based On,基於的部分,
Section Cards,分區卡,
Number of Columns,列數,
Number of columns for this section. 3 cards will be shown per row if you select 3 columns.,此部分的列數。如果選擇3列，每行將顯示3張卡片。,
Section HTML,HTML部分,
Use this field to render any custom HTML in the section.,使用此字段在該部分中呈現任何自定義HTML。,
Section Order,部分順序,
"Order in which sections should appear. 0 is first, 1 is second and so on.",應該出現哪些部分的順序。 0是第一個，1是第二個，依此類推。,
Homepage Section Card,主頁卡片,
Products Settings,產品設置,
Home Page is Products,首頁是產品頁,
"If checked, the Home page will be the default Item Group for the website",如果選中，主頁將是網站的默認項目組,
Show Availability Status,顯示可用性狀態,
Product Page,產品頁面,
Products per Page,每頁產品,
Enable Field Filters,啟用字段過濾器,
Item Fields,項目字段,
Enable Attribute Filters,啟用屬性過濾器,
Attributes,屬性,
Hide Variants,隱藏變體,
Website Attribute,網站屬性,
Attribute,屬性,
Website Filter Field,網站過濾字段,
Activity Cost,項目成本,
Billing Rate,結算利率,
title,標題,
Projects User,項目用戶,
Default Costing Rate,默認成本核算率,
Default Billing Rate,默認計費率,
Dependent Task,相關任務,
Project Type,專案類型,
% Complete Method,完成方法百分比,
Task Completion,任務完成,
Task Progress,任務進度,
% Completed,％已完成,
From Template,來自模板,
Project will be accessible on the website to these users,項目將在網站向這些用戶上訪問,
Copied From,複製自,
Start and End Dates,開始和結束日期,
Actual Time in Hours (via Timesheet),實際時間（以小時為單位）,
Costing and Billing,成本核算和計費,
Total Costing Amount (via Timesheet),總成本金額（通過時間表）,
Total Expense Claim (via Expense Claim),總費用報銷（通過費用報銷）,
Total Purchase Cost (via Purchase Invoice),總購買成本（通過採購發票）,
Total Sales Amount (via Sales Order),總銷售額（通過銷售訂單）,
Total Billable Amount (via Timesheet),總計費用金額（通過時間表）,
Total Billed Amount (via Sales Invoice),總開票金額（通過銷售發票）,
Total Consumed Material Cost (via Stock Entry),總消耗材料成本（通過股票輸入）,
Gross Margin,毛利率,
Monitor Progress,監視進度,
Collect Progress,收集進度,
Frequency To Collect Progress,頻率收集進展,
Twice Daily,每天兩次,
First Email,第一郵件,
Second Email,第二封郵件,
Time to send,發送時間,
Day to Send,發送日,
Message will be sent to the users to get their status on the Project,消息將發送給用戶以獲取其在項目中的狀態,
Projects Manager,項目經理,
Project Template,項目模板,
Project Template Task,項目模板任務,
Begin On (Days),開始（天）,
Duration (Days),持續時間（天）,
Project Update,項目更新,
Project User,項目用戶,
Projects Settings,項目設置,
Ignore Workstation Time Overlap,忽略工作站時間重疊,
Ignore User Time Overlap,忽略用戶時間重疊,
Ignore Employee Time Overlap,忽略員工時間重疊,
Parent Task,父任務,
Timeline,時間線,
Expected Time (in hours),預期時間（以小時計）,
% Progress,％進展,
Is Milestone,是里程碑,
Task Description,任務描述,
Dependencies,依賴,
Dependent Tasks,相關任務,
Depends on Tasks,取決於任務,
Actual Start Date (via Timesheet),實際開始日期（通過時間表）,
Actual Time in Hours (via Timesheet),實際時間（小時）,
Actual End Date (via Timesheet),實際結束日期（通過時間表）,
Total Expense Claim (via Expense Claim),總費用報銷（通過費用報銷）,
Review Date,評論日期,
Closing Date,截止日期,
Task Depends On,任務取決於,
Task Type,任務類型,
Employee Detail,員工詳細信息,
Billing Details,結算明細,
Total Billable Hours,總計費時間,
Total Billed Hours,帳單總時間,
Total Costing Amount,總成本計算金額,
Total Billable Amount,總結算金額,
Total Billed Amount,總開單金額,
% Amount Billed,（％）金額已開立帳單,
Hrs,小時,
Costing Amount,成本核算金額,
Corrective/Preventive,糾正/預防,
Corrective,糾正的,
Preventive,預防,
Resolution,決議,
Resolutions,決議,
Quality Action Resolution,質量行動決議,
Quality Feedback Parameter,質量反饋參數,
Quality Feedback Template Parameter,質量反饋模板參數,
Quality Goal,質量目標,
Monitoring Frequency,監測頻率,
Objectives,目標,
Quality Goal Objective,質量目標,
Agenda,議程,
Minutes,分鐘,
Quality Meeting Agenda,質量會議議程,
Quality Meeting Minutes,質量會議紀要,
Minute,分鐘,
Parent Procedure,家長程序,
Processes,工藝流程,
Quality Procedure Process,質量程序流程,
Process Description,進度解析,
Link existing Quality Procedure.,鏈接現有的質量程序。,
Quality Review Objective,質量審查目標,
DATEV Settings,DATEV設置,
Regional,區域性,
Consultant ID,顧問編號,
GST HSN Code,GST HSN代碼,
HSN Code,HSN代碼,
GST Settings,GST設置,
GST Summary,消費稅總結,
GSTIN Email Sent On,發送GSTIN電子郵件,
GST Accounts,GST賬戶,
Set Invoice Value for B2C. B2CL and B2CS calculated based on this invoice value.,設置B2C的發票值。 B2CL和B2CS根據此發票值計算。,
GSTR 3B Report,GSTR 3B報告,
March,遊行,
May,可能,
JSON Output,JSON輸出,
Invoices with no Place Of Supply,沒有供應地的發票,
Import Supplier Invoice,進口供應商發票,
Invoice Series,發票系列,
Upload XML Invoices,上載XML發票,
Zip File,壓縮文件,
Import Invoices,進口發票,
Click on Import Invoices button once the zip file has been attached to the document. Any errors related to processing will be shown in the Error Log.,將zip文件附加到文檔後，單擊“導入發票”按鈕。與處理相關的任何錯誤將顯示在錯誤日誌中。,
Lower Deduction Certificate,降低扣除證明,
Certificate Details,證書詳細信息,
194H,194小時,
Certificate No,證書號碼,
Deductee Details,受扣人詳細信息,
PAN No,PAN號,
Validity Details,有效性詳細信息,
Rate Of TDS As Per Certificate,根據證書的TDS率,
Certificate Limit,證書限制,
Invoice Series Prefix,發票系列前綴,
Active Menu,活動菜單,
Restaurant Menu,餐廳菜單,
Price List (Auto created),價目表（自動創建）,
Restaurant Manager,餐廳經理,
Restaurant Menu Item,餐廳菜單項,
Restaurant Order Entry,餐廳訂單錄入,
Restaurant Table,餐廳表,
Click Enter To Add,點擊輸入要添加,
Last Sales Invoice,上次銷售發票,
Current Order,當前訂單,
Restaurant Order Entry Item,餐廳訂單錄入項目,
Served,曾任職,
Restaurant Reservation,餐廳預訂,
Waitlisted,輪候,
No Show,沒有出現,
No of People,沒有人,
Reservation Time,預訂時間,
Reservation End Time,預訂結束時間,
No of Seats,座位數,
Minimum Seating,最低座位,
"Keep Track of Sales Campaigns. Keep track of Leads, Quotations, Sales Order etc from Campaigns to gauge Return on Investment. ",追蹤銷售計劃。追踪訊息，報價，銷售訂單等，從競賽來衡量投資報酬。,
Campaign Schedules,活動時間表,
Buyer of Goods and Services.,買家商品和服務。,
CUST-.YYYY.-,客戶-.YYYY.-,
Default Company Bank Account,默認公司銀行帳戶,
From Lead,從鉛,
Account Manager,客戶經理,
Allow Sales Invoice Creation Without Sales Order,允許創建無銷售訂單的銷售發票,
Allow Sales Invoice Creation Without Delivery Note,允許在沒有交貨單的情況下創建銷售發票,
Default Price List,預設價格表,
Primary Address and Contact Detail,主要地址和聯繫人詳情,
"Select, to make the customer searchable with these fields",選擇，使客戶可以使用這些字段進行搜索,
Customer Primary Contact,客戶主要聯繫人,
"Reselect, if the chosen contact is edited after save",重新選擇，如果所選聯繫人在保存後被編輯,
Customer Primary Address,客戶主要地址,
"Reselect, if the chosen address is edited after save",重新選擇，如果所選地址在保存後被編輯,
Mention if non-standard receivable account,提到如果不規範應收賬款,
Credit Limit and Payment Terms,信用額度和付款條款,
Additional information regarding the customer.,對於客戶的其他訊息。,
Sales Partner and Commission,銷售合作夥伴及佣金,
Commission Rate,佣金比率,
Sales Team Details,銷售團隊詳細,
Customer POS id,客戶POS ID,
Customer Credit Limit,客戶信用額度,
Bypass Credit Limit Check at Sales Order,在銷售訂單旁邊繞過信貸限額檢查,
Industry Type,行業類型,
Installation Date,安裝日期,
Installation Time,安裝時間,
Installation Note Item,安裝注意項,
Installed Qty,安裝數量,
Lead Source,主導來源,
Period Start Date,期間開始日期,
Period End Date,期末結束日期,
Cashier,出納員,
Difference,區別,
Linked Invoices,鏈接的發票,
POS Closing Voucher Details,POS關閉憑證詳細信息,
Collected Amount,收集金額,
Expected Amount,預期金額,
POS Closing Voucher Invoices,POS關閉憑證發票,
Quantity of Items,項目數量,
"Aggregate group of **Items** into another **Item**. This is useful if you are bundling a certain **Items** into a package and you maintain stock of the packed **Items** and not the aggregate **Item**. \n\nThe package **Item** will have ""Is Stock Item"" as ""No"" and ""Is Sales Item"" as ""Yes"".\n\nFor Example: If you are selling Laptops and Backpacks separately and have a special price if the customer buys both, then the Laptop + Backpack will be a new Product Bundle Item.\n\nNote: BOM = Bill of Materials",聚合組** **項目到另一個項目** **的。如果你是捆綁了一定**項目你保持股票的包裝**項目的**，而不是聚集**項這是一個有用的**到一個包和**。包** **項目將有“是股票項目”為“否”和“是銷售項目”為“是”。例如：如果你是銷售筆記本電腦和背包分開，並有一個特殊的價格，如果客戶購買兩個，那麼筆記本電腦+背包將是一個新的產品包項目。注：物料BOM =比爾,
Parent Item,父項目,
List items that form the package.,形成包列表項。,
Quotation To,報價到,
Rate at which customer's currency is converted to company's base currency,客戶貨幣被換算成公司基礎貨幣的匯率,
Rate at which Price list currency is converted to company's base currency,價目表貨幣被換算成公司基礎貨幣的匯率,
Additional Discount and Coupon Code,附加折扣和優惠券代碼,
Referral Sales Partner,推薦銷售合作夥伴,
In Words will be visible once you save the Quotation.,報價一被儲存，就會顯示出來。,
Term Details,長期詳情,
Quotation Item,產品報價,
Against Doctype,針對文檔類型,
Against Docname,對Docname,
Additional Notes,補充說明,
Skip Delivery Note,跳過交貨單,
In Words will be visible once you save the Sales Order.,銷售訂單一被儲存，就會顯示出來。,
Track this Sales Order against any Project,跟踪對任何項目這個銷售訂單,
Billing and Delivery Status,結算和交貨狀態,
Not Delivered,沒送到,
Not Applicable,不適用,
%  Delivered,％交付,
% of materials delivered against this Sales Order,針對這張銷售訂單的已交貨物料的百分比(%),
% of materials billed against this Sales Order,針對這張銷售訂單的已開立帳單的百分比(%),
Not Billed,不發單,
Fully Billed,完全開票,
Partly Billed,天色帳單,
Ensure Delivery Based on Produced Serial No,確保基於生產的序列號的交貨,
Supplier delivers to Customer,供應商提供給客戶,
Delivery Warehouse,交貨倉庫,
Planned Quantity,計劃數量,
For Production,對於生產,
Work Order Qty,工作訂單數量,
Produced Quantity,生產的產品數量,
Used for Production Plan,用於生產計劃,
Sales Partner Type,銷售夥伴類型,
Contact No.,聯絡電話,
Contribution (%),貢獻（％）,
Contribution to Net Total,貢獻淨合計,
Selling Settings,銷售設置,
Settings for Selling Module,設置銷售模塊,
Customer Naming By,客戶命名由,
Campaign Naming By,活動命名由,
Default Customer Group,預設客戶群組,
Default Territory,預設地域,
Close Opportunity After Days,關閉機會後日,
Default Quotation Validity Days,默認報價有效天數,
Sales Update Frequency,銷售更新頻率,
Each Transaction,每筆交易,
Send To,發送到,
All Contact,所有聯絡,
All Customer Contact,所有的客戶聯絡,
All Supplier Contact,所有供應商聯絡,
All Sales Partner Contact,所有的銷售合作夥伴聯絡,
All Lead (Open),所有鉛（開放）,
All Employee (Active),所有員工（活動）,
All Sales Person,所有的銷售人員,
Create Receiver List,創建接收器列表,
Receiver List,收受方列表,
Messages greater than 160 characters will be split into multiple messages,大於160個字元的訊息將被分割成多個訊息送出,
Total Characters,總字元數,
Total Message(s),訊息總和（s ）,
Authorization Control,授權控制,
Authorization Rule,授權規則,
Customerwise Discount,Customerwise折扣,
Itemwise Discount,Itemwise折扣,
Customer or Item,客戶或項目,
Customer / Item Name,客戶／品項名稱,
Authorized Value,授權值,
Applicable To (Role),適用於（角色）,
Applicable To (Employee),適用於（員工）,
Applicable To (User),適用於（用戶）,
Applicable To (Designation),適用於（指定）,
Approving Role (above authorized value),批准角色（上述授權值）,
Approving User  (above authorized value),批准的用戶（上述授權值）,
Brand Defaults,品牌默認值,
Legal Entity / Subsidiary with a separate Chart of Accounts belonging to the Organization.,法人/子公司與帳戶的獨立走勢屬於該組織。,
Change Abbreviation,更改縮寫,
Default Values,默認值,
Default Holiday List,預設假日表列,
Default Selling Terms,默認銷售條款,
Default Buying Terms,默認購買條款,
Create Chart Of Accounts Based On,創建圖表的帳戶根據,
Standard Template,標準模板,
Existing Company,現有公司,
Chart Of Accounts Template,圖表帳戶模板,
Existing Company ,現有的公司,
Date of Establishment,成立時間,
Sales Settings,銷售設置,
Monthly Sales Target,每月銷售目標,
Sales Monthly History,銷售月曆,
Transactions Annual History,交易年曆,
Total Monthly Sales,每月銷售總額,
Default Cash Account,預設的現金帳戶,
Default Receivable Account,預設應收帳款,
Round Off Cost Center,四捨五入成本中心,
Discount Allowed Account,折扣允許的帳戶,
Discount Received Account,折扣收到的帳戶,
Exchange Gain / Loss Account,兌換收益/損失帳戶,
Unrealized Exchange Gain/Loss Account,未實現的匯兌收益/損失賬戶,
Allow Account Creation Against Child Company,允許針對兒童公司創建帳戶,
Default Payable Account,預設應付賬款,
Default Employee Advance Account,默認員工高級帳戶,
Default Cost of Goods Sold Account,銷貨帳戶的預設成本,
Default Income Account,預設之收入帳戶,
Default Deferred Revenue Account,默認遞延收入賬戶,
Default Deferred Expense Account,默認遞延費用帳戶,
Default Payroll Payable Account,默認情況下，應付職工薪酬帳戶,
Default Expense Claim Payable Account,默認費用索賠應付帳款,
Stock Settings,庫存設定,
Enable Perpetual Inventory,啟用永久庫存,
Default Inventory Account,默認庫存帳戶,
Stock Adjustment Account,庫存調整帳戶,
Fixed Asset Depreciation Settings,固定資產折舊設置,
Series for Asset Depreciation Entry (Journal Entry),資產折舊條目系列（期刊條目）,
Gain/Loss Account on Asset Disposal,在資產處置收益/損失帳戶,
Asset Depreciation Cost Center,資產折舊成本中心,
Budget Detail,預算案詳情,
Exception Budget Approver Role,例外預算審批人角色,
Company Info,公司資訊,
For reference only.,僅供參考。,
Company Logo,公司標誌,
Date of Incorporation,註冊成立日期,
Date of Commencement,開始日期,
Phone No,電話號碼,
Company Description,公司介紹,
Registration Details,註冊細節,
Company registration numbers for your reference. Tax numbers etc.,公司註冊號碼，供大家參考。稅務號碼等,
Delete Company Transactions,刪除公司事務,
Currency Exchange,外幣兌換,
Specify Exchange Rate to convert one currency into another,指定的匯率將一種貨幣兌換成另一種,
From Currency,從貨幣,
To Currency,到貨幣,
For Buying,為了購買,
Customer Group Name,客戶群組名稱,
Parent Customer Group,母客戶群組,
Only leaf nodes are allowed in transaction,只有葉節點中允許交易,
Mention if non-standard receivable account applicable,何況，如果不規範應收賬款適用,
Credit Limits,信用額度,
Email Digest,電子郵件摘要,
Send regular summary reports via Email.,使用電子郵件發送定期匯總報告。,
Email Digest Settings,電子郵件摘要設定,
How frequently?,多久？,
Next email will be sent on:,接下來的電子郵件將被發送：,
Note: Email will not be sent to disabled users,注意：電子郵件將不會被發送到被禁用的用戶,
Profit & Loss,利潤損失,
New Income,新收入,
New Expenses,新的費用,
Annual Expenses,年度費用,
Bank Balance,銀行結餘,
Bank Credit Balance,銀行信貸餘額,
Receivables,應收賬款,
Payables,應付賬款,
Sales Orders to Bill,比爾的銷售訂單,
Purchase Orders to Bill,向比爾購買訂單,
New Sales Orders,新的銷售訂單,
New Purchase Orders,新的採購訂單,
Sales Orders to Deliver,要交付的銷售訂單,
Purchase Orders to Receive,要收貨的採購訂單,
New Purchase Invoice,新購買發票,
New Quotations,新報價,
Open Quotations,打開報價單,
Open Issues,開放式問題,
Open Projects,公開項目,
Purchase Orders Items Overdue,採購訂單項目逾期,
Upcoming Calendar Events,即將進行的日曆活動,
Open To Do,開放做,
Add Quote,添加報價,
Global Defaults,全域預設值,
Default Company,預設公司,
Current Fiscal Year,當前會計年度,
Default Distance Unit,默認距離單位,
Hide Currency Symbol,隱藏貨幣符號,
Do not show any symbol like $ etc next to currencies.,不要顯示如$等任何貨幣符號。,
"If disable, 'Rounded Total' field will not be visible in any transaction",如果禁用，“圓角總計”字段將不可見的任何交易,
Disable In Words,禁用詞,
"If disable, 'In Words' field will not be visible in any transaction",如果禁用“，在詞”字段不會在任何交易可見,
Item Classification,項目分類,
General Settings,一般設定,
Item Group Name,項目群組名稱,
Parent Item Group,父項目群組,
Item Group Defaults,項目組默認值,
Item Tax,產品稅,
Check this if you want to show in website,勾選本項以顯示在網頁上,
Show this slideshow at the top of the page,這顯示在幻燈片頁面頂部,
HTML / Banner that will show on the top of product list.,HTML／橫幅，將顯示在產品列表的頂部。,
Set prefix for numbering series on your transactions,為你的交易編號序列設置的前綴,
Setup Series,設置系列,
Select Transaction,選擇交易,
Help HTML,HTML幫助,
Series List for this Transaction,本交易系列表,
User must always select,用戶必須始終選擇,
Check this if you want to force the user to select a series before saving. There will be no default if you check this.,如果要強制用戶在儲存之前選擇了一系列，則勾選此項。如果您勾選此項，則將沒有預設值。,
Change the starting / current sequence number of an existing series.,更改現有系列的開始/當前的序列號。,
Current Value,當前值,
This is the number of the last created transaction with this prefix,這就是以這個前綴的最後一個創建的事務數,
Update Series Number,更新序列號,
Quotation Lost Reason,報價遺失原因,
A third party distributor / dealer / commission agent / affiliate / reseller who sells the companies products for a commission.,第三方分銷商/經銷商/代理商/分支機構/分銷商誰銷售公司產品的佣金。,
Sales Partner Name,銷售合作夥伴名稱,
Partner Type,合作夥伴類型,
Address & Contacts,地址及聯絡方式,
Address Desc,地址說明,
Contact Desc,聯絡倒序,
Sales Partner Target,銷售合作夥伴目標,
Targets,目標,
Show In Website,顯示在網站,
Referral Code,推薦碼,
To Track inbound purchase,跟踪入站購買,
Logo,標誌,
Partner website,合作夥伴網站,
All Sales Transactions can be tagged against multiple **Sales Persons** so that you can set and monitor targets.,所有的銷售交易，可以用來標記針對多個**銷售**的人，這樣你可以設置和監控目標。,
Name and Employee ID,姓名和僱員ID,
Sales Person Name,銷售人員的姓名,
Parent Sales Person,母公司銷售人員,
Select company name first.,先選擇公司名稱。,
Sales Person Targets,銷售人員目標,
Set targets Item Group-wise for this Sales Person.,為此銷售人員設定跨項目群組間的目標。,
Supplier Group Name,供應商集團名稱,
Parent Supplier Group,父供應商組,
Target Detail,目標詳細資訊,
Target Qty,目標數量,
Target  Amount,目標金額,
Target Distribution,目標分佈,
"Standard Terms and Conditions that can be added to Sales and Purchases.\n\nExamples:\n\n1. Validity of the offer.\n1. Payment Terms (In Advance, On Credit, part advance etc).\n1. What is extra (or payable by the Customer).\n1. Safety / usage warning.\n1. Warranty if any.\n1. Returns Policy.\n1. Terms of shipping, if applicable.\n1. Ways of addressing disputes, indemnity, liability, etc.\n1. Address and Contact of your Company.",標準條款和可以添加到銷售和購買條件。\n\n例子：\n\n 1。有效性的報價。\n 1。付款條款（事先，在信貸，部分提前等）。\n 1。什麼是多餘的（或支付的客戶）。\n 1。安全/使用警告。\n 1。保修（如有）。\n 1。退貨政策。\n 1。航運條款（如果適用）。\n 1。的解決糾紛，賠償，法律責任等\n 1的方式。地址和公司聯繫。,
Applicable Modules,適用模塊,
Terms and Conditions Help,條款和條件幫助,
Classification of Customers by region,客戶按區域分類,
Territory Name,地區名稱,
Parent Territory,家長領地,
Territory Manager,區域經理,
For reference,供參考,
Territory Targets,境內目標,
Set Item Group-wise budgets on this Territory. You can also include seasonality by setting the Distribution.,在此地域設定跨群組項目間的預算。您還可以通過設定分配來包含季節性。,
UOM Name,計量單位名稱,
Check this to disallow fractions. (for Nos),勾選此選項則禁止分數。 （對於NOS）,
Website Item Group,網站項目群組,
Cross Listing of Item in multiple groups,在多組項目的交叉上市,
Default settings for Shopping Cart,對購物車的預設設定,
Enable Shopping Cart,啟用購物車,
Display Settings,顯示設置,
Show Public Attachments,顯示公共附件,
Show Price,顯示價格,
Show Stock Availability,顯示股票可用性,
Show Contact Us Button,顯示聯繫我們按鈕,
Show Stock Quantity,顯示庫存數量,
Show Apply Coupon Code,顯示申請優惠券代碼,
Allow items not in stock to be added to cart,允許將無庫存的商品添加到購物車,
Prices will not be shown if Price List is not set,價格將不會顯示如果沒有設置價格,
Quotation Series,報價系列,
Checkout Settings,結帳設定,
Enable Checkout,啟用結帳,
Payment Success Url,付款成功網址,
After payment completion redirect user to selected page.,支付完成後重定向用戶選擇的頁面。,
Batch Details,批次明細,
Batch ID,批次編號,
image,圖片,
Parent Batch,父母批,
Manufacturing Date,生產日期,
Batch Quantity,批次數量,
Source Document Type,源文檔類型,
Source Document Name,源文檔名稱,
Batch Description,批次說明,
Bin,箱子,
Reserved Quantity,保留數量,
Actual Quantity,實際數量,
Requested Quantity,要求的數量,
Reserved Qty for sub contract,分包合同的保留數量,
Moving Average Rate,移動平均房價,
Customs Tariff Number,海關稅則號,
Tariff Number,稅則號,
Delivery To,交貨給,
Is Return,退貨,
Issue Credit Note,發行信用票據,
Return Against Delivery Note,射向送貨單,
Customer's Purchase Order No,客戶的採購訂單編號,
Billing Address Name,帳單地址名稱,
Required only for sample item.,只對樣品項目所需。,
"If you have created a standard template in Sales Taxes and Charges Template, select one and click on the button below.",如果您已經創建了銷售稅和費模板標準模板，選擇一個，然後點擊下面的按鈕。,
In Words will be visible once you save the Delivery Note.,送貨單一被儲存，就會顯示出來。,
In Words (Export) will be visible once you save the Delivery Note.,送貨單一被儲存，(Export)就會顯示出來。,
Transporter Info,貨運公司資訊,
Driver Name,司機姓名,
Track this Delivery Note against any Project,跟踪此送貨單反對任何項目,
Inter Company Reference,公司間參考,
Print Without Amount,列印表單時不印金額,
% Installed,％已安裝,
% of materials delivered against this Delivery Note,針對這張送貨單物料已交貨的百分比(%),
Installation Status,安裝狀態,
Excise Page Number,消費頁碼,
Instructions,說明,
From Warehouse,從倉庫,
Against Sales Order,對銷售訂單,
Against Sales Order Item,對銷售訂單項目,
Against Sales Invoice,對銷售發票,
Against Sales Invoice Item,對銷售發票項目,
Available Batch Qty at From Warehouse,在從倉庫可用的批次數量,
Available Qty at From Warehouse,可用數量從倉庫,
Delivery Settings,交貨設置,
Dispatch Settings,發貨設置,
Dispatch Notification Template,調度通知模板,
Dispatch Notification Attachment,發貨通知附件,
Leave blank to use the standard Delivery Note format,留空以使用標準的交貨單格式,
Send with Attachment,發送附件,
Delay between Delivery Stops,交貨停止之間的延遲,
Delivery Stop,交貨停止,
Lock,鎖,
Visited,已到訪,
Order Information,訂單信息,
Contact Information,聯繫信息,
Email sent to,電子郵件發送給,
Dispatch Information,發貨信息,
Estimated Arrival,預計抵達時間,
Initial Email Notification Sent,初始電子郵件通知已發送,
Delivery Details,交貨細節,
Driver Email,司機電郵,
Driver Address,司機地址,
Total Estimated Distance,總估計距離,
Distance UOM,距離UOM,
Departure Time,出發時間,
Delivery Stops,交貨停止,
Calculate Estimated Arrival Times,計算預計到達時間,
Use Google Maps Direction API to calculate estimated arrival times,使用Google Maps Direction API計算預計到達時間,
Optimize Route,優化路線,
Use Google Maps Direction API to optimize route,使用Google Maps Direction API優化路線,
In Transit,運輸中,
Fulfillment User,履行用戶,
"A Product or a Service that is bought, sold or kept in stock.",產品或服務已購買，出售或持有的股票。,
Variant Of,的變體,
"If item is a variant of another item then description, image, pricing, taxes etc will be set from the template unless explicitly specified",如果項目是另一項目，然後描述，圖像，定價，稅費等會從模板中設定的一個變體，除非明確指定,
Is Item from Hub,是來自Hub的Item,
Default Unit of Measure,預設的計量單位,
Maintain Stock,維護庫存資料,
Standard Selling Rate,標準銷售率,
Auto Create Assets on Purchase,自動創建購買資產,
Asset Naming Series,資產命名系列,
Over Delivery/Receipt Allowance (%),超過交貨/收據津貼（％）,
Barcodes,條形碼,
Shelf Life In Days,保質期天數,
End of Life,壽命結束,
Default Material Request Type,默認材料請求類型,
Valuation Method,評估方法,
FIFO,FIFO,
Moving Average,移動平均線,
Warranty Period (in days),保修期限（天數）,
Auto re-order,自動重新排序,
Reorder level based on Warehouse,根據倉庫訂貨點水平,
Will also apply for variants unless overrridden,同時將申請變種，除非overrridden,
Units of Measure,測量的單位,
Will also apply for variants,同時將申請變種,
Serial Nos and Batches,序列號和批號,
Has Batch No,有批號,
Automatically Create New Batch,自動創建新批,
Batch Number Series,批號系列,
"Example: ABCD.#####. If series is set and Batch No is not mentioned in transactions, then automatic batch number will be created based on this series. If you always want to explicitly mention Batch No for this item, leave this blank. Note: this setting will take priority over the Naming Series Prefix in Stock Settings.",例如：ABCD。#####。如果系列已設置且交易中未提及批號，則將根據此系列創建自動批號。如果您始終想要明確提及此料品的批號，請將此留為空白。注意：此設置將優先於庫存設置中的命名系列前綴。,
Has Expiry Date,有過期日期,
Retain Sample,保留樣品,
Max Sample Quantity,最大樣品量,
Maximum sample quantity that can be retained,可以保留的最大樣品數量,
Has Serial No,有序列號,
Serial Number Series,序列號系列,
"Example: ABCD.#####\nIf series is set and Serial No is not mentioned in transactions, then automatic serial number will be created based on this series. If you always want to explicitly mention Serial Nos for this item. leave this blank.",例如：ABCD ##### \n如果串聯設定並且序列號沒有在交易中提到，然後自動序列號將在此基礎上創建的系列。如果你總是想明確提到序號為這個項目。留空。,
Variants,變種,
Has Variants,有變種,
"If this item has variants, then it cannot be selected in sales orders etc.",如果此項目已變種，那麼它不能在銷售訂單等選擇,
Variant Based On,基於變異對,
Item Attribute,項目屬性,
"Sales, Purchase, Accounting Defaults",銷售，採購，會計違約,
Item Defaults,項目默認值,
"Purchase, Replenishment Details",購買，補貨細節,
Is Purchase Item,是購買項目,
Default Purchase Unit of Measure,默認採購單位,
Minimum Order Qty,最低起訂量,
Minimum quantity should be as per Stock UOM,最小數量應按照庫存單位,
Average time taken by the supplier to deliver,採取供應商的平均時間交付,
Is Customer Provided Item,是客戶提供的項目,
Delivered by Supplier (Drop Ship),由供應商交貨（直接發運）,
Supplier Items,供應商項目,
Foreign Trade Details,外貿詳細,
Country of Origin,出生國家,
Sales Details,銷售詳細資訊,
Default Sales Unit of Measure,默認銷售單位,
Is Sales Item,是銷售項目,
Max Discount (%),最大折讓（％）,
No of Months,沒有幾個月,
Customer Items,客戶項目,
Inspection Criteria,檢驗標準,
Inspection Required before Purchase,購買前檢查所需,
Inspection Required before Delivery,分娩前檢查所需,
Default BOM,預設的BOM,
Supply Raw Materials for Purchase,供應原料採購,
If subcontracted to a vendor,如果分包給供應商,
Customer Code,客戶代碼,
Default Item Manufacturer,默認項目製造商,
Default Manufacturer Part No,默認製造商零件號,
Show in Website (Variant),展網站（變體）,
Items with higher weightage will be shown higher,具有較高權重的項目將顯示更高的可,
Show a slideshow at the top of the page,顯示幻燈片在頁面頂部,
Website Image,網站圖片,
Website Warehouse,網站倉庫,
"Show ""In Stock"" or ""Not in Stock"" based on stock available in this warehouse.",基於倉庫內存貨的狀態顯示「有或」或「無貨」。,
Website Item Groups,網站項目群組,
List this Item in multiple groups on the website.,列出這個項目在網站上多個組。,
Copy From Item Group,從項目群組複製,
Website Content,網站內容,
You can use any valid Bootstrap 4 markup in this field. It will be shown on your Item Page.,您可以在此字段中使用任何有效的Bootstrap 4標記。它將顯示在您的項目頁面上。,
Total Projected Qty,預計總數量,
Hub Publishing Details,Hub發布細節,
Publish in Hub,在發布中心,
Publish Item to hub.erpnext.com,發布項目hub.erpnext.com,
Hub Category to Publish,集線器類別發布,
Hub Warehouse,Hub倉庫,
"Publish ""In Stock"" or ""Not in Stock"" on Hub based on stock available in this warehouse.",基於倉庫中的庫存，在Hub上發布“庫存”或“庫存”。,
Synced With Hub,同步轂,
Item Alternative,項目選擇,
Alternative Item Code,替代項目代碼,
Two-way,雙向,
Alternative Item Name,替代項目名稱,
Attribute Name,屬性名稱,
Numeric Values,數字值,
From Range,從範圍,
To Range,為了範圍,
Item Attribute Values,項目屬性值,
Item Attribute Value,項目屬性值,
Attribute Value,屬性值,
Abbreviation,縮寫,
"This will be appended to the Item Code of the variant. For example, if your abbreviation is ""SM"", and the item code is ""T-SHIRT"", the item code of the variant will be ""T-SHIRT-SM""",這將追加到變異的項目代碼。例如，如果你的英文縮寫為“SM”，而該項目的代碼是“T-SHIRT”，該變種的項目代碼將是“T-SHIRT-SM”,
Item Barcode,商品條碼,
Barcode Type,條碼類型,
EAN,東盟,
Item Customer Detail,項目客戶詳細,
"For the convenience of customers, these codes can be used in print formats like Invoices and Delivery Notes",為方便客戶，這些代碼可以在列印格式，如發票和送貨單使用,
Ref Code,參考代碼,
Item Default,項目默認值,
Purchase Defaults,購買默認值,
Default Buying Cost Center,預設採購成本中心,
Default Supplier,預設的供應商,
Default Expense Account,預設費用帳戶,
Sales Defaults,銷售默認值,
Default Selling Cost Center,預設銷售成本中心,
Item Manufacturer,產品製造商,
Item Price,商品價格,
Packing Unit,包裝單位,
Quantity  that must be bought or sold per UOM,每個UOM必須購買或出售的數量,
Item Quality Inspection Parameter,產品質量檢驗參數,
Acceptance Criteria,驗收標準,
Item Reorder,項目重新排序,
Check in (group),檢查（組）,
Request for,要求,
Re-order Level,重新排序級別,
Re-order Qty,重新排序數量,
Item Supplier,產品供應商,
Item Variant,項目變,
Item Variant Attribute,產品規格屬性,
Do not update variants on save,不要在保存時更新變體,
Fields will be copied over only at time of creation.,字段將僅在創建時復制。,
Allow Rename Attribute Value,允許重命名屬性值,
Rename Attribute Value in Item Attribute.,在項目屬性中重命名屬性值。,
Copy Fields to Variant,將字段複製到變式,
Item Website Specification,項目網站規格,
Table for Item that will be shown in Web Site,表項，將在網站顯示出來,
Landed Cost Item,到岸成本項目,
Receipt Document Type,收據憑證類型,
Receipt Document,收據文件,
Applicable Charges,相關費用,
Purchase Receipt Item,採購入庫項目,
Landed Cost Purchase Receipt,到岸成本採購入庫單,
Landed Cost Taxes and Charges,到岸成本稅費,
Landed Cost Voucher,到岸成本憑證,
Purchase Receipts,採購入庫,
Purchase Receipt Items,採購入庫項目,
Get Items From Purchase Receipts,從採購入庫單取得項目,
Distribute Charges Based On,分銷費基於,
Landed Cost Help,到岸成本幫助,
Manufacturers used in Items,在項目中使用製造商,
Limited to 12 characters,限12個字符,
Partially Ordered,部分訂購,
Transferred,轉入,
% Ordered,% 已訂購,
Terms and Conditions Content,條款及細則內容,
Quantity and Warehouse,數量和倉庫,
Lead Time Date,交貨時間日期,
Min Order Qty,最小訂貨量,
Packed Item,盒裝產品,
To Warehouse (Optional),倉庫（可選）,
Actual Batch Quantity,實際批次數量,
Prevdoc DocType,Prevdoc的DocType,
Parent Detail docname,家長可採用DocName細節,
"Generate packing slips for packages to be delivered. Used to notify package number, package contents and its weight.",產生交貨的包裝單。用於通知箱號，內容及重量。,
Indicates that the package is a part of this delivery (Only Draft),表示該包是這個交付的一部分（僅草案）,
From Package No.,從包裹編號,
Identification of the package for the delivery (for print),寄送包裹的識別碼（用於列印）,
To Package No.,以包號,
If more than one package of the same type (for print),如果不止一個相同類型的包裹（用於列印）,
Package Weight Details,包裝重量詳情,
The net weight of this package. (calculated automatically as sum of net weight of items),淨重這個包。 （當項目的淨重量總和自動計算）,
Net Weight UOM,淨重計量單位,
Gross Weight,總重量,
The gross weight of the package. Usually net weight + packaging material weight. (for print),包裹的總重量。通常為淨重+包裝材料的重量。 （用於列印）,
Gross Weight UOM,毛重計量單位,
Packing Slip Item,包裝單項目,
DN Detail,DN詳細,
Material Transfer for Manufacture,物料轉倉用於製造,
Qty of raw materials will be decided based on the qty of the Finished Goods Item,原材料的數量將根據成品的數量來確定,
Parent Warehouse,家長倉庫,
Items under this warehouse will be suggested,將建議此倉庫下的項目,
Get Item Locations,獲取物品位置,
Pick List Item,選擇清單項目,
Picked Qty,挑選數量,
Price List Master,價格表主檔,
Price List Name,價格列表名稱,
Price Not UOM Dependent,價格不是UOM依賴,
Applicable for Countries,適用於國家,
Price List Country,價目表國家,
Supplier Delivery Note,供應商交貨單,
Time at which materials were received,物料收到的時間,
Return Against Purchase Receipt,採購入庫的退貨,
Rate at which supplier's currency is converted to company's base currency,供應商貨幣被換算成公司基礎貨幣的匯率,
Sets 'Accepted Warehouse' in each row of the items table.,在項目表的每一行中設置“可接受的倉庫”。,
Sets 'Rejected Warehouse' in each row of the items table.,在項目表的每一行中設置“拒絕倉庫”。,
Get Current Stock,取得當前庫存資料,
Add / Edit Taxes and Charges,新增 / 編輯稅金及費用,
Auto Repeat Detail,自動重複細節,
Transporter Details,貨運公司細節,
Vehicle Number,車號,
Vehicle Date,車日期,
Received and Accepted,收貨及允收,
Accepted Quantity,允收數量,
Rejected Quantity,拒絕數量,
Accepted Qty as per Stock UOM,根據庫存單位數量的可接受數量,
Sample Quantity,樣品數量,
Rate and Amount,率及金額,
Report Date,報告日期,
Inspection Type,檢驗類型,
Item Serial No,產品序列號,
Sample Size,樣本大小,
Inspected By,視察,
Readings,閱讀,
Quality Inspection Reading,質量檢驗閱讀,
Reading 1,閱讀1,
Reading 2,閱讀2,
Reading 3,閱讀3,
Reading 4,4閱讀,
Reading 5,閱讀5,
Reading 6,6閱讀,
Reading 7,7閱讀,
Reading 8,閱讀8,
Reading 9,9閱讀,
Reading 10,閱讀10,
Quality Inspection Template Name,質量檢驗模板名稱,
Quick Stock Balance,快速庫存平衡,
Available Quantity,可用數量,
Distinct unit of an Item,一個項目的不同的單元,
Warehouse can only be changed via Stock Entry / Delivery Note / Purchase Receipt,倉庫只能通過存貨分錄/送貨單/採購入庫單來改變,
Purchase / Manufacture Details,採購／製造詳細資訊,
Creation Document Type,創建文件類型,
Creation Document No,文檔創建編號,
Creation Date,創建日期,
Creation Time,創作時間,
Asset Details,資產詳情,
Asset Status,資產狀態,
Delivery Document Type,交付文件類型,
Delivery Document No,交貨證明文件號碼,
Delivery Time,交貨時間,
Invoice Details,發票明細,
Warranty / AMC Details,保修/ AMC詳情,
Warranty Expiry Date,保證期到期日,
AMC Expiry Date,AMC到期時間,
Under Warranty,在保修期,
Under AMC,在AMC,
Out of AMC,出資產管理公司,
Warranty Period (Days),保修期限（天數）,
Serial No Details,序列號詳細資訊,
Stock Entry Type,股票進入類型,
Stock Entry (Outward GIT),股票進入（外向GIT）,
Material Consumption for Manufacture,材料消耗製造,
Repack,重新包裝,
Send to Subcontractor,發送給分包商,
Delivery Note No,送貨單號,
Sales Invoice No,銷售發票號碼,
Purchase Receipt No,採購入庫單編號,
Inspection Required,需要檢驗,
From BOM,從BOM,
For Quantity,對於數量,
As per Stock UOM,按庫存計量單位,
Including items for sub assemblies,包括子組件項目,
Default Source Warehouse,預設來源倉庫,
Source Warehouse Address,來源倉庫地址,
Default Target Warehouse,預設目標倉庫,
Target Warehouse Address,目標倉庫地址,
Update Rate and Availability,更新率和可用性,
Total Incoming Value,總收入值,
Total Outgoing Value,出貨總計值,
Total Value Difference (Out - In),總價值差（輸出 - ）,
Additional Costs,額外費用,
Total Additional Costs,總額外費用,
Customer or Supplier Details,客戶或供應商詳細訊息,
Per Transferred,每次轉移,
Stock Entry Detail,存貨分錄明細,
Basic Rate (as per Stock UOM),基本速率（按庫存計量單位）,
Basic Amount,基本金額,
Additional Cost,額外費用,
Serial No / Batch,序列號/批次,
BOM No. for a Finished Good Item,BOM編號為成品產品,
Material Request used to make this Stock Entry,做此存貨分錄所需之物料需求,
Subcontracted Item,轉包項目,
Against Stock Entry,反對股票進入,
Stock Entry Child,股票入境兒童,
PO Supplied Item,採購訂單提供的物品,
Reference Purchase Receipt,參考購買收據,
Stock Ledger Entry,庫存總帳條目,
Outgoing Rate,傳出率,
Actual Qty After Transaction,交易後實際數量,
Stock Value Difference,庫存價值差異,
Stock Queue (FIFO),庫存序列（先進先出）,
Is Cancelled,被註銷,
Stock Reconciliation,庫存調整,
This tool helps you to update or fix the quantity and valuation of stock in the system. It is typically used to synchronise the system values and what actually exists in your warehouses.,此工具可幫助您更新或修復系統中的庫存數量和價值。它通常被用於同步系統值和實際存在於您的倉庫。,
Reconciliation JSON,JSON對賬,
Stock Reconciliation Item,庫存調整項目,
Before reconciliation,調整前,
Current Serial No,目前的序列號,
Current Valuation Rate,目前的估值價格,
Current Amount,電流量,
Quantity Difference,數量差異,
Amount Difference,金額差異,
Item Naming By,產品命名規則,
Default Item Group,預設項目群組,
Default Stock UOM,預設庫存計量單位,
Sample Retention Warehouse,樣品保留倉庫,
Default Valuation Method,預設的估值方法,
Show Barcode Field,顯示條形碼域,
Convert Item Description to Clean HTML,將項目描述轉換為清理HTML,
Allow Negative Stock,允許負庫存,
Automatically Set Serial Nos based on FIFO,自動設置序列號的基礎上FIFO,
Auto Material Request,自動物料需求,
Inter Warehouse Transfer Settings,倉庫間轉移設置,
Freeze Stock Entries,凍結庫存項目,
Stock Frozen Upto,存貨凍結到...為止,
Batch Identification,批次標識,
Use Naming Series,使用命名系列,
Naming Series Prefix,命名系列前綴,
UOM Category,UOM類別,
UOM Conversion Detail,計量單位換算詳細,
Variant Field,變種場,
A logical Warehouse against which stock entries are made.,對這些庫存分錄帳進行的邏輯倉庫。,
Warehouse Detail,倉庫的詳細資訊,
Warehouse Name,倉庫名稱,
Warehouse Contact Info,倉庫聯絡方式,
PIN,銷,
Raised By (Email),由（電子郵件）提出,
Issue Type,發行類型,
Issue Split From,問題拆分,
Service Level,服務水平,
Response By,回應,
Response By Variance,按方差回應,
Ongoing,不斷的,
Resolution By,解析依據,
Resolution By Variance,方差解析,
Service Level Agreement Creation,服務水平協議創建,
First Responded On,首先作出回應,
Resolution Details,詳細解析,
Opening Date,開幕日期,
Opening Time,開放時間,
Resolution Date,決議日期,
Via Customer Portal,通過客戶門戶,
Support Team,支持團隊,
Issue Priority,問題優先,
Service Day,服務日,
Workday,勞動日,
Default Priority,默認優先級,
Priorities,優先級,
Support Hours,支持小時,
Support and Resolution,支持和解決,
Default Service Level Agreement,默認服務水平協議,
Entity,實體,
Agreement Details,協議細節,
Response and Resolution Time,響應和解決時間,
Service Level Priority,服務水平優先,
Resolution Time,解決時間,
Source Type,來源類型,
Query Route String,查詢路由字符串,
Search Term Param Name,搜索字詞Param Name,
Response Options,響應選項,
Response Result Key Path,響應結果關鍵路徑,
Post Route String,郵政路線字符串,
Post Route Key List,發布路由密鑰列表,
Post Title Key,帖子標題密鑰,
Post Description Key,發布說明密鑰,
Link Options,鏈接選項,
Source DocType,源DocType,
Result Title Field,結果標題字段,
Result Preview Field,結果預覽字段,
Result Route Field,結果路由字段,
Service Level Agreements,服務等級協定,
Track Service Level Agreement,跟踪服務水平協議,
Allow Resetting Service Level Agreement,允許重置服務水平協議,
Close Issue After Days,關閉問題天后,
Auto close Issue after 7 days,7天之後自動關閉問題,
Support Portal,支持門戶,
Get Started Sections,入門部分,
Show Latest Forum Posts,顯示最新的論壇帖子,
Forum Posts,論壇帖子,
Forum URL,論壇URL,
Get Latest Query,獲取最新查詢,
Response Key List,響應密鑰列表,
Post Route Key,郵政路線密鑰,
Issue Date,發行日期,
Item and Warranty Details,項目和保修細節,
Warranty / AMC Status,保修/ AMC狀態,
Resolved By,議決,
Service Address,服務地址,
If different than customer address,如果與客戶地址不同,
Raised By,提出者,
From Company,從公司,
Utilities,公用事業,
Type of document to rename.,的文件類型進行重命名。,
File to Rename,檔案重命名,
"Attach .csv file with two columns, one for the old name and one for the new name",附加.csv文件有兩列，一為舊名稱，一個用於新名稱,
Rename Log,重命名日誌,
SMS Log,短信日誌,
Sender Name,發件人名稱,
Sent On,發送於,
No of Requested SMS,無的請求短信,
Requested Numbers,請求號碼,
No of Sent SMS,沒有發送短信,
Sent To,發給,
Absent Student Report,缺席學生報告,
Assessment Plan Status,評估計劃狀態,
Asset Depreciation Ledger,資產減值總帳,
Asset Depreciations and Balances,資產折舊和平衡,
Available Stock for Packing Items,可用庫存包裝項目,
Bank Clearance Summary,銀行結算摘要,
Bank Remittance,銀行匯款,
Batch Item Expiry Status,批處理項到期狀態,
Batch-Wise Balance History,間歇式平衡歷史,
BOM Explorer,BOM管理器,
BOM Search,BOM搜索,
BOM Stock Calculated,BOM庫存計算,
BOM Variance Report,BOM差異報告,
Campaign Efficiency,運動效率,
Cash Flow,現金周轉,
Completed Work Orders,完成的工作訂單,
To Produce,以生產,
Produced,生產,
Consolidated Financial Statement,合併財務報表,
Course wise Assessment Report,課程明智的評估報告,
Customer Acquisition and Loyalty,客戶取得和忠誠度,
Customer Credit Balance,客戶信用平衡,
Customer Ledger Summary,客戶分類帳摘要,
Customer-wise Item Price,客戶明智的物品價格,
Customers Without Any Sales Transactions,沒有任何銷售交易的客戶,
Daily Timesheet Summary,每日時間表摘要,
Daily Work Summary Replies,日常工作總結回复,
Delayed Item Report,延遲物品報告,
Delayed Order Report,延遲訂單報告,
Delivered Items To Be Billed,交付項目要被收取,
Delivery Note Trends,送貨單趨勢,
Electronic Invoice Register,電子發票登記,
Employee Advance Summary,員工提前總結,
Employee Billing Summary,員工賬單摘要,
Employee Birthday,員工生日,
Employee Information,僱員資料,
Employee Leave Balance,員工休假餘額,
Employee Leave Balance Summary,員工休假餘額摘要,
Employees working on a holiday,員工在假期工作,
Eway Bill,依比爾,
Expiring Memberships,即將到期的會員,
Final Assessment Grades,最終評估等級,
Fixed Asset Register,固定資產登記冊,
Gross and Net Profit Report,毛利潤和淨利潤報告,
GST Itemised Purchase Register,GST成品採購登記冊,
GST Itemised Sales Register,消費稅商品銷售登記冊,
GST Purchase Register,消費稅購買登記冊,
GST Sales Register,消費稅銷售登記冊,
Hotel Room Occupancy,酒店房間入住,
HSN-wise-summary of outward supplies,HSN明智的向外供應摘要,
Inactive Customers,不活躍的客戶,
Inactive Sales Items,非活動銷售項目,
IRS 1099,美國國稅局1099,
Issued Items Against Work Order,針對工單發布物品,
Projected Quantity as Source,預計庫存量的來源,
Item Balance (Simple),物品餘額（簡單）,
Item Price Stock,項目價格股票,
Item Prices,產品價格,
Item Shortage Report,商品短缺報告,
Item Variant Details,項目變體的詳細信息,
Item-wise Price List Rate,全部項目的價格表,
Item-wise Purchase History,全部項目的購買歷史,
Item-wise Purchase Register,項目明智的購買登記,
Item-wise Sales History,項目明智的銷售歷史,
Item-wise Sales Register,項目明智的銷售登記,
Items To Be Requested,需求項目,
Reserved,已預留,
Itemwise Recommended Reorder Level,Itemwise推薦級別重新排序,
Lead Details,潛在客戶詳情,
Lead Owner Efficiency,主導效率,
Loan Repayment and Closure,償還和結清貸款,
Loan Security Status,貸款安全狀態,
Lost Opportunity,失去的機會,
Maintenance Schedules,保養時間表,
Material Requests for which Supplier Quotations are not created,尚未建立供應商報價的材料需求,
Monthly Attendance Sheet,每月出勤表,
Open Work Orders,打開工作訂單,
Qty to Deliver,數量交付,
Patient Appointment Analytics,患者預約分析,
Payment Period Based On Invoice Date,基於發票日的付款期,
Pending SO Items For Purchase Request,待處理的SO項目對於採購申請,
Procurement Tracker,採購跟踪器,
Product Bundle Balance,產品包餘額,
Production Analytics,生產Analytics（分析）,
Profit and Loss Statement,損益表,
Project Billing Summary,項目開票摘要,
Project wise Stock Tracking,項目明智的庫存跟踪,
Project wise Stock Tracking ,項目明智的庫存跟踪,
Purchase Analytics,採購分析,
Purchase Invoice Trends,購買發票趨勢,
Qty to Receive,未到貨量,
Received Qty Amount,收到的數量,
Billed Qty,開票數量,
Purchase Order Trends,採購訂單趨勢,
Purchase Receipt Trends,採購入庫趨勢,
Purchase Register,購買註冊,
Quotation Trends,報價趨勢,
Received Items To Be Billed,待付款的收受品項,
Qty to Order,訂購數量,
Requested Items To Be Transferred,將要轉倉的需求項目,
Qty to Transfer,轉移數量,
Salary Register,薪酬註冊,
Sales Analytics,銷售分析,
Sales Invoice Trends,銷售發票趨勢,
Sales Order Trends,銷售訂單趨勢,
Sales Partner Commission Summary,銷售合作夥伴佣金摘要,
Sales Partner Target Variance based on Item Group,銷售合作夥伴基於項目組的目標差異,
Sales Partner Transaction Summary,銷售合作夥伴交易摘要,
Sales Partners Commission,銷售合作夥伴佣金,
Invoiced Amount (Exclusive Tax),發票金額（不含稅）,
Average Commission Rate,平均佣金比率,
Sales Payment Summary,銷售付款摘要,
Sales Person Commission Summary,銷售人員委員會摘要,
Sales Person Target Variance Based On Item Group,基於項目組的銷售人員目標差異,
Sales Person-wise Transaction Summary,銷售人員相關的交易匯總,
Sales Register,銷售登記,
Serial No Service Contract Expiry,序號服務合同到期,
Serial No Status,序列號狀態,
Serial No Warranty Expiry,序列號保修到期,
Stock Ageing,存貨帳齡分析表,
Stock and Account Value Comparison,股票和賬戶價值比較,
Stock Projected Qty,存貨預計數量,
Student and Guardian Contact Details,學生和監護人聯繫方式,
Student Batch-Wise Attendance,學生分批出席,
Student Fee Collection,學生費徵收,
Student Monthly Attendance Sheet,學生每月考勤表,
Subcontracted Item To Be Received,要轉包的分包物品,
Subcontracted Raw Materials To Be Transferred,分包原材料將被轉讓,
Supplier Ledger Summary,供應商分類帳摘要,
Supplier-Wise Sales Analytics,供應商相關的銷售分析,
Support Hour Distribution,支持小時分配,
TDS Computation Summary,TDS計算摘要,
TDS Payable Monthly,TDS應付月度,
Territory Target Variance Based On Item Group,基於項目組的地域目標差異,
Territory-wise Sales,區域銷售,
Total Stock Summary,總庫存總結,
Trial Balance,試算表,
Trial Balance (Simple),試算平衡（簡單）,
Trial Balance for Party,試算表的派對,
Unpaid Expense Claim,未付費用報銷,
Warehouse wise Item Balance Age and Value,倉庫明智的項目平衡年齡和價值,
Work Order Stock Report,工單庫存報表,
Work Orders in Progress,工作訂單正在進行中,
Validation Error,驗證錯誤,
Automatically Process Deferred Accounting Entry,自動處理遞延會計分錄,
Bank Clearance,銀行清算,
Bank Clearance Detail,銀行清算詳細信息,
Update Cost Center Name / Number,更新成本中心名稱/編號,
Journal Entry Template,日記條目模板,
Template Title,模板標題,
Journal Entry Type,日記帳分錄類型,
Journal Entry Template Account,日記帳分錄模板帳戶,
Process Deferred Accounting,遞延會計處理,
Manual entry cannot be created! Disable automatic entry for deferred accounting in accounts settings and try again,無法創建手動輸入！禁用自動輸入帳戶設置中的遞延會計，然後重試,
End date cannot be before start date,結束日期不能早於開始日期,
Total Counts Targeted,目標總數,
Total Counts Completed,總計數已完成,
Counts Targeted: {0},目標計數：{0},
Payment Account is mandatory,付款帳戶是必填項,
"If checked, the full amount will be deducted from taxable income before calculating income tax without any declaration or proof submission.",如果選中此復選框，則在計算所得稅前將從所有應納稅所得額中扣除全部金額，而無需進行任何聲明或提交證明。,
Disbursement Details,支付明細,
Material Request Warehouse,物料請求倉庫,
Select warehouse for material requests,選擇物料需求倉庫,
Transfer Materials For Warehouse {0},倉庫{0}的轉移物料,
Production Plan Material Request Warehouse,生產計劃物料申請倉庫,
Sets 'Source Warehouse' in each row of the items table.,在項目表的每一行中設置“源倉庫”。,
Sets 'Target Warehouse' in each row of the items table.,在項目表的每一行中設置“目標倉庫”。,
Show Cancelled Entries,顯示已取消的條目,
Backdated Stock Entry,回溯的庫存輸入,
Row #{}: Currency of {} - {} doesn't matches company currency.,第＃{}行：{}-{}的貨幣與公司貨幣不符。,
{} Assets created for {},{}為{}創建的資產,
{0} Number {1} is already used in {2} {3},{0}數字{1}已在{2} {3}中使用,
Update Bank Clearance Dates,更新銀行清算日期,
Healthcare Practitioner: ,醫療保健從業者：,
Lab Test Conducted: ,進行的實驗室測試：,
Lab Test Event: ,實驗室測試事件：,
Lab Test Result: ,實驗室測試結果：,
Clinical Procedure conducted: ,進行的臨床程序：,
Therapy Session Charges: {0},療程費用：{0},
Therapy: ,治療：,
Therapy Plan: ,治療計劃：,
Total Counts Targeted: ,目標總數：,
Total Counts Completed: ,已完成的總數：,
Andaman and Nicobar Islands,安達曼和尼科巴群島,
Arunachal Pradesh,阿魯納恰爾邦,
Assam,阿薩姆邦,
Bihar,比哈爾,
Chandigarh,昌迪加爾,
Chhattisgarh,恰蒂斯加爾邦,
Dadra and Nagar Haveli,達德拉和納加爾·哈維里,
Daman and Diu,達曼和丟,
Haryana,哈里亞納邦,
Himachal Pradesh,喜馬al爾邦,
Jammu and Kashmir,查mu和克什米爾,
Jharkhand,賈坎德邦,
Karnataka,卡納塔克邦,
Lakshadweep Islands,拉克肖普群島,
Maharashtra,馬哈拉施特拉邦,
Manipur,馬尼布爾,
Meghalaya,梅加拉亞邦,
Mizoram,咪唑侖,
Nagaland,那加蘭邦,
Odisha,奧迪沙,
Other Territory,其他地區,
Pondicherry,朋迪榭裡,
Punjab,旁遮普語,
Rajasthan,拉賈斯坦邦,
Sikkim,錫金,
Tamil Nadu,泰米爾納德邦,
Is Mandatory,是強制性的,
Published on,發表於,
Service Received But Not Billed,服務已收到但未計費,
Deferred Accounting Settings,遞延會計設置,
Book Deferred Entries Based On,基於的圖書遞延分錄,
Months,月數,
Book Deferred Entries Via Journal Entry,通過日記帳分錄遞延分錄,
Submit Journal Entries,提交日記帳分錄,
If this is unchecked Journal Entries will be saved in a Draft state and will have to be submitted manually,如果未選中，則日記帳分錄將保存為草稿狀態，並且必須手動提交,
Enable Distributed Cost Center,啟用分佈式成本中心,
Distributed Cost Center,分佈式成本中心,
DUNN-.MM.-.YY.-,鄧恩-.MM .-。YY.-,
Dunning Type,催款類型,
Dunning Fee,催款費,
Dunning Amount,催款金額,
Resolved,解決,
Unresolved,未解決,
Printing Setting,打印設定,
Body Text,主體,
Closing Text,結束語,
Resolve,解決,
Is Default Language,是默認語言,
Letter or Email Body Text,信件或電子郵件正文,
Letter or Email Closing Text,信件或電子郵件結束文字,
Body and Closing Text Help,正文和結束語幫助,
Overdue Interval,逾期間隔,
"This section allows the user to set the Body and Closing text of the Dunning Letter for the Dunning Type based on language, which can be used in Print.",該部分允許用戶根據語言設置催款類型的催款信的正文和關閉文本，可以在打印中使用。,
Reference Detail No,參考詳細信息,
Custom Remarks,自訂備註,
Please select a Company first.,請先選擇一個公司。,
"Row #{0}: Reference Document Type must be one of Sales Order, Sales Invoice, Journal Entry or Dunning",行＃{0}：參考單據類型必須是銷售訂單，銷售發票，日記帳分錄或催款中的一種,
POS Closing Entry,POS結賬單,
POS Opening Entry,POS入口條目,
POS Closing Entry Detail,POS關閉輸入明細,
Opening Amount,期初金額,
Closing Amount,結算金額,
POS Closing Entry Taxes,POS結關進項稅,
POS Invoice,POS發票,
Consolidated Sales Invoice,合併銷售發票,
Return Against POS Invoice,退回POS發票,
Consolidated,合併,
POS Invoice Item,POS發票項目,
POS Invoice Merge Log,POS發票合併日誌,
POS Invoices,POS發票,
Consolidated Credit Note,合併貸方通知單,
POS Invoice Reference,POS發票參考,
Set Posting Date,設定過帳日期,
Opening Balance Details,期初餘額明細,
POS Opening Entry Detail,POS入口條目詳細信息,
Process Statement Of Accounts,流程帳目表,
General Ledger Filters,總帳過濾器,
Customers,顧客,
Select Customers By,選擇客戶依據,
Fetch Customers,獲取客戶,
Send To Primary Contact,發送給主要聯繫人,
Print Preferences,打印首選項,
Include Ageing Summary,包括帳齡摘要,
Enable Auto Email,啟用自動電子郵件,
Filter Duration (Months),篩選時間（月）,
Help Text,幫助文字,
Emails Queued,電子郵件已排隊,
Process Statement Of Accounts Customer,流程帳目客戶,
Billing Email,帳單電郵,
Primary Contact Email,主要聯繫人電子郵件,
PSOA Project,PSOA項目,
Supplier GSTIN,供應商GSTIN,
Place of Supply,供貨地點,
Select Billing Address,選擇帳單地址,
GST Details,GST詳細信息,
GST Category,消費稅類別,
Registered Regular,註冊普通,
Registered Composition,註冊組成,
Unregistered,未註冊,
SEZ,經濟特區,
With Payment of Tax,交稅,
Without Payment of Tax,免稅,
Invoice Copy,發票副本,
Duplicate for Transporter,複製轉運蛋白,
Duplicate for Supplier,供方重複,
Triplicate for Supplier,供應商一式三份,
Reverse Charge,反向充電,
E-commerce GSTIN,電子商務GSTIN,
Reason For Issuing document,簽發文件的原因,
01-Sales Return,01銷售退貨,
02-Post Sale Discount,02-售後折扣,
03-Deficiency in services,03-服務不足,
04-Correction in Invoice,04-發票更正,
06-Finalization of Provisional assessment,06-臨時評估完成,
Eligibility For ITC,ITC資格,
Input Service Distributor,輸入服務分銷商,
Import Of Service,服務導入,
Import Of Capital Goods,資本貨物進口,
Availed ITC Integrated Tax,ITC綜合稅,
Availed ITC Central Tax,ITC中央稅可用,
Availed ITC State/UT Tax,ITC州/ UT可用稅,
Is Nil Rated or Exempted,零分或免稅,
Is Non GST,是非消費稅,
E-Way Bill No.,電子通單號,
Is Consolidated,已合併,
Billing Address GSTIN,帳單地址GSTIN,
Customer GSTIN,客戶GSTIN,
GST Transporter ID,GST運輸者ID,
Distance (in km),距離（公里）,
Air,空氣,
Rail,軌,
GST Vehicle Type,GST車輛類型,
Over Dimensional Cargo (ODC),超尺寸貨物（ODC）,
Consumer,消費者,
Deemed Export,被視為出口,
Port Code,港口代碼,
 Shipping Bill Number,發貨單號,
Shipping Bill Date,發貨單日期,
Subscription End Date,訂閱結束日期,
Follow Calendar Months,跟隨日曆月,
If this is checked subsequent new invoices will be created on calendar  month and quarter start dates irrespective of current invoice start date,如果選中此選項，則無論當前發票的開始日期如何，都將在日曆月和季度開始日期創建後續的新發票。,
Generate New Invoices Past Due Date,生成過期的新發票,
New invoices will be generated as per schedule even if current invoices are unpaid or past due date,即使當前發票未付款或過期，也會按照計劃生成新發票,
Document Type ,文件類型,
Subscription Price Based On,訂閱價格基於,
Based On Price List,根據價目表,
Monthly Rate,月費,
Cancel Subscription After Grace Period,寬限期後取消訂閱,
Source State,源狀態,
Is Inter State,是州際,
Purchase Details,採購明細,
Depreciation Posting Date,折舊過帳日期,
"By default, the Supplier Name is set as per the Supplier Name entered. If you want Suppliers to be named by a  ",默認情況下，根據輸入的供應商名稱設置供應商名稱。如果您希望供應商以,
 choose the 'Naming Series' option.,選擇“命名系列”選項。,
Configure the default Price List when creating a new Purchase transaction. Item prices will be fetched from this Price List.,在創建新的採購交易時配置默認的價目表。項目價格將從此價格表中獲取。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Purchase Invoice or Receipt without creating a Purchase Order first. This configuration can be overridden for a particular supplier by enabling the 'Allow Purchase Invoice Creation Without Purchase Order' checkbox in the Supplier master.",如果將此選項配置為“是”，ERPNext將阻止您創建採購發票或收據而無需先創建採購訂單。通過啟用供應商主數據中的“允許創建無購買訂單的發票”複選框，可以為特定供應商覆蓋此配置。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Purchase Invoice without creating a Purchase Receipt first. This configuration can be overridden for a particular supplier by enabling the 'Allow Purchase Invoice Creation Without Purchase Receipt' checkbox in the Supplier master.",如果將此選項配置為“是”，則ERPNext將阻止您創建採購發票而不先創建採購收據。通過啟用供應商主數據中的“允許創建沒有購買收據的購買發票”複選框，可以為特定供應商覆蓋此配置。,
Quantity & Stock,數量和庫存,
Call Details,通話詳情,
Authorised By,授權人,
Signee (Company),簽收人（公司）,
Signed By (Company),簽名人（公司）,
First Response Time,第一次響應時間,
Request For Quotation,要求報價,
Opportunity Lost Reason Detail,機會喪失原因詳細信息,
Access Token Secret,訪問令牌機密,
Add to Topics,添加到主題,
...Adding Article to Topics,...在主題中添加文章,
Add Article to Topics,將文章添加到主題,
This article is already added to the existing topics,本文已添加到現有主題,
...Adding Course to Programs,...將課程添加到程序中,
Add Course to Programs,將課程添加到程序,
This course is already added to the existing programs,該課程已被添加到現有程序中,
Learning Management System Settings,學習管理系統設置,
Enable Learning Management System,啟用學習管理系統,
Learning Management System Title,學習管理系統標題,
...Adding Quiz to Topics,...將測驗添加到主題,
Add Quiz to Topics,將測驗添加到主題,
This quiz is already added to the existing topics,該測驗已添加到現有主題中,
Enable Admission Application,啟用入學申請,
Add Guardians to Email Group,將監護人添加到電子郵件組,
Attendance Based On,出勤依據,
Check this to mark the student as present in case the student is not attending the institute to participate or represent the institute in any event.\n\n,在任何情況下，如果學生不參加學院參加或代表學院，請選中該複選框以將學生標記為在場。,
Add to Courses,添加到課程,
...Adding Topic to Courses,...為課程添加主題,
Add Topic to Courses,向課程添加主題,
This topic is already added to the existing courses,該主題已被添加到現有課程中,
"If Shopify does not have a customer in the order, then while syncing the orders, the system will consider the default customer for the order",如果Shopify的訂單中沒有客戶，則在同步訂單時，系統將考慮該訂單的默認客戶,
The accounts are set by the system automatically but do confirm these defaults,帳戶由系統自動設置，但請確認這些默認設置,
Default Round Off Account,默認四捨五入帳戶,
Failed Import Log,導入日誌失敗,
Fixed Error Log,固定錯誤日誌,
Company {0} already exists. Continuing will overwrite the Company and Chart of Accounts,公司{0}已存在。繼續將覆蓋公司和會計科目表,
Meta Data,元數據,
Unresolve,未解決,
Mark as unresolved,標記為未解決,
TaxJar Settings,TaxJar設置,
Enable Tax Calculation,啟用稅收計算,
Create TaxJar Transaction,創建TaxJar交易,
Credentials,證書,
Live API Key,實時API密鑰,
Sandbox API Key,沙盒API密鑰,
Configuration,組態,
Tax Account Head,稅務科目主管,
Shipping Account Head,運送帳戶負責人,
Practitioner Name,執業者姓名,
Enter a name for the Clinical Procedure Template,輸入臨床程序模板的名稱,
Set the Item Code which will be used for billing the Clinical Procedure.,設置將用於計費臨床程序的項目代碼。,
Select an Item Group for the Clinical Procedure Item.,為臨床程序項目選擇一個項目組。,
Clinical Procedure Rate,臨床程序率,
Check this if the Clinical Procedure is billable and also set the rate.,如果臨床過程是可計費的，請檢查此項目並設置費率。,
Check this if the Clinical Procedure utilises consumables. Click ,如果臨床程序使用消耗品，請檢查此項目。請點擊,
"You can also set the Medical Department for the template. After saving the document, an Item will automatically be created for billing this Clinical Procedure. You can then use this template while creating Clinical Procedures for Patients. Templates save you from filling up redundant data every single time. You can also create templates for other operations like Lab Tests, Therapy Sessions, etc.",您也可以為模板設置醫療部門。保存文檔後，將自動創建一個項目，以為此臨床程序開票。然後，您可以在創建患者臨床程序時使用此模板。模板使您不必每次都填充冗餘數據。您還可以為其他操作（如實驗室測試，治療會議等）創建模板。,
Descriptive Test Result,描述性測試結果,
Allow Blank,允許空白,
Descriptive Test Template,描述性測試模板,
"If you want to track Payroll and other HRMS operations for a Practitoner, create an Employee and link it here.",如果要跟踪從業人員的薪資和其他HRMS操作，請創建一個Employee並將其鏈接到此處。,
Set the Practitioner Schedule you just created. This will be used while booking appointments.,設置您剛剛創建的從業者時間表。這將在預訂約會時使用。,
Create a service item for Out Patient Consulting.,為門診諮詢創建服務項目。,
"If this Healthcare Practitioner works for the In-Patient Department, create a service item for Inpatient Visits.",如果此醫療保健從業者在門診部工作，請為住院患者就診創建服務項目。,
Set the Out Patient Consulting Charge for this Practitioner.,設置該從業者的門診諮詢費用。,
"If this Healthcare Practitioner also works for the In-Patient Department, set the inpatient visit charge for this Practitioner.",如果該醫療保健從業者也為門診部工作，請為該從業者設定住院費用。,
"If checked, a customer will be created for every Patient. Patient Invoices will be created against this Customer. You can also select existing Customer while creating a Patient. This field is checked by default.",如果選中，將為每個患者創建一個客戶。將針對該客戶創建患者發票。您還可以在創建患者時選擇現有客戶。默認情況下選中此字段。,
Collect Registration Fee,收取註冊費,
"If your Healthcare facility bills registrations of Patients, you can check this and set the Registration Fee in the field below. Checking this will create new Patients with a Disabled status by default and will only be enabled after invoicing the Registration Fee.",如果您的醫療保健機構對患者的註冊開具賬單，則可以進行檢查並在下面的字段中設置註冊費。選中此選項將默認創建具有“禁用”狀態的新患者，並且僅在開具註冊費發票後才能啟用。,
Checking this will automatically create a Sales Invoice whenever an appointment is booked for a Patient.,選中此選項將在為患者預約約會時自動創建銷售發票。,
Healthcare Service Items,醫療服務項目,
"You can create a service item for Inpatient Visit Charge and set it here. Similarly, you can set up other Healthcare Service Items for billing in this section. Click ",您可以為“住院訪問費用”創建服務項目並在此處進行設置。同樣，您可以在此部分中設置其他醫療服務項目以進行計費。請點擊,
Set up default Accounts for the Healthcare Facility,為醫療保健設施設置默認帳戶,
"If you wish to override default accounts settings and configure the Income and Receivable accounts for Healthcare, you can do so here.",如果您希望覆蓋默認帳戶設置並為醫療保健配置收入帳戶和應收帳款帳戶，則可以在此處進行。,
Out Patient SMS alerts,門診短信提醒,
"If you want to send SMS alert on Patient Registration, you can enable this option. Similary, you can set up Out Patient SMS alerts for other functionalities in this section. Click ",如果要發送有關患者掛號的SMS警報，則可以啟用此選項。同樣，您可以在本節中為其他功能設置門診SMS警報。請點擊,
Admission Order Details,入場訂單詳細信息,
Admission Ordered For,訂購入場,
Expected Length of Stay,預計停留時間,
Admission Service Unit Type,入學服務單位類型,
Healthcare Practitioner (Primary),保健醫生（小學）,
Healthcare Practitioner (Secondary),保健醫生（中學）,
Admission Instruction,入學須知,
Chief Complaint,首席投訴,
Medications,藥物治療,
Investigations,調查,
Discharge Detials,放電細節,
Discharge Ordered Date,卸貨訂購日期,
Discharge Instructions,卸貨說明,
Follow Up Date,跟進日期,
Discharge Notes,出院注意事項,
Processing Inpatient Discharge,處理住院病人出院,
Processing Patient Admission,處理患者入院,
Check-in time cannot be greater than the current time,入住時間不能大於當前時間,
Process Transfer,流程轉移,
Expected Result Date,預期結果日期,
Expected Result Time,預期結果時間,
Printed on,印於,
Requesting Practitioner,要求從業者,
Requesting Department,要求部門,
Employee (Lab Technician),員工（實驗室技術員）,
Lab Technician Name,實驗室技術員姓名,
Lab Technician Designation,實驗室技術員指定,
Compound Test Result,複合測試結果,
Organism Test Result,生物測試結果,
Sensitivity Test Result,靈敏度測試結果,
Worksheet Instructions,工作表說明,
Result Legend Print,結果圖例打印,
Result Legend,結果圖例,
Lab Tests,實驗室測試,
No Lab Tests found for the Patient {0},找不到針對患者{0}的實驗室測試,
"Did not send SMS, missing patient mobile number or message content.",沒有發送短信，缺少患者的手機號碼或消息內容。,
No Lab Tests created,未創建實驗室測試,
Creating Lab Tests...,創建實驗室測試...,
Lab Test Group Template,實驗室測試組模板,
"<b>Single</b>: Results which require only a single input.\n<br>\n<b>Compound</b>: Results which require multiple event inputs.\n<br>\n<b>Descriptive</b>: Tests which have multiple result components with manual result entry.\n<br>\n<b>Grouped</b>: Test templates which are a group of other test templates.\n<br>\n<b>No Result</b>: Tests with no results, can be ordered and billed but no Lab Test will be created. e.g.. Sub Tests for Grouped results",<b>單項</b>：僅需一個輸入的結果。<br><b>複合</b>：需要多個事件輸入的結果。<br><b>描述性</b>：具有多個結果成分且帶有手動結果輸入的測試。<br><b>分組</b>：測試模板，它是一組其他測試模板。<br><b>無結果</b>：可以訂購<b>無結果的</b>測試，可以訂購和計費，但不會創建實驗室測試。例如。分組結果的子測試,
"If unchecked, the item will not be available in Sales Invoices for billing but can be used in group test creation. ",如果未選中，則該項目在“銷售發票”中將不可用，但可以在組測試創建中使用。,
Descriptive Test,描述性測試,
Group Tests,小組測試,
Instructions to be printed on the worksheet,在工作表上打印的說明,
"Information to help easily interpret the test report, will be printed as part of the Lab Test result.",幫助您輕鬆解釋測試報告的信息將作為實驗室測試結果的一部分進行打印。,
Normal Test Result,正常測試結果,
Secondary UOM Result,次要UOM結果,
Italic,斜體,
Underline,強調,
Organism Test Item,生物測試項目,
Tobacco Consumption (Past),煙草消費量（過去）,
Tobacco Consumption (Present),煙草消費（現在）,
Alcohol Consumption (Past),飲酒量（過去）,
Alcohol Consumption (Present),飲酒量（當前）,
Billing Item,開票項目,
Medical Codes,醫療法規,
Clinical Procedures,臨床程序,
Order Admission,訂單入場,
Order Discharge,訂單卸貨,
Sample Details,樣品細節,
Collected On,收集於,
No. of prints,印刷數量,
Number of prints required for labelling the samples,標記樣品所需的打印數量,
In Time,及時,
Out Time,時差,
Payroll Cost Center,工資成本中心,
The first Approver in the list will be set as the default Approver.,列表中的第一個批准人將被設置為默認批准人。,
Shift Request Approver,輪班請求批准人,
PAN Number,PAN號碼,
Provident Fund Account,公積金帳戶,
MICR Code,MICR代碼,
Repay unclaimed amount from salary,從工資中償還無人認領的金額,
Deduction from salary,從工資中扣除,
Expired Leaves,過期的葉子,
Reference No,參考編號,
Haircut percentage is the percentage difference between market value of the Loan Security and the value ascribed to that Loan Security when used as collateral for that loan.,減價百分比是貸款抵押品的市場價值與該貸款抵押品的價值之間的百分比差異。,
Loan To Value Ratio expresses the ratio of the loan amount to the value of the security pledged. A loan security shortfall will be triggered if this falls below the specified value for any loan ,貸款價值比表示貸款額與所抵押擔保物價值之比。如果低於任何貸款的指定值，就會觸發貸款抵押短缺,
If this is not checked the loan by default will be considered as a Demand Loan,如果未選中此選項，則默認情況下該貸款將被視為需求貸款,
This account is used for booking loan repayments from the borrower and also disbursing loans to the borrower,此帳戶用於預訂借款人的還款，也用於向借款人發放貸款,
This account is capital account which is used to allocate capital for loan disbursal account ,該帳戶是資本帳戶，用於為貸款支付帳戶分配資本,
This account will be used for booking loan interest accruals,此帳戶將用於預訂應計貸款利息,
This account will be used for booking penalties levied due to delayed repayments,該帳戶將用於因延遲還款而收取的訂艙罰款,
Variant BOM,變體BOM,
Template Item,模板項目,
Select template item,選擇模板項目,
Select variant item code for the template item {0},選擇模板項目{0}的變體項目代碼,
Downtime Entry,停機時間輸入,
Workstation / Machine,工作站/機器,
Operator,操作員,
In Mins,分鐘,
Downtime Reason,停機原因,
Excessive machine set up time,機器設置時間過多,
Unplanned machine maintenance,計劃外的機器維護,
On-machine press checks,機上印刷檢查,
Machine operator errors,機器操作員錯誤,
Machine malfunction,機器故障,
Electricity down,斷電,
Operation Row Number,操作行號,
Operation {0} added multiple times in the work order {1},操作{0}已在工作訂單{1}中多次添加,
"If ticked, multiple materials can be used for a single Work Order. This is useful if one or more time consuming products are being manufactured.",如果選中，則可以將多個物料用於單個工單。如果要生產一種或多種耗時的產品，這將很有用。,
Backflush Raw Materials,反沖原料,
"The Stock Entry of type 'Manufacture' is known as backflush. Raw materials being consumed to manufacture finished goods is known as backflushing. <br><br> When creating Manufacture Entry, raw-material items are backflushed based on BOM of production item. If you want raw-material items to be backflushed based on Material Transfer entry made against that Work Order instead, then you can set it under this field.",類型“製造”的庫存分錄稱為反沖。生產成品所消耗的原材料稱為反沖。<br><br>創建生產分錄時，將根據生產物料的物料清單對物料物料進行反沖。如果您希望根據針對該工單的物料轉移條目來回算原始物料，則可以在此字段下進行設置。,
Work In Progress Warehouse,在製品倉庫,
This Warehouse will be auto-updated in the Work In Progress Warehouse field of Work Orders.,該倉庫將在工作單的“進行中的倉庫”字段中自動更新。,
Finished Goods Warehouse,成品倉庫,
This Warehouse will be auto-updated in the Target Warehouse field of Work Order.,該倉庫將在工作單的目標倉庫字段中自動更新。,
"If ticked, the BOM cost will be automatically updated based on Valuation Rate / Price List Rate / last purchase rate of raw materials.",如果選中，則物料清單成本將根據評估價/價目表價格/原材料的最後購買價自動更新。,
Source Warehouses (Optional),源倉庫（可選）,
"System will pickup the materials from the selected warehouses. If not specified, system will create material request for purchase.",系統將從選定的倉庫提取物料。如果未指定，系統將創建採購物料申請。,
Lead Time,交貨時間,
PAN Details,PAN詳細信息,
Create Customer,建立客戶,
Invoicing,開票,
Enable Auto Invoicing,啟用自動開票,
Send Membership Acknowledgement,發送會員確認,
Send Invoice with Email,通過電子郵件發送發票,
Membership Print Format,會員打印格式,
Invoice Print Format,發票打印格式,
You can learn more about memberships in the manual. ,您可以在手冊中了解有關會員資格的更多信息。,
ERPNext Docs,ERPNext文檔,
Copy Webhook URL,複製Webhook URL,
Linked Item,關聯項目,
Is Recurring,正在重複,
Rented in Metro City,在都會區租用,
HRA as per Salary Structure,根據薪資結構的HRA,
House Rent Payment Amount,房屋租金支付金額,
Monthly Eligible Amount,每月合格金額,
Total Eligible HRA Exemption,符合條件的HRA總免稅額,
Validating Employee Attendance...,驗證員工出勤情況...,
Submitting Salary Slips and creating Journal Entry...,提交工資單並創建日記帳分錄...,
Calculate Payroll Working Days Based On,根據以下內容計算工資核算工作日,
Consider Unmarked Attendance As,將未標記的出勤視為,
Fraction of Daily Salary for Half Day,半天的日薪分數,
Component Type,組件類型,
Provident Fund,公積金,
Additional Provident Fund,額外公積金,
Provident Fund Loan,公積金貸款,
Professional Tax,專業稅收,
Is Income Tax Component,是所得稅組成部分,
Component properties and references ,組件屬性和參考,
Additional Salary ,額外工資,
Unmarked days,無標記的日子,
Absent Days,缺席天數,
Conditions and Formula variable and example,條件和公式變量以及示例,
Feedback By,反饋者,
Manufacturing Section,製造部,
"By default, the Customer Name is set as per the Full Name entered. If you want Customers to be named by a ",默認情況下，根據輸入的全名設置客戶名。如果您希望客戶以,
Configure the default Price List when creating a new Sales transaction. Item prices will be fetched from this Price List.,在創建新的銷售交易時配置默認的價目表。項目價格將從此價格表中獲取。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Sales Invoice or Delivery Note without creating a Sales Order first. This configuration can be overridden for a particular Customer by enabling the 'Allow Sales Invoice Creation Without Sales Order' checkbox in the Customer master.",如果將此選項配置為“是”，則ERPNext將阻止您創建銷售發票或交貨單，而無需先創建銷售訂單。通過啟用“客戶”主數據中的“允許在沒有銷售訂單的情況下創建銷售發票”複選框，可以為特定客戶覆蓋此配置。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Sales Invoice without creating a Delivery Note first. This configuration can be overridden for a particular Customer by enabling the 'Allow Sales Invoice Creation Without Delivery Note' checkbox in the Customer master.",如果將此選項配置為“是”，則ERPNext將阻止您創建銷售發票而不先創建交貨單。通過啟用客戶主數據中的“允許創建沒有交貨單的銷售發票”複選框，可以為特定客戶覆蓋此配置。,
Default Warehouse for Sales Return,默認退貨倉庫,
Default In Transit Warehouse,默認在途倉庫,
Enable Perpetual Inventory For Non Stock Items,為非庫存項目啟用永久庫存,
HRA Settings,HRA設定,
Basic Component,基本組成,
HRA Component,HRA組件,
Arrear Component,Arrear組件,
Please enter the company name to confirm,請輸入公司名稱進行確認,
Quotation Lost Reason Detail,報價丟失原因明細,
Enable Variants,啟用變體,
Save Quotations as Draft,將報價另存為草稿,
Please Select a Customer,請選擇一個客戶,
Against Delivery Note Item,針對交貨單項目,
Is Non GST ,是非消費稅,
Image Description,圖片描述,
Transfer Status,轉移狀態,
Track this Purchase Receipt against any Project,針對任何項目跟踪此採購收據,
Please Select a Supplier,請選擇供應商,
Set Basic Rate Manually,手動設置基本費率,
"By default, the Item Name is set as per the Item Code entered. If you want Items to be named by a ",默認情況下，根據輸入的項目代碼設置項目名稱。如果您希望項目以,
Set a Default Warehouse for Inventory Transactions. This will be fetched into the Default Warehouse in the Item master.,為庫存交易設置默認倉庫。這將被提取到物料主數據中的默認倉庫中。,
"This will allow stock items to be displayed in negative values. Using this option depends on your use case. With this option unchecked, the system warns before obstructing a transaction that is causing negative stock.",這將使庫存項目顯示為負值。使用此選項取決於您的用例。取消選中此選項，系統會在阻止導致負庫存的交易之前發出警告。,
Choose between FIFO and Moving Average Valuation Methods. Click ,在FIFO和移動平均估值方法之間選擇。請點擊,
 to know more about them.,進一步了解他們。,
Show 'Scan Barcode' field above every child table to insert Items with ease.,在每個子表上方顯示“掃描條形碼”字段，以輕鬆插入項目。,
"Serial numbers for stock will be set automatically based on the Items entered based on first in first out in transactions like Purchase/Sales Invoices, Delivery Notes, etc.",庫存的序列號將根據在採購/銷售發票，交貨單等交易中基於先進先出輸入的項目自動設置。,
"If blank, parent Warehouse Account or company default will be considered in transactions",如果為空白，則將在交易中考慮父倉庫帳戶或公司默認值,
Service Level Agreement Details,服務水平協議詳細信息,
Service Level Agreement Status,服務水平協議狀態,
On Hold Since,暫停後,
Total Hold Time,總保持時間,
Response Details,回應詳情,
Average Response Time,平均響應時間,
User Resolution Time,用戶解析時間,
SLA is on hold since {0},自{0}起，SLA處於保留狀態,
Pause SLA On Status,暫停狀態中的SLA,
Pause SLA On,暫停SLA,
Greetings Section,問候部分,
Greeting Title,問候標題,
Greeting Subtitle,問候字幕,
Youtube Statistics,YouTube統計,
Views,觀看次數,
Dislikes,不喜歡,
Video Settings,影片設定,
Enable YouTube Tracking,啟用YouTube跟踪,
30 mins,30分鐘,
1 hr,1小時,
6 hrs,6小時,
Patient Progress,患者進展,
Targetted,有針對性,
Score Obtained,獲得分數,
Sessions,屆會,
Select Assessment Template,選擇評估模板,
Select Assessment Parameter,選擇評估參數,
Gender: ,性別：,
Contact: ,聯繫：,
Total Therapy Sessions: ,總治療會議：,
Monthly Therapy Sessions: ,每月治療會議：,
Patient Profile,患者簡介,
Point Of Sale,銷售點,
Email sent successfully.,電子郵件發送成功。,
Search by invoice id or customer name,按發票編號或客戶名稱搜索,
Invoice Status,發票狀態,
Filter by invoice status,按發票狀態過濾,
Select item group,選擇項目組,
No items found. Scan barcode again.,未找到任何項目。再次掃描條形碼。,
"Search by customer name, phone, email.",通過客戶名稱，電話，電子郵件進行搜索。,
Enter discount percentage.,輸入折扣百分比。,
Discount cannot be greater than 100%,折扣不能大於100％,
Enter customer's email,輸入客戶的電子郵件,
Enter customer's phone number,輸入客戶的電話號碼,
Customer contact updated successfully.,客戶聯繫人已成功更新。,
Item will be removed since no serial / batch no selected.,由於未選擇序列/批次，因此將刪除項目。,
Discount (%),優惠（％）,
You cannot submit the order without payment.,您必須先付款才能提交訂單。,
You cannot submit empty order.,您不能提交空訂單。,
Create POS Opening Entry,創建POS開幕條目,
Please add Mode of payments and opening balance details.,請添加付款方式和期初餘額詳細信息。,
Toggle Recent Orders,切換最近的訂單,
Save as Draft,保存為草稿,
You must add atleast one item to save it as draft.,您必須添加至少一項才能將其保存為草稿。,
There was an error saving the document.,保存文檔時出錯。,
You must select a customer before adding an item.,在添加項目之前，您必須選擇一個客戶。,
Please Select a Company,請選擇公司,
Active Leads,主動線索,
Please Select a Company.,請選擇一個公司。,
BOM Operations Time,BOM操作時間,
BOM ID,物料清單編號,
BOM Item Code,BOM項目代碼,
Time (In Mins),時間（分鐘）,
Sub-assembly BOM Count,子組件BOM計數,
View Type,查看類型,
Total Delivered Amount,總交付量,
Downtime Analysis,停機時間分析,
Machine,機,
Downtime (In Hours),停機時間（小時）,
Employee Analytics,員工分析,
"""From date"" can not be greater than or equal to ""To date""",“起始日期”不能大於或等於“起始日期”,
Exponential Smoothing Forecasting,指數平滑預測,
First Response Time for Issues,問題的第一響應時間,
First Response Time for Opportunity,機會的第一響應時間,
Depreciatied Amount,折舊額,
Period Based On,期間基於,
Date Based On,日期依據,
{0} and {1} are mandatory,{0}和{1}是強制性的,
Consider Accounting Dimensions,考慮會計維度,
Income Tax Deductions,所得稅減免,
Income Tax Component,所得稅構成,
Income Tax Amount,所得稅金額,
Reserved Quantity for Production,預留生產量,
Projected Quantity,預計數量,
 Total Sales Amount,總銷售金額,
Time Required (In Mins),所需時間（分鐘）,
From Posting Date,從發布日期開始,
To Posting Date,到發布日期,
No records found,沒有找到記錄,
Customer/Lead Name,客戶/姓氏,
Unmarked Days,無標記的日子,
Summarized View,匯總視圖,
Production Planning Report,生產計劃報告,
Order Qty,訂貨量,
Raw Material Code,原材料代碼,
Raw Material Name,原料名稱,
Allotted Qty,分配數量,
Expected Arrival Date,預計到達日期,
Arrival Quantity,到達數量,
Raw Material Warehouse,原料倉庫,
Order By,訂購依據,
Include Sub-assembly Raw Materials,包括子裝配原材料,
Professional Tax Deductions,專業稅收減免,
Program wise Fee Collection,程序明智的收費,
Fees Collected,收費,
Project Summary,項目總結,
Total Tasks,總任務,
Tasks Completed,任務完成,
Tasks Overdue,任務逾期,
Completion,完成時間,
Provident Fund Deductions,公積金扣除,
Purchase Order Analysis,採購訂單分析,
From and To Dates are required.,必須提供“自”和“至”日期。,
To Date cannot be before From Date.,截止日期不能早於截止日期。,
Qty to Bill,賬單數量,
Group by Purchase Order,按採購訂單分組,
 Purchase Value,購買價值,
Total Received Amount,收款總額,
Quality Inspection Summary,質量檢驗總結,
 Quoted Amount,報價金額,
Lead Time (Days),交貨時間（天）,
Include Expired,包括過期,
Applicant name,申請人姓名,
Job Offer status,工作機會狀態,
On Date,時間到了,
Requested Items to Order and Receive,要求訂購和接收的物品,
Salary Payments Based On Payment Mode,基於付款方式的工資支付,
Salary Payments via ECS,通過ECS支付工資,
Account No,戶口號碼,
Sales Order Analysis,銷售訂單分析,
Amount Delivered,交付金額,
Delay (in Days),延誤（天）,
Group by Sales Order,按銷售訂單分組,
 Sales Value,銷售價值,
Stock Qty vs Serial No Count,庫存數量vs序列號不計,
Serial No Count,序列號,
Work Order Summary,工作單摘要,
Produce Qty,產生數量,
Lead Time (in mins),交貨時間（以分鐘為單位）,
Charts Based On,基於的圖表,
YouTube Interactions,YouTube互動,
Published Date,發布日期,
Barnch,倒鉤,
Select a Company,選擇公司,
Opportunity {0} created,機會{0}已創建,
Kindly select the company first,請首先選擇公司,
Please enter From Date and To Date to generate JSON,請輸入起始日期和截止日期以生成JSON,
PF Account,PF賬戶,
PF Amount,PF金額,
PF Loan,PF貸款,
Download DATEV File,下載DATEV文件,
Numero has not set in the XML file,Numero尚未在XML文件中設置,
Inward Supplies(liable to reverse charge),內向耗材（易於反向充電）,
This is based on the course schedules of this Instructor,這是基於該教師的課程表,
Course and Assessment,課程與評估,
Course {0} has been added to all the selected programs successfully.,課程{0}已成功添加到所有選定程序。,
Program and Course,課程與課程,
{0} or {1} is mandatory,{0}或{1}是強制性的,
Mandatory Fields,必須填寫,
Student {0}: {1} does not belong to Student Group {2},學生{0}：{1}不屬於學生組{2},
Student Attendance record {0} already exists against the Student {1},針對學生{1}的學生出勤記錄{0}已存在,
Duplicate Entry,雙重輸入,
Course and Fee,課程和費用,
Not eligible for the admission in this program as per Date Of Birth,根據出生日期，不符合該計劃的入學條件,
Topic {0} has been added to all the selected courses successfully.,主題{0}已成功添加到所有所選課程。,
Courses updated,課程更新,
{0} {1} has been added to all the selected topics successfully.,{0} {1}已成功添加到所有選定主題。,
Topics updated,主題已更新,
Academic Term and Program,學期和課程,
Please remove this item and try to submit again or update the posting time.,請刪除該項目，然後嘗試再次提交或更新發佈時間。,
Failed to Authenticate the API key.,無法驗證API密鑰。,
Invalid Credentials,無效證件,
URL can only be a string,網址只能是一個字符串,
"Here is your webhook secret, this will be shown to you only once.",這是您的Webhook機密，此秘密僅顯示給您一次。,
The payment for this membership is not paid. To generate invoice fill the payment details,未支付此會員資格的費用。要生成發票，請填寫付款明細,
An invoice is already linked to this document,發票已鏈接到該文件,
No customer linked to member {},沒有客戶鏈接到成員{},
You need to set <b>Debit Account</b> in Membership Settings,您需要在會員設置中設置<b>借記帳戶</b>,
You need to set <b>Default Company</b> for invoicing in Membership Settings,您需要在會員設置中設置<b>默認公司</b>以開發票,
You need to enable <b>Send Acknowledge Email</b> in Membership Settings,您需要在會員設置中啟用<b>發送確認電子郵件</b>,
Error creating membership entry for {0},為{0}創建成員資格條目時出錯,
A customer is already linked to this Member,客戶已經鏈接到該會員,
End Date must not be lesser than Start Date,結束日期不得小於開始日期,
Employee {0} already has Active Shift {1}: {2},員工{0}已具有活動班次{1}：{2},
 from {0},來自{0},
Please select Employee first.,請首先選擇員工。,
Please set {0} for the Employee or for Department: {1},請為員工或部門設置{0}：{1},
To Date should be greater than From Date,截止日期應大於起始日期,
Employee Onboarding: {0} is already for Job Applicant: {1},員工入職：{0}已適用於求職者：{1},
Job Offer: {0} is already for Job Applicant: {1},職位空缺：{0}已提供給職位申請人：{1},
Only Shift Request with status 'Approved' and 'Rejected' can be submitted,只能提交狀態為“已批准”和“已拒絕”的輪班請求,
Shift Assignment: {0} created for Employee: {1},輪班分配：為員工{1}創建的{0},
You can not request for your Default Shift: {0},您無法請求默認班次：{0},
Only Approvers can Approve this Request.,只有批准者可以批准此請求。,
Asset Value Analytics,資產價值分析,
Category-wise Asset Value,類別資產價值,
Total Assets,總資產,
New Assets (This Year),新資產（今年）,
Row #{}: Depreciation Posting Date should not be equal to Available for Use Date.,第{}行：折舊過帳日期不應等於可用日期。,
Incorrect Date,日期不正確,
Invalid Gross Purchase Amount,無效的總購買金額,
There are active maintenance or repairs against the asset. You must complete all of them before cancelling the asset.,對資產進行了積極的維護或修理。您必須先完成所有操作，然後才能取消資產。,
Back to Course,回到課程,
Finish Topic,完成主題,
Mins,分鐘,
by,通過,
Enrolling...,正在註冊...,
You have successfully enrolled for the program ,您已成功註冊該計劃,
Enrolled,已入學,
Watch Intro,觀看介紹,
We're here to help!,我們在這里為您提供幫助！,
Frequently Read Articles,經常閱讀的文章,
Please set a default company address,請設置默認公司地址,
{0} is not a valid state! Check for typos or enter the ISO code for your state.,{0}不是有效狀態！檢查拼寫錯誤或輸入您所在州的ISO代碼。,
Error occured while parsing Chart of Accounts: Please make sure that no two accounts have the same name,解析會計科目表時發生錯誤：請確保沒有兩個帳戶具有相同的名稱,
Plaid invalid request error,格子無效的請求錯誤,
Please check your Plaid client ID and secret values,請檢查您的格子客戶ID和機密值,
Bank transaction creation error,銀行交易創建錯誤,
Unit of Measurement,測量單位,
Fiscal Year {0} Does Not Exist,會計年度{0}不存在,
Row # {0}: Returned Item {1} does not exist in {2} {3},第＃0行：{2} {3}中不存在返回的項目{1},
Valuation type charges can not be marked as Inclusive,評估類型的費用不能標記為包含,
You do not have permissions to {} items in a {}.,您無權訪問{}中的{}個項目。,
Insufficient Permissions,權限不足,
You are not allowed to update as per the conditions set in {} Workflow.,您無法按照{}工作流程中設置的條件進行更新。,
Expense Account Missing,費用帳戶丟失,
{0} is not a valid Value for Attribute {1} of Item {2}.,{0}不是項{2}的屬性{1}的有效值。,
Invalid Value,無效值,
The value {0} is already assigned to an existing Item {1}.,值{0}已分配給現有項{1}。,
"To still proceed with editing this Attribute Value, enable {0} in Item Variant Settings.",要繼續編輯該屬性值，請在“項目變式設置”中啟用{0}。,
Edit Not Allowed,不允許編輯,
Row #{0}: Item {1} is already fully received in Purchase Order {2},第＃0行：採購訂單{2}中已完全收到項目{1},
You cannot create or cancel any accounting entries with in the closed Accounting Period {0},您無法在關閉的會計期間{0}中創建或取消任何會計分錄,
POS Invoice should have {} field checked.,POS發票應選中{}字段。,
Invalid Item,無效的項目,
Row #{}: You cannot add postive quantities in a return invoice. Please remove item {} to complete the return.,第{}行：您不能在退貨發票中添加肯定數量。請刪除項目{}以完成退貨。,
The selected change account {} doesn't belongs to Company {}.,所選的更改帳戶{}不屬於公司{}。,
Atleast one invoice has to be selected.,必須選擇至少一張發票。,
Payment methods are mandatory. Please add at least one payment method.,付款方式是強制性的。請添加至少一種付款方式。,
Please select a default mode of payment,請選擇默認付款方式,
You can only select one mode of payment as default,您只能選擇一種付款方式作為默認付款方式,
Missing Account,帳戶遺失,
Customers not selected.,未選擇客戶。,
Statement of Accounts,決算報告,
Ageing Report Based On ,基於的賬齡報告,
Please enter distributed cost center,請進入分佈式成本中心,
Total percentage allocation for distributed cost center should be equal to 100,分佈式成本中心的總百分比分配應等於100,
Cannot enable Distributed Cost Center for a Cost Center already allocated in another Distributed Cost Center,無法為已在另一個分佈式成本中心中分配的成本中心啟用分佈式成本中心,
Parent Cost Center cannot be added in Distributed Cost Center,父成本中心不能添加到分佈式成本中心中,
A Distributed Cost Center cannot be added in the Distributed Cost Center allocation table.,無法在“分佈式成本中心”分配表中添加“分佈式成本中心”。,
Cost Center with enabled distributed cost center can not be converted to group,啟用了分佈式成本中心的成本中心無法轉換為組,
Cost Center Already Allocated in a Distributed Cost Center cannot be converted to group,無法將已分配在分佈式成本中心中的成本中心轉換為組,
Trial Period Start date cannot be after Subscription Start Date,試用期開始日期不能晚於訂閱開始日期,
Subscription End Date must be after {0} as per the subscription plan,根據訂閱計劃，訂閱結束日期必須在{0}之後,
Subscription End Date is mandatory to follow calendar months,訂閱結束日期必須遵循日曆月,
Row #{}: POS Invoice {} is not against customer {},第＃{}行：POS發票{}不針對客戶{},
Row #{}: POS Invoice {} is not submitted yet,第{}行：尚未提交POS發票{},
Row #{}: POS Invoice {} has been {},第＃{}行：POS發票{}已被{},
No Supplier found for Inter Company Transactions which represents company {0},找不到代表公司{0}的公司間交易的供應商,
No Customer found for Inter Company Transactions which represents company {0},找不到代表公司{0}的公司間交易的客戶,
Invalid Period,無效期間,
Selected POS Opening Entry should be open.,所選的POS入口條目應打開。,
Invalid Opening Entry,無效的開幕詞,
Please set a Company,請設置公司,
"Sorry, this coupon code's validity has not started",抱歉，此優惠券代碼的有效性尚未開始,
"Sorry, this coupon code's validity has expired",抱歉，此優惠券代碼的有效性已過期,
"Sorry, this coupon code is no longer valid",抱歉，此優惠券代碼不再有效,
For the 'Apply Rule On Other' condition the field {0} is mandatory,對於“其他應用規則”條件，字段{0}是必填字段,
{1} Not in Stock,{1}無庫存,
Only {0} in Stock for item {1},項目{1}的庫存中只有{0},
Please enter a coupon code,請輸入優惠券代碼,
Please enter a valid coupon code,請輸入有效的優惠券代碼,
Invalid Child Procedure,無效的子程序,
Import Italian Supplier Invoice.,導入意大利供應商發票。,
"Valuation Rate for the Item {0}, is required to do accounting entries for {1} {2}.",要為{1} {2}進行會計分錄，必須使用項目{0}的評估率。,
 Here are the options to proceed:,以下是繼續進行的選項：,
"If the item is transacting as a Zero Valuation Rate item in this entry, please enable 'Allow Zero Valuation Rate' in the {0} Item table.",如果該項在本條目中正在作為零評估率項目進行交易，請在{0}項表中啟用“允許零評估率”。,
"If not, you can Cancel / Submit this entry ",如果沒有，您可以取消/提交此條目,
 performing either one below:,執行以下任一操作：,
Create an incoming stock transaction for the Item.,創建物料的進貨庫存交易。,
Mention Valuation Rate in the Item master.,物料主數據中的提及評估率。,
Valuation Rate Missing,估價率缺失,
Serial Nos Required,需要序列號,
Quantity Mismatch,數量不匹配,
"Please Restock Items and Update the Pick List to continue. To discontinue, cancel the Pick List.",請重新入庫並更新選擇清單以繼續。要中止，取消選擇列表。,
Out of Stock,缺貨,
{0} units of Item {1} is not available.,項目{1}的{0}個單位不可用。,
Item for row {0} does not match Material Request,第{0}行的項目與物料請求不匹配,
Warehouse for row {0} does not match Material Request,第{0}行的倉庫與物料請求不匹配,
Accounting Entry for Service,服務會計分錄,
All items have already been Invoiced/Returned,所有商品均已開票/退貨,
All these items have already been Invoiced/Returned,所有這些物品已經開票/退貨,
Stock Reconciliations,庫存對帳,
Merge not allowed,不允許合併,
The following deleted attributes exist in Variants but not in the Template. You can either delete the Variants or keep the attribute(s) in template.,以下已刪除的屬性存在於變式中，但不在模板中。您可以刪除變體，也可以將屬性保留在模板中。,
Variant Items,變體物品,
Variant Attribute Error,變體屬性錯誤,
The serial no {0} does not belong to item {1},序列號{0}不屬於項目{1},
There is no batch found against the {0}: {1},找不到針對{0}的批次：{1},
Completed Operation,完成作業,
Work Order Analysis,工單分析,
Quality Inspection Analysis,質量檢驗分析,
Pending Work Order,待處理的工作單,
Last Month Downtime Analysis,上個月停機時間分析,
Work Order Qty Analysis,工單數量分析,
Monthly Total Work Orders,每月總工單,
Monthly Completed Work Orders,每月完成的工作單,
Ongoing Job Cards,持續的工作卡,
Monthly Quality Inspections,每月質量檢查,
(Forecast),（預測）,
Total Demand (Past Data),總需求（過去數據）,
Total Forecast (Past Data),總預測（過去數據）,
Total Forecast (Future Data),總預測（未來數據）,
Based On Document,基於文件,
Based On Data ( in years ),基於數據（以年為單位）,
Smoothing Constant,平滑常數,
Please fill the Sales Orders table,請填寫銷售訂單表,
Sales Orders Required,需要銷售訂單,
Please fill the Material Requests table,請填寫材料申請表,
Items to Manufacture are required to pull the Raw Materials associated with it.,需要製造的物品才能拉動與其關聯的原材料。,
Items Required,必填項,
Operation {0} does not belong to the work order {1},操作{0}不屬於工作訂單{1},
Print UOM after Quantity,數量後打印UOM,
Set default {0} account for perpetual inventory for non stock items,為非庫存項目的永久庫存設置默認的{0}帳戶,
Loan Security {0} added multiple times,貸款安全性{0}已多次添加,
Loan Securities with different LTV ratio cannot be pledged against one loan,不同LTV比率的貸款證券不能以一項貸款作為抵押,
Qty or Amount is mandatory for loan security!,數量或金額對於貸款擔保而言是強制性的！,
Only submittted unpledge requests can be approved,只有已提交的未承諾請求可以被批准,
Interest Amount or Principal Amount is mandatory,利息金額或本金金額是強制性的,
Disbursed Amount cannot be greater than {0},支出金額不能大於{0},
Row {0}: Loan Security {1} added multiple times,第{0}行：多次添加了貸款安全性{1},
Row #{0}: Child Item should not be a Product Bundle. Please remove Item {1} and Save,第＃{0}行：子項不應是產品捆綁包。請刪除項目{1}並保存,
Credit limit reached for customer {0},客戶{0}已達到信用額度,
Could not auto create Customer due to the following missing mandatory field(s):,由於缺少以下必填字段，因此無法自動創建客戶：,
Please create Customer from Lead {0}.,請根據潛在客戶{0}創建客戶。,
Mandatory Missing,必填項,
Please set Payroll based on in Payroll settings,請根據薪資設置設置薪資,
Additional Salary: {0} already exist for Salary Component: {1} for period {2} and {3},工資成分：{1}的{2}和{3}期間已經存在附加工資：{0},
From Date can not be greater than To Date.,起始日期不能大於截止日期。,
Payroll date can not be less than employee's joining date.,薪資日期不能少於員工的入職日期。,
From date can not be less than employee's joining date.,起始日期不能少於員工的加入日期。,
To date can not be greater than employee's relieving date.,迄今為止不能大於僱員的救濟日期。,
Payroll date can not be greater than employee's relieving date.,薪資日期不能大於員工的救濟日期。,
Row #{0}: Please enter the result value for {1},第＃0行：請輸入{1}的結果值,
Mandatory Results,強制性結果,
Sales Invoice or Patient Encounter is required to create Lab Tests,創建實驗室測試需要銷售發票或患者En,
Insufficient Data,資料不足,
Lab Test(s) {0} created successfully,實驗測試{0}已成功創建,
Test :,測試：,
Sample Collection {0} has been created,樣品採集{0}已創建,
Normal Range: ,普通範圍：,
Row #{0}: Check Out datetime cannot be less than Check In datetime,第＃{0}行：簽出日期時間不能小於簽入日期時間,
"Missing required details, did not create Inpatient Record",缺少必需的詳細信息，未創建住院記錄,
Unbilled Invoices,未開票發票,
Standard Selling Rate should be greater than zero.,標准銷售率應大於零。,
Conversion Factor is mandatory,轉換因子為必填項,
Row #{0}: Conversion Factor is mandatory,第＃0行：轉換因子為必填項,
Sample Quantity cannot be negative or 0,樣品數量不能為負或0,
Invalid Quantity,無效數量,
"Please set defaults for Customer Group, Territory and Selling Price List in Selling Settings",請在銷售設置中為客戶組，地區和銷售價格表設置默認值,
{0} with {1},{0}與{1},
Appointment Confirmation Message Not Sent,未發送約會確認消息,
"SMS not sent, please check SMS Settings",短信未發送，請檢查短信設置,
Healthcare Service Unit Type cannot have both {0} and {1},醫療保健服務單位類型不能同時具有{0}和{1},
Healthcare Service Unit Type must allow atleast one among {0} and {1},醫療服務單位類型必須允許{0}和{1}中的至少一個,
Set Response Time and Resolution Time for Priority {0} in row {1}.,在行{1}中設置優先級{0}的響應時間和解決時間。,
Response Time for {0} priority in row {1} can't be greater than Resolution Time.,第{1}行中{0}優先級的響應時間不能大於“解決時間”。,
{0} is not enabled in {1},{1}中未啟用{0},
Group by Material Request,按材料要求分組,
Email Sent to Supplier {0},通過電子郵件發送給供應商{0},
"The Access to Request for Quotation From Portal is Disabled. To Allow Access, Enable it in Portal Settings.",禁止從門戶網站訪問報價請求。要允許訪問，請在門戶設置中啟用它。,
Supplier Quotation {0} Created,供應商報價{0}已創建,
Valid till Date cannot be before Transaction Date,有效期至日期不能早於交易日期,
Unlink Advance Payment on Cancellation of Order,取消訂單時取消預付款鏈接,
"Simple Python Expression, Example: territory != 'All Territories'",簡單的Python表達式，例如：region！=&#39;All Territories&#39;,
Sales Contributions and Incentives,銷售貢獻和激勵,
Sourced by Supplier,由供應商採購,
Total weightage assigned should be 100%.<br>It is {0},分配的總重量應為100％。<br>是{0},
Account {0} exists in parent company {1}.,帳戶{0}在母公司{1}中。,
"To overrule this, enable '{0}' in company {1}",要否決此問題，請在公司{1}中啟用“ {0}”,
Invalid condition expression,條件表達式無效,
Please Select a Company First,請先選擇公司,
Please Select Both Company and Party Type First,請首先選擇公司和派對類型,
Provide the invoice portion in percent,提供發票百分比,
Give number of days according to prior selection,根據事先選擇給出天數,
Email Details,電子郵件詳細資料,
"Select a greeting for the receiver. E.g. Mr., Ms., etc.",選擇收件人的問候語。例如先生，女士等,
Preview Email,預覽電子郵件,
Please select a Supplier,請選擇供應商,
Supplier Lead Time (days),供應商交貨時間（天）,
Exit Interview Held On,退出面試舉行,
Condition and formula,條件和公式,
Sets 'Target Warehouse' in each row of the Items table.,在“物料”表的每一行中設置“目標倉庫”。,
Sets 'Source Warehouse' in each row of the Items table.,在“物料”表的每一行中設置“源倉庫”。,
POS Register,POS收銀機,
"Can not filter based on POS Profile, if grouped by POS Profile",如果按POS配置文件分組，則無法基於POS配置文件進行過濾,
"Can not filter based on Customer, if grouped by Customer",如果按客戶分組，則無法根據客戶進行過濾,
"Can not filter based on Cashier, if grouped by Cashier",如果按收銀員分組，則無法根據收銀員進行過濾,
"Can not filter based on Payment Method, if grouped by Payment Method",如果按付款方式分組，則無法基於付款方式進行過濾,
Supplier Quotation Comparison,供應商報價比較,
Price per Unit (Stock UOM),單價（庫存單位）,
Group by Supplier,按供應商分組,
Group by Item,按項目分組,
Remember to set {field_label}. It is required by {regulation}.,請記住設置{field_label}。 {regulation}要求它。,
Enrollment Date cannot be before the Start Date of the Academic Year {0},入學日期不能早於學年的開始日期{0},
Enrollment Date cannot be after the End Date of the Academic Term {0},入學日期不能晚於學期結束日期{0},
Enrollment Date cannot be before the Start Date of the Academic Term {0},入學日期不能早於學期開始日期{0},
Future Posting Not Allowed,不允許將來發布,
"To enable Capital Work in Progress Accounting, ",要啟用基本工程進度會計，,
you must select Capital Work in Progress Account in accounts table,您必須在帳戶表中選擇正在進行的資本工程帳戶,
You can also set default CWIP account in Company {},您還可以在公司{}中設置默認的CWIP帳戶,
The Request for Quotation can be accessed by clicking on the following button,點擊以下按鈕可以訪問報價請求,
Regards,問候,
Please click on the following button to set your new password,請點擊以下按鈕設置新密碼,
Update Password,更新密碼,
Row #{}: Selling rate for item {} is lower than its {}. Selling {} should be atleast {},第＃{}行：商品{}的銷售價格低於其{}。出售{}應該至少{},
You can alternatively disable selling price validation in {} to bypass this validation.,您也可以在{}中禁用售價驗證，以繞過此驗證。,
Invalid Selling Price,無效的售價,
Address needs to be linked to a Company. Please add a row for Company in the Links table.,地址需要鏈接到公司。請在“鏈接”表中為“公司”添加一行。,
Company Not Linked,公司未鏈接,
Import Chart of Accounts from CSV / Excel files,從CSV / Excel文件導入會計科目表,
Completed Qty cannot be greater than 'Qty to Manufacture',完成的數量不能大於“製造數量”,
"Row {0}: For Supplier {1}, Email Address is Required to send an email",第{0}行：對於供應商{1}，需要電子郵件地址才能發送電子郵件,
"If enabled, the system will post accounting entries for inventory automatically",如果啟用，系統將自動過帳庫存的會計分錄,
Accounts Frozen Till Date,帳戶凍結日期,
Accounting entries are frozen up to this date. Nobody can create or modify entries except users with the role specified below,截止到此日期，會計條目被凍結。除具有以下指定角色的用戶外，任何人都無法創建或修改條目,
Role Allowed to Set Frozen Accounts and Edit Frozen Entries,允許角色設置凍結帳戶和編輯凍結條目,
Address used to determine Tax Category in transactions,用於確定交易中稅種的地址,
"The percentage you are allowed to bill more against the amount ordered. For example, if the order value is $100 for an item and tolerance is set as 10%, then you are allowed to bill up to $110 ",您可以針對所訂購的金額開具更多費用的百分比。例如，如果某商品的訂單價值為$ 100且容差設置為10％，則您最多可收取$ 110的費用,
This role is allowed to submit transactions that exceed credit limits,允許該角色提交超出信用額度的交易,
"If ""Months"" is selected, a fixed amount will be booked as deferred revenue or expense for each month irrespective of the number of days in a month. It will be prorated if deferred revenue or expense is not booked for an entire month",如果選擇“月”，則固定金額將記為每月的遞延收入或費用，而與一個月中的天數無關。如果遞延的收入或費用沒有整月預定，則將按比例分配,
"If this is unchecked, direct GL entries will be created to book deferred revenue or expense",如果未選中此復選框，則將創建直接總帳分錄以預定遞延收入或費用,
Show Inclusive Tax in Print,在打印中顯示含稅,
Only select this if you have set up the Cash Flow Mapper documents,僅在設置了現金流量映射器文檔後才選擇此選項,
Is Purchase Order Required for Purchase Invoice & Receipt Creation?,採購發票和收貨創建是否需要採購訂單？,
Is Purchase Receipt Required for Purchase Invoice Creation?,創建採購發票是否需要採購收據？,
Maintain Same Rate Throughout the Purchase Cycle,在整個購買週期中保持相同的費率,
Allow Item To Be Added Multiple Times in a Transaction,允許在事務中多次添加項目,
Suppliers,供應商,
Send Emails to Suppliers,發送電子郵件給供應商,
Select a Supplier,選擇供應商,
Cannot mark attendance for future dates.,無法標記出將來的日期。,
Do you want to update attendance? <br> Present: {0} <br> Absent: {1},您要更新出勤率嗎？<br>目前：{0}<br>缺席：{1},
Mpesa Settings,Mpesa設置,
Initiator Name,發起方名稱,
Till Number,耕種數,
 Online PassKey,在線密碼,
Security Credential,安全憑證,
Get Account Balance,獲取帳戶餘額,
Please set the initiator name and the security credential,請設置發起方名稱和安全憑證,
Inpatient Medication Entry,住院藥物輸入,
Item Code (Drug),物品代碼（藥品）,
Medication Orders,藥物訂單,
Get Pending Medication Orders,獲取待處理的藥物訂單,
Inpatient Medication Orders,住院用藥單,
Medication Warehouse,藥物倉庫,
Warehouse from where medication stock should be consumed,應從那裡消耗藥品庫存的倉庫,
Fetching Pending Medication Orders,提取待處理的藥物訂單,
Inpatient Medication Entry Detail,住院藥物輸入詳細信息,
Medication Details,用藥細節,
Drug Code,藥品代碼,
Drug Name,藥品名稱,
Against Inpatient Medication Order,反對住院用藥令,
Against Inpatient Medication Order Entry,反對住院藥物訂單輸入,
Inpatient Medication Order,住院用藥令,
Total Orders,訂單總數,
Completed Orders,已完成的訂單,
Add Medication Orders,添加藥物訂單,
Adding Order Entries,添加訂單條目,
{0} medication orders completed,{0}個藥物訂單已完成,
{0} medication order completed,{0}個藥物訂單已完成,
Inpatient Medication Order Entry,住院藥物訂單輸入,
Is Order Completed,訂單完成了嗎,
Employee Records to Be Created By,要創建的員工記錄,
Employee records are created using the selected field,使用所選字段創建員工記錄,
Don't send employee birthday reminders,不要發送員工生日提醒,
Restrict Backdated Leave Applications,限制回請假申請,
Sequence ID,序列號,
Sequence Id,序列編號,
Allow multiple material consumptions against a Work Order,允許根據工單消耗多種物料,
Plan time logs outside Workstation working hours,計劃工作站工作時間以外的時間日誌,
Plan operations X days in advance,提前X天計劃運營,
Time Between Operations (Mins),間隔時間（分鐘）,
Default: 10 mins,默認值：10分鐘,
Overproduction for Sales and Work Order,銷售和工單的生產過剩,
"Update BOM cost automatically via scheduler, based on the latest Valuation Rate/Price List Rate/Last Purchase Rate of raw materials",根據最新的評估價/清單價格/原材料的最新購買價，通過計劃程序自動更新BOM成本,
Purchase Order already created for all Sales Order items,已經為所有銷售訂單項目創建了採購訂單,
Select Items,選擇項目,
Against Default Supplier,針對默認供應商,
Auto close Opportunity after the no. of days mentioned above,否後自動關閉機會。上述天數,
Is Sales Order Required for Sales Invoice & Delivery Note Creation?,創建銷售發票和交貨單是否需要銷售訂單？,
Is Delivery Note Required for Sales Invoice Creation?,創建銷售發票是否需要交貨單？,
How often should Project and Company be updated based on Sales Transactions?,應根據銷售交易更新項目和公司的頻率？,
Allow User to Edit Price List Rate in Transactions,允許用戶編輯交易中的價目表價格,
Allow Item to Be Added Multiple Times in a Transaction,允許在事務中多次添加項目,
Allow Multiple Sales Orders Against a Customer's Purchase Order,允許針對客戶的採購訂單的多個銷售訂單,
Validate Selling Price for Item Against Purchase Rate or Valuation Rate,根據購買率或評估率驗證項目的售價,
Hide Customer's Tax ID from Sales Transactions,從銷售交易中隱藏客戶的稅號,
"The percentage you are allowed to receive or deliver more against the quantity ordered. For example, if you have ordered 100 units, and your Allowance is 10%, then you are allowed to receive 110 units.",相對於訂購數量，您可以接收或交付更多的百分比。例如，如果您訂購了100個單位，而您的津貼為10％，那麼您將獲得110個單位。,
Action If Quality Inspection Is Not Submitted,未提交質量檢驗的措施,
Auto Insert Price List Rate If Missing,缺少時自動插入價目表價格,
Automatically Set Serial Nos Based on FIFO,基於FIFO自動設置序列號,
Set Qty in Transactions Based on Serial No Input,根據無序列號輸入設置交易數量,
Raise Material Request When Stock Reaches Re-order Level,庫存達到再訂購水平時提高物料請求,
Notify by Email on Creation of Automatic Material Request,通過電子郵件通知創建自動物料請求,
Allow Material Transfer from Delivery Note to Sales Invoice,允許物料從交貨單轉移到銷售發票,
Allow Material Transfer from Purchase Receipt to Purchase Invoice,允許從收貨到採購發票的物料轉移,
Freeze Stocks Older Than (Days),凍結大於（天）的股票,
Role Allowed to Edit Frozen Stock,允許角色編輯凍結庫存,
The unallocated amount of Payment Entry {0} is greater than the Bank Transaction's unallocated amount,付款條目{0}的未分配金額大於銀行交易的未分配金額,
Attendance cannot be marked outside of Academic Year {0},無法在學年{0}以外標記出勤,
Student is already enrolled via Course Enrollment {0},已經通過課程註冊{0}來註冊學生,
Attendance cannot be marked for future dates.,無法標記出將來的出勤日期。,
Please add programs to enable admission application.,請添加程序以啟用入學申請。,
The following employees are currently still reporting to {0}:,以下員工目前仍在向{0}報告：,
Please make sure the employees above report to another Active employee.,請確保上述員工向另一位在職員工報告。,
Cannot Relieve Employee,無法解僱員工,
Please enter {0},請輸入{0},
Please select another payment method. Mpesa does not support transactions in currency '{0}',請選擇其他付款方式。 Mpesa不支持使用貨幣“ {0}”的交易,
Transaction Error,交易錯誤,
Mpesa Express Transaction Error,Mpesa Express交易錯誤,
"Issue detected with Mpesa configuration, check the error logs for more details",使用Mpesa配置檢測到問題，請查看錯誤日誌以獲取更多詳細信息,
Mpesa Express Error,Mpesa Express錯誤,
Account Balance Processing Error,帳戶餘額處理錯誤,
Please check your configuration and try again,請檢查您的配置，然後重試,
Mpesa Account Balance Processing Error,Mpesa帳戶餘額處理錯誤,
Balance Details,餘額明細,
Current Balance,當前餘額,
Available Balance,可用餘額,
Reserved Balance,預留餘額,
Uncleared Balance,未結餘額,
Payment related to {0} is not completed,與{0}相關的付款尚未完成,
Row #{}: Item Code: {} is not available under warehouse {}.,第{}行：項目代碼：{}在倉庫{}下不可用。,
Row #{}: Stock quantity not enough for Item Code: {} under warehouse {}. Available quantity {}.,第＃{}行：倉庫{}下的庫存數量不足以用於項目代碼{}。可用數量{}。,
Row #{}: Please select a serial no and batch against item: {} or remove it to complete transaction.,第{}行：請選擇一個序列號，並針對{}進行批處理或將其刪除以完成交易。,
Row #{}: No serial number selected against item: {}. Please select one or remove it to complete transaction.,行＃{}：未針對項目{}選擇序列號。請選擇一項或將其刪除以完成交易。,
Row #{}: No batch selected against item: {}. Please select a batch or remove it to complete transaction.,行＃{}：未針對項目{}選擇批次。請選擇一個批次或將其刪除以完成交易。,
Payment amount cannot be less than or equal to 0,付款金額不能小於或等於0,
Please enter the phone number first,請先輸入電話號碼,
Row #{0}: {1} is required to create the Opening {2} Invoices,行＃{0}：創建期初{2}發票需要{1},
You had {} errors while creating opening invoices. Check {} for more details,創建期初發票時出現{}個錯誤。檢查{}了解更多詳細信息,
Error Occured,發生了錯誤,
Opening Invoice Creation In Progress,進行中的開立發票創建,
Creating {} out of {} {},在{} {}中創建{},
(Serial No: {0}) cannot be consumed as it's reserverd to fullfill Sales Order {1}.,（序列號：{0}）無法使用，因為它是完成銷售訂單{1}的保留。,
Item {0} {1},項目{0} {1},
Last Stock Transaction for item {0} under warehouse {1} was on {2}.,倉庫{1}下項目{0}的上次庫存交易在{2}上。,
Stock Transactions for Item {0} under warehouse {1} cannot be posted before this time.,在此之前，不能過帳倉庫{1}下物料{0}的庫存交易。,
Posting future stock transactions are not allowed due to Immutable Ledger,由於總帳不可變，不允許過帳未來的股票交易,
A BOM with name {0} already exists for item {1}.,項目{1}的名稱為{0}的BOM已存在。,
{0}{1} Did you rename the item? Please contact Administrator / Tech support,{0} {1}您是否重命名了該項目？請聯繫管理員/技術支持,
At row #{0}: the sequence id {1} cannot be less than previous row sequence id {2},在第{0}行：序列ID {1}不能小於上一行的序列ID {2},
The {0} ({1}) must be equal to {2} ({3}),{0}（{1}）必須等於{2}（{3}）,
Cannot ensure delivery by Serial No as Item {0} is added with and without Ensure Delivery by Serial No.,無法確保按序列號交貨，因為添加和不保證按序列號交貨都添加了項目{0}。,
Item {0} has no Serial No. Only serilialized items can have delivery based on Serial No,物料{0}沒有序列號。只有序列化的物料才能根據序列號交貨,
No active BOM found for item {0}. Delivery by Serial No cannot be ensured,找不到項目{0}的活動BOM。無法確保按序列號交貨,
No pending medication orders found for selected criteria,找不到符合所選條件的待處理藥物訂單,
From Date cannot be after the current date.,起始日期不能晚於當前日期。,
To Date cannot be after the current date.,截止日期不能晚於當前日期。,
From Time cannot be after the current time.,“開始時間”不能晚於當前時間。,
To Time cannot be after the current time.,到時間不能晚於當前時間。,
Stock Entry {0} created and ,創建庫存條目{0}並,
Inpatient Medication Orders updated successfully,住院用藥單已成功更新,
Row {0}: Cannot create Inpatient Medication Entry against cancelled Inpatient Medication Order {1},第{0}行：無法針對已取消的住院藥物命令{1}創建住院藥物分錄,
Row {0}: This Medication Order is already marked as completed,第{0}行：此藥物訂單已被標記為已完成,
Quantity not available for {0} in warehouse {1},倉庫{1}中{0}不可用的數量,
Please enable Allow Negative Stock in Stock Settings or create Stock Entry to proceed.,請啟用“允許庫存設置中的負庫存”或創建“庫存輸入”以繼續。,
No Inpatient Record found against patient {0},找不到針對患者{0}的住院記錄,
An Inpatient Medication Order {0} against Patient Encounter {1} already exists.,針對患者遭遇{1}的住院藥物命令{0}已存在。,
Allow In Returns,允許退貨,
Hide Unavailable Items,隱藏不可用的物品,
Apply Discount on Discounted Rate,對折現率應用折扣,
Therapy Plan Template,治療計劃模板,
Fetching Template Details,提取模板詳細信息,
Linked Item Details,鏈接項目詳細信息,
Therapy Types,治療類型,
Therapy Plan Template Detail,治療計劃模板詳細信息,
Process Owner,流程負責人,
Corrective Action,糾正措施,
Preventive Action,預防措施,
Problem,問題,
Responsible,負責任的,
Right Index,正確的索引,
Passed,已通過,
Print Receipt,打印收據,
Edit Receipt,編輯收據,
Focus on search input,專注於搜索輸入,
Focus on Item Group filter,專注於項目組過濾器,
Checkout Order / Submit Order / New Order,結帳訂單/提交訂單/新訂單,
Add Order Discount,添加訂單折扣,
Item Code: {0} is not available under warehouse {1}.,項目代碼：{0}在倉庫{1}下不可用。,
Serial numbers unavailable for Item {0} under warehouse {1}. Please try changing warehouse.,倉庫{1}下項目{0}的序列號不可用。請嘗試更換倉庫。,
Fetched only {0} available serial numbers.,僅獲取了{0}個可用序列號。,
Switch Between Payment Modes,在付款模式之間切換,
Enter {0} amount.,輸入{0}金額。,
You don't have enough points to redeem.,您的積分不足以兌換。,
You can redeem upto {0}.,您最多可以兌換{0}。,
Enter amount to be redeemed.,輸入要兌換的金額。,
You cannot redeem more than {0}.,您最多只能兌換{0}個。,
Open Form View,打開表單視圖,
POS invoice {0} created succesfully,成功創建POS發票{0},
Stock quantity not enough for Item Code: {0} under warehouse {1}. Available quantity {2}.,倉庫{1}下的存貨數量不足以用於物料代碼{0}。可用數量{2}。,
Serial No: {0} has already been transacted into another POS Invoice.,序列號：{0}已被交易到另一個POS發票中。,
Balance Serial No,天平序列號,
Warehouse: {0} does not belong to {1},倉庫：{0}不屬於{1},
Please select batches for batched item {0},請為批次項目{0}選擇批次,
Please select quantity on row {0},請在第{0}行上選擇數量,
Please enter serial numbers for serialized item {0},請輸入序列化項目{0}的序列號,
Batch {0} already selected.,已選擇批次{0}。,
Please select a warehouse to get available quantities,請選擇一個倉庫以獲取可用數量,
"For transfer from source, selected quantity cannot be greater than available quantity",對於從源轉移，所選數量不能大於可用數量,
Cannot find Item with this Barcode,用此條形碼找不到物品,
{0} is mandatory. Maybe Currency Exchange record is not created for {1} to {2},{0}是必需的。也許沒有為{1}至{2}創建貨幣兌換記錄,
{} has submitted assets linked to it. You need to cancel the assets to create purchase return.,{}已提交與其關聯的資產。您需要取消資產以創建購買退貨。,
Cannot cancel this document as it is linked with submitted asset {0}. Please cancel it to continue.,由於該文檔與已提交的資產{0}鏈接，因此無法取消。請取消它以繼續。,
Row #{}: Serial No. {} has already been transacted into another POS Invoice. Please select valid serial no.,第{}行：序列號{}已被交易到另一個POS發票中。請選擇有效的序列號,
Row #{}: Serial Nos. {} has already been transacted into another POS Invoice. Please select valid serial no.,第{}行：序列號{}已被交易到另一個POS發票中。請選擇有效的序列號,
Row #{}: Serial No {} cannot be returned since it was not transacted in original invoice {},第＃{}行：由於未在原始發票{}中進行交易，因此無法返回序列號{},
Please set default Cash or Bank account in Mode of Payment {},請在付款方式{}中設置默認的現金或銀行帳戶,
Please set default Cash or Bank account in Mode of Payments {},請在付款方式{}中設置默認的現金或銀行帳戶,
Please ensure {} account is a Balance Sheet account. You can change the parent account to a Balance Sheet account or select a different account.,請確保{}帳戶是資產負債表帳戶。您可以將父帳戶更改為資產負債表帳戶，也可以選擇其他帳戶。,
Please ensure {} account is a Payable account. Change the account type to Payable or select a different account.,請確保{}帳戶是應付帳戶。將帳戶類型更改為“應付帳款”或選擇其他帳戶。,
Row {}: Expense Head changed to {} ,第{}行：費用總目已更改為{},
because account {} is not linked to warehouse {} ,因為帳戶{}未鏈接到倉庫{},
or it is not the default inventory account,或它不是默認的庫存帳戶,
Expense Head Changed,費用總目已更改,
because expense is booked against this account in Purchase Receipt {},因為費用是在採購收據{}中為此帳戶預訂的,
as no Purchase Receipt is created against Item {}. ,因為沒有針對物料{}創建採購收據。,
This is done to handle accounting for cases when Purchase Receipt is created after Purchase Invoice,這樣做是為了處理在採購發票後創建採購收貨的情況,
Purchase Order Required for item {},項目{}所需的採購訂單,
To submit the invoice without purchase order please set {} ,要提交不含採購訂單的發票，請設置{},
Mandatory Purchase Order,強制性採購訂單,
Purchase Receipt Required for item {},項目{}的採購收據,
To submit the invoice without purchase receipt please set {} ,要提交沒有購買收據的發票，請設置{},
Mandatory Purchase Receipt,強制性收貨,
POS Profile {} does not belongs to company {},POS個人資料{}不屬於公司{},
User {} is disabled. Please select valid user/cashier,用戶{}被禁用。請選擇有效的用戶/出納員,
Row #{}: Original Invoice {} of return invoice {} is {}. ,第＃{}行：退貨發票{}的原始發票{}為{}。,
Original invoice should be consolidated before or along with the return invoice.,原始發票應在退貨發票之前或與之合併。,
You can add original invoice {} manually to proceed.,您可以手動添加原始發票{}以繼續。,
Please ensure {} account is a Balance Sheet account. ,請確保{}帳戶是資產負債表帳戶。,
You can change the parent account to a Balance Sheet account or select a different account.,您可以將父帳戶更改為資產負債表帳戶，也可以選擇其他帳戶。,
Please ensure {} account is a Receivable account. ,請確保{}帳戶是應收帳款帳戶。,
Change the account type to Receivable or select a different account.,將帳戶類型更改為“應收帳款”或選擇其他帳戶。,
{} can't be cancelled since the Loyalty Points earned has been redeemed. First cancel the {} No {},由於所賺取的忠誠度積分已被兌換，因此無法取消{}。首先取消{}否{},
already exists,已經存在,
POS Closing Entry {} against {} between selected period,選定期間之間的POS關閉條目{}對{},
POS Invoice is {},POS發票為{},
POS Profile doesn't matches {},POS個人資料與{}不匹配,
POS Invoice is not {},POS發票不是{},
POS Invoice isn't created by user {},POS發票不是由用戶{}創建的,
Invalid POS Invoices,無效的POS發票,
Please add the account to root level Company - {},請將帳戶添加到根級別的公司-{},
"While creating account for Child Company {0}, parent account {1} not found. Please create the parent account in corresponding COA",為子公司{0}創建帳戶時，找不到父帳戶{1}。請在相應的COA中創建上級帳戶,
Account Not Found,找不到帳戶,
"While creating account for Child Company {0}, parent account {1} found as a ledger account.",在為子公司{0}創建帳戶時，發現父帳戶{1}是分類帳。,
Please convert the parent account in corresponding child company to a group account.,請將相應子公司中的母公司帳戶轉換為組帳戶。,
Invalid Parent Account,無效的上級帳戶,
"Renaming it is only allowed via parent company {0}, to avoid mismatch.",重命名僅允許通過母公司{0}進行，以避免不匹配。,
"If you {0} {1} quantities of the item {2}, the scheme {3} will be applied on the item.",如果您{0} {1}數量的項目{2}，則方案{3}將應用於該項目。,
"If you {0} {1} worth item {2}, the scheme {3} will be applied on the item.",如果您{0} {1}值得項目{2}，則方案{3}將應用於該項目。,
"As the field {0} is enabled, the field {1} is mandatory.",當啟用字段{0}時，字段{1}是必填字段。,
"As the field {0} is enabled, the value of the field {1} should be more than 1.",啟用字段{0}時，字段{1}的值應大於1。,
Cannot deliver Serial No {0} of item {1} as it is reserved to fullfill Sales Order {2},無法交付物料{1}的序列號{0}，因為已保留該物料以填寫銷售訂單{2},
"Sales Order {0} has reservation for the item {1}, you can only deliver reserved {1} against {0}.",銷售訂單{0}對物料{1}有保留，您只能針對{0}交付保留的{1}。,
{0} Serial No {1} cannot be delivered,{0}序列號{1}無法傳遞,
Row {0}: Subcontracted Item is mandatory for the raw material {1},第{0}行：原材料{1}必須使用轉包物料,
"As there are sufficient raw materials, Material Request is not required for Warehouse {0}.",由於有足夠的原材料，因此倉庫{0}不需要“物料請求”。,
" If you still want to proceed, please enable {0}.",如果仍然要繼續，請啟用{0}。,
The item referenced by {0} - {1} is already invoiced,{0}-{1}引用的商品已開票,
Therapy Session overlaps with {0},治療會話與{0}重疊,
Therapy Sessions Overlapping,治療會議重疊,
Therapy Plans,治療計劃,
"Item Code, warehouse, quantity are required on row {0}",在第{0}行中需要提供物料代碼，倉庫，數量,
Get Items from Material Requests against this Supplier,從針對此供應商的物料請求中獲取物料,
Enable European Access,啟用歐洲訪問,
Creating Purchase Order ...,創建採購訂單...,
"Select a Supplier from the Default Suppliers of the items below. On selection, a Purchase Order will be made against items belonging to the selected Supplier only.",從以下各項的默認供應商中選擇供應商。選擇後，將針對僅屬於所選供應商的項目下達採購訂單。,
Row #{}: You must select {} serial numbers for item {}.,行號{}：您必須為項目{}選擇{}序列號。,
