"""Customer Provided Item"" cannot be Purchase Item also","""Customer Provided Item（客户提供的物品）"" 也不可被采购",
"""Customer Provided Item"" cannot have Valuation Rate","""Customer Provided Item（客户提供的物品）"" 不允许拥有 Valuation Rate（估值比率）",
"""Is Fixed Asset"" cannot be unchecked, as Asset record exists against the item",是固定资产不能被反选，因为存在资产记录的项目,
'Based On' and 'Group By' can not be same,“根据”和“分组依据”不能相同,
'Days Since Last Order' must be greater than or equal to zero,“ 最后的订单到目前的天数”必须大于或等于零,
'Entries' cannot be empty,“分录”不能为空,
'From Date' is required,“起始日期”是必需的,
'From Date' must be after 'To Date',“起始日期”必须早于'终止日期',
'Has Serial No' can not be 'Yes' for non-stock item,不能为非库存物料勾选'是否有序列号',
'Opening',“打开”,
'To Case No.' cannot be less than 'From Case No.','结束箱号'不能小于'开始箱号',
'To Date' is required,“结束日期”必需设置,
'Total',&#39;总&#39;,
'Update Stock' can not be checked because items are not delivered via {0},因为物料还未通过{0）交付，“库存更新'不能被勾选,
'Update Stock' cannot be checked for fixed asset sale,固定资产销售不能选择“更新库存”,
1 exact match.,1完全匹配。,
90-Above,90以上,
A Customer Group exists with same name please change the Customer name or rename the Customer Group,同名的客户组已经存在，请更改客户姓名或重命名该客户组,
A Default Service Level Agreement already exists.,已存在默认服务级别协议。,
A Lead requires either a person's name or an organization's name,领导者需要一个人的姓名或组织的名称,
A customer with the same name already exists,已存在同名客户,
A question must have more than one options,一个问题必须有多个选项,
A qustion must have at least one correct options,Qustion必须至少有一个正确的选项,
A4,A4,
API Endpoint,应用程序界面端点,
API Key,应用程序界面密钥,
Abbr can not be blank or space,缩写不能为空或空格,
Abbreviation already used for another company,缩写已用于另一家公司,
Abbreviation cannot have more than 5 characters,缩写不能超过5个字符,
Abbreviation is mandatory,缩写字段必填,
About the Company,关于公司,
About your company,关于贵公司,
Above,以上,
Academic Term,学期,
Academic Term: ,学术期限：,
Academic Year,学年,
Academic Year: ,学年：,
Accepted + Rejected Qty must be equal to Received quantity for Item {0},已接收+已拒收数量须等于物料{0}的已接收数量,
Access Token,访问令牌,
Accessable Value,可访问的值,
Account,科目,
Account Number,帐号,
Account Number {0} already used in account {1},已在帐户{1}中使用的帐号{0},
Account Pay Only,账户仅用于支付,
Account Type,科目类型,
Account Type for {0} must be {1},{0}的科目类型必须为{1},
"Account balance already in Credit, you are not allowed to set 'Balance Must Be' as 'Debit'",科目余额已设置为'贷方'，不能设置为'借方',
"Account balance already in Debit, you are not allowed to set 'Balance Must Be' as 'Credit'",账户余额已设置为'借方'，不能设置为'贷方',
Account number for account {0} is not available.<br> Please setup your Chart of Accounts correctly.,科目{0}的科目号码不可用。 <br>请正确设置您的会计科目表。,
Account with child nodes cannot be converted to ledger,科目与子节点不能转换为分类账,
Account with child nodes cannot be set as ledger,科目与子节点不能被设置为分类帐,
Account with existing transaction can not be converted to group.,有交易的科目不能被转换为组。,
Account with existing transaction can not be deleted,有交易的科目不能被删除,
Account with existing transaction cannot be converted to ledger,有交易的科目不能被转换为分类账,
Account {0} does not belong to company: {1},科目{0}不属于公司：{1},
Account {0} does not belongs to company {1},科目{0}不属于公司{1},
Account {0} does not exist,科目{0}不存在,
Account {0} does not exists,科目{0}不存在,
Account {0} does not match with Company {1} in Mode of Account: {2},科目{0}与科目模式{2}中的公司{1}不符,
Account {0} has been entered multiple times,科目{0}已多次输入,
Account {0} is added in the child company {1},子公司{1}中添加了帐户{0},
Account {0} is frozen,科目{0}已冻结,
Account {0} is invalid. Account Currency must be {1},科目{0}状态为非激活。科目货币必须是{1},
Account {0}: Parent account {1} can not be a ledger,科目{0}的上级科目{1}不能是分类账,
Account {0}: Parent account {1} does not belong to company: {2},科目{0}的上级科目{1}不属于公司{2},
Account {0}: Parent account {1} does not exist,科目{0}的上级科目{1}不存在,
Account {0}: You can not assign itself as parent account,科目{0}不能是自己的上级科目,
Account: {0} can only be updated via Stock Transactions,科目{0}只能通过库存处理更新,
Account: {0} with currency: {1} can not be selected,帐号：{0}币种：{1}不能选择,
Accountant,会计,
Accounting,会计,
Accounting Entry for Asset,资产会计分录,
Accounting Entry for Stock,库存的会计分录,
Accounting Entry for {0}: {1} can only be made in currency: {2},会计分录为{0}：{1}只能在货币做：{2},
Accounting Ledger,会计分类帐,
Accounting journal entries.,会计记账日历分录。,
Accounts,会计,
Accounts Manager,会计经理,
Accounts Payable,应付帐款,
Accounts Payable Summary,应付帐款摘要,
Accounts Receivable,应收帐款,
Accounts Receivable Summary,应收账款汇总,
Accounts User,会计人员,
Accounts table cannot be blank.,账表不能为空。,
Accumulated Depreciation,累计折旧,
Accumulated Depreciation Amount,累计折旧额,
Accumulated Depreciation as on,作为累计折旧,
Accumulated Monthly,每月累计,
Accumulated Values,累积值,
Accumulated Values in Group Company,集团公司累计价值,
Achieved ({}),达到（{}）,
Action,行动,
Action Initialised,行动初始化,
Actions,操作,
Active,活动,
Activity Cost exists for Employee {0} against Activity Type - {1},显存员工活动费用{0}对应活动类型 -  {1},
Activity Cost per Employee,每个员工活动费用,
Activity Type,活动类型,
Actual Cost,实际成本,
Actual Delivery Date,实际交货日期,
Actual Qty,实际数量,
Actual Qty is mandatory,实际数量是必须项,
Actual Qty {0} / Waiting Qty {1},实际数量{0} /等待数量{1},
Actual Qty: Quantity available in the warehouse.,实际数量:仓库可用量。,
Actual qty in stock,实际库存数量,
Actual type tax cannot be included in Item rate in row {0},实际类型税不能被包含在连续的物料等级中{0},
Add,添加,
Add / Edit Prices,添加/编辑价格,
Add Comment,添加评论,
Add Customers,添加客户,
Add Employees,添加员工,
Add Item,新增,
Add Items,添加项目,
Add Leads,添加潜在客户,
Add Multiple Tasks,添加多个任务,
Add Sales Partners,添加销售合作伙伴,
Add Serial No,添加序列号,
Add Students,新增学生,
Add Suppliers,添加供应商,
Add Time Slots,添加时间空挡,
Add Timesheets,添加工时单,
Add Timeslots,添加时间空挡,
Add Users to Marketplace,将用户添加到市场,
Add a new address,添加新地址,
Add cards or custom sections on homepage,在主页上添加卡片或自定义栏目,
Add more items or open full form,添加更多项目或全开放形式,
Add notes,添加备注,
Add the rest of your organization as your users. You can also add invite Customers to your portal by adding them from Contacts,添加您的组织的其余部分用户。您还可以添加邀请客户到您的门户网站通过从联系人中添加它们,
Add/Remove Recipients,添加/删除收件人,
Added,添加,
Added {0} users,添加了{0}个用户,
Additional Salary Component Exists.,额外的薪资组件存在。,
Address,地址,
Address Line 2,地址行2,
Address Name,地址名称,
Address Title,地址名称,
Address Type,地址类型,
Administrative Expenses,行政费用,
Administrative Officer,行政主任,
Administrator,管理员,
Admission,准入,
Admission and Enrollment,入学和入学,
Admissions for {0},对...的准入{0},
Admit,准入,
Admitted,准入,
Advance Amount,预付款总额,
Advance Payments,预付款,
Advance account currency should be same as company currency {0},预付科目货币应与公司货币{0}相同,
Advance amount cannot be greater than {0} {1},预付金额不能大于{0} {1},
Advertising,广告,
Aerospace,航天,
Against,针对,
Against Account,针对的科目,
Against Journal Entry {0} does not have any unmatched {1} entry,针对的手工凭证{0}没有不符合的{1}分录,
Against Journal Entry {0} is already adjusted against some other voucher,针对的手工凭证{0}已经被其他凭证调整,
Against Supplier Invoice {0} dated {1},针对的日期为{1}的供应商费用清单{0},
Against Voucher,针对的凭证,
Against Voucher Type,针对的凭证类型,
Age,账龄,
Age (Days),时间（天）,
Ageing Based On,账龄基于,
Ageing Range 1,账龄范围1,
Ageing Range 2,账龄范围2,
Ageing Range 3,账龄范围3,
Agriculture,农业,
Agriculture (beta),农业（测试版）,
Airline,航空公司,
All Accounts,所有科目,
All Addresses.,所有地址。,
All Assessment Groups,所有评估组,
All BOMs,全部物料清单,
All Contacts.,所有联系人。,
All Customer Groups,所有客户群组,
All Day,全日,
All Departments,所有部门,
All Healthcare Service Units,所有医疗服务单位,
All Item Groups,所有物料群组,
All Products,所有产品,
All Products or Services.,所有的产品或服务。,
All Student Admissions,所有学生入学,
All Supplier Groups,所有供应商组织,
All Supplier scorecards.,所有供应商记分卡。,
All Territories,所有的区域,
All Warehouses,所有仓库,
All communications including and above this shall be moved into the new Issue,包括及以上的所有通信均应移至新发行中,
All items have already been transferred for this Work Order.,所有物料已发料到该工单。,
All other ITC,所有其他ITC,
All the mandatory Task for employee creation hasn't been done yet.,尚未全部完成创建新员工时必要任务。,
Allocate Payment Amount,分配付款金额,
Allocated Amount,已核销金额,
Allocating leaves...,分配假期......,
Already record exists for the item {0},物料{0}已存在,
"Already set default in pos profile {0} for user {1}, kindly disabled default",已经在用户{1}的pos配置文件{0}中设置了默认值，请禁用默认值,
Alternate Item,替代物料,
Alternative item must not be same as item code,替代物料不能与物料代码相同,
Amended From,修订源,
Amount,金额,
Amount After Depreciation,折旧金额后,
Amount of Integrated Tax,综合税额,
Amount of TDS Deducted,扣除TDS的金额,
Amount should not be less than zero.,金额不应小于零。,
Amount to Bill,待开费用清单金额,
Amount {0} {1} against {2} {3},数量 {0}{1} 对应 {2}{3},
Amount {0} {1} deducted against {2},金额{0} {1}抵扣{2},
Amount {0} {1} transferred from {2} to {3},金额{0} {1}从转移{2}到{3},
Amount {0} {1} {2} {3},金额{0} {1} {2} {3},
Amt,金额,
"An Item Group exists with same name, please change the item name or rename the item group",同名物料群组已存在，请修改物料名或群组名,
An academic term with this 'Academic Year' {0} and 'Term Name' {1} already exists. Please modify these entries and try again.,这个“学年”一个学期{0}和“术语名称”{1}已经存在。请修改这些条目，然后再试一次。,
An error occurred during the update process,更新过程中发生错误,
"An item exists with same name ({0}), please change the item group name or rename the item",具有名称 {0} 的物料已存在，请更名,
Analyst,分析员,
Annual Billing: {0},本年总发票金额：{0},
Another Budget record '{0}' already exists against {1} '{2}' and account '{3}' for fiscal year {4},对于财务年度{4}，{1}&#39;{2}&#39;和帐户“{3}”已存在另一个预算记录“{0}”,
Another Period Closing Entry {0} has been made after {1},在{1}之后另一个期间结束分录{0}已经被录入,
Another Sales Person {0} exists with the same Employee id,另外销售人员{0}存在具有相同员工ID,
Antibiotic,抗生素,
Apparel & Accessories,服装及配饰,
Applicable For,适用于,
"Applicable if the company is SpA, SApA or SRL",如果公司是SpA，SApA或SRL，则适用,
Applicable if the company is a limited liability company,适用于公司是有限责任公司的情况,
Applicable if the company is an Individual or a Proprietorship,适用于公司是个人或独资企业的情况,
Application of Funds (Assets),资金(资产)申请,
Applied,应用的,
Appointment Confirmation,约定确认,
Appointment Duration (mins),约定持续时间（分钟）,
Appointment Type,预约类型,
Appointment {0} and Sales Invoice {1} cancelled,约定{0}和销售费用清单{1}已取消,
Appointments and Encounters,约会和偶遇,
Appointments and Patient Encounters,预约和患者遭遇,
Appraisal {0} created for Employee {1} in the given date range,员工{1}的限期评估{0}已经创建,
Approving Role cannot be same as role the rule is Applicable To,审批与被审批角色不能相同,
Approving User cannot be same as user the rule is Applicable To,审批与被审批用户不能相同,
"Apps using current key won't be able to access, are you sure?",使用当前密钥的应用程序将无法访问，您确定吗？,
Are you sure you want to cancel this appointment?,你确定要取消这个预约吗？,
Arrear,欠款,
As Examiner,作为考官,
As On Date,截至日期,
As Supervisor,作为主管,
As per rules 42 & 43 of CGST Rules,根据CGST规则第42和43条,
As per section 17(5),根据第17（5）条,
Assessment,评估,
Assessment Criteria,评估标准,
Assessment Group,评估小组,
Assessment Group: ,评估组：,
Assessment Plan,评估计划,
Assessment Plan Name,评估计划名称,
Assessment Report,评估报表,
Assessment Reports,评估报表,
Assessment Result,评价结果,
Assessment Result record {0} already exists.,评估结果记录{0}已经存在。,
Asset,资产,
Asset Category,资产类别,
Asset Category is mandatory for Fixed Asset item,固定资产类的物料其资产类别字段是必填的,
Asset Maintenance,资产维护,
Asset Movement,资产移动,
Asset Movement record {0} created,资产移动记录{0}创建,
Asset Name,资产名称,
Asset Received But Not Billed,在途资产科目（已收到，未开票）,
Asset Value Adjustment,资产价值调整,
"Asset cannot be cancelled, as it is already {0}",资产不能被取消，因为它已经是{0},
Asset scrapped via Journal Entry {0},通过资产手工凭证报废{0},
"Asset {0} cannot be scrapped, as it is already {1}",资产{0}不能被废弃，因为它已经是{1},
Asset {0} does not belong to company {1},资产{0}不属于公司{1},
Asset {0} must be submitted,资产{0}必须提交,
Assets,资产,
Assign To,分配给,
Associate,协理,
At least one mode of payment is required for POS invoice.,需要为POS费用清单定义至少一种付款模式,
Atleast one item should be entered with negative quantity in return document,在退货凭证中至少一个物料的数量应该是负数,
Atleast one of the Selling or Buying must be selected,必须至少选择'销售'或'采购'其中的一项,
Atleast one warehouse is mandatory,必须选择至少一个仓库,
Attach Logo,附加标志,
Attachment,附件,
Attachments,附件,
Attendance can not be marked for future dates,考勤不能选未来的日期,
Attendance date can not be less than employee's joining date,考勤日期不得早于员工入职日期,
Attendance for employee {0} is already marked,员工{0}的考勤已标记,
Attendance has been marked successfully.,考勤登记成功。,
Attendance not submitted for {0} as {1} on leave.,因{1}尚在休假中，{0}的考勤未能提交。,
Attribute table is mandatory,属性表中的信息必填,
Attribute {0} selected multiple times in Attributes Table,属性{0}多次选择在属性表,
Authorized Signatory,授权签字人,
Auto Material Requests Generated,已自动生成材料需求,
Auto Repeat,自动重复,
Auto repeat document updated,自动重复文件更新,
Automotive,汽车,
Available,可用的,
Available Qty,可用数量,
Available Selling,可用销售,
Available for use date is required,需要使用可用日期,
Available slots,可用插槽,
Available {0},可用{0},
Available-for-use Date should be after purchase date,可供使用的日期应在购买日期之后,
Average Age,平均库龄,
Average Rate,平均率,
Avg Daily Outgoing,平均每日出货,
Avg. Buying Price List Rate,平均采购价格清单价格,
Avg. Selling Price List Rate,平均销售价格清单单价,
Avg. Selling Rate,平均卖出价,
BOM,BOM,
BOM Browser,物料清单浏览器,
BOM No,物料清单编号,
BOM Rate,物料清单税率,
BOM Stock Report,物料清单库存报表,
BOM and Manufacturing Quantity are required,物料清单和生产量是必需的,
BOM does not contain any stock item,物料清单不包含任何库存物料,
BOM {0} does not belong to Item {1},物料清单{0}不属于物料{1},
BOM {0} must be active,物料清单{0}必须处于激活状态,
BOM {0} must be submitted,BOM{0}未提交,
Balance,余额,
Balance (Dr - Cr),结余（Dr  -  Cr）,
Balance ({0}),余额（{0}）,
Balance Qty,结余数量,
Balance Sheet,资产负债表,
Balance Value,结余金额,
Balance for Account {0} must always be {1},科目{0}的余额必须是{1},
Bank,银行,
Bank Account,银行科目,
Bank Accounts,银行账户,
Bank Draft,银行汇票,
Bank Name,银行名称,
Bank Overdraft Account,银行透支账户,
Bank Reconciliation,银行对帐,
Bank Reconciliation Statement,银行对帐单,
Bank Statement,银行对帐单,
Bank Statement Settings,银行对账单设置,
Bank Statement balance as per General Ledger,基于总帐的银行对账单余额,
Bank account cannot be named as {0},银行账户不能命名为{0},
Bank/Cash transactions against party or for internal transfer,针对往来单位或内部转帐的银行/现金交易,
Banking,银行业,
Banking and Payments,银行和支付,
Barcode {0} already used in Item {1},条码{0}已被物料{1}使用,
Barcode {0} is not a valid {1} code,条形码{0}不是有效的{1}代码,
Base URL,基本网址,
Based On,基于,
Based On Payment Terms,根据付款条款,
Batch,批次,
Batch Entries,批量条目,
Batch ID is mandatory,批号是必需的,
Batch Inventory,批号库存,
Batch Name,批名,
Batch No,批号,
Batch number is mandatory for Item {0},物料{0}必须指定批次号,
Batch {0} of Item {1} has expired.,物料{1}的批号{0} 已过期。,
Batch {0} of Item {1} is disabled.,项目{1}的批处理{0}已禁用。,
Batch: ,批次：,
Batches,批,
Become a Seller,成为卖家,
Bill,账单,
Bill Date,账单日期,
Bill No,账单编号,
Bill of Materials,材料清单,
Bill of Materials (BOM),物料清单（BOM）,
Billable Hours,可开票时间,
Billed,已开票,
Billed Amount,已开票金额,
Billing Address,帐单地址,
Billing Address is same as Shipping Address,帐单地址与送货地址相同,
Billing Amount,开票金额,
Billing Status,账单状态,
Billing currency must be equal to either default company's currency or party account currency,帐单货币必须等于默认公司的货币或科目币种,
Bills raised by Suppliers.,供应商开出的账单,
Bills raised to Customers.,对客户开出的账单。,
Biotechnology,生物技术,
Black,黑,
Blanket Orders from Costumers.,来自客户的毯子订单。,
Block Invoice,阻止费用清单,
Boms,物料清单,
Both Trial Period Start Date and Trial Period End Date must be set,必须设置试用期开始日期和试用期结束日期,
Both Warehouse must belong to same Company,两个仓库必须属于同一公司,
Branch,分支机构（分公司）,
Broadcasting,广播,
Brokerage,佣金,
Browse BOM,浏览BOM,
Budget Against,预算对象,
Budget List,预算清单,
Budget Variance Report,预算差异报表,
Budget cannot be assigned against Group Account {0},预算不能分派给群组类科目{0},
"Budget cannot be assigned against {0}, as it's not an Income or Expense account",财务预算案不能对{0}指定的，因为它不是一个收入或支出科目,
Buildings,房屋,
Bundle items at time of sale.,用于销售的产品组合。,
Business Development Manager,业务发展经理,
Buy,采购,
Buying,采购,
Buying Amount,采购数量,
Buying Price List,采购价格清单,
Buying Rate,采购价,
"Buying must be checked, if Applicable For is selected as {0}",“适用于”为{0}时必须勾选“采购”,
By {0},到{0},
Bypass credit check at Sales Order ,在销售订单绕过信用检查,
C-Form records,C-表记录,
C-form is not applicable for Invoice: {0},C-形式不适用费用清单：{0},
CEO,CEO,
CESS Amount,CESS金额,
CGST Amount,CGST金额,
CRM,CRM,
CWIP Account,CWIP账户,
Calculated Bank Statement balance,计算的银行对账单余额,
Campaign,促销活动,
Can be approved by {0},可以被{0}的批准,
"Can not filter based on Account, if grouped by Account",按科目分类后不能根据科目过滤,
"Can not filter based on Voucher No, if grouped by Voucher",按凭证分类后不能根据凭证编号过滤,
"Can not mark Inpatient Record Discharged, there are Unbilled Invoices {0}",无法标记出院的住院病历，有未开单的费用清单{0},
Can only make payment against unbilled {0},只能为未开票{0}付款,
Can refer row only if the charge type is 'On Previous Row Amount' or 'Previous Row Total',收取类型类型必须是“基于上一行的金额”或者“前一行的总计”才能引用组,
"Can't change valuation method, as there are transactions against some items which does not have it's own valuation method",不能改变估值方法，因为已有业务费用清单使用了没有估值方法的物料,
Can't create standard criteria. Please rename the criteria,无法创建标准条件。请重命名标准,
Cancel,取消,
Cancel Material Visit {0} before cancelling this Warranty Claim,取消此保修要求之前请先取消物料访问{0},
Cancel Material Visits {0} before cancelling this Maintenance Visit,取消此上门保养之前请先取消物料访问{0},
Cancel Subscription,取消订阅,
Cancel the journal entry {0} first,首先取消日记条目{0},
Canceled,取消,
"Cannot Submit, Employees left to mark attendance",无法提交，不能为已离职员工登记考勤,
Cannot be a fixed asset item as Stock Ledger is created.,不能成为库存分类账创建的固定资产项目。,
Cannot cancel because submitted Stock Entry {0} exists,不能取消，因为提交的仓储记录{0}已经存在,
Cannot cancel transaction for Completed Work Order.,无法取消已完成工单的交易。,
Cannot cancel {0} {1} because Serial No {2} does not belong to the warehouse {3},无法取消{0} {1}，因为序列号{2}不属于仓库{3},
Cannot change Attributes after stock transaction. Make a new Item and transfer stock to the new Item,库存交易后不能更改属性。创建一个新项目并将库存转移到新项目,
Cannot change Fiscal Year Start Date and Fiscal Year End Date once the Fiscal Year is saved.,财年保存后便不能更改财年开始日期和结束日期,
Cannot change Service Stop Date for item in row {0},无法更改行{0}中项目的服务停止日期,
Cannot change Variant properties after stock transaction. You will have to make a new Item to do this.,存货业务发生后不能更改变体物料的属性。需要新建新物料。,
"Cannot change company's default currency, because there are existing transactions. Transactions must be cancelled to change the default currency.",因为已有交易不能改变公司的默认货币，请先取消交易。,
Cannot change status as student {0} is linked with student application {1},无法改变状态，因为学生{0}与学生的申请相链接{1},
Cannot convert Cost Center to ledger as it has child nodes,不能将成本中心转换为分类账，因为它有子项。,
Cannot covert to Group because Account Type is selected.,不能转换到组，因为已选择账户类型。,
Cannot create Retention Bonus for left Employees,无法为已离职员工创建持续服务奖,
Cannot create a Delivery Trip from Draft documents.,不能从“草稿”建立“销售出货配送路线安排”,
Cannot deactivate or cancel BOM as it is linked with other BOMs,无法停用或取消BOM，因为它被其他BOM引用。,
"Cannot declare as lost, because Quotation has been made.",已有报价的情况下，不能更改状态为遗失。,
Cannot deduct when category is for 'Valuation' or 'Valuation and Total',分类是“估值”或“估值和总计”的时候不能扣税。,
Cannot deduct when category is for 'Valuation' or 'Vaulation and Total',当类是“估值”或“Vaulation和总&#39;不能扣除,
"Cannot delete Serial No {0}, as it is used in stock transactions",无法删除序列号{0}，因为它已被库存活动使用,
Cannot enroll more than {0} students for this student group.,不能注册超过{0}学生到该学生群体。,
Cannot produce more Item {0} than Sales Order quantity {1},不能生产超过销售订单数量{1}的物料{0},
Cannot promote Employee with status Left,状态为已离职的员工不能晋升,
Cannot refer row number greater than or equal to current row number for this Charge type,此收取类型不能引用大于或等于本行的数据。,
Cannot select charge type as 'On Previous Row Amount' or 'On Previous Row Total' for first row,第一行的“收取类型”不能是“基于上一行的金额”或者“前一行的总计”,
Cannot set as Lost as Sales Order is made.,已有销售订单的情况下，不能更改状态为遗失。,
Cannot set authorization on basis of Discount for {0},不能为{0}设置折扣授权,
Cannot set multiple Item Defaults for a company.,无法为公司设置多个项目默认值。,
Cannot set quantity less than delivered quantity,无法设定数量小于交货数量,
Cannot set quantity less than received quantity,无法设置小于收货数量的数量,
Cannot set the field <b>{0}</b> for copying in variants,无法将字段<b>{0}设置</b>为在变体中进行复制,
Cannot transfer Employee with status Left,状态为已离职的员工不能进行调动,
Cannot {0} {1} {2} without any negative outstanding invoice,没有负的未完成费用清单，无法{0} {1} {2},
Capital Equipments,资本设备,
Capital Stock,股本,
Capital Work in Progress,在途资本,
Cart,购物车,
Cart is Empty,购物车是空的,
Case No(s) already in use. Try from Case No {0},箱号已被使用，请尝试从{0}开始,
Cash,现金,
Cash Flow Statement,现金流量表,
Cash Flow from Financing,融资现金流,
Cash Flow from Investing,投资现金流,
Cash Flow from Operations,运营现金流,
Cash In Hand,现款,
Cash or Bank Account is mandatory for making payment entry,“现金”或“银行账户”是付款分录的必须项,
Cashier Closing,收银员关闭,
Category,类别,
Category Name,分类名称,
Caution,警告,
Central Tax,中央税,
Certification,证明,
Cess,塞斯,
Change Amount,找零,
Change Item Code,更改物料代码,
Change Release Date,更改审批日期,
Change Template Code,更改模板代码,
Changing Customer Group for the selected Customer is not allowed.,不允许更改所选客户的客户组。,
Chapter,章节,
Chapter information.,章节信息。,
Charge of type 'Actual' in row {0} cannot be included in Item Rate,行{0}中的收取类型“实际”不能包含在“物料税率”,
Chargeble,Chargeble,
Charges are updated in Purchase Receipt against each item,费用会在每个物料的采购收货单中更新,
"Charges will be distributed proportionately based on item qty or amount, as per your selection",费用会根据你选择的物料数量和金额按比例分配。,
Chart of Cost Centers,成本中心表,
Check all,全选,
Checkout,退出,
Chemical,化学品,
Cheque,支票,
Cheque/Reference No,支票/参考编号,
Cheques Required,需要的支票,
Cheques and Deposits incorrectly cleared,支票及存款非正常清账,
Child Task exists for this Task. You can not delete this Task.,子任务存在这个任务。你不能删除这个任务。,
Child nodes can be only created under 'Group' type nodes,子节点只可创建在群组类节点下,
Child warehouse exists for this warehouse. You can not delete this warehouse.,子仓库存在于这个仓库。您不能删除这个仓库。,
Circular Reference Error,循环引用错误,
City,城市,
City/Town,市/镇,
Clay,粘土,
Clear filters,清除过滤器,
Clear values,清晰的价值观,
Clearance Date,清帐日期,
Clearance Date not mentioned,请填写清帐日期,
Clearance Date updated,间隙更新日期,
Client,客户,
Client ID,客户端ID,
Client Secret,客户秘密,
Clinical Procedure,临床程序,
Clinical Procedure Template,临床步骤模板,
Close Balance Sheet and book Profit or Loss.,关闭资产负债表，记帐到损益表。,
Close Loan,关闭贷款,
Close the POS,关闭POS,
Closed,已关闭,
Closed order cannot be cancelled. Unclose to cancel.,关闭的定单不能被取消。 打开关闭再取消。,
Closing (Cr),结算(信用),
Closing (Dr),结算(借记),
Closing (Opening + Total),期末（期初+总计）,
Closing Account {0} must be of type Liability / Equity,关闭科目{0}的类型必须是负债/权益,
Closing Balance,结算余额,
Code,代码,
Collapse All,全部收缩,
Color,颜色,
Colour,颜色,
Combined invoice portion must equal 100%,合并费用清单部分必须等于100％,
Commercial,商业,
Commission,佣金,
Commission Rate %,佣金率％,
Commission on Sales,销售佣金,
Commission rate cannot be greater than 100,佣金率不能大于100,
Community Forum,社区论坛,
Company (not Customer or Supplier) master.,公司(非客户或供应商)主数据。,
Company Abbreviation,公司缩写,
Company Abbreviation cannot have more than 5 characters,公司缩写不能超过5个字符,
Company Name,公司名称,
Company Name cannot be Company,公司名称不能为公司,
Company currencies of both the companies should match for Inter Company Transactions.,两家公司的公司货币应该符合Inter公司交易。,
Company is manadatory for company account,公司是公司账户的强制项,
Company name not same,公司名称不一样,
Company {0} does not exist,公司{0}不存在,
Compensatory leave request days not in valid holidays,补休假申请日不是在有效假期内,
Complaint,抱怨,
Completion Date,完成日期,
Computer,电脑,
Condition,条件,
Configure,配置,
Configure {0},配置{0},
Confirmed orders from Customers.,确认客户订单。,
Connect Amazon with ERPNext,将Amazon与ERPNext连接起来,
Connect Shopify with ERPNext,将Shopify与ERPNext连接,
Connect to Quickbooks,连接到Quickbooks,
Connected to QuickBooks,连接到QuickBooks,
Connecting to QuickBooks,连接到QuickBooks,
Consultation,会诊,
Consultations,磋商,
Consulting,咨询,
Consumable,耗材,
Consumed,已消耗,
Consumed Amount,消耗量,
Consumed Qty,已消耗数量,
Consumer Products,消费类产品,
Contact,联系人,
Contact Us,联系我们,
Content,内容,
Content Masters,内容大师,
Content Type,内容类型,
Continue Configuration,继续配置,
Contract,合同,
Contract End Date must be greater than Date of Joining,合同结束日期必须大于加入的日期,
Contribution %,贡献％,
Contribution Amount,贡献金额,
Conversion factor for default Unit of Measure must be 1 in row {0},行{0}中默认计量单位的转换系数必须是1,
Conversion rate cannot be 0 or 1,汇率不能为0或1,
Convert to Group,转换为组,
Convert to Non-Group,转换为非群组,
Cosmetics,化妆品,
Cost Center,成本中心,
Cost Center Number,成本中心编号,
Cost Center and Budgeting,成本中心和预算编制,
Cost Center is required in row {0} in Taxes table for type {1},类型{1}税费表的行{0}必须有成本中心,
Cost Center with existing transactions can not be converted to group,有交易的成本中心不能转化为组,
Cost Center with existing transactions can not be converted to ledger,有交易的成本中心不能转化为总账,
Cost Centers,成本中心,
Cost Updated,成本更新,
Cost as on,成本上,
Cost of Delivered Items,出货物料成本,
Cost of Goods Sold,销货成本,
Cost of Issued Items,已发料物料成本,
Cost of New Purchase,新的采购成本,
Cost of Purchased Items,采购物料成本,
Cost of Scrapped Asset,报废资产成本,
Cost of Sold Asset,出售资产的成本,
Cost of various activities,各种活动的费用,
"Could not create Credit Note automatically, please uncheck 'Issue Credit Note' and submit again",无法自动创建Credit Note，请取消选中&#39;Issue Credit Note&#39;并再次提交,
Could not generate Secret,无法生成秘密,
Could not retrieve information for {0}.,无法检索{0}的信息。,
Could not solve criteria score function for {0}. Make sure the formula is valid.,无法解决{0}的标准分数函数。确保公式有效。,
Could not solve weighted score function. Make sure the formula is valid.,无法解决加权分数函数。确保公式有效。,
Could not submit some Salary Slips,无法提交一些薪资单,
"Could not update stock, invoice contains drop shipping item.",无法更新库存，费用清单包含由供应商交货（直接发运）项目。,
Country wise default Address Templates,国家的默认地址模板,
Course Code: ,课程编号：,
Course Enrollment {0} does not exists,课程注册{0}不存在,
Course Schedule,课程表,
Course: ,课程：,
Cr,信用,
Create,创建,
Create BOM,创建BOM,
Create Delivery Trip,建立销售出货配送路线安排,
Create Employee,创建员工,
Create Employee Records,建立员工档案,
"Create Employee records to manage leaves, expense claims and payroll",建立员工档案管理叶，报销和工资,
Create Fee Schedule,创建收费表,
Create Fees,创造费用,
Create Inter Company Journal Entry,创建国际公司日记帐分录,
Create Invoice,创建发票,
Create Invoices,创建发票,
Create Job Card,创建工作卡,
Create Journal Entry,创建日记帐分录,
Create Lead,创造领导力,
Create Leads,建立潜在客户,
Create Maintenance Visit,创建维护访问,
Create Material Request,创建物料申请,
Create Multiple,创建多个,
Create Opening Sales and Purchase Invoices,创建开仓销售和采购发票,
Create Payment Entries,创建付款条目,
Create Payment Entry,创建付款条目,
Create Print Format,创建打印格式,
Create Purchase Order,创建采购订单,
Create Purchase Orders,创建采购订单,
Create Quotation,创建报价,
Create Sales Invoice,创建销售发票,
Create Sales Order,创建销售订单,
Create Sales Orders to help you plan your work and deliver on-time,创建销售订单以帮助您规划工作并按时交付,
Create Sample Retention Stock Entry,创建样本保留库存条目,
Create Student,创建学生,
Create Student Batch,创建学生批处理,
Create Student Groups,创建学生组,
Create Supplier Quotation,创建供应商报价,
Create Tax Template,创建税务模板,
Create Timesheet,创建时间表,
Create User,创建用户,
Create Users,创建用户,
Create Variant,创建变体,
Create Variants,创建变体,
"Create and manage daily, weekly and monthly email digests.",创建和管理每日，每周和每月的电子邮件摘要。,
Create customer quotes,创建客户报价,
Create rules to restrict transactions based on values.,创建规则，根据属性值来限制交易。,
Created {0} scorecards for {1} between: ,为{1}创建{0}记分卡：,
Creating Company and Importing Chart of Accounts,创建公司并导入会计科目表,
Creating Fees,创造费用,
Creating student groups,创建学生组,
Creating {0} Invoice,创建{0}费用清单,
Credit,贷方,
Credit ({0}),信用（{0}）,
Credit Account,贷方科目,
Credit Balance,贷方余额,
Credit Card,信用卡,
Credit Days cannot be a negative number,信用日不能是负数,
Credit Limit,信用额度,
Credit Note,换货凭单,
Credit Note Amount,换货凭单金额,
Credit Note Issued,换货凭单已发出,
Credit Note {0} has been created automatically,换货凭单{0}已自动创建,
Credit limit has been crossed for customer {0} ({1}/{2}),客户{0}（{1} / {2}）的信用额度已超过,
Creditors,债权人,
Criteria weights must add up to 100%,标准重量必须达100％,
Crop Cycle,作物周期,
Crops & Lands,作物和土地,
Currency Exchange must be applicable for Buying or for Selling.,外币汇率必须适用于买入或卖出。,
Currency can not be changed after making entries using some other currency,货币不能使用其他货币进行输入后更改,
Currency exchange rate master.,货币汇率主数据,
Currency for {0} must be {1},货币{0}必须{1},
Currency is required for Price List {0},价格清单{0}需要设定货币,
Currency of the Closing Account must be {0},在关闭科目的货币必须是{0},
Currency of the price list {0} must be {1} or {2},价格清单{0}的货币必须是{1}或{2},
Currency should be same as Price List Currency: {0},货币应与价格清单货币相同：{0},
Current Assets,流动资产,
Current BOM and New BOM can not be same,当前BOM和新BOM不能相同,
Current Liabilities,流动负债,
Current Qty,目前数量,
Current invoice {0} is missing,当前费用清单{0}缺失,
Custom HTML,自定义HTML,
Custom?,自定义？,
Customer,客户,
Customer Addresses And Contacts,客户的地址和联系方式,
Customer Contact,客户联系,
Customer Database.,客户数据库。,
Customer Group,客户群组,
Customer LPO,客户采购订单号,
Customer LPO No.,客户采购订单号,
Customer Name,客户名称,
Customer POS Id,客户POS ID,
Customer Service,顾客服务,
Customer and Supplier,客户和供应商,
Customer is required,客户是必须项,
Customer isn't enrolled in any Loyalty Program,客户未加入任何忠诚度计划,
Customer required for 'Customerwise Discount',”客户折扣“需要指定客户,
Customer {0} does not belong to project {1},客户{0}不属于项目{1},
Customer {0} is created.,客户{0}已创建。,
Customers in Queue,在排队的客户,
Customize Homepage Sections,自定义主页部分,
Customizing Forms,自定义表单,
Daily Project Summary for {0},{0}的每日项目摘要,
Daily Reminders,每日提醒,
Data Import and Export,数据导入和导出,
Data Import and Settings,数据导入和设置,
Database of potential customers.,潜在客户数据库。,
Date Format,日期格式,
Date Of Retirement must be greater than Date of Joining,退休日期必须大于入职日期,
Date of Birth,出生日期,
Date of Birth cannot be greater than today.,出生日期不能大于今天。,
Date of Commencement should be greater than Date of Incorporation,开始日期应大于公司注册日期,
Date of Joining,入职日期,
Date of Joining must be greater than Date of Birth,入职日期必须大于出生日期,
Date of Transaction,交易日期,
Day,天,
Debit,借方,
Debit ({0}),借记卡（{0}）,
Debit Account,借方科目,
Debit Note,借项通知单,
Debit Note Amount,借项金额,
Debit Note Issued,借项通知已发送,
Debit To is required,借记是必需的,
Debit and Credit not equal for {0} #{1}. Difference is {2}.,借贷{0}＃不等于{1}。不同的是{2}。,
Debtors,债务人,
Debtors ({0}),债务人（{0}）,
Declare Lost,宣布失去,
Default Activity Cost exists for Activity Type - {0},默认情况下存在的活动类型的作业成本活动类型 -  {0},
Default BOM ({0}) must be active for this item or its template,该物料或其模板物料的默认物料清单状态必须是激活的,
Default BOM for {0} not found,默认BOM {0}未找到,
Default BOM not found for Item {0} and Project {1},找不到物料{0}和项目{1}的默认BOM,
Default Letter Head,默认信头,
Default Tax Template,默认税收模板,
Default Unit of Measure for Item {0} cannot be changed directly because you have already made some transaction(s) with another UOM. You will need to create a new Item to use a different Default UOM.,因为该物料已经有使用别的计量单位的交易记录存在了，不再允许直接修改其默认单位{0}了。如果需要请创建一个新物料，以使用不同的默认计量单位。,
Default Unit of Measure for Variant '{0}' must be same as in Template '{1}',物料变体的默认单位“{0}”必须与模板默认单位一致“{1}”,
Default settings for buying transactions.,采购业务的默认设置。,
Default settings for selling transactions.,销售业务的默认设置。,
Default tax templates for sales and purchase are created.,销售和采购的默认税收模板被创建。,
Defaults,默认,
Defense,Defense,
Define Project type.,定义项目类型。,
Define budget for a financial year.,定义预算财务年度。,
Define various loan types,定义不同的贷款类型,
Del,删除,
Delay in payment (Days),延迟支付（天）,
Delete all the Transactions for this Company,删除所有交易本公司,
Deletion is not permitted for country {0},国家{0}不允许删除,
Delivered,已交付,
Delivered Amount,已交付金额,
Delivered Qty,已交付数量,
Delivered: {0},交货：{0},
Delivery,交货,
Delivery Date,交货日期,
Delivery Note,销售出货,
Delivery Note {0} is not submitted,销售出货单{0}未提交,
Delivery Note {0} must not be submitted,销售出货单{0}不应该被提交,
Delivery Notes {0} must be cancelled before cancelling this Sales Order,取消这个销售订单之前必须取消销售出货单{0},
Delivery Notes {0} updated,已更新交货单{0},
Delivery Status,交货状态,
Delivery Trip,销售出货配送路线安排,
Delivery warehouse required for stock item {0},物料{0}为库存管理物料，且在主数据中未定义默认仓库，请在销售订单行填写出货仓库信息,
Department,部门,
Department Stores,百货,
Depreciation,折旧,
Depreciation Amount,折旧额,
Depreciation Amount during the period,期间折旧额,
Depreciation Date,折旧日期,
Depreciation Eliminated due to disposal of assets,折旧淘汰因处置资产,
Depreciation Entry,折旧分录,
Depreciation Method,折旧方法,
Depreciation Row {0}: Depreciation Start Date is entered as past date,折旧行{0}：折旧开始日期作为过去的日期输入,
Depreciation Row {0}: Expected value after useful life must be greater than or equal to {1},折旧行{0}：使用寿命后的预期值必须大于或等于{1},
Depreciation Row {0}: Next Depreciation Date cannot be before Available-for-use Date,折旧行{0}：下一个折旧日期不能在可供使用的日期之前,
Depreciation Row {0}: Next Depreciation Date cannot be before Purchase Date,折旧行{0}：下一个折旧日期不能在采购日期之前,
Designer,设计师,
Detailed Reason,详细原因,
Details,详细信息,
Details of Outward Supplies and inward supplies liable to reverse charge,向外供应和向内供应的详细信息可能被撤销费用,
Details of the operations carried out.,生产操作信息。,
Diagnosis,诊断,
Did not find any item called {0},没有找到名字为{0}的物料,
Diff Qty,差异数量,
Difference Account,差异科目,
"Difference Account must be a Asset/Liability type account, since this Stock Reconciliation is an Opening Entry",差异科目必须是资产/负债类型的科目，因为此库存盘点在期初进行,
Difference Amount,差额,
Difference Amount must be zero,差量必须是零,
Different UOM for items will lead to incorrect (Total) Net Weight value. Make sure that Net Weight of each item is in the same UOM.,不同计量单位的项目会导致不正确的（总）净重值。请确保每个物料的净重使用同一个计量单位。,
Direct Expenses,直接费用,
Direct Income,直接收益,
Disable,禁用,
Disabled template must not be default template,被禁用模板不能设为默认模板,
Disburse Loan,支付贷款,
Disbursed,支付,
Disc,圆盘,
Discharge,卸货,
Discount,折扣,
Discount Percentage can be applied either against a Price List or for all Price List.,折扣百分比可以应用于一个或所有的价格清单。,
Discount must be less than 100,折扣必须小于100,
Diseases & Fertilizers,疾病与肥料,
Dispatch,调度,
Dispatch Notification,发货通知,
Dispatch State,派遣国,
Distance,距离,
Distribution,分销,
Distributor,经销商,
Dividends Paid,股利支付,
Do you really want to restore this scrapped asset?,难道你真的想恢复这个报废的资产？,
Do you really want to scrap this asset?,难道你真的想放弃这项资产？,
Do you want to notify all the customers by email?,你想通过电子邮件通知所有的客户？,
Doc Date,Doc Date,
Doc Name,文档名称,
Doc Type,文档类型,
Docs Search,Google文档搜索,
Document Name,文档名称,
Document Type,文档类型,
Domain,领域,
Domains,域,
Done,DONE,
Donor,捐赠者,
Donor Type information.,捐助者类型信息。,
Donor information.,捐助者信息。,
Download JSON,下载JSON,
Draft,草案,
Drop Ship,由供应商交货（直接发运）,
Drug,药物,
Due / Reference Date cannot be after {0},到期/参照日期不能迟于{0},
Due Date cannot be before Posting / Supplier Invoice Date,截止日期不能在付帐前/供应商发票日期之前,
Due Date is mandatory,截止日期字段必填,
Duplicate Entry. Please check Authorization Rule {0},重复的条目，请检查授权规则{0},
Duplicate Serial No entered for Item {0},物料{0}的序列号重复,
Duplicate customer group found in the cutomer group table,在CUTOMER组表中找到重复的客户群,
Duplicate entry,重复的条目,
Duplicate item group found in the item group table,在物料组中有重复物料组,
Duplicate roll number for student {0},学生{0}的重复卷号,
Duplicate row {0} with same {1},重复的行{0}同{1},
Duplicate {0} found in the table,表中找到重复的{0},
Duration in Days,持续时间天数,
Duties and Taxes,关税与税项,
E-Invoicing Information Missing,电子发票信息丢失,
ERPNext Demo,ERPNext演示,
ERPNext Settings,ERP下载设置,
Earliest,最早,
Earnest Money,保证金,
Edit,编辑,
Edit Publishing Details,编辑发布细节,
"Edit in full page for more options like assets, serial nos, batches etc.",在整页上编辑更多的选项，如资产，序列号，批次等。,
Education,教育,
Either location or employee must be required,必须要求地点或员工,
Either target qty or target amount is mandatory,需要指定目标数量和金额,
Either target qty or target amount is mandatory.,需要指定目标数量和金额。,
Electrical,电气,
Electronic Equipments,电子设备,
Electronics,电子,
Eligible ITC,符合条件的ITC,
Email Account,邮件帐户,
Email Address,电子邮箱,
"Email Address must be unique, already exists for {0}",电子邮件地址必须是唯一的，已经存在了{0},
Email Digest: ,邮件摘要：,
Email Reminders will be sent to all parties with email contacts,电子邮件提醒将通过电子邮件联系方式发送给各方,
Email Sent,邮件已发送,
Email Template,电子邮件模板,
Email not found in default contact,在默认联系人中找不到电子邮件,
Email sent to {0},邮件已发送到{0},
Employee,员工,
Employee Advances,员工预支,
Employee ID,员工ID,
Employee Lifecycle,员工生命周期,
Employee Name,员工姓名,
Employee Promotion cannot be submitted before Promotion Date ,员工晋升不能在晋升日期前提交,
Employee Transfer cannot be submitted before Transfer Date ,员工变动无法在变动日期前提交,
Employee cannot report to himself.,员工不能向自己报表。,
Employee {0} has already applied for {1} between {2} and {3} : ,员工{0}已在{2}和{3}之间申请{1}：,
Employee {0} of grade {1} have no default leave policy,{1}职级员工{0}没有默认休假政策,
Enable / disable currencies.,启用/禁用货币。,
Enabled,已启用,
"Enabling 'Use for Shopping Cart', as Shopping Cart is enabled and there should be at least one Tax Rule for Shopping Cart",作为启用的购物车已启用“使用购物车”，而应该有购物车至少有一个税务规则,
End Date,结束日期,
End Date can not be less than Start Date,结束日期不能小于开始日期,
End Date cannot be before Start Date.,结束日期不能在开始日期之前。,
End Year,结束年份,
End Year cannot be before Start Year,结束年份不能启动年前,
End on,结束,
Ends On date cannot be before Next Contact Date.,结束日期不能在下一次联系日期之前。,
Energy,能源,
Engineer,工程师,
Enough Parts to Build,足够的配件组装,
Enroll,注册,
Enrolling student,招生学生,
Enrolling students,招收学生,
Enter depreciation details,输入折旧信息,
Enter the Bank Guarantee Number before submittting.,在提交之前输入银行保证号码。,
Enter the name of the Beneficiary before submittting.,在提交之前输入受益人的姓名。,
Enter the name of the bank or lending institution before submittting.,在提交之前输入银行或贷款机构的名称。,
Enter value betweeen {0} and {1},输入{0}和{1}之间的值,
Entertainment & Leisure,娱乐休闲,
Entertainment Expenses,娱乐费用,
Equity,权益,
Error Log,错误日志,
Error evaluating the criteria formula,评估标准公式时出错,
Error in formula or condition: {0},公式或条件错误:{0},
Error: Not a valid id?,错误：没有有效的身份证？,
Estimated Cost,估计成本,
"Even if there are multiple Pricing Rules with highest priority, then following internal priorities are applied:",即使有多个最高优先级定价规则，使用以下的内部优先级：,
Exchange Gain/Loss,汇兑损益,
Exchange Rate Revaluation master.,汇率重估主数据。,
Exchange Rate must be same as {0} {1} ({2}),汇率必须一致{0} {1}（{2}）,
Excise Invoice,消费费用清单,
Execution,执行,
Executive Search,猎头,
Expand All,展开全部,
Expected Delivery Date,预计交货日期,
Expected Delivery Date should be after Sales Order Date,预计交货日期应在销售订单日期之后,
Expected End Date,预计结束日期,
Expected Hrs,预计的小时数,
Expected Start Date,预计开始日期,
Expense,费用,
Expense / Difference account ({0}) must be a 'Profit or Loss' account,费用/差异科目({0})必须是一个“益损”类科目,
Expense Account,费用科目,
Expense Claim,费用报销,
Expense Claims,报销,
Expense account is mandatory for item {0},必须为物料{0}指定费用科目,
Expenses,费用,
Expenses Included In Asset Valuation,包含在资产评估价中的费用科目,
Expenses Included In Valuation,计入库存评估价的费用科目,
Expired Batches,过期批次,
Expires On,到期,
Expiring On,即将到期,
Expiry (In Days),过期(按天计算),
Explore,探索,
Export E-Invoices,出口电子发票,
Extra Large,特大号,
Extra Small,超小,
Fail,失败,
Failed,失败,
Failed to create website,无法创建网站,
Failed to install presets,无法安装预设,
Failed to login,登录失败,
Failed to setup company,未能设立公司,
Failed to setup defaults,无法设置默认值,
Failed to setup post company fixtures,未能设置公司固定装置,
Fax,传真,
Fee,费用,
Fee Created,创建费用,
Fee Creation Failed,费用创作失败,
Fee Creation Pending,费用创作待定,
Fee Records Created - {0},费纪录已建立 -  {0},
Feedback,反馈,
Fees,费用,
Female,女,
Fetch Data,获取数据,
Fetch Subscription Updates,获取订阅更新,
Fetch exploded BOM (including sub-assemblies),获取展开BOM（包括子物料）,
Fetching records......,获取记录中......,
Field Name,字段名称,
Fieldname,字段名,
Fields,字段,
"Filter Fields Row #{0}: Fieldname <b>{1}</b> must be of type ""Link"" or ""Table MultiSelect""",筛选字段行＃{0}：字段名<b>{1}</b>必须是“链接”或“表格MultiSelect”类型,
Filter Total Zero Qty,过滤器总计零数量,
Finance Book,账簿,
Financial / accounting year.,财务/会计年度。,
Financial Services,金融服务,
Financial Statements,财务报表,
Financial Year,财务年度,
Finish,完成,
Finished Good,成品,
Finished Good Item Code,成品商品代码,
Finished Goods,成品,
Finished Item {0} must be entered for Manufacture type entry,生产制造类库存移动，请输入生产入库物料{0},
Finished product quantity <b>{0}</b> and For Quantity <b>{1}</b> cannot be different,成品数量<b>{0}</b>和数量<b>{1}</b>不能不同,
First Name,名,
"Fiscal Regime is mandatory, kindly set the fiscal regime in the company {0}",财政制度是强制性的，请在公司{0}设定财政制度,
Fiscal Year,财务年度,
Fiscal Year End Date should be one year after Fiscal Year Start Date,会计年度结束日期应为会计年度开始日期后一年,
Fiscal Year Start Date and Fiscal Year End Date are already set in Fiscal Year {0},财务年度开始日期和结束日期已经在财年{0}中设置,
Fiscal Year Start Date should be one year earlier than Fiscal Year End Date,会计年度开始日期应比会计年度结束日期提前一年,
Fiscal Year {0} does not exist,会计年度{0}不存在,
Fiscal Year {0} is required,会计年度{0}是必需的,
Fixed Asset,固定资产,
Fixed Asset Item must be a non-stock item.,固定资产物料必须是一个非库存物料。,
Fixed Assets,固定资产,
Following Material Requests have been raised automatically based on Item's re-order level,以下物料需求数量已自动根据重订货水平相应增加了,
Following accounts might be selected in GST Settings:,以下科目可能在GST设置中选择：,
Following course schedules were created,按照课程工时单创建,
Following item {0} is not marked as {1} item. You can enable them as {1} item from its Item master,下列项目{0}未标记为{1}项目。您可以从项目主文件中将它们作为{1}项启用,
Following items {0} are not marked as {1} item. You can enable them as {1} item from its Item master,以下项{0}未标记为{1}项。您可以从项目主文件中将它们作为{1}项启用,
"Food, Beverage & Tobacco",食品，饮料与烟草,
For,对于,
"For 'Product Bundle' items, Warehouse, Serial No and Batch No will be considered from the 'Packing List' table. If Warehouse and Batch No are same for all packing items for any 'Product Bundle' item, those values can be entered in the main Item table, values will be copied to 'Packing List' table.",对于“产品包”的物料，仓库，序列号和批号将被从“装箱单”表考虑。如果仓库和批次号是相同的任何“产品包”项目的所有包装物料，这些值可以在主项表中输入，值将被复制到“装箱单”表。,
For Quantity (Manufactured Qty) is mandatory,数量（制造数量）字段必填,
For Supplier,对供应商,
For Warehouse,仓库,
For Warehouse is required before Submit,提交前必须选择仓库,
"For an item {0}, quantity must be negative number",对于商品{0}，数量必须是负数,
"For an item {0}, quantity must be positive number",对于商品{0}，数量必须是正数,
"For job card {0}, you can only make the 'Material Transfer for Manufacture' type stock entry",对于作业卡{0}，您只能进行“制造材料转移”类型库存条目,
"For row {0} in {1}. To include {2} in Item rate, rows {3} must also be included",对于{1}中的行{0}。要包括物料率中{2}，行{3}也必须包括,
For row {0}: Enter Planned Qty,对于行{0}：输入计划的数量,
"For {0}, only credit accounts can be linked against another debit entry",对于{0}，只有贷方分录可以与另一个贷方科目链接,
"For {0}, only debit accounts can be linked against another credit entry",对于{0}，只有借方分录可以与另一个借方科目链接,
Forum Activity,论坛活动,
Free item code is not selected,未选择免费商品代码,
Freight and Forwarding Charges,货运及转运费用,
Frequency,频率,
Friday,星期五,
From,从,
From Address 1,来自地址1,
From Address 2,来自地址2,
From Currency and To Currency cannot be same,源货币和目标货币不能相同,
From Date and To Date lie in different Fiscal Year,从日期和到期日位于不同的财政年度,
From Date cannot be greater than To Date,起始日期不能大于结束日期,
From Date must be before To Date,起始日期日期必须在结束日期之前,
From Date should be within the Fiscal Year. Assuming From Date = {0},起始日期应该在财年之内。财年起始日是{0},
From Datetime,起始时间日期,
From Delivery Note,源销售出货单,
From Fiscal Year,从财政年度开始,
From GSTIN,来自GSTIN,
From Party Name,来自某方的名字,
From Pin Code,来自Pin码,
From Place,从地方,
From Range has to be less than To Range,从范围必须小于去范围,
From State,来自州,
From Time,起始时间,
From Time Should Be Less Than To Time,从时间应该少于去时间,
From Time cannot be greater than To Time.,从时间不能超过结束时间大。,
"From a supplier under composition scheme, Exempt and Nil rated",来自组成计划下的供应商，免税和零评级,
From and To dates required,必须指定起始和结束日期,
From value must be less than to value in row {0},行{0}的起始值必须小于去值,
From {0} | {1} {2},来自{0} | {1} {2},
Fulfillment,订单履行,
Full Name,全名,
Fully Depreciated,已提足折旧,
Furnitures and Fixtures,家具及固定装置,
"Further accounts can be made under Groups, but entries can be made against non-Groups",更多的科目可以归属到一个群组类的科目下，但会计凭证中只能使用非群组类的科目,
Further cost centers can be made under Groups but entries can be made against non-Groups,进一步的成本中心可以根据组进行，但项可以对非组进行,
Further nodes can be only created under 'Group' type nodes,只能在“组”节点下新建节点,
GSTIN,GSTIN,
GSTR3B-Form,GSTR3B表格,
Gain/Loss on Asset Disposal,在资产处置收益/损失,
Gantt Chart,甘特图,
Gantt chart of all tasks.,所有任务的甘特图。,
Gender,性别,
General,总的,
General Ledger,总帐,
Generate Material Requests (MRP) and Work Orders.,生成物料申请（MRP）和工单。,
Generate Secret,生成秘密,
Get Invocies,获取Invocies,
Get Invoices,获取发票,
Get Invoices based on Filters,根据过滤器获取发票,
Get Items from BOM,从物料清单获取物料,
Get Items from Healthcare Services,从医疗保健服务获取项目,
Get Items from Prescriptions,从处方获取物品,
Get Items from Product Bundle,从产品包获取物料,
Get Suppliers,获取供应商,
Get Suppliers By,获得供应商,
Get Updates,获取更新,
Get customers from,从...获取客户,
Get from Patient Encounter,从患者遭遇中获取,
Getting Started,入门,
GitHub Sync ID,GitHub同步ID,
Global settings for all manufacturing processes.,所有生产流程的全局设置。,
Go to the Desktop and start using ERPNext,转到桌面和开始使用ERPNext,
GoCardless SEPA Mandate,GoCardless SEPA授权,
GoCardless payment gateway settings,GoCardless支付网关设置,
Goal and Procedure,目标和程序,
Goals cannot be empty,目标不能为空,
Goods In Transit,货物正在运送中,
Goods Transferred,货物转移,
Goods and Services Tax (GST India),商品和服务税（印度消费税）,
Goods are already received against the outward entry {0},已收到针对外向条目{0}的货物,
Government,政府,
Grand Total,总计,
Grant,格兰特,
Grant Application,授予申请,
Grant Leaves,准予假期,
Grant information.,授予信息。,
Grocery,杂货,
Gross Profit,毛利,
Gross Profit %,毛利％,
Gross Profit / Loss,总利润/亏损,
Gross Purchase Amount,总采购金额,
Gross Purchase Amount is mandatory,总消费金额字段必填,
Group by Account,基于账户分组,
Group by Party,按照往来单位分组,
Group by Voucher,基于凭证分组,
Group by Voucher (Consolidated),按凭证分组（合并）,
Group node warehouse is not allowed to select for transactions,组节点仓库不允许选择用于交易,
Group to Non-Group,群组转换为非群组,
Group your students in batches,一群学生在分批,
Groups,组,
Guardian1 Email ID,Guardian1电子邮件ID,
Guardian1 Mobile No,Guardian1手机号码,
Guardian1 Name,Guardian1名称,
Guardian2 Email ID,Guardian2电子邮件ID,
Guardian2 Mobile No,Guardian2手机号码,
Guardian2 Name,Guardian2名称,
HR Manager,人力资源经理,
HSN,HSN,
HSN/SAC,HSN / SAC,
Half Yearly,半年度,
Half-Yearly,半年一次,
Hardware,硬件,
Head of Marketing and Sales,营销和销售主管,
Health Care,医疗保健,
Healthcare,卫生保健,
Healthcare (beta),医疗保健（beta）,
Healthcare Practitioner,医疗从业者,
Healthcare Practitioner not available on {0},医疗从业者在{0}上不可用,
Healthcare Practitioner {0} not available on {1},{1}上没有医疗从业者{0},
Healthcare Service Unit,医疗服务单位,
Healthcare Service Unit Tree,医疗服务单位树,
Healthcare Service Unit Type,医疗服务单位类型,
Healthcare Services,医疗服务,
Healthcare Settings,医疗设置,
Help Results for,帮助结果,
High,高,
High Sensitivity,高灵敏度,
Hold,暂缓处理,
Hold Invoice,暂缓处理费用清单,
Holiday,假期,
Holiday List,假期列表,
Hotel Rooms of type {0} are unavailable on {1},{0}类型的酒店客房不适用于{1},
Hotels,酒店,
Hourly,每小时,
Hours,小时,
How Pricing Rule is applied?,定价规则如何应用？,
Hub Category,集线器类别,
Hub Sync ID,集线器同步ID,
Human Resource,人力资源,
Human Resources,人力资源,
IGST Amount,IGST金额,
IP Address,IP地址,
ITC Available (whether in full op part),ITC可用（无论是全部操作部分）,
ITC Reversed,ITC逆转,
Identifying Decision Makers,确定决策者,
"If Auto Opt In is checked, then the customers will be automatically linked with the concerned Loyalty Program (on save)",如果选中自动选择，则客户将自动与相关的忠诚度计划链接（保存时）,
"If multiple Pricing Rules continue to prevail, users are asked to set Priority manually to resolve conflict.",如果几条价格规则同时使用，系统将提醒用户设置优先级。,
"If selected Pricing Rule is made for 'Rate', it will overwrite Price List. Pricing Rule rate is the final rate, so no further discount should be applied. Hence, in transactions like Sales Order, Purchase Order etc, it will be fetched in 'Rate' field, rather than 'Price List Rate' field.",如果选定的定价规则是针对“费率”制定的，则会覆盖价格清单。定价规则费率是最终费率，因此不应再应用更多折扣。因此，在诸如销售订单，采购订单等交易中，它将在&#39;费率&#39;字段中取代，而不是在&#39;价格清单单价&#39;字段中取出。,
"If two or more Pricing Rules are found based on the above conditions, Priority is applied. Priority is a number between 0 to 20 while default value is zero (blank). Higher number means it will take precedence if there are multiple Pricing Rules with same conditions.",如果存在多个价格规则，则会用优先级来区分。优先级是一个介于0到20的数字，默认值是零（或留空）。数字越大，意味着优先级别越高。,
"If unlimited expiry for the Loyalty Points, keep the Expiry Duration empty or 0.",如果忠诚度积分无限期到期，请将到期时间保持为空或0。,
"If you have any questions, please get back to us.",如果您有任何疑问，请再次与我们联系。,
Ignore Existing Ordered Qty,忽略现有的订购数量,
Image,图像,
Image View,图像视图,
Import Data,导入数据,
Import Day Book Data,导入日记簿数据,
Import Log,导入日志,
Import Master Data,导入主数据,
Import in Bulk,进口散装,
Import of goods,进口货物,
Import of services,进口服务,
Importing Items and UOMs,导入项目和UOM,
Importing Parties and Addresses,进口缔约方和地址,
In Maintenance,在维护中,
In Production,在生产中,
In Qty,数量,
In Stock Qty,库存量,
In Stock: ,有现货,
In Value,金额,
"In the case of multi-tier program, Customers will be auto assigned to the concerned tier as per their spent",在多层程序的情况下，客户将根据其花费自动分配到相关层,
Inactive,非活动的,
Incentives,激励政策,
Include Default FB Entries,包括默认工作簿条目,
Include Exploded Items,包含爆炸物料,
Include POS Transactions,包括POS交易,
Include UOM,包括基本计量单位,
Included in Gross Profit,包含在毛利润中,
Income,收益,
Income Account,收入科目,
Income Tax,所得税,
Incoming,来料检验,
Incoming Rate,入库库存评估价,
Incorrect number of General Ledger Entries found. You might have selected a wrong Account in the transaction.,总帐分录发现错误数字，可能是选择了错误的科目。,
Increment cannot be 0,增量不能为0,
Increment for Attribute {0} cannot be 0,增量属性{0}不能为0,
Indirect Expenses,间接支出,
Indirect Income,间接收益,
Individual,个人,
Ineligible ITC,不合格的ITC,
Initiated,已初始化,
Inpatient Record,住院病历,
Installation Note,安装通知单,
Installation Note {0} has already been submitted,安装单{0}已经提交了,
Installation date cannot be before delivery date for Item {0},物料{0}的安装日期不能早于交付日期,
Installing presets,安装预置,
Institute Abbreviation,机构缩写,
Institute Name,机构名称,
Instructor,讲师,
Insufficient Stock,库存不足,
Insurance Start date should be less than Insurance End date,保险开始日期应小于保险终止日期,
Integrated Tax,综合税,
Inter-State Supplies,国家间供应,
Internet Publishing,互联网出版,
Intra-State Supplies,国内供应,
Introduction,介绍,
Invalid Attribute,无效属性,
Invalid Blanket Order for the selected Customer and Item,无效框架订单对所选客户和物料无效,
Invalid Company for Inter Company Transaction.,公司间交易的公司无效。,
Invalid GSTIN! A GSTIN must have 15 characters.,GSTIN无效！ GSTIN必须有15个字符。,
Invalid GSTIN! First 2 digits of GSTIN should match with State number {0}.,GSTIN无效！ GSTIN的前2位数字应与州号{0}匹配。,
Invalid GSTIN! The input you've entered doesn't match the format of GSTIN.,GSTIN无效！您输入的输入与GSTIN的格式不匹配。,
Invalid Posting Time,记帐时间无效,
Invalid attribute {0} {1},无效的属性{0} {1},
Invalid quantity specified for item {0}. Quantity should be greater than 0.,项目{0}的数量无效，应为大于0的数字。,
Invalid reference {0} {1},无效的参考{0} {1},
Invalid {0},无效的{0},
Invalid {0} for Inter Company Transaction.,Inter Company Transaction无效{0}。,
Invalid {0}: {1},无效的{0}：{1},
Inventory,库存,
Investment Banking,投资银行业务,
Investments,投资,
Invoice,费用清单,
Invoice Created,已创建费用清单,
Invoice Discounting,发票贴现,
Invoice Patient Registration,患者登记费用清单,
Invoice Posting Date,费用清单记帐日期,
Invoice Type,费用清单类型,
Invoice already created for all billing hours,费用清单已在所有结算时间创建,
Invoice can't be made for zero billing hour,在零计费时间内无法开具费用清单,
Invoice {0} no longer exists,费用清单{0}不再存在,
Invoiced,已开费用清单,
Invoiced Amount,费用清单金额,
Invoices,费用清单,
Invoices for Costumers.,客户发票。,
Inward supplies from ISD,ISD的内向供应,
Inward supplies liable to reverse charge (other than 1 & 2 above),内向物品可能反向充电（上述1和2除外）,
Is Active,是活动的,
Is Default,是否默认,
Is Existing Asset,是对现有资产,
Is Frozen,被冻结,
Is Group,群组事件,
Issue,问题,
Issue Material,发料,
Issued,发行,
Issues,问题,
It is needed to fetch Item Details.,需要获取物料详细信息。,
Item,物料,
Item 1,项目1,
Item 2,物料2,
Item 3,物料3,
Item 4,物料4,
Item 5,物料5,
Item Cart,物料车,
Item Code,物料代码,
Item Code cannot be changed for Serial No.,物料代码不能因序列号改变,
Item Code required at Row No {0},行{0}中的物料代码是必须项,
Item Description,物料描述,
Item Group,物料群组,
Item Group Tree,物料群组树,
Item Group not mentioned in item master for item {0},物料{0}的所属的物料群组没有在物料主表中提及,
Item Name,项目名称,
Item Price added for {0} in Price List {1},物料价格{0}自动添加到价格清单{1}中了，以备以后订单使用,
"Item Price appears multiple times based on Price List, Supplier/Customer, Currency, Item, UOM, Qty and Dates.",物料价格根据价格表，供应商/客户，货币，物料，UOM，数量和日期多次出现。,
Item Price updated for {0} in Price List {1},物料价格{0}更新到价格清单{1}中了，之后的订单会使用新价格,
Item Row {0}: {1} {2} does not exist in above '{1}' table,项目行{0}：{1} {2}在上面的“{1}”表格中不存在,
Item Tax Row {0} must have account of type Tax or Income or Expense or Chargeable,物料税项行{0}中必须指定类型为税项/收益/支出/应课的科目。,
Item Template,物料模板,
Item Variant Settings,物料变式设置,
Item Variant {0} already exists with same attributes,项目变体{0}已经具有相同属性的存在,
Item Variants,物料变体,
Item Variants updated,项目变体已更新,
Item has variants.,物料有变体。,
Item must be added using 'Get Items from Purchase Receipts' button,项目必须要由“从采购收货单获取物料”按键添加,
Item valuation rate is recalculated considering landed cost voucher amount,物料评估价将基于到岸成本凭证金额重新计算,
Item variant {0} exists with same attributes,相同属性物料变体{0}已存在,
Item {0} does not exist,物料{0}不存在,
Item {0} does not exist in the system or has expired,物料{0}不存在于系统中或已过期,
Item {0} has already been returned,物料{0}已被退回,
Item {0} has been disabled,物料{0}已被禁用,
Item {0} has reached its end of life on {1},物料{0}已经到达寿命终止日期{1},
Item {0} ignored since it is not a stock item,产品{0}不属于库存产品，因此被忽略,
"Item {0} is a template, please select one of its variants",物料{0}是一个模板，请选择它的一个变体,
Item {0} is cancelled,物料{0}已取消,
Item {0} is disabled,物料{0}已被禁用,
Item {0} is not a serialized Item,物料{0}不是有序列号的物料,
Item {0} is not a stock Item,物料{0}不是库存物料,
Item {0} is not active or end of life has been reached,物料{0}处于非活动或寿命终止状态,
Item {0} is not setup for Serial Nos. Check Item master,物料{0}没有启用序列号管理功能，请检查物料主数据,
Item {0} is not setup for Serial Nos. Column must be blank,项目{0}没有启用序列好管理功能，序列号必须留空,
Item {0} must be a Fixed Asset Item,物料{0}必须被定义为是固定资产,
Item {0} must be a Sub-contracted Item,项目{0}必须是外包项目,
Item {0} must be a non-stock item,物料{0}必须是一个非库存物料,
Item {0} must be a stock Item,物料{0}必须是库存物料,
Item {0} not found,物料{0}未找到,
Item {0} not found in 'Raw Materials Supplied' table in Purchase Order {1},物料{0}未定义在采购订单{1}发给供应商的原材料清单中,
Item {0}: Ordered qty {1} cannot be less than minimum order qty {2} (defined in Item).,物料{0}的订单数量{1}不能小于最低订货量{2}（物料主数据中定义）。,
Item: {0} does not exist in the system,项目{0}不存在,
Items,物料,
Items Filter,物品过滤,
Items and Pricing,物料和定价,
Items for Raw Material Request,原料要求的项目,
Job Card,工作卡,
Job card {0} created,已创建作业卡{0},
Join,加入,
Journal Entries {0} are un-linked,手工凭证{0}没有关联,
Journal Entry,手工凭证,
Journal Entry {0} does not have account {1} or already matched against other voucher,手工凭证{0}没有科目{1}或已经匹配其他凭证,
Kanban Board,看板,
Key Reports,主要报告,
LMS Activity,LMS活动,
Lab Test,实验室测试,
Lab Test Report,实验室测试报表,
Lab Test Sample,实验室测试样品,
Lab Test Template,实验室测试模板,
Lab Test UOM,实验室测试基础计量单位,
Lab Tests and Vital Signs,实验室测试和重要标志,
Lab result datetime cannot be before testing datetime,实验结果日期时间不能在测试日期时间之前,
Lab testing datetime cannot be before collection datetime,实验室测试日期时间不能在收集日期时间之前,
Label,标签,
Laboratory,实验室,
Large,大,
Last Communication,最后沟通,
Last Communication Date,最后通讯日期,
Last Name,姓,
Last Order Amount,最后订单金额,
Last Order Date,最后订购日期,
Last Purchase Price,上次采购价格,
Last Purchase Rate,最后采购价格,
Latest,最新,
Latest price updated in all BOMs,最新价格在所有BOM中更新,
Lead,商机,
Lead Count,商机计数,
Lead Owner,线索负责人,
Lead Owner cannot be same as the Lead,线索负责人不能是线索本身,
Lead Time Days,交货天数,
Lead to Quotation,从线索到报价,
"Leads help you get business, add all your contacts and more as your leads",信息帮助你的业务，你所有的联系人和更添加为您的线索,
Learn,学习,
Leave Management,休假管理,
Leave and Attendance,休假和考勤,
Leave application {0} already exists against the student {1},对学生{1}已经存在申请{0},
Leaves has been granted sucessfully,叶子已成功获得,
Leaves must be allocated in multiples of 0.5,假期天数必须为0.5的倍数,
Ledger,分类账,
Legal,法律,
Legal Expenses,法律费用,
Letter Head,信头,
Letter Heads for print templates.,打印模板的信头。,
Level,级别,
Liability,负债,
Limit Crossed,限制交叉,
Link to Material Request,链接到材料请求,
List of all share transactions,所有股份交易清单,
List of available Shareholders with folio numbers,包含folio号码的可用股东名单,
Loading Payment System,加载支付系统,
Loan,贷款,
Loan Start Date and Loan Period are mandatory to save the Invoice Discounting,贷款开始日期和贷款期限是保存发票折扣的必要条件,
Loans (Liabilities),借款（负债）,
Loans and Advances (Assets),贷款及垫款（资产）,
Local,当地,
Logs for maintaining sms delivery status,日志维护短信发送状态,
Lost,遗失,
Lost Reasons,失去的原因,
Low,低,
Low Sensitivity,低灵敏度,
Lower Income,较低收益,
Loyalty Amount,忠诚金额,
Loyalty Point Entry,忠诚度积分,
Loyalty Points,忠诚度积分,
"Loyalty Points will be calculated from the spent done (via the Sales Invoice), based on collection factor mentioned.",忠诚度积分将根据所花费的完成量（通过销售费用清单）计算得出。,
Loyalty Points: {0},忠诚度积分：{0},
Loyalty Program,忠诚计划,
Main,主,
Maintenance,维护,
Maintenance Log,维护日志,
Maintenance Manager,维护经理,
Maintenance Schedule,维护计划,
Maintenance Schedule is not generated for all the items. Please click on 'Generate Schedule',维护计划没有为所有物料生成，请点击“生产计划”,
Maintenance Schedule {0} exists against {1},针对{1}存在维护计划{0},
Maintenance Schedule {0} must be cancelled before cancelling this Sales Order,取消此销售订单前必须取消维护计划{0},
Maintenance Status has to be Cancelled or Completed to Submit,维护状态必须取消或完成提交,
Maintenance User,维护用户,
Maintenance Visit,维护访问,
Maintenance Visit {0} must be cancelled before cancelling this Sales Order,取消此销售订单前必须取消维护访问{0},
Maintenance start date can not be before delivery date for Serial No {0},维护的开始日期不能早于序列号为{0}的交付日期,
Make,生成,
Make Payment,付款,
Make project from a template.,从模板创建项目。,
Making Stock Entries,创建手工入库,
Male,男性,
Manage Customer Group Tree.,管理客户群组,
Manage Sales Partners.,管理销售合作伙伴。,
Manage Sales Person Tree.,管理销售人员。,
Manage Territory Tree.,管理区域,
Manage your orders,管理您的订单,
Management,管理人员,
Manager,经理,
Managing Projects,项目管理,
Managing Subcontracting,管理外包,
Mandatory,强制性,
Mandatory field - Academic Year,必修课 - 学年,
Mandatory field - Get Students From,强制性领域 - 获得学生,
Mandatory field - Program,强制性字段 - 计划,
Manufacture,生产,
Manufacturer,制造商,
Manufacturer Part Number,制造商零件编号,
Manufacturing,生产,
Manufacturing Quantity is mandatory,生产数量为必须项,
Mapping,映射,
Mapping Type,映射类型,
Mark Absent,标记为缺勤,
Mark Half Day,标记半天,
Mark Present,标记为出勤,
Marketing,市场营销,
Marketing Expenses,市场营销费用,
Marketplace,商城,
Marketplace Error,市场错误,
Masters,主数据,
Match Payments with Invoices,匹配付款与发票,
Match non-linked Invoices and Payments.,匹配无链接的发票和付款。,
Material,材料,
Material Consumption,材料消耗,
Material Consumption is not set in Manufacturing Settings.,材料消耗未在生产设置中设置。,
Material Receipt,材料收讫,
Material Request,材料申请,
Material Request Date,材料申请日期,
Material Request No,物料申请编号,
"Material Request not created, as quantity for Raw Materials already available.",物料申请未创建，因为原物料的数量已经够用。,
Material Request of maximum {0} can be made for Item {1} against Sales Order {2},销售订单{2}中物料{1}的最大材料申请量为{0},
Material Request to Purchase Order,给采购订单的材料申请,
Material Request {0} is cancelled or stopped,材料申请{0}已取消或已停止,
Material Request {0} submitted.,材料申请{0}已提交。,
Material Transfer,材料转移,
Material Transferred,转移的材料,
Material to Supplier,给供应商的材料,
Max discount allowed for item: {0} is {1}%,物料{0}的最大折扣为 {1}%,
Max: {0},最大值：{0},
Maximum Samples - {0} can be retained for Batch {1} and Item {2}.,可以为批次{1}和物料{2}保留最大样本数量{0}。,
Maximum Samples - {0} have already been retained for Batch {1} and Item {2} in Batch {3}.,批次{1}和批次{3}中的项目{2}已保留最大样本数量{0}。,
Maximum discount for Item {0} is {1}%,第{0}项的最大折扣为{1}％,
Medical Code,医疗法,
Medical Code Standard,医疗代码标准,
Medical Department,医学系,
Medical Record,医疗记录,
Medium,中,
Member Activity,会员活动,
Member ID,会员ID,
Member Name,成员名字,
Member information.,会员信息。,
Membership,会员身份,
Membership Details,会员资格,
Membership ID,会员ID,
Membership Type,会员类型,
Memebership Details,会员细节,
Memebership Type Details,会员类型详细信息,
Merge,合并,
Merge Account,合并帐户,
Merge with Existing Account,与现有帐户合并,
"Merging is only possible if following properties are same in both records. Is Group, Root Type, Company",合并只能在以下属性中在两个记录中相同。是群组，根类型，公司,
Message Examples,消息示例,
Message Sent,消息已发送,
Method,方法,
Middle Income,中等收入,
Middle Name,中间名,
Middle Name (Optional),中间名（可选）,
Min Amt can not be greater than Max Amt,Min Amt不能大于Max Amt,
Min Qty can not be greater than Max Qty,最小数量不能大于最大数量,
Minimum Lead Age (Days),最低交货期（天）,
Miscellaneous Expenses,杂项费用,
Missing Currency Exchange Rates for {0},{0}没有货币汇率,
Missing email template for dispatch. Please set one in Delivery Settings.,缺少发送的电子邮件模板。请在“发送设置”中设置一个。,
"Missing value for Password, API Key or Shopify URL",缺少密码，API密钥或Shopify网址的值,
Mode of Payment,付款方式,
Mode of Payments,付款方式,
Mode of Transport,交通方式,
Mode of Transportation,运输方式,
Model,模型,
Moderate Sensitivity,中等敏感度,
Monday,星期一,
Monthly,每月,
Monthly Distribution,月度分布,
More,更多,
More Information,更多信息,
More...,更多...,
Motion Picture & Video,影视业,
Move,移动,
Move Item,移动物料,
Multi Currency,多币种,
Multiple Item prices.,多个物料的价格。,
Multiple Loyalty Program found for the Customer. Please select manually.,为客户找到多个忠诚度计划。请手动选择。,
"Multiple Price Rules exists with same criteria, please resolve conflict by assigning priority. Price Rules: {0}",海报价格规则，同样的标准存在，请分配优先级解决冲突。价格规则：{0},
Multiple Variants,多种变体,
Multiple fiscal years exist for the date {0}. Please set company in Fiscal Year,多个会计年度的日期{0}存在。请设置公司财年,
Music,音乐,
Name error: {0},名称错误：{0},
Name of new Account. Note: Please don't create accounts for Customers and Suppliers,新科目的名称。注：请不要创建科目的客户和供应商,
Name or Email is mandatory,姓名或电子邮件信息必填,
Nature Of Supplies,供应的性质,
Navigating,导航,
Needs Analysis,需求分析,
Negative Quantity is not allowed,不允许负数量,
Negative Valuation Rate is not allowed,评估价不可以为负数,
Negotiation/Review,谈判/评论,
Net Asset value as on,净资产值作为,
Net Cash from Financing,从融资产生的净现金,
Net Cash from Investing,从投资产生的净现金,
Net Cash from Operations,从运营产生的净现金,
Net Change in Accounts Payable,应付账款净额变化,
Net Change in Accounts Receivable,应收账款净额变化,
Net Change in Cash,现金净变动,
Net Change in Equity,净资产收益变化,
Net Change in Fixed Asset,固定资产净变动,
Net Change in Inventory,库存净变动,
Net ITC Available(A) - (B),净ITC可用（A） - （B）,
Net Profit,净利,
Net Total,总净,
New Account Name,新建科目名称,
New Address,新地址,
New BOM,新建物料清单,
New Batch ID (Optional),新批号（可选）,
New Batch Qty,新批量,
New Company,新建公司,
New Cost Center Name,新建成本中心名称,
New Customer Revenue,新客户收入,
New Customers,新客户,
New Department,新部门,
New Employee,新员工,
New Location,新位置,
New Quality Procedure,新的质量程序,
New Sales Invoice,新的销售费用清单,
New Sales Person Name,新销售人员的姓名,
New Serial No cannot have Warehouse. Warehouse must be set by Stock Entry or Purchase Receipt,新序列号不能有仓库，仓库只能通过手工库存移动和采购收货单设置。,
New Warehouse Name,新仓库名称,
New credit limit is less than current outstanding amount for the customer. Credit limit has to be atleast {0},新的信用额度小于该客户未付总额。信用额度至少应该是 {0},
New task,新任务,
New {0} pricing rules are created,创建新的{0}定价规则,
Newspaper Publishers,报纸出版商,
Next,下一个,
Next Contact By cannot be same as the Lead Email Address,下一个联络人不能与线索的邮箱地址相同,
Next Contact Date cannot be in the past,接下来跟日期不能过去,
Next Steps,下一步,
No Action,没有行动,
No Customers yet!,还没有客户！,
No Data,无数据,
No Delivery Note selected for Customer {},没有为客户{}选择销售出货单,
No Item with Barcode {0},没有条码为{0}的物料,
No Item with Serial No {0},没有序列号为{0}的物料,
No Items available for transfer,无可移转物料,
No Items selected for transfer,请选择物料,
No Items to pack,未选择需打包物料,
No Items with Bill of Materials to Manufacture,待生产物料没有物料清单,
No Items with Bill of Materials.,没有物料清单的物品。,
No Permission,无此权限,
No Remarks,暂无说明,
No Result to submit,没有结果提交,
No Student Groups created.,没有学生团体创建的。,
No Students in,没有学生,
No Tax Withholding data found for the current Fiscal Year.,未找到当前财年的预扣税数据。,
No Work Orders created,没有创建工单,
No accounting entries for the following warehouses,没有以下仓库的会计分录,
No contacts with email IDs found.,找不到与电子邮件ID的联系人。,
No data for this period,此期间没有数据,
No description given,未提供描述,
No employees for the mentioned criteria,没有员工提到的标准,
No gain or loss in the exchange rate,汇率没有收益或损失,
No items listed,没有物料,
No items to be received are overdue,没有收到的物品已逾期,
No material request created,没有创建重要请求,
No of Interactions,交互数,
No of Shares,股份数目,
No pending Material Requests found to link for the given items.,找不到针对给定项目链接的待处理物料请求。,
No products found,没有找到产品,
No products found.,找不到产品。,
No record found,未找到记录,
No records found in the Invoice table,没有在费用清单表中找到记录,
No records found in the Payment table,没有在支付表中找到记录,
No tasks,没有任务,
No time sheets,没有考勤表,
No values,没有价值,
No {0} found for Inter Company Transactions.,Inter公司没有找到{0}。,
Non GST Inward Supplies,非消费税进口供应,
Non Profit,非营利,
Non Profit (beta),非营利（测试版）,
Non-GST outward supplies,非商品及服务税外向供应,
Non-Group to Group,非群组转为群组,
None,没有,
None of the items have any change in quantity or value.,没有一个项目无论在数量或价值的任何变化。,
Nos,Nos,
Not Available,不可用,
Not Marked,未标记,
Not Paid and Not Delivered,没有支付，未送达,
Not Permitted,不允许,
Not Started,未开始,
Not active,非活动,
Not allow to set alternative item for the item {0},不允许为项目{0}设置替代项目,
Not allowed to update stock transactions older than {0},不允许对早于{0}的库存交易进行更新,
Not authorized to edit frozen Account {0},无权修改冻结科目{0},
Not authroized since {0} exceeds limits,不允许，因为{0}超出范围,
Not permitted for {0},不允许{0},
"Not permitted, configure Lab Test Template as required",不允许，根据需要配置实验室测试模板,
Not permitted. Please disable the Service Unit Type,不允许。请禁用服务单位类型,
Note: Due / Reference Date exceeds allowed customer credit days by {0} day(s),注意：到期日/计入日已超过客户信用日期{0}天。,
Note: Item {0} entered multiple times,注意：物料{0}已多次输入,
Note: Payment Entry will not be created since 'Cash or Bank Account' was not specified,注意：“现金或银行科目”未指定，付款凭证不会创建,
Note: System will not check over-delivery and over-booking for Item {0} as quantity or amount is 0,注意：系统将不会为物料{0}检查超额发货或超额预订，因为其数量或金额为0,
Note: There is not enough leave balance for Leave Type {0},注意：休假期类型{0}的剩余天数不够,
Note: This Cost Center is a Group. Cannot make accounting entries against groups.,注：此成本中心是一个组，会计分录不能对组录入。,
Note: {0},注： {0},
Notes,便签,
Nothing is included in gross,毛不包含任何内容,
Nothing more to show.,没有更多内容。,
Notify Customers via Email,通过电子邮件通知客户,
Number,数,
Number of Depreciations Booked cannot be greater than Total Number of Depreciations,预订折旧数不能超过折旧总数,
Number of Interaction,接洽次数,
Number of Order,订购次数,
"Number of new Account, it will be included in the account name as a prefix",新帐号的数量，将作为前缀包含在帐号名称中,
"Number of new Cost Center, it will be included in the cost center name as a prefix",新成本中心的数量，它将作为前缀包含在成本中心名称中,
Number of root accounts cannot be less than 4,root帐户数不能少于4,
Odometer,里程表,
Office Equipments,办公设备,
Office Maintenance Expenses,办公维护费用,
Office Rent,办公室租金,
On Hold,暂缓处理,
On Net Total,基于净总计,
One customer can be part of only single Loyalty Program.,一个客户只能参与一个忠诚度计划。,
Online Auctions,网上拍卖,
"Only the Student Applicant with the status ""Approved"" will be selected in the table below.",下表中将只选择状态为“已批准”的学生申请人。,
Only users with {0} role can register on Marketplace,只有{0}角色的用户才能在市场上注册,
Open BOM {0},开放物料清单 {0},
Open Item {0},打开物料{0},
Open Notifications,打开通知,
Open Orders,开放订单,
Open a new ticket,打开一张新票,
Opening,开帐,
Opening (Cr),期初（贷方 ）,
Opening (Dr),期初（借方）,
Opening Accounting Balance,初始科目余额,
Opening Accumulated Depreciation,期初累计折旧,
Opening Accumulated Depreciation must be less than equal to {0},期初累计折旧必须小于等于{0},
Opening Balance,期初余额,
Opening Balance Equity,期初余额权益,
Opening Date and Closing Date should be within same Fiscal Year,开帐日期和结帐日期应在同一会计年度,
Opening Date should be before Closing Date,开业日期应该是截止日期之前，,
Opening Entry Journal,开帐凭证,
Opening Invoice Creation Tool,费用清单创建工具,
Opening Invoice Item,待处理费用清单项,
Opening Invoices,待创建费用清单,
Opening Invoices Summary,待创建费用清单汇总,
Opening Qty,期初数量,
Opening Stock,期初库存,
Opening Stock Balance,期初存货余额,
Opening Value,期初金额,
Opening {0} Invoice created,期初{0}已创建费用清单,
Operation,操作,
Operation Time must be greater than 0 for Operation {0},运行时间必须大于0的操作{0},
"Operation {0} longer than any available working hours in workstation {1}, break down the operation into multiple operations",在工作站，操作{0}比任何可用的工作时间更长{1}，分解成运行多个操作,
Operations,操作,
Operations cannot be left blank,操作不能留空,
Opp Count,商机计数,
Opp/Lead %,机会 / 商机％,
Opportunities,机会,
Opportunities by lead source,商机来源的机会,
Opportunity,机会,
Opportunity Amount,机会金额,
"Optional. Sets company's default currency, if not specified.",可选。设置公司的默认货币，如果没有指定。,
Optional. This setting will be used to filter in various transactions.,可选。此设置将被应用于各种交易进行过滤。,
Options,选项,
Order Count,订单计数,
Order Entry,订单输入,
Order Value,订单价值,
Order rescheduled for sync,订单重新安排同步,
Order/Quot %,订单/报价％,
Ordered,已下单,
Ordered Qty,订单数量,
"Ordered Qty: Quantity ordered for purchase, but not received.",订购数量：订购数量的报价，但没有收到。,
Orders,订单,
Orders released for production.,工单已审批可开始生产。,
Organization,组织,
Organization Name,组织名称,
Other,其他,
Other Reports,其他报表,
"Other outward supplies(Nil rated,Exempted)",其他外向供应（未评级，豁免）,
Out Qty,发出数量,
Out Value,输出值,
Out of Order,乱序,
Outgoing,出货检验,
Outstanding,未付,
Outstanding Amount,未付金额,
Outstanding Amt,未付金额,
Outstanding Cheques and Deposits to clear,待清帐支票及存款,
Outstanding for {0} cannot be less than zero ({1}),未付{0}不能小于零（ {1} ）,
Outward taxable supplies(zero rated),外向应税物资（零评级）,
Overdue,逾期,
Overlap in scoring between {0} and {1},{0}和{1}之间的得分重叠,
Overlapping conditions found between:,之间存在重叠的条件：,
Owner,业主,
PAN,泛,
POS,销售终端,
POS Profile,销售终端配置,
POS Profile is required to use Point-of-Sale,销售终端配置文件需要使用销售点,
POS Profile required to make POS Entry,请创建POS配置记录,
POS Settings,POS设置,
Packed quantity must equal quantity for Item {0} in row {1},第{1}行的打包数量必须等于物料{0}数量,
Packing Slip,装箱单,
Packing Slip(s) cancelled,装箱单（ S）取消,
Paid,已付款,
Paid Amount,已付金额,
Paid Amount cannot be greater than total negative outstanding amount {0},支付金额不能大于总未付金额{0},
Paid amount + Write Off Amount can not be greater than Grand Total,已支付的金额+销帐金额不能大于总金额,
Paid and Not Delivered,已支付但未送达,
Parameter,参数,
Parent Item {0} must not be a Stock Item,上级项{0}不能是库存产品,
Parents Teacher Meeting Attendance,家长老师见面会,
Partially Depreciated,部分贬值,
Partially Received,部分收到,
Party,往来单位,
Party Name,往来单位名称,
Party Type,往来单位类型,
Party Type and Party is mandatory for {0} account,科目{0}业务伙伴类型及业务伙伴信息必填,
Party Type is mandatory,请输入往来单位类型,
Party is mandatory,请输入往来单位,
Past Due Date,过去的截止日期,
Patient,患者,
Patient Appointment,患者预约,
Patient Encounter,患者遭遇,
Patient not found,患者未找到,
Pay Remaining,支付剩余,
Pay {0} {1},支付{0} {1},
Payable,应付,
Payable Account,应付科目,
Payment,付款,
Payment Cancelled. Please check your GoCardless Account for more details,付款已取消。请检查您的GoCardless科目以了解更多信息,
Payment Confirmation,付款确认,
Payment Document,付款单据,
Payment Due Date,付款到期日,
Payment Entries {0} are un-linked,付款凭证{0}没有关联,
Payment Entry,付款凭证,
Payment Entry already exists,付款凭证已存在,
Payment Entry has been modified after you pulled it. Please pull it again.,获取付款凭证后有修改，请重新获取。,
Payment Entry is already created,付款凭证已创建,
Payment Failed. Please check your GoCardless Account for more details,支付失败。请检查您的GoCardless科目以了解更多信息,
Payment Gateway,支付网关,
"Payment Gateway Account not created, please create one manually.",支付网关科目没有创建，请手动创建一个。,
Payment Gateway Name,支付网关名称,
Payment Mode,付款方式,
Payment Receipt Note,付款收据,
Payment Request,付款申请,
Payment Request for {0},付款申请{0},
Payment Tems,付款方式,
Payment Term,付款期限,
Payment Terms,付款条件,
Payment Terms Template,付款条款模板,
Payment Terms based on conditions,付款条款基于条件,
Payment Type,付款类型,
"Payment Type must be one of Receive, Pay and Internal Transfer",付款方式必须是收、付或内部转结之一,
Payment against {0} {1} cannot be greater than Outstanding Amount {2},对{0} {1}的付款不能大于总未付金额{2},
Payment request {0} created,已创建付款申请{0},
Payments,付款,
Payroll Payable,应付职工薪资,
Payslip,工资单,
Pending Activities,待活动,
Pending Amount,待审核金额,
Pending Leaves,待审批的休假,
Pending Qty,待定数量,
Pending Quantity,待定数量,
Pending Review,待审核,
Pending activities for today,今天待定活动,
Pension Funds,养老基金,
Percentage Allocation should be equal to 100%,百分比分配应该等于100 ％,
Perception Analysis,感知分析,
Period,期,
Period Closing Entry,期末结帐凭证,
Period Closing Voucher,期末结帐凭证,
Periodicity,周期性,
Personal Details,个人资料,
Pharmaceutical,医药,
Pharmaceuticals,制药,
Physician,医师,
Place Of Supply (State/UT),供应地点（州/ UT）,
Place Order,下订单,
Plan Name,计划名称,
Plan for maintenance visits.,规划维护访问。,
Planned Qty,计划数量,
"Planned Qty: Quantity, for which, Work Order has been raised, but is pending to be manufactured.",计划数量：数量，已为此工作订单提出，但尚待制造。,
Planning,规划,
Plants and Machineries,植物和机械设备,
Please Set Supplier Group in Buying Settings.,请在采购设置中设置供应商组。,
Please add a Temporary Opening account in Chart of Accounts,请在会计科目表中添加一个临时开帐科目,
Please add the account to root level Company - ,请将帐户添加到根级公司 -,
Please check Multi Currency option to allow accounts with other currency,请选择多币种选项以允许账户有其他货币,
Please click on 'Generate Schedule',请点击“生成表”,
Please click on 'Generate Schedule' to fetch Serial No added for Item {0},请点击“生成表”来获取序列号增加了对项目{0},
Please click on 'Generate Schedule' to get schedule,请在“生成表”点击获取工时单,
Please create purchase receipt or purchase invoice for the item {0},请为物料{0}创建采购收货单或采购费用清单,
Please define grade for Threshold 0%,请定义等级为阈值0％,
Please enable Applicable on Booking Actual Expenses,请启用适用于预订实际费用,
Please enable Applicable on Purchase Order and Applicable on Booking Actual Expenses,请启用适用于采购订单并适用于预订实际费用,
Please enable pop-ups,请启用弹出窗口,
Please enter 'Is Subcontracted' as Yes or No,请输入'转包' YES或NO,
Please enter API Consumer Key,请输入API使用者密钥,
Please enter API Consumer Secret,请输入API消费者密码,
Please enter Account for Change Amount,请输入零钱科目,
Please enter Approving Role or Approving User,请输入角色核准或审批用户,
Please enter Cost Center,请输入成本中心,
Please enter Delivery Date,请输入交货日期,
Please enter Employee Id of this sales person,请输入这个销售人员的员工标识,
Please enter Expense Account,请输入您的费用科目,
Please enter Item Code to get Batch Number,请输入产品代码来获得批号,
Please enter Item Code to get batch no,请输入物料代码，以获得批号,
Please enter Item first,请先输入物料,
Please enter Maintaince Details first,请先输入客户拜访（维护）信息,
Please enter Planned Qty for Item {0} at row {1},请输入计划数量的项目{0}在行{1},
Please enter Preferred Contact Email,请输入首选电子邮件联系,
Please enter Production Item first,请先输入待生产物料,
Please enter Purchase Receipt first,请先输入采购收货单号,
Please enter Receipt Document,请输入收据凭证,
Please enter Reference date,参考日期请输入,
Please enter Reqd by Date,请输入按日期请求,
Please enter Woocommerce Server URL,请输入Woocommerce服务器网址,
Please enter Write Off Account,请输入销帐科目,
Please enter atleast 1 invoice in the table,请在表中至少输入1张费用清单,
Please enter company first,请先输入公司,
Please enter company name first,请先输入公司名称,
Please enter default currency in Company Master,请在公司设置中维护默认货币,
Please enter message before sending,在发送前，请填写留言,
Please enter parent cost center,请输入父成本中心,
Please enter quantity for Item {0},请输入物料{0}数量,
Please enter relieving date.,请输入离职日期。,
Please enter valid Financial Year Start and End Dates,请输入有效的财务年度开始和结束日期,
Please enter valid email address,请输入有效的电子邮件地址,
Please enter {0} first,请先输入{0},
Please fill in all the details to generate Assessment Result.,请填写所有详细信息以生成评估结果。,
Please identify/create Account (Group) for type - {0},请为类型{0}标识/创建帐户（组）,
Please identify/create Account (Ledger) for type - {0},请为类型{0}标识/创建帐户（分类帐）,
Please login as another user to register on Marketplace,请以另一个用户身份登录以在Marketplace上注册,
Please make sure you really want to delete all the transactions for this company. Your master data will remain as it is. This action cannot be undone.,请确保你真的要删除这家公司的所有交易。主数据将保持原样。这个动作不能撤消。,
Please mention Basic and HRA component in Company,请在公司中提及基本和HRA组件,
Please mention Round Off Account in Company,请在公司中提及圆整账户,
Please mention Round Off Cost Center in Company,请在公司中提及圆整成本中心,
Please mention no of visits required,请注明无需访问,
Please mention the Lead Name in Lead {0},请提及潜在客户名称{0},
Please pull items from Delivery Note,请从销售出货单获取物料,
Please register the SIREN number in the company information file,请在公司信息文件中注册SIREN号码,
Please remove this Invoice {0} from C-Form {1},请删除此费用清单{0}从C-表格{1},
Please save the patient first,请先保存患者,
Please save the report again to rebuild or update,请再次保存报告以重建或更新,
"Please select Allocated Amount, Invoice Type and Invoice Number in atleast one row",请ATLEAST一行选择分配金额，费用清单类型和费用清单号码,
Please select Apply Discount On,请选择适用的折扣,
Please select BOM against item {0},请选择物料{0}的物料清单,
Please select BOM for Item in Row {0},请为第{0}行的物料指定物料清单,
Please select BOM in BOM field for Item {0},请为物料{0}在主数据中设置BOM（字段）,
Please select Category first,请先选择类别。,
Please select Charge Type first,请先选择费用类型,
Please select Company,请选择公司,
Please select Company and Posting Date to getting entries,请选择公司和发布日期以获取条目,
Please select Company first,请首先选择公司,
Please select Completion Date for Completed Asset Maintenance Log,请选择已完成资产维护日志的完成日期,
Please select Completion Date for Completed Repair,请选择完成修复的完成日期,
Please select Course,请选择课程,
Please select Drug,请选择药物,
Please select Existing Company for creating Chart of Accounts,请选择现有的公司创建会计科目表,
Please select Healthcare Service,请选择医疗保健服务,
"Please select Item where ""Is Stock Item"" is ""No"" and ""Is Sales Item"" is ""Yes"" and there is no other Product Bundle",请选择项，其中“正股项”是“否”和“是销售物料”是“是”，没有其他产品捆绑,
Please select Maintenance Status as Completed or remove Completion Date,请选择维护状态为已完成或删除完成日期,
Please select Party Type first,请先选择往来单位,
Please select Patient,请选择患者,
Please select Patient to get Lab Tests,请选择患者以获得实验室测试,
Please select Posting Date before selecting Party,在选择往来单位之前请先选择记帐日期,
Please select Posting Date first,请先选择记帐日期,
Please select Price List,请选择价格清单,
Please select Program,请选择程序,
Please select Qty against item {0},请选择为物料{0}指定数量,
Please select Sample Retention Warehouse in Stock Settings first,请先在库存设置中选择留存样品仓库,
Please select Start Date and End Date for Item {0},请选择开始日期和结束日期的项目{0},
Please select Student Admission which is mandatory for the paid student applicant,请选择学生报到，这是对已付费学生申请者是强制性的,
Please select a BOM,请选择一个物料清单,
Please select a Batch for Item {0}. Unable to find a single batch that fulfills this requirement,请选择项目{0}的批次。无法找到满足此要求的单个批次,
Please select a Company,请选择一个公司,
Please select a batch,请选择一个批次,
Please select a field to edit from numpad,请选择要从数字键盘编辑的字段,
Please select a table,请选择一张桌子,
Please select a valid Date,请选择一个有效的日期,
Please select a value for {0} quotation_to {1},请选择一个值{0} quotation_to {1},
Please select a warehouse,请选择一个仓库,
Please select at least one domain.,请选择至少一个域名。,
Please select correct account,请选择正确的科目,
Please select date,请选择日期,
Please select item code,请选择商品代码,
Please select month and year,请选择年份和月份,
Please select the Company,请选择公司,
Please select the Multiple Tier Program type for more than one collection rules.,请为多个收集规则选择多层程序类型。,
Please select the assessment group other than 'All Assessment Groups',请选择“所有评估组”以外的评估组,
Please select the document type first,请先选择文档类型,
Please select weekly off day,请选择每周休息日,
Please select {0},请选择{0},
Please select {0} first,请先选择{0},
Please set 'Apply Additional Discount On',请设置“额外折扣基于”,
Please set 'Asset Depreciation Cost Center' in Company {0},请在公司{0}设置“资产折旧成本中心“,
Please set 'Gain/Loss Account on Asset Disposal' in Company {0},请在公司{0}制定“关于资产处置收益/损失科目”,
Please set Account in Warehouse {0} or Default Inventory Account in Company {1},请在仓库{0}中设置帐户或在公司{1}中设置默认库存帐户,
Please set B2C Limit in GST Settings.,请在GST设置中设置B2C限制。,
Please set Company,请设公司,
Please set Company filter blank if Group By is 'Company',如果按什么分组是“Company”，请设置公司过滤器空白,
Please set Default Payroll Payable Account in Company {0},请公司设定默认应付职工薪资科目{0},
Please set Depreciation related Accounts in Asset Category {0} or Company {1},请在资产类别{0}或公司{1}设置折旧相关科目,
Please set GST Accounts in GST Settings,请在GST设置中设置GST科目,
Please set Hotel Room Rate on {},请在{}上设置酒店房价,
Please set Number of Depreciations Booked,请设置折旧数预订,
Please set Unrealized Exchange Gain/Loss Account in Company {0},请在公司{0}中设置未实现汇兑损益科目,
Please set User ID field in an Employee record to set Employee Role,请在员工主数据里设置用户ID字段来分派员工角色,
Please set a default Holiday List for Employee {0} or Company {1},请为员工{0}或公司{1}设置一个默认的假日列表,
Please set account in Warehouse {0},请在仓库{0}中设置科目,
Please set an active menu for Restaurant {0},请设置餐馆{0}的有效菜单,
Please set associated account in Tax Withholding Category {0} against Company {1},请在针对公司{1}的预扣税分类{0}中设置关联帐户,
Please set at least one row in the Taxes and Charges Table,请在“税费和收费表”中至少设置一行,
Please set default Cash or Bank account in Mode of Payment {0},请为付款方式{0}设置默认的现金或银行科目,
Please set default account in Salary Component {0},请在薪资组件设置默认科目{0},
Please set default customer in Restaurant Settings,请在“餐厅设置”中设置默认客户,
Please set default {0} in Company {1},请在公司 {1}下设置在默认值{0},
Please set filter based on Item or Warehouse,根据项目或仓库请设置过滤器,
Please set leave policy for employee {0} in Employee / Grade record,请在员工/成绩记录中为员工{0}设置休假政策,
Please set recurring after saving,请设置保存后复发,
Please set the Customer Address,请设置客户地址,
Please set the Default Cost Center in {0} company.,请在{0}公司中设置默认成本中心。,
Please set the Email ID for the Student to send the Payment Request,请设置学生的电子邮件ID以发送付款申请,
Please set the Item Code first,请先设定商品代码,
Please set the Payment Schedule,请设置付款时间表,
Please set {0} for address {1},请为地址{1}设置{0},
Please setup Students under Student Groups,请设置学生组的学生,
Please specify Company,请注明公司,
Please specify Company to proceed,请注明公司进行,
Please specify a valid 'From Case No.',请指定一个有效的“从案号”,
Please specify a valid Row ID for row {0} in table {1},请指定行{0}在表中的有效行ID {1},
Please specify at least one attribute in the Attributes table,请指定属性表中的至少一个属性,
Please specify currency in Company,请公司指定的货币,
Please specify either Quantity or Valuation Rate or both,请注明无论是数量或估价率或两者,
Please specify from/to range,请从指定/至范围,
Please supply the specified items at the best possible rates,请在提供最好的利率规定的项目,
Please wait 3 days before resending the reminder.,请重新发送提醒之前请等待3天。,
Point of Sale,销售点,
Point-of-Sale,销售点,
Point-of-Sale Profile,POS配置,
Portal,门户,
Possible Supplier,可能的供应商,
Postal Expenses,邮政费用,
Posting Date,记帐日期,
Posting Date cannot be future date,记帐日期不能是未来的日期,
Posting Time,记帐时间,
Posting date and posting time is mandatory,记帐日期和记帐时间必填,
Posting timestamp must be after {0},记帐时间必须晚于{0},
Potential opportunities for selling.,销售的潜在机会,
Practitioner Schedule,从业者时间表,
Pre Sales,售前,
Preference,偏爱,
Prescribed Procedures,规定程序,
Prescription,处方,
Prescription Dosage,处方用量,
Prescription Duration,处方时间,
Prescriptions,处方,
Prev,上一页,
Preview,预览,
Previous Financial Year is not closed,上一财务年度未关闭,
Price,价格,
Price List,价格清单,
Price List Currency not selected,价格清单货币没有选择,
Price List Rate,价格清单单价,
Price List master.,价格清单主数据。,
Price List must be applicable for Buying or Selling,房产价格必须适用于采购或出售,
Price List {0} is disabled or does not exist,价格清单{0}禁用或不存在,
Price or product discount slabs are required,价格或产品折扣板是必需的,
Pricing,价钱,
Pricing Rule,定价规则,
"Pricing Rule is first selected based on 'Apply On' field, which can be Item, Item Group or Brand.",定价规则是基于“应用在”字段，可以是项目，项目组或品牌首先被选择的。,
"Pricing Rule is made to overwrite Price List / define discount percentage, based on some criteria.",定价规则是由覆盖价格清单/定义折扣百分比，基于某些条件制定的。,
Pricing Rule {0} is updated,定价规则{0}已更新,
Pricing Rules are further filtered based on quantity.,定价规则进一步过滤基于数量。,
Primary Address Details,主要地址信息,
Primary Contact Details,主要联系方式,
Print Format,打印格式,
Print IRS 1099 Forms,打印IRS 1099表格,
Print Report Card,打印报表卡,
Print Settings,打印设置,
Print and Stationery,打印和文具,
Print settings updated in respective print format,打印设置在相应的打印格式更新,
Print taxes with zero amount,打印零金额的税,
Printing and Branding,印刷及品牌,
Private Equity,私募股权投资,
Procedure,程序,
Process Day Book Data,处理日书数据,
Process Master Data,处理主数据,
Processing Chart of Accounts and Parties,处理会计科目和缔约方,
Processing Items and UOMs,处理物品和UOM,
Processing Party Addresses,处理方地址,
Processing Vouchers,处理优惠券,
Procurement,采购,
Produced Qty,生产数量,
Product,产品,
Product Bundle,产品包,
Product Search,产品搜索,
Production,生产,
Production Item,生产物料,
Products,产品展示,
Profit and Loss,损益表,
Profit for the year,年度利润,
Program,程序,
Program in the Fee Structure and Student Group {0} are different.,费用结构和学生组{0}中的课程是不同的。,
Program {0} does not exist.,程序{0}不存在。,
Program: ,程序：,
Progress % for a task cannot be more than 100.,为任务进度百分比不能超过100个。,
Project Collaboration Invitation,项目合作邀请,
Project Id,项目编号,
Project Manager,项目经理,
Project Name,项目名称,
Project Start Date,项目开始日期,
Project Status,项目状态,
Project Summary for {0},{0}的项目摘要,
Project Update.,项目更新。,
Project Value,项目价值,
Project activity / task.,项目活动/任务。,
Project master.,项目总经理,
Project-wise data is not available for Quotation,无工程数据，无法报价,
Projected,预期可用库存报表,
Projected Qty,预计数量,
Projected Quantity Formula,预计数量公式,
Projects,工程,
Proposal Writing,提案写作,
Proposal/Price Quote,提案/报价,
Prospecting,勘探,
Provisional Profit / Loss (Credit),临时溢利/（亏损）（信用）,
Publications,出版物,
Publish Items on Website,发布物料到网站上,
Published,发布时间,
Publishing,出版,
Purchase,采购,
Purchase Amount,采购金额,
Purchase Date,采购日期,
Purchase Invoice,采购费用清单,
Purchase Invoice {0} is already submitted,采购费用清单{0}已经提交了,
Purchase Manager,采购经理,
Purchase Master Manager,采购方面的主要经理,
Purchase Order,采购订单,
Purchase Order Amount,采购订单金额,
Purchase Order Amount(Company Currency),采购订单金额（公司货币）,
Purchase Order Date,采购订单日期,
Purchase Order Items not received on time,未按时收到采购订单项目,
Purchase Order number required for Item {0},请为物料{0}指定采购订单号,
Purchase Order to Payment,从采购订单到付款,
Purchase Order {0} is not submitted,采购订单{0}未提交,
Purchase Orders are not allowed for {0} due to a scorecard standing of {1}.,由于{1}的记分卡状态，{0}不允许采购订单。,
Purchase Orders given to Suppliers.,采购给供应商的订单。,
Purchase Price List,采购价格清单,
Purchase Receipt,采购收货单,
Purchase Receipt {0} is not submitted,采购收货单{0}未提交,
Purchase Tax Template,进项税模板,
Purchase User,购买用户,
Purchase orders help you plan and follow up on your purchases,采购订单帮助您规划和跟进您的采购,
Purchasing,采购,
Purpose must be one of {0},目的必须是一个{0},
Qty,数量,
Qty To Manufacture,生产数量,
Qty Total,数量总计,
Qty for {0},{0}数量,
Qualification,资历,
Quality,质量,
Quality Action,质量行动,
Quality Goal.,质量目标。,
Quality Inspection,质量检验,
Quality Inspection: {0} is not submitted for the item: {1} in row {2},质量检验：项目未提交{0}：行{2}中的{1},
Quality Management,质量管理,
Quality Meeting,质量会议,
Quality Procedure,质量程序,
Quality Procedure.,质量程序。,
Quality Review,质量审查,
Quantity,数量,
Quantity for Item {0} must be less than {1},物料{0}的数量必须小于{1},
Quantity in row {0} ({1}) must be same as manufactured quantity {2},行{0}中的数量({1})必须等于生产数量{2},
Quantity must be less than or equal to {0},量必须小于或等于{0},
Quantity must not be more than {0},数量不能超过{0},
Quantity required for Item {0} in row {1},行{1}中的物料{0}必须指定数量,
Quantity should be greater than 0,量应大于0,
Quantity to Make,待生产数量,
Quantity to Manufacture must be greater than 0.,量生产必须大于0。,
Quantity to Produce,生产数量,
Quantity to Produce can not be less than Zero,生产数量不能少于零,
Query Options,查询选项,
Queued for replacing the BOM. It may take a few minutes.,排队等待更换BOM。可能需要几分钟时间。,
Queued for updating latest price in all Bill of Materials. It may take a few minutes.,排队更新所有材料清单中的最新价格。可能需要几分钟。,
Quick Journal Entry,快速简化手工凭证,
Quot Count,报价计数,
Quot/Lead %,报价/铅％,
Quotation,报价,
Quotation {0} is cancelled,报价{0}已被取消,
Quotation {0} not of type {1},报价{0} 不属于{1}类型,
Quotations,报价,
"Quotations are proposals, bids you have sent to your customers",报价是你发送给客户的建议或出价,
Quotations received from Suppliers.,从供应商收到的报价。,
Quotations: ,报价：,
Quotes to Leads or Customers.,向潜在客户或客户发出的报价。,
RFQs are not allowed for {0} due to a scorecard standing of {1},由于{1}的记分卡，{0}不允许使用RFQ,
Range,范围,
Rate,单价,
Rate:,率：,
Rating,评分,
Raw Material,原材料,
Raw Materials,原材料,
Raw Materials cannot be blank.,原材料不能为空。,
Re-open,重新打开,
Read blog,阅读博客,
Read the ERPNext Manual,阅读ERPNext手册,
Reading Uploaded File,阅读上传的文件,
Real Estate,房地产,
Reason For Putting On Hold,搁置的理由,
Reason for Hold,保留原因,
Reason for hold: ,暂停原因：,
Receipt,收据,
Receipt document must be submitted,收到文件必须提交,
Receivable,应收账款,
Receivable Account,应收账款,
Received,收到,
Received On,收到的,
Received Quantity,收到的数量,
Received Stock Entries,收到的库存条目,
Receiver List is empty. Please create Receiver List,接收人列表为空。请创建接收人列表,
Recipients,收件人,
Reconcile,核消（对帐）,
"Record of all communications of type email, phone, chat, visit, etc.",包含电子邮件，电话，聊天，访问等所有通信记录,
Records,记录,
Redirect URL,URL重定向,
Ref,参考,
Ref Date,参考日期,
Reference,参考,
Reference #{0} dated {1},参考＃ {0}记载日期为{1},
Reference Date,参考日期,
Reference Doctype must be one of {0},参考文档类型必须是一个{0},
Reference Document,参考文献,
Reference Document Type,参考文档类型,
Reference No & Reference Date is required for {0},{0}需要参考编号与参考日期,
Reference No and Reference Date is mandatory for Bank transaction,银行交易中参考编号和参考日期必填,
Reference No is mandatory if you entered Reference Date,如果输入参考日期，参考编号是强制输入的,
Reference No.,参考编号。,
Reference Number,参考号码,
Reference Type,参考类型,
"Reference: {0}, Item Code: {1} and Customer: {2}",参考：{0}，物料代号：{1}和顾客：{2},
References,参考,
Refresh Token,刷新令牌,
Register,寄存器,
Rejected,拒绝,
Related,有关,
Relation with Guardian1,与监护人1的关系,
Relation with Guardian2,与监护人2的关系,
Release Date,发布日期,
Reload Linked Analysis,重新加载链接分析,
Remaining,剩余,
Remaining Balance,余额,
Remarks,备注,
Reminder to update GSTIN Sent,提醒更新GSTIN发送,
Remove item if charges is not applicable to that item,如果费用不适用某物料，请删除它,
Removed items with no change in quantity or value.,删除的项目在数量或价值没有变化。,
Reopen,重新打开,
Reorder Level,重订货水平,
Reorder Qty,再订购数量,
Repeat Customer Revenue,重复客户收入,
Repeat Customers,回头客,
Replace BOM and update latest price in all BOMs,更换BOM并更新所有BOM中的最新价格,
Replied,回答,
Report,报告,
Report Type,报告类型,
Report Type is mandatory,报表类型必填,
Reports,报告,
Reqd By Date,需求日期,
Reqd Qty,需要数量,
Request for Quotation,询价,
Request for Quotations,索取报价,
Request for Raw Materials,原材料申请,
Request for purchase.,请求您的报价。,
Request for quotation.,询价。,
Requested Qty,需求数量,
"Requested Qty: Quantity requested for purchase, but not ordered.",要求的数量：数量要求的报价，但没有下令。,
Requesting Site,请求网站,
Requesting payment against {0} {1} for amount {2},请求对付款{0} {1}量{2},
Requestor,请求者,
Required On,要求在,
Required Qty,需求数量,
Required Quantity,所需数量,
Reschedule,改期,
Research,研究,
Research & Development,研究与发展,
Researcher,研究员,
Resend Payment Email,重新发送付款电子邮件,
Reserve Warehouse,储备仓库,
Reserved Qty,预留数量,
Reserved Qty for Production,用于生产的预留数量,
Reserved Qty for Production: Raw materials quantity to make manufacturing items.,生产保留数量：生产制造项目的原材料数量。,
"Reserved Qty: Quantity ordered for sale, but not delivered.",版权所有数量：订购数量出售，但未交付。,
Reserved Warehouse is mandatory for Item {0} in Raw Materials supplied,发给供应供应商的原物料{0}其保留仓库字段必填,
Reserved for manufacturing,预留用于制造,
Reserved for sale,预留待售,
Reserved for sub contracting,留作分包合同,
Resistant,耐,
Resolve error and upload again.,解决错误并再次上传。,
Rest Of The World,世界其他地区,
Restart Subscription,重新启动订阅,
Restaurant,餐厅,
Result Date,结果日期,
Result already Submitted,结果已提交,
Resume,恢复,
Retail,零售,
Retail & Wholesale,零售及批发,
Retail Operations,零售业务,
Retained Earnings,留存收益,
Retention Stock Entry,留存样品手工库存移动,
Retention Stock Entry already created or Sample Quantity not provided,留存样品手工库存移动已创建或未提供“样品数量”,
Return,回报,
Return / Credit Note,退货/退款单,
Return / Debit Note,退货/借记单,
Returns,退货,
Reverse Journal Entry,反向手工凭证,
Review Invitation Sent,审核邀请已发送,
Review and Action,审查和行动,
Rooms Booked,客房预订,
Root Company,根公司,
Root Type,根类型,
Root Type is mandatory,根类型是强制性的,
Root cannot be edited.,根不能被编辑。,
Root cannot have a parent cost center,根本不能有一个父成本中心,
Round Off,四舍五入,
Rounded Total,圆整后金额,
Route,路线,
Row # {0}: ,行＃{0}：,
Row # {0}: Batch No must be same as {1} {2},行＃{0}：批号必须与{1} {2},
Row # {0}: Cannot return more than {1} for Item {2},行＃{0}：无法退回超过{1}的物料{2},
Row # {0}: Rate cannot be greater than the rate used in {1} {2},行＃{0}：速率不能大于{1} {2}中使用的速率,
Row # {0}: Serial No is mandatory,行＃{0}：序列号是必需的,
Row # {0}: Serial No {1} does not match with {2} {3},行＃{0}：序列号{1}不相匹配{2} {3},
Row #{0} (Payment Table): Amount must be negative,行＃{0}（付款表）：金额必须为负数,
Row #{0} (Payment Table): Amount must be positive,行＃{0}（付款表）：金额必须为正值,
Row #{0}: Account {1} does not belong to company {2},行＃{0}：科目{1}不属于公司{2},
Row #{0}: Allocated Amount cannot be greater than outstanding amount.,行＃{0}：已分配金额不能大于未付金额。,
"Row #{0}: Asset {1} cannot be submitted, it is already {2}",行＃{0}：资产{1}无法提交，这已经是{2},
Row #{0}: Cannot set Rate if amount is greater than billed amount for Item {1}.,行＃{0}：如果金额大于项目{1}的开帐单金额，则无法设置费率。,
Row #{0}: Clearance date {1} cannot be before Cheque Date {2},行＃{0}：清除日期{1}无法支票日期前{2},
Row #{0}: Duplicate entry in References {1} {2},行＃{0}：引用{1} {2}中的重复条目,
Row #{0}: Expected Delivery Date cannot be before Purchase Order Date,行＃{0}：预计交货日期不能在采购订单日期之前,
Row #{0}: Item added,行＃{0}：已添加项目,
Row #{0}: Journal Entry {1} does not have account {2} or already matched against another voucher,行＃{0}：日记条目{1}没有科目{2}或已经对另一凭证匹配,
Row #{0}: Not allowed to change Supplier as Purchase Order already exists,行＃{0}：不能更改供应商的采购订单已经存在,
Row #{0}: Please set reorder quantity,行＃{0}：请设置再订购数量,
Row #{0}: Please specify Serial No for Item {1},行＃{0}：请为物料{1}指定序号,
Row #{0}: Qty increased by 1,行＃{0}：数量增加1,
Row #{0}: Rate must be same as {1}: {2} ({3} / {4}) ,行＃{0}：速率必须与{1}：{2}（{3} / {4}）,
Row #{0}: Reference Document Type must be one of Expense Claim or Journal Entry,行＃{0}：参考文档类型必须是费用报销或手工凭证之一,
"Row #{0}: Reference Document Type must be one of Purchase Order, Purchase Invoice or Journal Entry",行＃{0}：参考文件类型必须是采购订单之一，采购费用清单或手工凭证,
Row #{0}: Rejected Qty can not be entered in Purchase Return,行＃{0}：拒收数量不能包含在采购退货数量中,
Row #{0}: Rejected Warehouse is mandatory against rejected Item {1},第{0}行物料{1}被拒收，其拒收仓库字段必填,
Row #{0}: Reqd by Date cannot be before Transaction Date,行号{0}：按日期请求不能在交易日期之前,
Row #{0}: Set Supplier for item {1},行＃{0}：设置供应商项目{1},
Row #{0}: Status must be {1} for Invoice Discounting {2},行＃{0}：发票贴现的状态必须为{1} {2},
"Row #{0}: The batch {1} has only {2} qty. Please select another batch which has {3} qty available or split the row into multiple rows, to deliver/issue from multiple batches",行＃{0}：批次{1}只有{2}数量。请选择具有{3}数量的其他批次，或将该行拆分成多行，以便从多个批次出货/发料,
Row #{0}: Timings conflicts with row {1},行＃{0}：与排时序冲突{1},
Row #{0}: {1} can not be negative for item {2},行＃{0}：{1}不能为负值对项{2},
Row No {0}: Amount cannot be greater than Pending Amount against Expense Claim {1}. Pending Amount is {2},行无{0}：金额不能大于金额之前对报销{1}。待审核金额为{2},
Row {0} : Operation is required against the raw material item {1},行{0}：对原材料项{1}需要操作,
Row {0}# Item {1} cannot be transferred more than {2} against Purchase Order {3},对于采购订单{3}，行{0}＃项目{1}不能超过{2},
Row {0}: Activity Type is mandatory.,行{0}：活动类型是强制性的。,
Row {0}: Advance against Customer must be credit,行{0}：预收客户款项须记在贷方,
Row {0}: Advance against Supplier must be debit,行{0}：对供应商预付应为借方,
Row {0}: Allocated amount {1} must be less than or equals to Payment Entry amount {2},行{0}：分配金额{1}必须小于或等于输入付款金额{2},
Row {0}: Allocated amount {1} must be less than or equals to invoice outstanding amount {2},行{0}：已分配金额{1}必须小于或等于费用清单余额{2},
Row {0}: An Reorder entry already exists for this warehouse {1},第{0}行：仓库{1}中已存在重订货记录,
Row {0}: Bill of Materials not found for the Item {1},行{0}：材料清单未找到物料{1},
Row {0}: Conversion Factor is mandatory,行{0}：转换系数必填,
Row {0}: Cost center is required for an item {1},行{0}：项目{1}需要费用中心,
Row {0}: Credit entry can not be linked with a {1},行{0}：信用记录无法被链接的{1},
Row {0}: Currency of the BOM #{1} should be equal to the selected currency {2},行{0}：BOM＃的货币{1}应等于所选货币{2},
Row {0}: Debit entry can not be linked with a {1},行{0}：借记分录不能与连接的{1},
Row {0}: Depreciation Start Date is required,行{0}：折旧开始日期是必需的,
Row {0}: Enter location for the asset item {1},行{0}：请为第{0}行的资产，即物料号{1}输入位置信息,
Row {0}: Exchange Rate is mandatory,行{0}：汇率是必须的,
Row {0}: Expected Value After Useful Life must be less than Gross Purchase Amount,行{0}：使用寿命后的预期值必须小于总采购额,
Row {0}: From Time and To Time is mandatory.,行{0}：开始时间和结束时间必填。,
Row {0}: From Time and To Time of {1} is overlapping with {2},行{0}：从时间和结束时间{1}是具有重叠{2},
Row {0}: From time must be less than to time,行{0}：从时间开始必须小于时间,
Row {0}: Hours value must be greater than zero.,行{0}：小时值必须大于零。,
Row {0}: Invalid reference {1},行{0}：无效参考{1},
Row {0}: Party / Account does not match with {1} / {2} in {3} {4},行{0}：往来单位/科目{1} / {2}与{3} {4}不匹配,
Row {0}: Party Type and Party is required for Receivable / Payable account {1},行{0}：请为应收/应付科目输入{1}往来单位类型和往来单位,
Row {0}: Payment against Sales/Purchase Order should always be marked as advance,行{0}：针对销售/采购订单收付款均须标记为预收/付,
Row {0}: Please check 'Is Advance' against Account {1} if this is an advance entry.,行{0}：如果预付凭证，请为科目{1}勾选'预付？'。,
Row {0}: Please set at Tax Exemption Reason in Sales Taxes and Charges,行{0}：请设置销售税和费用中的免税原因,
Row {0}: Please set the Mode of Payment in Payment Schedule,行{0}：请在付款时间表中设置付款方式,
Row {0}: Please set the correct code on Mode of Payment {1},行{0}：请在付款方式{1}上设置正确的代码,
Row {0}: Qty is mandatory,第{0}行的数量字段必填,
Row {0}: Quality Inspection rejected for item {1},行{0}：项目{1}的质量检验被拒绝,
Row {0}: UOM Conversion Factor is mandatory,行{0}：计量单位转换系数是必需的,
Row {0}: select the workstation against the operation {1},行{0}：根据操作{1}选择工作站,
Row {0}: {1} Serial numbers required for Item {2}. You have provided {3}.,{1}请为第{0}行的物料{2}指定序列号。你已经提供{3}。,
Row {0}: {1} must be greater than 0,行{0}：{1}必须大于0,
Row {0}: {1} {2} does not match with {3},行{0}：{1} {2}不相匹配{3},
Row {0}:Start Date must be before End Date,行{0} ：开始日期必须是之前结束日期,
Rows with duplicate due dates in other rows were found: {0},发现其他行中具有重复截止日期的行：{0},
Rules for adding shipping costs.,规则增加运输成本。,
Rules for applying pricing and discount.,规则适用的定价和折扣。,
S.O. No.,销售订单号,
SGST Amount,SGST金额,
SO Qty,销售订单数量,
Safety Stock,安全库存,
Salary,工资,
Salary Slip submitted for period from {0} to {1},从{0}到{1}期间的工资单已提交,
Salary Structure must be submitted before submission of Tax Ememption Declaration,薪酬结构必须在提交税务征收声明之前提交,
Sales,销售,
Sales Account,销售科目,
Sales Expenses,销售费用,
Sales Funnel,销售漏斗,
Sales Invoice,销售费用清单,
Sales Invoice {0} has already been submitted,销售费用清单{0}已提交过,
Sales Invoice {0} must be cancelled before cancelling this Sales Order,取消此销售订单前必须取消销售费用清单{0},
Sales Manager,销售经理,
Sales Master Manager,销售经理大师,
Sales Order,销售订单,
Sales Order Item,销售订单物料,
Sales Order required for Item {0},销售订单为物料{0}的必须项,
Sales Order to Payment,销售订单到付款,
Sales Order {0} is not submitted,销售订单{0}未提交,
Sales Order {0} is not valid,销售订单{0}无效,
Sales Order {0} is {1},销售订单{0} {1},
Sales Orders,销售订单,
Sales Partner,销售合作伙伴,
Sales Pipeline,销售渠道,
Sales Price List,销售价格清单,
Sales Return,销售退货,
Sales Summary,销售摘要,
Sales Tax Template,销售税模板,
Sales Team,销售团队,
Sales User,销售用户,
Sales and Returns,销售和退货,
Sales campaigns.,促销活动。,
Sales orders are not available for production,销售订单不可用于生产,
Salutation,称呼,
Same Company is entered more than once,公司代码在另一行已输入过，重复了,
Same item cannot be entered multiple times.,同一物料不能输入多次。,
Same supplier has been entered multiple times,同一个供应商已多次输入,
Sample Collection,样品收集,
Sample quantity {0} cannot be more than received quantity {1},采样数量{0}不能超过接收数量{1},
Sanctioned,核准,
Sand,砂,
Saturday,星期六,
Saving {0},保存{0},
Scan Barcode,扫条码,
Schedule,计划任务,
Schedule Admission,安排入场,
Schedule Course,课程工时单,
Schedule Date,计划任务日期,
Schedule Discharge,附表卸货,
Scheduled,已计划,
Scheduled Upto,计划的高级,
"Schedules for {0} overlaps, do you want to proceed after skiping overlaped slots ?",{0}计划任务重叠，是否要在滑动重叠的插槽后继续？,
Score cannot be greater than Maximum Score,分数不能超过最高得分更大,
Scorecards,记分卡,
Scrapped,报废,
Search,搜索,
Search Results,搜索结果,
Search Sub Assemblies,搜索子组件,
"Search by item code, serial number, batch no or barcode",按物料代码，序列号，批号或条码进行搜索,
"Seasonality for setting budgets, targets etc.",设置季节性的预算，目标等。,
Secret Key,密钥,
Secretary,秘书,
Section Code,部分代码,
Secured Loans,抵押贷款,
Securities & Commodity Exchanges,证券及商品交易,
Securities and Deposits,证券及存款,
See All Articles,查看所有文章,
See all open tickets,查看所有打开的门票,
See past orders,查看过去的订单,
See past quotations,查看过去的报价,
Select,选择,
Select Alternate Item,选择替代物料,
Select Attribute Values,选择属性值,
Select BOM,选择BOM,
Select BOM and Qty for Production,选择BOM和数量生产,
"Select BOM, Qty and For Warehouse",选择BOM，Qty和For Warehouse,
Select Batch,选择批次,
Select Batch Numbers,选择批号,
Select Brand...,选择品牌...,
Select Company,选择公司,
Select Company...,选择公司...,
Select Customer,选择客户,
Select Days,选择天数,
Select Default Supplier,选择默认供应商,
Select DocType,选择文档类型,
Select Fiscal Year...,选择财务年度...,
Select Item (optional),选择项目（可选）,
Select Items based on Delivery Date,根据交货日期选择物料,
Select Items to Manufacture,选择待生产物料,
Select Loyalty Program,选择忠诚度计划,
Select Patient,选择患者,
Select Possible Supplier,选择潜在供应商,
Select Quantity,选择数量,
Select Serial Numbers,选择序列号,
Select Target Warehouse,选择目标仓库,
Select Warehouse...,选择仓库...,
Select an account to print in account currency,选择一个科目以科目币别进行打印,
Select at least one value from each of the attributes.,从每个属性中至少选择一个值。,
Select change amount account,选择零钱科目,
Select company first,首先选择公司,
Select students manually for the Activity based Group,为基于活动的组手动选择学生,
Select the customer or supplier.,选择客户或供应商。,
Select the nature of your business.,选择您的业务的性质。,
Select the program first,首先选择程序,
Select to add Serial Number.,选择添加序列号。,
Select your Domains,选择您的域名,
Selected Price List should have buying and selling fields checked.,选定价格清单应该有买入和卖出的字段。,
Sell,销售,
Selling,销售,
Selling Amount,销售金额,
Selling Price List,销售价格清单,
Selling Rate,销售价,
"Selling must be checked, if Applicable For is selected as {0}",如果“适用于”的值为{0}，则必须选择“销售”,
Send Grant Review Email,发送格兰特回顾邮件,
Send Now,立即发送,
Send SMS,发送短信,
Send mass SMS to your contacts,向你的联系人群发短信。,
Sensitivity,灵敏度,
Sent,已发送,
Serial No and Batch,序列号和批次,
Serial No is mandatory for Item {0},序列号是物料{0}的必须项,
Serial No {0} does not belong to Batch {1},序列号{0}不属于批次{1},
Serial No {0} does not belong to Delivery Note {1},序列号{0}不属于销售出货单{1},
Serial No {0} does not belong to Item {1},序列号{0}不属于物料{1},
Serial No {0} does not belong to Warehouse {1},序列号{0}不属于仓库{1},
Serial No {0} does not belong to any Warehouse,序列号{0}不属于任何仓库,
Serial No {0} does not exist,序列号{0}不存在,
Serial No {0} has already been received,序列号{0}已收到过,
Serial No {0} is under maintenance contract upto {1},序列号{0}截至至{1}之前在年度保养合同内。,
Serial No {0} is under warranty upto {1},序列号{0}截至至{1}之前在保修内。,
Serial No {0} not found,序列号{0}未找到,
Serial No {0} not in stock,序列号{0}无库存,
Serial No {0} quantity {1} cannot be a fraction,序列号{0}的数量{1}不能是分数,
Serial Nos Required for Serialized Item {0},序列化的物料{0}必须指定序列号,
Serial Number: {0} is already referenced in Sales Invoice: {1},序号：{0}已在销售费用清单中引用：{1},
Serial Numbers,序列号,
Serial Numbers in row {0} does not match with Delivery Note,行{0}中的序列号与交货单不匹配,
Serial no {0} has been already returned,序列号{0}已被退回,
Serial number {0} entered more than once,序列号{0}已多次输入,
Serialized Inventory,序列化库存,
Series Updated,系列已更新,
Series Updated Successfully,系列已成功更新,
Series is mandatory,系列是必须项,
Service,服务,
Service Level Agreement,服务水平协议,
Service Level Agreement.,服务水平协议。,
Service Level.,服务水平。,
Service Stop Date cannot be after Service End Date,服务停止日期不能在服务结束日期之后,
Service Stop Date cannot be before Service Start Date,服务停止日期不能早于服务开始日期,
Services,服务,
"Set Default Values like Company, Currency, Current Fiscal Year, etc.",设置例如公司，货币，当前财务年度等的默认值,
Set Details,设置细节,
Set New Release Date,设置新的审批日期,
Set Project and all Tasks to status {0}?,将项目和所有任务设置为状态{0}？,
Set Status,设置状态,
Set Tax Rule for shopping cart,为购物车设置税收规则,
Set as Closed,设置为关闭,
Set as Completed,设为已完成,
Set as Lost,设置为输,
Set as Open,设置为打开,
Set default inventory account for perpetual inventory,设置永续库存模式下的默认库存科目,
Set this if the customer is a Public Administration company.,如果客户是公共管理公司，请设置此项。,
Set {0} in asset category {1} or company {2},在资产类别{1}或公司{2}中设置{0},
"Setting Events to {0}, since the Employee attached to the below Sales Persons does not have a User ID{1}",设置活动为{0}，因为附连到下面的销售者的员工不具有用户ID {1},
Setting defaults,设置默认值,
Setting up Email,设置电子邮件,
Setting up Email Account,设置电子邮件科目,
Setting up Employees,建立员工,
Setting up Taxes,设置税码及税务规则,
Setting up company,建立公司,
Settings,设置,
"Settings for online shopping cart such as shipping rules, price list etc.",网上购物车，如配送规则，价格清单等的设置,
Settings for website homepage,对网站的主页设置,
Settings for website product listing,网站产品列表的设置,
Settled,安定,
Setup Gateway accounts.,设置网关科目。,
Setup SMS gateway settings,短信网关的设置,
Setup cheque dimensions for printing,设置检查尺寸打印,
Setup default values for POS Invoices,设置POS费用清单的默认值,
Setup mode of POS (Online / Offline),POS（在线/离线）的设置模式,
Setup your Institute in ERPNext,在ERPNext中设置您的研究所,
Share Balance,份额平衡,
Share Ledger,Share Ledger,
Share Management,股份管理,
Share Transfer,股份转让,
Share Type,分享类型,
Shareholder,股东,
Ship To State,送到州,
Shipments,运输,
Shipping Address,销售出货地址,
"Shipping Address does not have country, which is required for this Shipping Rule",销售出货地址没有国家，这是运输规则所必需的,
Shipping rule only applicable for Buying,运费规则只适用于采购,
Shipping rule only applicable for Selling,运费规则仅适用于销售,
Shopify Supplier,Shopify供应商,
Shopping Cart,购物车,
Shopping Cart Settings,购物车设置,
Short Name,简称,
Shortage Qty,短缺数量,
Show Completed,显示已完成,
Show Cumulative Amount,显示累计金额,
Show Open,公开显示,
Show Opening Entries,显示开场条目,
Show Payment Details,显示付款信息,
Show Return Entries,显示返回条目,
Show Variant Attributes,显示变体属性,
Show Variants,显示变体,
Show closed,显示关闭,
Show exploded view,显示爆炸视图,
Show only POS,只显示POS,
Show unclosed fiscal year's P&L balances,显示未关闭的会计年度的盈亏平衡,
Show zero values,显示零值,
Silt,淤泥,
Single Variant,单一变种,
Single unit of an Item.,此物料的一件。,
"Skipping Leave Allocation for the following employees, as Leave Allocation records already exists against them. {0}",跳过以下员工的休假分配，因为已经存在针对他们的休假分配记录。 {0},
Slideshow,幻灯片,
Slots for {0} are not added to the schedule,{0}的插槽未添加到计划中,
Small,小,
Soap & Detergent,肥皂和洗涤剂,
Software,软件,
Software Developer,软件开发人员,
Softwares,软件,
Soil compositions do not add up to 100,土壤成分不加100,
Sold,出售,
Some emails are invalid,有些电子邮件无效,
Some information is missing,一些信息缺失,
Something went wrong!,发生错误！,
"Sorry, Serial Nos cannot be merged",抱歉，序列号无法合并,
Source,源,
Source Name,源名称,
Source Warehouse,源仓库,
Source and Target Location cannot be same,源和目标位置不能相同,
Source and target warehouse cannot be same for row {0},行{0}中的源和目标仓库不能相同,
Source and target warehouse must be different,源和目标仓库必须是不同的,
Source of Funds (Liabilities),资金来源（负债）,
Source warehouse is mandatory for row {0},行{0}中源仓库为必须项,
Specified BOM {0} does not exist for Item {1},物料{1}不存在于指定的BOM{0},
Split,分裂,
Split Batch,拆分批,
Split Issue,拆分问题,
Sports,体育,
Standard Buying,标准采购,
Standard Selling,标准销售,
Standard contract terms for Sales or Purchase.,销售或采购的标准合同条款。,
Start Date,开始日期,
Start Date of Agreement can't be greater than or equal to End Date.,协议的开始日期不得大于或等于结束日期。,
Start Year,开始年份,
Start date should be less than end date for Item {0},物料{0}的开始日期必须小于结束日期,
Start date should be less than end date for task {0},开始日期应该小于任务{0}的结束日期,
Start day is greater than end day in task '{0}',开始日期大于任务“{0}”的结束日期,
Start on,开始,
State,州,
State/UT Tax,州/ UT税,
Statement of Account,对账单,
Status must be one of {0},状态必须是{0}中的一个,
Stock,库存,
Stock Adjustment,库存调整,
Stock Analytics,库存分析,
Stock Assets,库存资产,
Stock Available,可用库存,
Stock Balance,库存余额,
Stock Entries already created for Work Order ,已为工单创建的库存条目,
Stock Entry,手工库存移动,
Stock Entry {0} created,手工库存移动{0}已创建,
Stock Entry {0} is not submitted,手工库存移动{0}不提交,
Stock Expenses,库存费用,
Stock In Hand,在手库存,
Stock Items,库存产品,
Stock Ledger,库存总帐,
Stock Ledger Entries and GL Entries are reposted for the selected Purchase Receipts,为所选采购收货单重新产生了库存和会计总帐凭证,
Stock Levels,库存水平,
Stock Liabilities,库存负债,
Stock Qty,库存数量,
Stock Received But Not Billed,已收货未开票/在途物资:/GR/IR,
Stock Reports,库存报表,
Stock Summary,库存摘要,
Stock Transactions,库存交易,
Stock UOM,库存计量单位,
Stock Value,库存值,
Stock balance in Batch {0} will become negative {1} for Item {2} at Warehouse {3},批次{0}中，仓库{3}中物料{2}的库存余额将变为{1},
Stock cannot be updated against Delivery Note {0},销售出货单{0}不能更新库存,
Stock cannot be updated against Purchase Receipt {0},库存不能对采购收货单进行更新{0},
Stock cannot exist for Item {0} since has variants,物料{0}不能有库存，因为他存在变体,
Stock transactions before {0} are frozen,早于{0}的库存事务已冻结,
Stop,停止,
Stopped,已停止,
"Stopped Work Order cannot be cancelled, Unstop it first to cancel",停止的工单不能取消，先取消停止,
Stores,仓库,
Student,学生,
Student Activity,学生活动,
Student Address,学生地址,
Student Admissions,学生入学,
Student Attendance,学生出勤,
"Student Batches help you track attendance, assessments and fees for students",学生批次帮助您跟踪学生的出勤，评估和费用,
Student Email Address,学生的电子邮件地址,
Student Email ID,学生的电子邮件ID,
Student Group,学生组,
Student Group Strength,学生组强度,
Student Group is already updated.,学生组已经更新。,
Student Group: ,学生组：,
Student ID,学生卡,
Student ID: ,学生卡：,
Student LMS Activity,学生LMS活动,
Student Mobile No.,学生手机号码,
Student Name,学生姓名,
Student Name: ,学生姓名：,
Student Report Card,学生报表卡,
Student is already enrolled.,学生已经注册。,
Student {0} - {1} appears Multiple times in row {2} & {3},学生{0}  -  {1}出现连续中多次{2}和{3},
Student {0} does not belong to group {1},学生{0}不属于组{1},
Student {0} exist against student applicant {1},学生{0}已存在学生申请{1}中,
"Students are at the heart of the system, add all your students",学生是系统的核心，添加所有的学生,
Sub Assemblies,半成品,
Sub Type,子类型,
Sub-contracting,分包,
Subcontract,外包,
Subject,主题,
Submit,提交,
Submit this Work Order for further processing.,提交此工单以进一步处理。,
Subscription,循环分录系列/循环凭证,
Subscription Management,订阅管理,
Subscriptions,订阅,
Subtotal,小计,
Successful,成功,
Successfully Reconciled,核消/对账成功,
Successfully Set Supplier,成功设置供应商,
Successfully created payment entries,成功创建付款条目,
Successfully deleted all transactions related to this company!,成功删除与该公司相关的所有交易！,
Sum of Scores of Assessment Criteria needs to be {0}.,评估标准的得分之和必须是{0}。,
Sum of points for all goals should be 100. It is {0},对所有目标点的总和应该是100。{0},
Summary,概要,
Summary for this month and pending activities,本月和待活动总结,
Summary for this week and pending activities,本周和待活动总结,
Sunday,星期天,
Suplier,供应商,
Supplier,供应商,
Supplier Group,供应商群组,
Supplier Group master.,供应商组主数据。,
Supplier Id,供应商编号,
Supplier Invoice Date cannot be greater than Posting Date,供应商费用清单的日期不能超过过帐日期更大,
Supplier Invoice No,供应商费用清单编号,
Supplier Invoice No exists in Purchase Invoice {0},供应商费用清单不存在采购费用清单{0},
Supplier Name,供应商名称,
Supplier Part No,供应商部件号,
Supplier Quotation,供应商报价,
Supplier Scorecard,供应商记分卡,
Supplier Warehouse mandatory for sub-contracted Purchase Receipt,外包采购收货单必须指定供应商仓库,
Supplier database.,供应商数据库。,
Supplier {0} not found in {1},在{1}中找不到供应商{0},
Supplier(s),供应商,
Supplies made to UIN holders,供应给UIN持有人的供应品,
Supplies made to Unregistered Persons,向未登记人员提供的物资,
Suppliies made to Composition Taxable Persons,对合成纳税人的补贴,
Supply Type,供应类型,
Support,支持,
Support Analytics,客户支持分析,
Support Settings,支持设置,
Support Tickets,支持门票,
Support queries from customers.,回应客户咨询。,
Susceptible,易感,
Sync has been temporarily disabled because maximum retries have been exceeded,暂时禁用了同步，因为已超出最大重试次数,
Syntax error in condition: {0},条件中的语法错误：{0},
Syntax error in formula or condition: {0},式或条件语法错误：{0},
System Manager,系统管理员,
TDS Rate %,TDS率％,
Tap items to add them here,点击项目将其添加到此处,
Target,目标,
Target ({}),目标（{}）,
Target On,目标类型,
Target Warehouse,目标仓库,
Target warehouse is mandatory for row {0},行{0}必须指定目标仓库,
Task,任务,
Tasks,任务,
Tasks have been created for managing the {0} disease (on row {1}),为管理{0}疾病创建了任务（在第{1}行）,
Tax,税项,
Tax Assets,所得税资产,
Tax Category,税种,
Tax Category for overriding tax rates.,最高税率的税收类别。,
"Tax Category has been changed to ""Total"" because all the Items are non-stock items",税项类别已更改为“合计”，因为所有物料均为非库存物料,
Tax ID,纳税登记号,
Tax Id: ,纳税登记号：,
Tax Rate,税率,
Tax Rule Conflicts with {0},税收规则与{0}冲突,
Tax Rule for transactions.,税收规则进行的交易。,
Tax Template is mandatory.,税务模板字段必填。,
Tax Withholding rates to be applied on transactions.,税收预扣税率适用于交易。,
Tax template for buying transactions.,采购业务的税项模板。,
Tax template for item tax rates.,项目税率的税收模板。,
Tax template for selling transactions.,销售业务的税务模板。,
Taxable Amount,应税金额,
Taxes,税,
Technology,技术,
Telecommunications,电信,
Telephone Expenses,电话费,
Television,电视,
Template Name,模板名称,
Template of terms or contract.,条款或合同模板。,
Templates of supplier scorecard criteria.,供应商计分卡标准模板。,
Templates of supplier scorecard variables.,供应商记分卡变数模板。,
Templates of supplier standings.,供应商榜单。,
Temporarily on Hold,暂时搁置,
Temporary,临时,
Temporary Accounts,临时科目,
Temporary Opening,临时开账,
Terms and Conditions,条款和条件,
Terms and Conditions Template,条款和条件模板,
Territory,区域,
Thank you for your business!,感谢您的业务！,
The 'From Package No.' field must neither be empty nor it's value less than 1.,“From Package No.”字段不能为空，也不能小于1。,
The Brand,你的品牌,
The Item {0} cannot have Batch,物料{0}不能有批次,
The Loyalty Program isn't valid for the selected company,忠诚度计划对所选公司无效,
The Payment Term at row {0} is possibly a duplicate.,第{0}行的支付条款可能是重复的。,
The Term End Date cannot be earlier than the Term Start Date. Please correct the dates and try again.,该期限结束日期不能超过期限开始日期。请更正日期，然后再试一次。,
The Term End Date cannot be later than the Year End Date of the Academic Year to which the term is linked (Academic Year {}). Please correct the dates and try again.,该期限结束日期不能晚于学年年终日期到这个词联系在一起（学年{}）。请更正日期，然后再试一次。,
The Term Start Date cannot be earlier than the Year Start Date of the Academic Year to which the term is linked (Academic Year {}). Please correct the dates and try again.,这个词开始日期不能超过哪个术语链接学年的开学日期较早（学年{}）。请更正日期，然后再试一次。,
The Year End Date cannot be earlier than the Year Start Date. Please correct the dates and try again.,年度结束日期不能早于年度开始日期。请更正日期，然后再试一次。,
The amount of {0} set in this payment request is different from the calculated amount of all payment plans: {1}. Make sure this is correct before submitting the document.,此付款申请中设置的{0}金额与所有付款计划的计算金额不同：{1}。在提交文档之前确保这是正确的。,
The field From Shareholder cannot be blank,来自股东的字段不能为空,
The field To Shareholder cannot be blank,“股东”字段不能为空,
The fields From Shareholder and To Shareholder cannot be blank,来自股东和股东的字段不能为空,
The folio numbers are not matching,作品集编号不匹配,
The holiday on {0} is not between From Date and To Date,在{0}这个节日之间没有从日期和结束日期,
The name of the institute for which you are setting up this system.,对于要为其建立这个系统的该机构的名称。,
The name of your company for which you are setting up this system.,贵公司的名称,
The number of shares and the share numbers are inconsistent,股份数量和库存数量不一致,
The payment gateway account in plan {0} is different from the payment gateway account in this payment request,计划{0}中的支付网关帐户与此付款请求中的支付网关帐户不同,
The selected BOMs are not for the same item,所选物料清单不能用于同一个物料,
The selected item cannot have Batch,所选物料不能有批次,
The seller and the buyer cannot be the same,卖方和买方不能相同,
The shareholder does not belong to this company,该股东不属于这家公司,
The shares already exist,股份已经存在,
The shares don't exist with the {0},这些份额不存在{0},
"The task has been enqueued as a background job. In case there is any issue on processing in background, the system will add a comment about the error on this Stock Reconciliation and revert to the Draft stage",该任务已被列入后台工作。如果在后台处理有任何问题，系统将在此库存对帐中添加有关错误的注释并恢复到草稿阶段,
"Then Pricing Rules are filtered out based on Customer, Customer Group, Territory, Supplier, Supplier Type, Campaign, Sales Partner etc.",然后定价规则将基于客户，客户组，地区，供应商，供应商类型，活动，销售合作伙伴等条件过滤。,
"There are inconsistencies between the rate, no of shares and the amount calculated",费率，股份数量和计算的金额之间不一致,
There can be multiple tiered collection factor based on the total spent. But the conversion factor for redemption will always be same for all the tier.,根据总花费可以有多个分层收集因子。但兑换的兑换系数对于所有等级总是相同的。,
There can only be 1 Account per Company in {0} {1},在{0} {1}中每个公司只能有1个帐户,
"There can only be one Shipping Rule Condition with 0 or blank value for ""To Value""",“至值”为0或为空的运输规则条件最多只能有一个,
There is not enough leave balance for Leave Type {0},假期类型{0}的剩余天数不足了,
There is nothing to edit.,无需编辑。,
There isn't any item variant for the selected item,所选物料无相关变体物料,
"There seems to be an issue with the server's GoCardless configuration. Don't worry, in case of failure, the amount will get refunded to your account.",服务器的GoCardless配置似乎存在问题。别担心，如果失败，这笔款项将退还给您的科目。,
There were errors creating Course Schedule,创建课程表时曾出现错误,
There were errors.,曾有错误发生。,
This Item is a Template and cannot be used in transactions. Item attributes will be copied over into the variants unless 'No Copy' is set,此项目为模板，不可用于交易。项目的属性会被复制到变量，除非设置“不允许复制”,
This Item is a Variant of {0} (Template).,此项目是{0}（模板）的一个变量。,
This Month's Summary,本月摘要,
This Week's Summary,本周总结,
This action will stop future billing. Are you sure you want to cancel this subscription?,此操作将停止未来的结算。您确定要取消此订阅吗？,
This covers all scorecards tied to this Setup,这涵盖了与此安装程序相关的所有记分卡,
This document is over limit by {0} {1} for item {4}. Are you making another {3} against the same {2}?,这份文件超过对于项{4}的{0} {1}的限制。你在做针对同一的{2}另一个{3}？,
This is a root account and cannot be edited.,这是一个顶层（根）科目，不能被编辑。,
This is a root customer group and cannot be edited.,这是一个根客户组，并且不能编辑。,
This is a root department and cannot be edited.,这是根部门，无法编辑。,
This is a root healthcare service unit and cannot be edited.,这是根医疗保健服务单位，不能编辑。,
This is a root item group and cannot be edited.,这是一个根物料群组，无法被编辑。,
This is a root sales person and cannot be edited.,这是一个根销售人员，无法被编辑。,
This is a root supplier group and cannot be edited.,这是一个根源供应商组，无法编辑。,
This is a root territory and cannot be edited.,这是一个根区域，无法被编辑。,
This is an example website auto-generated from ERPNext,这是一个从ERPNext自动生成的示例网站,
This is based on logs against this Vehicle. See timeline below for details,这是基于该车辆日志。请看以下时间轴记录的详细内容,
This is based on stock movement. See {0} for details,这是基于库存变动。见{0}信息,
This is based on the Time Sheets created against this project,基于该工程产生的时间表,
This is based on the attendance of this Student,基于该学生的考勤,
This is based on transactions against this Customer. See timeline below for details,本统计信息基于该客户的过往交易。详情请参阅表单下方的时间轴记录,
This is based on transactions against this Healthcare Practitioner.,这是基于针对此医疗保健从业者的交易。,
This is based on transactions against this Patient. See timeline below for details,这是基于对这个病人的交易。有关信息，请参阅下面的工时单,
This is based on transactions against this Sales Person. See timeline below for details,这是基于针对此销售人员的交易。请参阅下面的时间表了解详情,
This is based on transactions against this Supplier. See timeline below for details,本统计基于该供应商的过往交易。详情请参阅表单下方的时间线记录,
This {0} conflicts with {1} for {2} {3},此{0}与在{2} {3}的{1}冲突,
Time Sheet for manufacturing.,制造方面的时间表。,
Time Tracking,时间跟踪,
"Time slot skiped, the slot {0} to {1} overlap exisiting slot {2} to {3}",时隙滑动，时隙{0}到{1}与现有时隙{2}重叠到{3},
Time slots added,添加时隙,
Time(in mins),时间（分钟）,
Timer,计时器,
Timer exceeded the given hours.,计时器超出了指定的小时数,
Timesheet,时间表,
Timesheet for tasks.,任务方面的时间表。,
Timesheet {0} is already completed or cancelled,时间表{0}已完成或已取消,
Timesheets,时间表,
"Timesheets help keep track of time, cost and billing for activites done by your team",工时单帮助追踪记录你的团队完成的时间，费用和活动的账单,
Titles for print templates e.g. Proforma Invoice.,标题打印模板例如形式费用清单。,
To,至,
To Address 1,致地址1,
To Address 2,致地址2,
To Bill,待开费用清单,
To Date,至今,
To Date cannot be before From Date,无效的主名称,
To Date cannot be less than From Date,迄今不能少于起始日期,
To Date must be greater than From Date,到日期必须大于从日期,
To Date should be within the Fiscal Year. Assuming To Date = {0},日期应该是在财务年度内。假设终止日期= {0},
To Datetime,以日期时间,
To Deliver,待出货,
To Deliver and Bill,待出货与开票,
To Fiscal Year,到财政年度,
To GSTIN,到GSTIN,
To Party Name,到党名,
To Pin Code,要密码,
To Place,放置,
To Receive,等收货,
To Receive and Bill,待收货与开票,
To State,国家,
To Warehouse,到仓库,
To create a Payment Request reference document is required,必须生成一个付款申请参考文档,
"To filter based on Party, select Party Type first",如果基于往来单位过滤，请先选择往来单位类型,
"To get the best out of ERPNext, we recommend that you take some time and watch these help videos.",为了用好ERPNext，我们建议您花一些时间来观看这些帮助视频。,
"To include tax in row {0} in Item rate, taxes in rows {1} must also be included",要包括税款，行{0}项率，税收行{1}也必须包括在内,
To make Customer based incentive schemes.,制定基于客户的激励计划。,
"To merge, following properties must be same for both items",若要合并，两个物料的以下属性必须相同,
"To not apply Pricing Rule in a particular transaction, all applicable Pricing Rules should be disabled.",要在一个特定的交易不适用于定价规则，所有适用的定价规则应该被禁用。,
"To set this Fiscal Year as Default, click on 'Set as Default'",要设置这个财务年度为默认值，点击“设为默认”,
To view logs of Loyalty Points assigned to a Customer.,查看分配给客户的忠诚度积分的日志。,
To {0},{0},
To {0} | {1} {2},{0} | {1} {2},
Toggle Filters,切换过滤器,
Too many columns. Export the report and print it using a spreadsheet application.,太多的列。导出报表，并使用电子表格应用程序进行打印。,
Tools,工具,
Total (Credit),总（信用）,
Total (Without Tax),总计（不含税）,
Total Achieved,总体上实现的,
Total Actual,实际总和,
Total Amount,总金额,
Total Amount Credited,记入贷方的总金额,
Total Applicable Charges in Purchase Receipt Items table must be same as Total Taxes and Charges,基于采购收货单信息计算的总税费必须与采购单（单头）的总税费一致,
Total Budget,预算总额,
Total Collected: {0},总计：{0},
Total Commission,总佣金,
Total Contribution Amount: {0},总贡献金额：{0},
Total Credit/ Debit Amount should be same as linked Journal Entry,总信用/借方金额应与链接的手工凭证相同,
Total Debit must be equal to Total Credit. The difference is {0},总借方必须等于总贷方金额，差异{0}。,
Total Invoiced Amount,发票金额,
Total Order Considered,总订货,
Total Order Value,总订单价值,
Total Outgoing,总待付款,
Total Outstanding,总未付,
Total Outstanding Amount,总待处理金额,
Total Outstanding: {0},总未付：{0},
Total Paid Amount,已支付总金额,
Total Payment Amount in Payment Schedule must be equal to Grand / Rounded Total,支付计划中的总付款金额必须等于总计/圆整的总计,
Total Payments,总付款,
Total Qty,总数量,
Total Quantity,总数量,
Total Revenue,总收入,
Total Student,学生总数,
Total Target,总目标,
Total Tax,总税额,
Total Taxable Amount,应纳税总额,
Total Taxable Value,应税总额,
Total Unpaid: {0},总未付：{0},
Total Variance,总差异,
Total Weightage of all Assessment Criteria must be 100%,所有评估标准的权重总数要达到100％,
Total advance ({0}) against Order {1} cannot be greater than the Grand Total ({2}),对订单{1}的合计的预付款（{0}）不能大于总计（{2}）,
Total advance amount cannot be greater than total claimed amount,总预付金额不能超过申报总额,
Total allocated leaves are more days than maximum allocation of {0} leave type for employee {1} in the period,在此期间，合计分配的假期大于员工{1}的最大分配{0}离职类型的天数,
Total allocated leaves are more than days in the period,总已分配休假天数大于此期间天数,
Total allocated percentage for sales team should be 100,对于销售团队总分配比例应为100,
Total cannot be zero,总分数不能为零,
Total contribution percentage should be equal to 100,总贡献百分比应等于100,
Total hours: {0},总时间：{0},
Total {0} ({1}),总{0}（{1}）,
"Total {0} for all items is zero, may be you should change 'Distribute Charges Based On'",所有项目合计{0}为零，可能你应该改变“基于分布式费用”,
Total(Amt),共（AMT）,
Total(Qty),总计（数量）,
Traceability,可追溯性,
Track Leads by Lead Source.,通过线索来源进行追踪。,
Transaction,交易,
Transaction Date,交易日期,
Transaction Type,交易类型,
Transaction currency must be same as Payment Gateway currency,交易货币必须与支付网关货币,
Transaction not allowed against stopped Work Order {0},不允许对停止的工单{0}进行交易,
Transaction reference no {0} dated {1},交易参考编号{0}日{1},
Transactions,交易,
Transactions can only be deleted by the creator of the Company,交易只能由公司的创建者删除,
Transfer,转移,
Transfer Material,转移材料,
Transfer Type,转移类型,
Transfer an asset from one warehouse to another,从一个仓库转移资产到另一仓库,
Transfered,转移,
Transferred Quantity,转移数量,
Transport Receipt Date,运输收货日期,
Transport Receipt No,运输收据编号,
Transportation,运输,
Transporter ID,承运商ID,
Transporter Name,承运商名称,
Travel Expenses,差旅费,
Tree Type,树类型,
Tree of Bill of Materials,物料清单树,
Tree of Item Groups.,物料群组树。,
Tree of Procedures,程序树,
Tree of Quality Procedures.,质量树程序。,
Tree of financial Cost Centers.,财务成本中心的树。,
Tree of financial accounts.,财务账目的树。,
Treshold {0}% appears more than once,Treshold {0}出现％不止一次,
Trial Period End Date Cannot be before Trial Period Start Date,试用期结束日期不能在试用期开始日期之前,
Trialling,试用,
Type of Business,业务类型,
Types of activities for Time Logs,用于工时记录的活动类型,
UOM,计量单位,
UOM Conversion factor is required in row {0},行{0}计量单位换算系数是必须项,
UOM coversion factor required for UOM: {0} in Item: {1},物料{1}的计量单位{0}需要单位换算系数,
URL,网址,
Unable to find exchange rate for {0} to {1} for key date {2}. Please create a Currency Exchange record manually,无法为关键日期{2}查找{0}到{1}的汇率。请手动创建汇率记录,
Unable to find score starting at {0}. You need to have standing scores covering 0 to 100,无法从{0}开始获得分数。你需要有0到100的常规分数,
Unable to find variable: ,无法找到变量：,
Unblock Invoice,解锁该费用清单,
Uncheck all,取消全选,
Unclosed Fiscal Years Profit / Loss (Credit),未关闭的财年利润/损失,
Unit,单位,
Unit of Measure,计量单位,
Unit of Measure {0} has been entered more than once in Conversion Factor Table,计量单位{0}已经在换算系数表内,
Unknown,未知,
Unpaid,未付,
Unsecured Loans,无担保贷款,
Unsubscribe from this Email Digest,从该电子邮件摘要退订,
Unsubscribed,已退订,
Until,直到,
Unverified Webhook Data,未经验证的Webhook数据,
Update Account Name / Number,更新帐户名称/号码,
Update Account Number / Name,更新帐号/名称,
Update Cost,更新成本,
Update Items,更新项目,
Update Print Format,更新打印格式,
Update bank payment dates with journals.,用日记账更新银行付款时间,
Update in progress. It might take a while.,正在更新。请稍等。,
Update rate as per last purchase,根据上次购买更新率,
Update stock must be enable for the purchase invoice {0},必须为采购费用清单{0}启用更新库存,
Updating Variants...,更新变体......,
Upload your letter head and logo. (you can edit them later).,上传你的信头和logo。(您可以在以后对其进行编辑）。,
Upper Income,高收入,
Use Sandbox,使用沙盒,
User,用户,
User ID,用户ID,
User ID not set for Employee {0},员工设置{0}为设置用户ID,
User Remark,用户备注,
User has not applied rule on the invoice {0},用户未在发票{0}上应用规则,
User {0} already exists,用户{0}已经存在,
User {0} created,用户{0}已创建,
User {0} does not exist,用户{0}不存在,
User {0} doesn't have any default POS Profile. Check Default at Row {1} for this User.,用户{0}没有任何默认的POS配置文件。检查此用户的行{1}处的默认值。,
User {0} is already assigned to Employee {1},用户{0}已经被分配给员工{1},
User {0} is already assigned to Healthcare Practitioner {1},用户{0}已分配给Healthcare Practitioner {1},
Users,用户,
Utility Expenses,基础设施费用,
Valid From Date must be lesser than Valid Upto Date.,有效起始日期必须小于有效起始日期。,
Valid Till,有效期至,
Valid from and valid upto fields are mandatory for the cumulative,有效且有效的最多字段对于累积是必需的,
Valid from date must be less than valid upto date,从日期开始有效必须低于最新有效期,
Valid till date cannot be before transaction date,有效期至日期不得在交易日之前,
Validity,有效性,
Validity period of this quotation has ended.,此报价的有效期已经结束。,
Valuation Rate,库存评估价,
Valuation Rate is mandatory if Opening Stock entered,库存开帐凭证中评估价字段必填,
Valuation type charges can not marked as Inclusive,估值类型罪名不能标记为包容性,
Value Or Qty,价值或数量,
Value Proposition,价值主张,
Value for Attribute {0} must be within the range of {1} to {2} in the increments of {3} for Item {4},物料{4}的属性{0}其属性值必须{1}到{2}范围内，且增量{3},
Value must be between {0} and {1},值必须介于{0}和{1}之间,
"Values of exempt, nil rated and non-GST inward supplies",豁免，零税率和非商品及服务税内向供应的价值,
Variance,方差,
Variance ({}),差异（{}）,
Variant,变体,
Variant Attributes,变量属性,
Variant Based On cannot be changed,Variant Based On无法更改,
Variant Details Report,变量详细信息报表,
Variant creation has been queued.,变量创建已经排队。,
Vehicle Expenses,车辆费用,
Vehicle No,车辆编号,
Vehicle Type,车辆类型,
Vehicle/Bus Number,车辆/巴士号码,
Venture Capital,创业投资,
View Chart of Accounts,查看会计科目表,
View Fees Records,查看费用记录,
View Form,查看表格,
View Lab Tests,查看实验室测试,
View Leads,查看（销售）线索,
View Ledger,查看总帐,
View Now,立即查看,
View a list of all the help videos,查看所有帮助视频清单,
View in Cart,查看你的购物车,
Visit report for maintenance call.,保修电话的回访报表。,
Visit the forums,访问论坛,
Vital Signs,生命体征,
Volunteer,志愿者,
Volunteer Type information.,志愿者类型信息。,
Volunteer information.,志愿者信息。,
Voucher #,凭证 #,
Voucher No,凭证编号,
Voucher Type,凭证类型,
WIP Warehouse,在制品仓库,
Warehouse can not be deleted as stock ledger entry exists for this warehouse.,无法删除，因为此仓库有库存分类账分录。,
Warehouse cannot be changed for Serial No.,仓库不能因为序列号变更,
Warehouse is mandatory,仓库信息必填,
Warehouse is mandatory for stock Item {0} in row {1},行{1}中的物料{0}必须指定仓库,
Warehouse not found in the system,仓库在系统中未找到,
"Warehouse required at Row No {0}, please set default warehouse for the item {1} for the company {2}",在第{0}行需要仓库，请为公司{2}的物料{1}设置默认仓库,
Warehouse required for stock Item {0},物料{0}需要指定仓库,
Warehouse {0} can not be deleted as quantity exists for Item {1},仓库{0}无法删除，因为产品{1}有库存量,
Warehouse {0} does not belong to company {1},仓库{0}不属于公司{1},
Warehouse {0} does not exist,仓库{0}不存在,
"Warehouse {0} is not linked to any account, please mention the account in  the warehouse record or set default inventory account in company {1}.",仓库{0}未与任何科目关联，请在仓库记录提及科目或在公司{1}设置默认库存科目。,
Warehouses with child nodes cannot be converted to ledger,与子节点仓库不能转换为分类账,
Warehouses with existing transaction can not be converted to group.,有现有交易的仓库不能转换为组。,
Warehouses with existing transaction can not be converted to ledger.,有现有的交易的仓库不能转换到总帐。,
Warning,警告,
Warning: Another {0} # {1} exists against stock entry {2},警告：库存凭证{2}中已存在另一个{0}＃{1},
Warning: Invalid SSL certificate on attachment {0},警告：附件{0}中存在无效的SSL证书,
Warning: Invalid attachment {0},警告：无效的附件{0},
Warning: Material Requested Qty is less than Minimum Order Qty,警告：物料需求数量低于最小起订量,
Warning: Sales Order {0} already exists against Customer's Purchase Order {1},警告：已经有销售订单{0}关联了客户采购订单号{1},
Warning: System will not check overbilling since amount for Item {0} in {1} is zero,警告： 因为{1}中的物料{0}为零，系统将不会检查超额,
Warranty,质量保证,
Warranty Claim,保修申请,
Warranty Claim against Serial No.,针对序列号提出的保修申请,
Website,网站,
Website Image should be a public file or website URL,网站形象应该是一个公共文件或网站网址,
Website Image {0} attached to Item {1} cannot be found,网站图像{0}附加到物料{1}无法找到,
Website Manager,网站管理员,
Website Settings,网站设置,
Wednesday,星期三,
Week,周,
Weekly,每周,
"Weight is mentioned,\nPlease mention ""Weight UOM"" too",重量被提及，\n请注明“重量计量单位”,
Welcome email sent,欢迎电子邮件已发送,
Welcome to ERPNext,欢迎使用ERPNext,
What do you need help with?,你有什么需要帮助的？,
What does it do?,贵公司的做什么,
Where manufacturing operations are carried.,生产流程进行的地方。,
White,白,
Wire Transfer,电汇,
WooCommerce Products,WooCommerce产品,
Work In Progress,在制品,
Work Order,工单,
Work Order already created for all items with BOM,已经为包含物料清单的所有料品创建工单,
Work Order cannot be raised against a Item Template,不能为模板物料新建工单,
Work Order has been {0},工单已{0},
Work Order not created,工单未创建,
Work Order {0} must be cancelled before cancelling this Sales Order,在取消此销售订单之前，必须先取消工单{0},
Work Order {0} must be submitted,必须提交工单{0},
Work Orders Created: {0},创建的工单：{0},
Work-in-Progress Warehouse is required before Submit,提交前需要指定在制品仓库,
Working,工作,
Working Hours,工作时间,
Workstation,工作站,
Workstation is closed on the following dates as per Holiday List: {0},工作站在以下假期关闭：{0},
Wrapping up,包起来,
Wrong Password,密码错误,
Year start date or end date is overlapping with {0}. To avoid please set company,新财年开始或结束日期与{0}重叠。请在公司主数据中设置,
You are not authorized to add or update entries before {0},你没有权限在{0}前添加或更改分录。,
You are not authorized to set Frozen value,您没有权限设定冻结值,
You can not change rate if BOM mentioned agianst any item,如果任何条目中引用了BOM，你不能更改其税率,
You can not enter current voucher in 'Against Journal Entry' column,您不能在“对日记账分录”列中选择此凭证。,
You can only have Plans with the same billing cycle in a Subscription,您只能在订阅中拥有相同结算周期的计划,
You can only redeem max {0} points in this order.,您只能按此顺序兑换最多{0}个积分。,
You can only renew if your membership expires within 30 days,如果您的会员资格在30天内到期，您只能续订,
You can only select a maximum of one option from the list of check boxes.,您只能从复选框列表中选择最多一个选项。,
You can't redeem Loyalty Points having more value than the Grand Total.,您无法兑换价值超过总计的忠诚度积分。,
You cannot credit and debit same account at the same time,你不能同时借机和贷记同一账户。,
You cannot delete Fiscal Year {0}. Fiscal Year {0} is set as default in Global Settings,您不能删除会计年度{0}。会计年度{0}被设置为默认的全局设置,
You cannot delete Project Type 'External',您不能删除“外部”类型的项目,
You cannot edit root node.,您不能编辑根节点。,
You cannot restart a Subscription that is not cancelled.,您无法重新启动未取消的订阅。,
You don't have enough Loyalty Points to redeem,您没有获得忠诚度积分兑换,
You have already assessed for the assessment criteria {}.,您已经评估了评估标准{}。,
You have already selected items from {0} {1},您已经选择从项目{0} {1},
You have been invited to collaborate on the project: {0},您已被邀请在项目上进行合作：{0},
You have entered duplicate items. Please rectify and try again.,您输入了重复的条目。请纠正然后重试。,
You need to be a user other than Administrator with System Manager and Item Manager roles to register on Marketplace.,您需要是Administrator以外的System Manager和Item Manager角色的用户才能在Marketplace上注册。,
You need to be a user with System Manager and Item Manager roles to add users to Marketplace.,您需要是具有System Manager和Item Manager角色的用户才能将用户添加到Marketplace。,
You need to be a user with System Manager and Item Manager roles to register on Marketplace.,您需要是具有System Manager和Item Manager角色的用户才能在Marketplace上注册。,
You need to enable Shopping Cart,您需要启用购物车,
You will lose records of previously generated invoices. Are you sure you want to restart this subscription?,您将失去先前生成的费用清单记录。您确定要重新启用此订阅吗？,
Your Organization,你的组织,
Your cart is Empty,您的购物车是空的,
Your email address...,您的电子邮件地址...,
Your order is out for delivery!,您的订单已发货！,
Your tickets,你的票,
ZIP Code,邮编,
[Error],[错误],
[{0}](#Form/Item/{0}) is out of stock,[{0}](#Form/Item/{0}) 超出了库存,
`Freeze Stocks Older Than` should be smaller than %d days.,`冻结老于此天数的库存`应该比％d天小。,
based_on,基于,
cannot be greater than 100,不能大于100,
disabled user,已禁用用户,
"e.g. ""Build tools for builders""",例如“建筑工人的建筑工具！”,
"e.g. ""Primary School"" or ""University""",如“小学”或“大学”,
"e.g. Bank, Cash, Credit Card",例如：银行，现金，信用卡,
hidden,已隐藏,
modified,改性,
old_parent,旧的_父系,
on,于,
{0} '{1}' is disabled,{0}“{1}”被禁用,
{0} '{1}' not in Fiscal Year {2},{0}“ {1}”不属于{2}财年,
{0} ({1}) cannot be greater than planned quantity ({2}) in Work Order {3},{0}（{1}）不能大于工单{3}中的计划数量（{2}）,
{0} - {1} is inactive student,{0}  -  {1}是非活跃学生,
{0} - {1} is not enrolled in the Batch {2},{0}  -  {1}未注册批次{2},
{0} - {1} is not enrolled in the Course {2},{0}  -  {1}未加入课程{2},
{0} Budget for Account {1} against {2} {3} is {4}. It will exceed by {5},{0} 账户{1}对于{2}{3}的预算是{4}. 预期增加{5},
{0} Digest,{0}摘要,
{0} Request for {1},{0}申请{1},
{0} Result submittted,{0}结果提交,
{0} Serial Numbers required for Item {1}. You have provided {2}.,物料{1}需要{0}的序列号。您已提供{2}。,
{0} Student Groups created.,{0}学生组已创建,
{0} Students have been enrolled,{0}学生已被注册,
{0} against Bill {1} dated {2},{0}对日期为{2}的账单{1},
{0} against Purchase Order {1},{0}不允许采购订单{1},
{0} against Sales Invoice {1},{0}不允许销售发票{1},
{0} against Sales Order {1},{0}不允许销售订单{1},
{0} asset cannot be transferred,{0}资产不得转让,
{0} can not be negative,{0}不能为负,
{0} created,{0}已创建,
"{0} currently has a {1} Supplier Scorecard standing, and Purchase Orders to this supplier should be issued with caution.",{0}目前拥有{1}供应商记分卡，而采购订单应谨慎提供给供应商。,
"{0} currently has a {1} Supplier Scorecard standing, and RFQs to this supplier should be issued with caution.",{0}目前拥有{1}供应商记分卡，并且谨慎地向该供应商发出询价。,
{0} does not belong to Company {1},{0}不属于公司{1},
{0} does not have a Healthcare Practitioner Schedule. Add it in Healthcare Practitioner master,{0}没有医疗从业者时间表。将其添加到医疗从业者主表中,
{0} entered twice in Item Tax,{0}输入了两次税项,
{0} for {1},{0} {1},
{0} has been submitted successfully,{0}已成功提交,
{0} has fee validity till {1},{0}有效期至{1},
{0} hours,{0}小时,
{0} in row {1},{1}行中的{0},
{0} is blocked so this transaction cannot proceed,{0}被阻止，所以此事务无法继续,
{0} is mandatory,{0}是必填项,
{0} is mandatory for Item {1},{0}是{1}的必填项,
{0} is mandatory. Maybe Currency Exchange record is not created for {1} to {2}.,{0}是必填项。{1}和{2}的货币转换记录可能还未生成。,
{0} is not a stock Item,{0}不是一个库存物料,
{0} is not a valid Batch Number for Item {1},{0}不是物料{1}的有效批次号,
{0} is not added in the table,表中未添加{0},
{0} is now the default Fiscal Year. Please refresh your browser for the change to take effect.,默认财务年度已经更新为{0}。请刷新您的浏览器以使更改生效。,
{0} is on hold till {1},{0}暂缓处理，直到{1},
{0} item found.,找到{0}项。,
{0} items found.,找到{0}个项目。,
{0} items in progress,{0}处理项,
{0} items produced,{0}物料已生产,
{0} must appear only once,{0}只能出现一次,
{0} must be negative in return document,{0}在退货凭证中必须为负,
{0} not allowed to transact with {1}. Please change the Company.,不允许{0}与{1}进行交易。请更改公司。,
{0} not found for item {1},在{0}中找不到物料{1},
{0} parameter is invalid,{0}参数无效,
{0} payment entries can not be filtered by {1},{0}付款凭证不能由{1}过滤,
{0} should be a value between 0 and 100,{0}应该是0到100之间的一个值,
{0} units of [{1}](#Form/Item/{1}) found in [{2}](#Form/Warehouse/{2}),{0} [{1}]的单位（＃窗体/项目/ {1}）在[{2}]研究发现（＃窗体/仓储/ {2}）,
{0} units of {1} needed in {2} on {3} {4} for {5} to complete this transaction.,{0} {1}在需要{2}在{3} {4}：{5}来完成这一交易单位。,
{0} units of {1} needed in {2} to complete this transaction.,{0}单位{1}在{2}完成此交易所需。,
{0} valid serial nos for Item {1},物料{1}有{0}个有效序列号,
{0} variants created.,{0}变量已创建,
{0} {1} created,{0} {1} 已被创建,
{0} {1} does not exist,{0} {1}不存在,
{0} {1} has been modified. Please refresh.,{0} {1}已被修改过，请刷新。,
{0} {1} has not been submitted so the action cannot be completed,{0} {1}尚未提交，因此无法完成此操作,
"{0} {1} is associated with {2}, but Party Account is {3}",{0} {1}与{2}相关联，但业务伙伴科目为{3},
{0} {1} is cancelled or closed,{0} {1}被取消或关闭,
{0} {1} is cancelled or stopped,{0} {1}被取消或停止,
{0} {1} is cancelled so the action cannot be completed,{0} {1}被取消，因此无法完成操作,
{0} {1} is closed,{0} {1} 已关闭,
{0} {1} is disabled,{0} {1}已禁用,
{0} {1} is frozen,{0} {1}已冻结,
{0} {1} is fully billed,{0} {1}已完全开票,
{0} {1} is not active,{0} {1} 未激活,
{0} {1} is not associated with {2} {3},{0} {1}与{2} {3}无关,
{0} {1} is not present in the parent company,母公司中不存在{0} {1},
{0} {1} is not submitted,{0} {1}未提交,
{0} {1} is {2},{0} {1}是{2},
{0} {1} must be submitted,{0} {1}必须提交,
{0} {1} not in any active Fiscal Year.,{0} {1} 不在任一激活的财务年度中。,
{0} {1} status is {2},{0} {1}的状态为{2},
{0} {1}: 'Profit and Loss' type account {2} not allowed in Opening Entry,{0} {1}：“损益”科目类型{2}不允许开帐凭证,
{0} {1}: Account {2} does not belong to Company {3},{0} {1}科目{2}不属于公司{3},
{0} {1}: Account {2} is inactive,{0} {1}: 科目{2}无效,
{0} {1}: Accounting Entry for {2} can only be made in currency: {3},{0} {1}在{2}会计分录只能用货币单位：{3},
{0} {1}: Cost Center is mandatory for Item {2},{0} {1}：成本中心是物料{2}的必须项,
{0} {1}: Cost Center is required for 'Profit and Loss' account {2}. Please set up a default Cost Center for the Company.,{0} {1}：成本中心对于损益类科目来说是必须的{2}。请为公司设置一个默认的成本中心。,
{0} {1}: Cost Center {2} does not belong to Company {3},{0} {1}：成本中心{2}不属于公司{3},
{0} {1}: Customer is required against Receivable account {2},{0} {1}：需要客户支付的应收账款{2},
{0} {1}: Either debit or credit amount is required for {2},{0} {1}：借方或贷方金额是必输的{2},
{0} {1}: Supplier is required against Payable account {2},{0} {1}：供应商对于应付账款科目来说是必输的{2},
{0}% Billed,{0}％帐单,
{0}% Delivered,{0}％交付,
{0}: From {1},{0}：来自{1},
{0}: {1} does not exists,{0}：{1}不存在,
{0}: {1} not found in Invoice Details table,{0}：{1}在发票信息表中无法找到,
{} of {},{} {},
Assigned To,已分配给,
Chat,聊天,
Completed By,由...完成,
Day of Week,星期几,
"Dear System Manager,",亲爱的系统管理经理，,
Default Value,默认值,
Email Group,电子邮件组,
Email Settings,邮件设置,
Email not sent to {0} (unsubscribed / disabled),电子邮件不会被发送到{0}（退订/禁用）,
Error Message,错误信息,
Fieldtype,字段类型,
Help Articles,帮助文章,
ID,ID,
Import,导入,
Language,语言,
Likes,喜欢,
Merge with existing,与现有合并,
Orientation,方向,
Parent,上级,
Payment Failed,支付失败,
Personal,个人,
Post,发送,
Postal Code,邮政编码,
Provider,提供商,
Read Only,只读,
Recipient,收件人,
Reviews,评测,
Sender,发件人,
There were errors while sending email. Please try again.,邮件发送曾发生错误，请重试。,
Values Changed,值已更改,
or,或,
Ageing Range 4,老化范围4,
Allocated amount cannot be greater than unadjusted amount,分配的金额不能大于未调整的金额,
Allocated amount cannot be negative,分配数量不能为负数,
"Difference Account must be a Asset/Liability type account, since this Stock Entry is an Opening Entry",差异账户必须是资产/负债类型账户，因为此库存分录是开仓分录,
Import Successful,导入成功,
Please save first,请先保存,
Price not found for item {0} in price list {1},价格表{1}中的商品{0}找不到价格,
Warehouse Type,仓库类型,
'Date' is required,&#39;日期&#39;是必需的,
Budgets,预算,
Bundle Qty,捆绑数量,
Company GSTIN,公司GSTIN,
Company field is required,公司字段是必填项,
Creating Dimensions...,创建尺寸......,
Duplicate entry against the item code {0} and manufacturer {1},项目代码{0}和制造商{1}的重复输入,
Invalid GSTIN! The input you've entered doesn't match the GSTIN format for UIN Holders or Non-Resident OIDAR Service Providers,GSTIN无效！您输入的输入与UIN持有人或非居民OIDAR服务提供商的GSTIN格式不符,
Invoice Grand Total,发票总计,
Last carbon check date cannot be a future date,最后的碳检查日期不能是未来的日期,
Make Stock Entry,进入库存,
Quality Feedback,质量反馈,
Quality Feedback Template,质量反馈模板,
Rules for applying different promotional schemes.,适用不同促销计划的规则。,
Show {0},显示{0},
Target Details,目标细节,
{0} already has a Parent Procedure {1}.,{0}已有父程序{1}。,
API,应用程序界面,
Annual,全年,
Change,变化,
Contact Email,联络人电邮,
From Date,起始日期,
Group By,通过...分组,
Invalid URL,无效的网址,
Landscape,景观,
Naming Series,名录,
No data to export,没有要导出的数据,
Portrait,肖像,
Print Heading,打印标题,
Scheduler Inactive,调度程序无效,
Scheduler is inactive. Cannot import data.,调度程序处于非活动状态。无法导入数据。,
Show Document,显示文件,
Show Traceback,显示回溯,
Video,视频,
% Of Grand Total,占总数的百分比,
<b>Company</b> is a mandatory filter.,<b>公司</b>是强制性过滤器。,
<b>From Date</b> is a mandatory filter.,<b>“起始日期”</b>是强制性过滤器。,
<b>From Time</b> cannot be later than <b>To Time</b> for {0},{0}的<b>起始时间</b>不能晚于<b>起始时间</b>,
<b>To Date</b> is a mandatory filter.,<b>截止日期</b>是强制性过滤器。,
A new appointment has been created for you with {0},已为您创建一个{0}的新约会,
Account Value,账户价值,
Account is mandatory to get payment entries,必须输入帐户才能获得付款条目,
Account is not set for the dashboard chart {0},没有为仪表板图表{0}设置帐户,
Account {0} does not exists in the dashboard chart {1},帐户{0}在仪表板图表{1}中不存在,
Account: <b>{0}</b> is capital Work in progress and can not be updated by Journal Entry,帐户： <b>{0}</b>是资金正在进行中，日记帐分录无法更新,
Account: {0} is not permitted under Payment Entry,帐户：付款条目下不允许{0},
Accounting Dimension <b>{0}</b> is required for 'Balance Sheet' account {1}.,“资产负债表”帐户{1}需要会计维度<b>{0</b> }。,
Accounting Dimension <b>{0}</b> is required for 'Profit and Loss' account {1}.,“损益”帐户{1}需要会计维度<b>{0</b> }。,
Accounting Masters,会计大师,
Accounting Period overlaps with {0},会计期间与{0}重叠,
Activity,活动,
Add / Manage Email Accounts.,添加/管理电子邮件帐户。,
Add Child,添加子项,
Add Multiple,添加多个,
Add Participants,添加参与者,
Add to Featured Item,添加到特色商品,
Add your review,添加您的评论,
Add/Edit Coupon Conditions,添加/编辑优惠券条件,
Added to Featured Items,已添加到精选商品,
Added {0} ({1}),已添加{0}（{1}）,
Address Line 1,地址行1,
Addresses,地址,
Admission End Date should be greater than Admission Start Date.,入学结束日期应大于入学开始日期。,
All,所有,
All bank transactions have been created,已创建所有银行交易,
All the depreciations has been booked,所有折旧已被预订,
Allow Resetting Service Level Agreement from Support Settings.,允许从支持设置重置服务水平协议。,
Amount of {0} is required for Loan closure,结清贷款需要{0}的金额,
Applied Coupon Code,应用的优惠券代码,
Apply Coupon Code,申请优惠券代码,
Appointment Booking,预约预约,
"As there are existing transactions against item {0}, you can not change the value of {1}",由于有对项目{0}现有的交易，你不能改变的值{1},
Asset Id,资产编号,
Asset Value,资产值,
Asset Value Adjustment cannot be posted before Asset's purchase date <b>{0}</b>.,资产价值调整不能在资产购买日期<b>{0}</b>之前过账。,
Asset {0} does not belongs to the custodian {1},资产{0}不属于托管人{1},
Asset {0} does not belongs to the location {1},资产{0}不属于位置{1},
At least one of the Applicable Modules should be selected,应选择至少一个适用模块,
Atleast one asset has to be selected.,必须选择至少一项资产。,
Authentication Failed,身份验证失败,
Automatic Reconciliation,自动对帐,
Available For Use Date,可用日期,
Available Stock,可用库存,
"Available quantity is {0}, you need {1}",可用数量为{0}，您需要{1},
BOM 1,BOM 1,
BOM 2,BOM 2,
BOM Comparison Tool,BOM比较工具,
BOM recursion: {0} cannot be child of {1},BOM递归：{0}不能是{1}的子代,
BOM recursion: {0} cannot be parent or child of {1},BOM递归：{0}不能是{1}的父级或子级,
Back to Messages,回到消息,
Bank Data mapper doesn't exist,银行数据映射器不存在,
Bank Details,银行明细,
Bank account '{0}' has been synchronized,银行帐户“{0}”已同步,
Bank account {0} already exists and could not be created again,银行帐户{0}已存在，无法再次创建,
Bank accounts added,银行账户补充说,
Batch no is required for batched item {0},批处理项{0}需要批次否,
Billing Date,结算日期,
Billing Interval Count cannot be less than 1,账单间隔计数不能小于1,
Blue,蓝色,
Book,书,
Book Appointment,预约书,
Brand,品牌,
Browse,浏览,
Call Connected,呼叫已连接,
Call Disconnected,呼叫已断开连接,
Call Missed,打电话错过了,
Call Summary,呼叫摘要,
Call Summary Saved,保存呼叫摘要,
Cancelled,取消,
Cannot Calculate Arrival Time as Driver Address is Missing.,由于缺少驱动程序地址，无法计算到达时间。,
Cannot Optimize Route as Driver Address is Missing.,由于缺少驱动程序地址，无法优化路由。,
Cannot complete task {0} as its dependant task {1} are not ccompleted / cancelled.,无法完成任务{0}，因为其相关任务{1}尚未完成/取消。,
Cannot find a matching Item. Please select some other value for {0}.,无法找到匹配的项目。请选择其他值{0}。,
"Cannot overbill for Item {0} in row {1} more than {2}. To allow over-billing, please set allowance in Accounts Settings",第{1}行中的项目{0}的出价不能超过{2}。要允许超额计费，请在“帐户设置”中设置配额,
"Capacity Planning Error, planned start time can not be same as end time",容量规划错误，计划的开始时间不能与结束时间相同,
Categories,分类,
Changes in {0},{0}中的更改,
Chart,图表,
Choose a corresponding payment,选择相应的付款,
Click on the link below to verify your email and confirm the appointment,单击下面的链接以验证您的电子邮件并确认约会,
Close,关闭,
Communication,通讯,
Compact Item Print,紧凑型项目打印,
Company,公司,
Company of asset {0} and purchase document {1} doesn't matches.,资产{0}和购买凭证{1}的公司不匹配。,
Compare BOMs for changes in Raw Materials and Operations,比较原材料和操作中的更改的BOM,
Compare List function takes on list arguments,比较List函数采用列表参数,
Complete,完成,
Completed,已完成,
Completed Quantity,完成数量,
Connect your Exotel Account to ERPNext and track call logs,将您的Exotel帐户连接到ERPNext并跟踪通话记录,
Connect your bank accounts to ERPNext,将您的银行帐户连接到ERPNext,
Contact Seller,联系卖家,
Continue,继续,
Cost Center: {0} does not exist,成本中心：{0}不存在,
Couldn't Set Service Level Agreement {0}.,无法设置服务水平协议{0}。,
Country,国家,
Country Code in File does not match with country code set up in the system,文件中的国家/地区代码与系统中设置的国家/地区代码不匹配,
Create New Contact,创建新联系人,
Create New Lead,创造新的领导者,
Create Pick List,创建选择列表,
Create Quality Inspection for Item {0},为项目{0}创建质量检验,
Creating Accounts...,创建帐户......,
Creating bank entries...,创建银行条目......,
Credit limit is already defined for the Company {0},已为公司{0}定义信用额度,
Ctrl + Enter to submit,按Ctrl + Enter提交,
Ctrl+Enter to submit,按Ctrl + Enter提交,
Currency,货币,
Current Status,现状,
Customer PO,客户PO,
Daily,每日,
Date,日期,
Date of Birth cannot be greater than Joining Date.,出生日期不能大于加入日期。,
Dear,亲爱,
Default,默认,
Define coupon codes.,定义优惠券代码。,
Delayed Days,延迟天数,
Delete,删除,
Delivered Quantity,交货数量,
Delivery Notes,送货单,
Depreciated Amount,折旧额,
Description,描述,
Designation,职位,
Difference Value,差异值,
Dimension Filter,尺寸过滤器,
Disabled,禁用,
Disbursement and Repayment,支付和还款,
Distance cannot be greater than 4000 kms,距离不能超过4000公里,
Do you want to submit the material request,您要提交材料申请吗？,
Doctype,文档类型,
Document {0} successfully uncleared,文档{0}成功未清除,
Download Template,下载模板,
Dr,借方,
Due Date,到期日,
Duplicate,复制,
Duplicate Project with Tasks,带有任务的重复项目,
Duplicate project has been created,复制项目已创建,
E-Way Bill JSON can only be generated from a submitted document,e-Way Bill JSON只能从提交的文档中生成,
E-Way Bill JSON can only be generated from submitted document,e-Way Bill JSON只能从提交的文档中生成,
E-Way Bill JSON cannot be generated for Sales Return as of now,到目前为止，无法为销售回报生成电子方式账单JSON,
ERPNext could not find any matching payment entry,ERPNext找不到任何匹配的付款条目,
Earliest Age,最早年龄,
Edit Details,编辑细节,
Either GST Transporter ID or Vehicle No is required if Mode of Transport is Road,如果运输方式为道路，则需要GST运输车ID或车辆号,
Email,电子邮件,
Email Campaigns,电邮广告系列,
Employee ID is linked with another instructor,员工ID与另一位讲师链接,
Employee Tax and Benefits,员工税和福利,
Employee is required while issuing Asset {0},发放资产{0}时要求员工,
Employee {0} does not belongs to the company {1},员工{0}不属于公司{1},
Enable Auto Re-Order,启用自动重新排序,
End Date of Agreement can't be less than today.,协议的结束日期不能低于今天。,
End Time,结束时间,
Energy Point Leaderboard,能源点排行榜,
Enter API key in Google Settings.,在Google设置中输入API密钥。,
Enter Supplier,输入供应商,
Enter Value,输入值,
Entity Type,实体类型,
Error,错误,
Error in Exotel incoming call,Exotel来电错误,
Error: {0} is mandatory field,错误：{0}是必填字段,
Exception occurred while reconciling {0},协调{0}时发生异常,
Expected and Discharge dates cannot be less than Admission Schedule date,预计出院日期不得少于入学时间表,
Expired,已过期,
Export,导出,
Export not allowed. You need {0} role to export.,不允许导出，您没有{0}的角色。,
Failed to add Domain,添加域失败,
Fetch Items from Warehouse,从仓库中获取物品,
Fetching...,正在获取...,
Field,字段,
Filters,过滤器,
Finding linked payments,查找关联付款,
Fleet Management,车队的管理,
Following fields are mandatory to create address:,必须填写以下字段才能创建地址：,
For Month,每月,
"For item {0} at row {1}, count of serial numbers does not match with the picked quantity",对于行{1}处的项目{0}，序列号计数与拾取的数量不匹配,
For operation {0}: Quantity ({1}) can not be greter than pending quantity({2}),对于操作{0}：数量（{1}）不能大于挂起的数量（{2}）,
For quantity {0} should not be greater than work order quantity {1},对于数量{0}，不应大于工作订单数量{1},
Free item not set in the pricing rule {0},未在定价规则{0}中设置免费项目,
From Date and To Date are Mandatory,从日期到日期是强制性的,
From employee is required while receiving Asset {0} to a target location,在接收资产{0}到目标位置时需要从雇员那里,
Future Payment Amount,未来付款金额,
Future Payment Ref,未来付款参考,
Future Payments,未来付款,
GST HSN Code does not exist for one or more items,一个或多个项目不存在GST HSN代码,
Generate E-Way Bill JSON,生成e-Way Bill JSON,
Get Items,获取物料,
Get Outstanding Documents,获取优秀文件,
Goal,目标,
Greater Than Amount,大于金额,
Green,绿,
Group,组,
Group By Customer,按客户分组,
Group By Supplier,按供应商分组,
Group Node,组节点,
Group Warehouses cannot be used in transactions. Please change the value of {0},不能在事务中使用组仓库。请更改值{0},
Help,帮助,
Help Article,帮助文章,
"Helps you keep tracks of Contracts based on Supplier, Customer and Employee",帮助您根据供应商，客户和员工记录合同,
Helps you manage appointments with your leads,帮助您管理潜在客户的约会,
Home,主页,
IBAN is not valid,IBAN无效,
Import Data from CSV / Excel files.,从CSV / Excel文件导入数据。,
In Progress,进行中,
Incoming call from {0},来自{0}的来电,
Incorrect Warehouse,仓库不正确,
Invalid Barcode. There is no Item attached to this barcode.,无效的条形码。该条形码没有附件。,
Invalid credentials,无效证件,
Issue Priority.,问题优先。,
Issue Type.,问题类型。,
"It seems that there is an issue with the server's stripe configuration. In case of failure, the amount will get refunded to your account.",看起来服务器的条带配置存在问题。如果失败，这笔款项将退还给您的账户。,
Item Reported,项目报告,
Item listing removed,项目清单已删除,
Item quantity can not be zero,物品数量不能为零,
Item taxes updated,物品税已更新,
Item {0}: {1} qty produced. ,项目{0}：产生了{1}数量。,
Joining Date can not be greater than Leaving Date,加入日期不能大于离开日期,
Lab Test Item {0} already exist,实验室测试项目{0}已存在,
Last Issue,最后一期,
Latest Age,后期,
Leaves Taken,叶子采取,
Less Than Amount,少于金额,
Liabilities,负债,
Loading...,载入中...,
Loan Applications from customers and employees.,客户和员工的贷款申请。,
Loan Processes,贷款流程,
Loan Type for interest and penalty rates,利率和罚款率的贷款类型,
Loans,贷款,
Loans provided to customers and employees.,提供给客户和员工的贷款。,
Location,位置,
Looks like someone sent you to an incomplete URL. Please ask them to look into it.,貌似有人送你一个不完整的URL。请让他们寻找到它。,
Make Journal Entry,创建日志录入,
Make Purchase Invoice,创建购买发票,
Manufactured,制成的,
Mark Work From Home,标记在家工作,
Master,大师,
Max strength cannot be less than zero.,最大强度不能小于零。,
Maximum attempts for this quiz reached!,达到此测验的最大尝试次数！,
Message,信息,
Missing Values Required,缺少需要的值,
Mobile No,手机号码,
Mobile Number,手机号码,
Month,月,
Name,名称,
Near you,在你旁边,
Net Profit/Loss,净利润/亏损,
New Expense,新费用,
New Invoice,新发票,
New Payment,新付款,
New release date should be in the future,新的发布日期应该是将来的,
No Account matched these filters: {},没有帐户符合这些过滤条件：{},
No communication found.,没有找到通讯。,
No correct answer is set for {0},没有为{0}设置正确答案,
No description,没有说明,
No issue has been raised by the caller.,调用者没有提出任何问题。,
No items to publish,没有要发布的项目,
No outstanding invoices found,没有找到未完成的发票,
No outstanding invoices found for the {0} {1} which qualify the filters you have specified.,未找到符合您指定的过滤条件的{0} {1}的未结发票。,
No outstanding invoices require exchange rate revaluation,没有未结清的发票需要汇率重估,
No reviews yet,还没有评论,
No views yet,还没有意见,
Non stock items,非库存物品,
Not Allowed,不允许,
Not allowed to create accounting dimension for {0},不允许为{0}创建会计维度,
Not permitted. Please disable the Lab Test Template,不允许。请禁用实验室测试模板,
Note,注,
Notes: ,注意事项：,
On Converting Opportunity,转换机会,
On Purchase Order Submission,提交采购订单时,
On Sales Order Submission,提交销售订单,
On Task Completion,完成任务,
On {0} Creation,在{0}创建时,
Only .csv and .xlsx files are supported currently,目前仅支持.csv和.xlsx文件,
Open,开,
Open Contact,打开联系,
Open Lead,开放领导,
Opening and Closing,开幕式和闭幕式,
Operating Cost as per Work Order / BOM,根据工单/物料单的运营成本,
Order Amount,订单金额,
Page {0} of {1},第{0} {1},
Paid amount cannot be less than {0},付费金额不能小于{0},
Parent Company must be a group company,母公司必须是集团公司,
Passing Score value should be between 0 and 100,传球得分值应在0到100之间,
Patient History,病史,
Pause,暂停,
Pay,支付,
Payment Document Type,付款单据类型,
Payment Name,付款名称,
Pending,有待,
Performance,性能,
Period based On,期间基于,
Perpetual inventory required for the company {0} to view this report.,公司{0}查看此报告所需的永久清单。,
Phone,电话,
Pick List,选择列表,
Plaid authentication error,格子认证错误,
Plaid public token error,格子公共令牌错误,
Plaid transactions sync error,格子交易同步错误,
Please check the error log for details about the import errors,有关导入错误的详细信息，请查看错误日志,
Please create <b>DATEV Settings</b> for Company <b>{}</b>.,请为公司<b>{}</b>创建<b>DATEV设置</b> 。,
Please create adjustment Journal Entry for amount {0} ,请为金额{0}创建调整日记帐分录,
Please do not create more than 500 items at a time,请不要一次创建超过500个项目,
Please enter <b>Difference Account</b> or set default <b>Stock Adjustment Account</b> for company {0},请输入<b>差异帐户</b>或为公司{0}设置默认的<b>库存调整帐户</b>,
Please enter GSTIN and state for the Company Address {0},请输入GSTIN并说明公司地址{0},
Please enter Item Code to get item taxes,请输入商品代码以获取商品税,
Please enter Warehouse and Date,请输入仓库和日期,
Please login as a Marketplace User to edit this item.,请以市场用户身份登录以编辑此项目。,
Please login as a Marketplace User to report this item.,请以市场用户身份登录以报告此项目。,
Please select <b>Template Type</b> to download template,请选择<b>模板类型</b>以下载模板,
Please select Customer first,请先选择客户,
Please select Item Code first,请先选择商品代码,
Please select a Delivery Note,请选择送货单,
Please select a Sales Person for item: {0},请为以下项目选择销售人员：{0},
Please select another payment method. Stripe does not support transactions in currency '{0}',请选择其他付款方式。 Stripe不支持货币“{0}”的交易,
Please select the customer.,请选择客户。,
Please set a Supplier against the Items to be considered in the Purchase Order.,请根据采购订单中要考虑的项目设置供应商。,
Please set account heads in GST Settings for Compnay {0},请在Compnay {0}的GST设置中设置帐户首长,
Please set an email id for the Lead {0},请为潜在客户{0}设置电子邮件ID,
Please set default UOM in Stock Settings,请在“库存设置”中设置默认的UOM,
Please set filter based on Item or Warehouse due to a large amount of entries.,由于条目很多，请根据物料或仓库设置过滤器。,
Please set up the Campaign Schedule in the Campaign {0},请在广告系列{0}中设置广告系列计划,
Please set valid GSTIN No. in Company Address for company {0},请在公司地址中为公司{0}设置有效的GSTIN号。,
Please set {0},请设置{0},customer
Please setup a default bank account for company {0},请为公司{0}设置默认银行帐户,
Please specify,请注明,
Please specify a {0},请指定一个{0},lead
Priority,优先,
Priority has been changed to {0}.,优先级已更改为{0}。,
Priority {0} has been repeated.,优先级{0}已重复。,
Processing XML Files,处理XML文件,
Profitability,盈利能力,
Project,项目,
Provide the academic year and set the starting and ending date.,提供学年并设置开始和结束日期。,
Public token is missing for this bank,此银行缺少公共令牌,
Publish 1 Item,发布1项,
Publish Items,发布项目,
Publish More Items,发布更多项目,
Publish Your First Items,发布您的第一个项目,
Publish {0} Items,发布{0}项,
Published Items,发布的项目,
Purchase Invoice cannot be made against an existing asset {0},无法针对现有资产{0}生成采购发票,
Purchase Invoices,购买发票,
Purchase Orders,订单,
Purchase Receipt doesn't have any Item for which Retain Sample is enabled.,购买收据没有任何启用了保留样本的项目。,
Purchase Return,采购退货,
Qty of Finished Goods Item,成品数量,
Quality Inspection required for Item {0} to submit,要提交项目{0}所需的质量检验,
Quantity to Manufacture,制造数量,
Quantity to Manufacture can not be zero for the operation {0},操作{0}的制造数量不能为零,
Quarterly,季度,
Queued,排队,
Quick Entry,快速入门,
Quiz {0} does not exist,测验{0}不存在,
Quotation Amount,报价金额,
Rate or Discount is required for the price discount.,价格折扣需要Rate或Discount。,
Reason,原因,
Reconcile Entries,协调条目,
Reconcile this account,核对此帐户,
Reconciled,不甘心,
Recruitment,招聘,
Red,红,
Release date must be in the future,发布日期必须在将来,
Relieving Date must be greater than or equal to Date of Joining,取消日期必须大于或等于加入日期,
Rename,重命名,
Rename Not Allowed,重命名不允许,
Report Item,报告项目,
Report this Item,举报此项目,
Reserved Qty for Subcontract: Raw materials quantity to make subcontracted items.,分包的预留数量：制造分包项目的原材料数量。,
Reset,重启,
Reset Service Level Agreement,重置服务水平协议,
Resetting Service Level Agreement.,重置服务水平协议。,
Return amount cannot be greater unclaimed amount,退货金额不能大于无人认领的金额,
Review,评论,
Room,房间,
Room Type,房型,
Row # ,行＃,
Row #{0}: Accepted Warehouse and Supplier Warehouse cannot be same,第＃0行：接受仓库和供应商仓库不能相同,
Row #{0}: Cannot delete item {1} which has already been billed.,第＃{0}行：无法删除已计费的项目{1}。,
Row #{0}: Cannot delete item {1} which has already been delivered,第{0}行：无法删除已交付的项目{1},
Row #{0}: Cannot delete item {1} which has already been received,第＃0行：无法删除已收到的项目{1},
Row #{0}: Cannot delete item {1} which has work order assigned to it.,第＃{0}行：无法删除已为其分配了工作订单的项目{1}。,
Row #{0}: Cannot delete item {1} which is assigned to customer's purchase order.,第＃{0}行：无法删除分配给客户采购订单的项目{1}。,
Row #{0}: Cannot select Supplier Warehouse while suppling raw materials to subcontractor,第{0}行：在向分包商供应原材料时无法选择供应商仓库,
Row #{0}: Cost Center {1} does not belong to company {2},第{0}行：成本中心{1}不属于公司{2},
Row #{0}: Operation {1} is not completed for {2} qty of finished goods in Work Order {3}. Please update operation status via Job Card {4}.,行＃{0}：对于工作订单{3}中的{2}数量的成品，未完成操作{1}。请通过工作卡{4}更新操作状态。,
Row #{0}: Payment document is required to complete the transaction,行＃{0}：完成交易需要付款文件,
Row #{0}: Serial No {1} does not belong to Batch {2},行＃{0}：序列号{1}不属于批次{2},
Row #{0}: Service End Date cannot be before Invoice Posting Date,行＃{0}：服务终止日期不能早于发票过帐日期,
Row #{0}: Service Start Date cannot be greater than Service End Date,行＃{0}：服务开始日期不能大于服务结束日期,
Row #{0}: Service Start and End Date is required for deferred accounting,行＃{0}：延期计费需要服务开始和结束日期,
Row {0}: Invalid Item Tax Template for item {1},第{0}行：项目{1}的项目税模板无效,
Row {0}: Quantity not available for {4} in warehouse {1} at posting time of the entry ({2} {3}),第{0}行：在输入条目（{2} {3}）时，仓库{1}中{4}不可使用的数量,
Row {0}: user has not applied the rule {1} on the item {2},第{0}行：用户尚未在项目{2}上应用规则{1},
Row {0}:Sibling Date of Birth cannot be greater than today.,第{0}行：同级出生日期不能大于今天。,
Row({0}): {1} is already discounted in {2},行（{0}）：{1}已在{2}中打折,
Rows Added in {0},{0}中添加的行数,
Rows Removed in {0},在{0}中删除的行,
Save,保存,
Save Item,保存项目,
Saved Items,保存的物品,
Search Items ...,搜索项目......,
Search for a payment,搜索付款,
Search for anything ...,搜索任何东西......,
Search results for,为。。。。寻找结果,
Select Difference Account,选择差异账户,
Select a Default Priority.,选择默认优先级。,
Select a company,选择一家公司,
Select finance book for the item {0} at row {1},为行{1}中的项{0}选择财务手册,
Select only one Priority as Default.,仅选择一个优先级作为默认值。,
Seller Information,卖家信息,
Send,发送,
Send a message,发送一个消息,
Sending,发送中,
Sends Mails to lead or contact based on a Campaign schedule,根据Campaign计划发送邮件以进行引导或联系,
Serial Number Created,序列号已创建,
Serial Numbers Created,序列号已创建,
Serial no(s) required for serialized item {0},序列化项目{0}所需的序列号,
Series,系列,
Server Error,服务器错误,
Service Level Agreement has been changed to {0}.,服务水平协议已更改为{0}。,
Service Level Agreement was reset.,服务水平协议已重置。,
Service Level Agreement with Entity Type {0} and Entity {1} already exists.,与实体类型{0}和实体{1}的服务水平协议已存在。,
Set Meta Tags,设置元标记,
Set {0} in company {1},在公司{1}中设置{0},
Setup,设置,
Shift Management,班别管理,
Show Future Payments,显示未来付款,
Show Linked Delivery Notes,显示链接的交货单,
Show Sales Person,显示销售人员,
Show Stock Ageing Data,显示库存账龄数据,
Show Warehouse-wise Stock,显示仓库库存,
Size,尺寸,
Something went wrong while evaluating the quiz.,评估测验时出了点问题。,
Sr,锶,
Start,开始,
Start Date cannot be before the current date,开始日期不能早于当前日期,
Start Time,开始时间,
Status,状态,
Status must be Cancelled or Completed,状态必须已取消或已完成,
Stock Balance Report,库存余额报告,
Stock Entry has been already created against this Pick List,已经根据此选择列表创建了库存输入,
Stock Ledger ID,库存分类帐编号,
Stock Value ({0}) and Account Balance ({1}) are out of sync for account {2} and it's linked warehouses.,库存值（{0}）和帐户余额（{1}）与帐户{2}及其链接的仓库不同步。,
Stores - {0},商店-{0},
Student with email {0} does not exist,电子邮件{0}的学生不存在,
Submit Review,提交评论,
Submitted,已提交,
Supplier Addresses And Contacts,供应商的地址和联系方式,
Synchronize this account,同步此帐户,
Tag,标签,
Target Location is required while receiving Asset {0} from an employee,从员工那里收到资产{0}时需要目标位置,
Target Location is required while transferring Asset {0},转移资产{0}时需要目标位置,
Target Location or To Employee is required while receiving Asset {0},接收资产{0}时需要“目标位置”或“发给员工”,
Task's {0} End Date cannot be after Project's End Date.,任务的{0}结束日期不能晚于项目的结束日期。,
Task's {0} Start Date cannot be after Project's End Date.,任务的{0}开始日期不能晚于项目的结束日期。,
Tax Account not specified for Shopify Tax {0},没有为Shopify Tax {0}指定税务帐户,
Tax Total,税收总额,
Template,模板,
The Campaign '{0}' already exists for the {1} '{2}',{1}&#39;{2}&#39;广告系列“{0}”已存在,
The difference between from time and To Time must be a multiple of Appointment,时间与时间之间的差异必须是约会的倍数,
The field Asset Account cannot be blank,字段资产帐户不能为空,
The field Equity/Liability Account cannot be blank,字段权益/责任帐户不能为空,
The following serial numbers were created: <br><br> {0},创建了以下序列号： <br><br> {0},
The parent account {0} does not exists in the uploaded template,上级模板中不存在上级帐户{0},
The question cannot be duplicate,问题不能重复,
The selected payment entry should be linked with a creditor bank transaction,所选付款条目应与债权银行交易相关联,
The selected payment entry should be linked with a debtor bank transaction,所选付款条目应与债务人银行交易挂钩,
The total allocated amount ({0}) is greated than the paid amount ({1}).,总分配金额（{0}）比付款金额（{1}）更重要。,
This Service Level Agreement is specific to Customer {0},此服务级别协议特定于客户{0},
This action will unlink this account from any external service integrating ERPNext with your bank accounts. It cannot be undone. Are you certain ?,此操作将取消此帐户与将ERPNext与您的银行帐户集成的任何外部服务的链接。它无法撤消。你确定吗 ？,
This bank account is already synchronized,此银行帐户已同步,
This bank transaction is already fully reconciled,此银行交易已完全已对帐,
This page keeps track of items you want to buy from sellers.,此页面会跟踪您要从卖家处购买的商品。,
This page keeps track of your items in which buyers have showed some interest.,此页面会跟踪您的商品，其中买家已表现出一些兴趣。,
Thursday,星期四,
Title,标题,
"To allow over billing, update ""Over Billing Allowance"" in Accounts Settings or the Item.",要允许超额结算，请在“帐户设置”或“项目”中更新“超额结算限额”。,
"To allow over receipt / delivery, update ""Over Receipt/Delivery Allowance"" in Stock Settings or the Item.",要允许超过收货/交货，请在库存设置或项目中更新“超过收货/交货限额”。,
Total,总,
Total Payment Request amount cannot be greater than {0} amount,总付款请求金额不能大于{0}金额,
Total payments amount can't be greater than {},付款总额不得超过{},
Totals,总计,
Transactions already retreived from the statement,已从报表中检索到的交易,
Transfer Material to Supplier,转印材料供应商,
Transport Receipt No and Date are mandatory for your chosen Mode of Transport,您选择的运输方式必须提供运输收据号和日期,
Tuesday,星期二,
Type,类型,
Unable to find the time slot in the next {0} days for the operation {1}.,无法找到操作{1}的未来{0}天的时间段。,
Unable to update remote activity,无法更新远程活动,
Unknown Caller,未知的来电者,
Unlink external integrations,取消外部集成的链接,
Unpublish Item,取消发布项目,
Unreconciled,未调节,
Unsupported GST Category for E-Way Bill JSON generation,用于e-Way Bill JSON生成的不支持的GST类别,
Update,更新,
Update Taxes for Items,更新项目税金,
"Upload a bank statement, link or reconcile a bank account",上传银行对帐单，关联或核对银行帐户,
Upload a statement,上传声明,
Use a name that is different from previous project name,使用与先前项目名称不同的名称,
User {0} is disabled,用户{0}已禁用,
Users and Permissions,用户和权限,
Valuation Rate required for Item {0} at row {1},第{1}行的第{0}项所需的估价率,
Values Out Of Sync,值不同步,
Vehicle Type is required if Mode of Transport is Road,如果运输方式为道路，则需要车辆类型,
Vendor Name,供应商名称,
Verify Email,验证邮件,
View,查看,
View all issues from {0},查看{0}中的所有问题,
View call log,查看通话记录,
Warehouse,仓库,
Warehouse not found against the account {0},在帐户{0}中找不到仓库,
Welcome to {0},欢迎{0},
Why do think this Item should be removed?,为什么要认为这个项目应该删除？,
Work Order {0}: Job Card not found for the operation {1},工作单{0}：找不到工序{1}的工作卡,
Workday {0} has been repeated.,工作日{0}已重复。,
XML Files Processed,处理的XML文件,
Year,年,
Yearly,每年,
You are not allowed to enroll for this course,您无权注册此课程,
You are not enrolled in program {0},您尚未加入计划{0},
You can Feature upto 8 items.,最多可以包含8个项目。,
You can also copy-paste this link in your browser,您也可以复制粘贴此链接到您的浏览器地址栏中,
You can publish upto 200 items.,您最多可以发布200个项目。,
You have to enable auto re-order in Stock Settings to maintain re-order levels.,您必须在库存设置中启用自动重新订购才能维持重新订购级别。,
You must be a registered supplier to generate e-Way Bill,您必须是注册供应商才能生成电子方式账单,
You need to login as a Marketplace User before you can add any reviews.,在添加任何评论之前，您需要以市场用户身份登录。,
Your Featured Items,你的特色商品,
Your Items,你的物品,
Your Profile,您的个人资料,
Your rating:,您的评分：,
and,和,
e-Way Bill already exists for this document,e-Way Bill已存在于本文件中,
woocommerce - {0},woocommerce-{0},
{0} Coupon used are {1}. Allowed quantity is exhausted,{0}使用的优惠券是{1}。允许数量已耗尽,
{0} Operations: {1},{0}操作：{1},
{0} bank transaction(s) created,创建了{0}银行交易,
{0} bank transaction(s) created and {1} errors,创建了{0}个银行交易和{1}个错误,
{0} can not be greater than {1},{0}不能大于{1},
{0} conversations,{0}次对话,
{0} is not a company bank account,{0}不是公司银行帐户,
{0} is not a group node. Please select a group node as parent cost center,{0}不是组节点。请选择一个组节点作为父成本中心,
{0} is not the default supplier for any items.,{0}不是任何商品的默认供应商。,
{0} is required,{0}是必填项,
{0}: {1} must be less than {2},{0}：{1}必须小于{2},
{} is required to generate E-Way Bill JSON,{}需要生成e-Way Bill JSON,
"Invalid lost reason {0}, please create a new lost reason",无效的丢失原因{0}，请创建一个新的丢失原因,
Profit This Year,今年获利,
Total Expense,总费用,
Total Expense This Year,今年总费用,
Total Income,总收入,
Total Income This Year,今年总收入,
Barcode,条码,
Clear,明确,
Comments,评论,
DocType,DocType,
Download,下载,
Left,左边,
Link,链接,
New,新,
Print,打印,
Reference Name,参考名称,
Refresh,刷新,
Success,成功,
Time,时间,
Value,值,
Actual,实际,
Add to Cart,加入购物车,
Days Since Last Order,自上次订购以来的天数,
In Stock,库存,
Mode Of Payment,付款方式,
No students Found,找不到学生,
Not in Stock,仓库无货,
Please select a Customer,请选择一位客户,
Received From,从......收到,
Sales Person,销售人员,
To date cannot be before From date,无效的主名称,
Write Off,内部销账,
{0} Created,{0}已创建,
Email Id,电子邮件ID,
No,No,
Reference Doctype,参考文档类型,
Yes,是,
Actual ,实际数据,
Add to cart,加入购物车,
Budget,预算,
Chart of Accounts,会计科目表,
Customer database.,客户数据库。,
Days Since Last order,自上次订购天数,
Download as JSON,下载为JSON,
End date can not be less than start date,结束日期不能小于开始日期,
For Default Supplier (Optional),对于默认供应商（可选）,
From date cannot be greater than To date,从日期不能大于到日期,
Group by,分组基于,
In stock,有现货,
Item name,物料名称,
Minimum Qty,最低数量,
More details,更多信息,
Nature of Supplies,供应的性质,
No Items found.,未找到任何项目。,
No students found,没有发现学生,
Not in stock,没存货,
Not permitted,不允许,
Open Issues ,待处理问题,
Open Projects ,打开项目,
Open To Do ,打开代办事项,
Operation Id,操作ID,
Partially ordered,偏序,
Please select company first,请首先选择公司,
Please select patient,请选择患者,
Printed On ,印在,
Projected qty,预期可用数量,
Sales person,销售人员,
Serial No {0} Created,序列号{0}已创建,
Source Location is required for the Asset {0},源位置对资产{0}是必需的,
Tax Id,税号,
To Time,要时间,
To date cannot be before from date,到日期不能早于日期,
Total Taxable value,应税总额,
Upcoming Calendar Events ,即将到来的日历事件,
Value or Qty,价值或数量,
Variance ,方差,
Variant of,变量属于,
Write off,内部销账,
hours,小时,
received from,从......收到,
to,到,
Cards,牌,
Percentage,百分比,
Failed to setup defaults for country {0}. <NAME_EMAIL>,无法设置国家/地区{0}的默认设置。请联系*******************,
Row #{0}: Item {1} is not a Serialized/Batched Item. It cannot have a Serial No/Batch No against it.,行＃{0}：项目{1}不是序列化/批量项目。它不能有序列号/批号。,
Please set {0},请设置{0},
Please set {0},请设置{0},supplier
Draft,草案,"docstatus,=,0"
Cancelled,取消,"docstatus,=,2"
Please setup Instructor Naming System in Education > Education Settings,请在“教育”&gt;“教育设置”中设置教师命名系统,
Please set Naming Series for {0} via Setup > Settings > Naming Series,请通过设置&gt;设置&gt;命名系列为{0}设置命名系列,
UOM Conversion factor ({0} -> {1}) not found for item: {2},找不到项目{2}的UOM转换因子（{0}-&gt; {1}）,
Item Code > Item Group > Brand,物料代码&gt;物料组&gt;品牌,
Customer > Customer Group > Territory,客户&gt;客户组&gt;地区,
Supplier > Supplier Type,供应商&gt;供应商类型,
The value of {0} differs between Items {1} and {2},{0}的值在项目{1}和{2}之间有所不同,
Auto Fetch,自动提取,
Fetch Serial Numbers based on FIFO,根据FIFO获取序列号,
"Outward taxable supplies(other than zero rated, nil rated and exempted)",向外应税供应（零税率，零税率和免税额除外）,
"To allow different rates, disable the {0} checkbox in {1}.",要允许不同的速率，请禁用{1}中的{0}复选框。,
Asset{} {assets_link} created for {},为{}创建的资产{} {assets_link},
Row {}: Asset Naming Series is mandatory for the auto creation for item {},第{}行：对于项目{}的自动创建，必须使用资产命名系列,
Assets not created for {0}. You will have to create asset manually.,未为{0}创建资产。您将必须手动创建资产。,
{0} {1} has accounting entries in currency {2} for company {3}. Please select a receivable or payable account with currency {2}.,{0} {1}拥有公司{3}的币种为{2}的会计分录。请选择币种为{2}的应收或应付帐户。,
Invalid Account,无效账户,
Purchase Order Required,需要采购订单,
Purchase Receipt Required,需要采购收据,
Account Missing,帐户遗失,
Requested,要求,
Partially Paid,部分付款,
Invalid Account Currency,无效的帐户币种,
"Row {0}: The item {1}, quantity must be positive number",第{0}行：项目{1}，数量必须为正数,
"Please set {0} for Batched Item {1}, which is used to set {2} on Submit.",请为批处理项目{1}设置{0}，该项目用于在提交时设置{2}。,
Expiry Date Mandatory,有效期至,
Variant Item,变项,
BOM 1 {0} and BOM 2 {1} should not be same,BOM 1 {0}和BOM 2 {1}不应相同,
Note: Item {0} added multiple times,注意：项目{0}被多次添加,
YouTube,YouTube的,
Vimeo,Vimeo的,
Publish Date,发布日期,
Duration,持续时间,
Advanced Settings,高级设置,
Path,路径,
Components,组件,
Verified By,认证,
Invalid naming series (. missing) for {0},{0}的无效命名系列（。丢失）,
Filter Based On,过滤依据,
Reqd by date,按日期要求,
Manufacturer Part Number <b>{0}</b> is invalid,制造商零件编号<b>{0}</b>无效,
Invalid Part Number,无效的零件号,
Select atleast one Social Media from Share on.,从“共享”中选择至少一个社交媒体。,
Invalid Scheduled Time,无效的计划时间,
Length Must be less than 280.,长度必须小于280。,
Error while POSTING {0},发布{0}时出错,
"Session not valid, Do you want to login?",会话无效，您要登录吗？,
Session Active,会话进行中,
Session Not Active. Save doc to login.,会话无效。保存文档以登录。,
Error! Failed to get request token.,错误！无法获取请求令牌。,
Invalid {0} or {1},无效的{0}或{1},
Error! Failed to get access token.,错误！无法获取访问令牌。,
Invalid Consumer Key or Consumer Secret Key,无效的消费者密钥或消费者秘密密钥,
Your Session will be expire in ,您的会话将在过期,
 days.,天。,
Session is expired. Save doc to login.,会话已过期。保存文档以登录。,
Error While Uploading Image,上传图片时出错,
You Didn't have permission to access this API,您无权访问此API,
Valid Upto date cannot be before Valid From date,有效截止日期不能早于有效起始日期,
Valid From date not in Fiscal Year {0},有效起始日期不在会计年度{0}中,
Valid Upto date not in Fiscal Year {0},有效最新日期不在会计年度{0}中,
Group Roll No,组卷编号,
Maintain Same Rate Throughout Sales Cycle,在整个销售周期使用同一价格,
"Row {1}: Quantity ({0}) cannot be a fraction. To allow this, disable '{2}' in UOM {3}.",第{1}行：数量（{0}）不能为小数。为此，请在UOM {3}中禁用“ {2}”。,
Must be Whole Number,必须是整数,
Please setup Razorpay Plan ID,请设置Razorpay计划ID,
Contact Creation Failed,联系人创建失败,
Leaves Expired,叶子过期,
Row #{}: {} of {} should be {}. Please modify the account or select a different account.,第{{}行：{}的{}应该是{}。请修改帐户或选择其他帐户。,
Row #{}: Please asign task to a member.,第{}行：请将任务分配给成员。,
Process Failed,处理失败,
Tally Migration Error,理货迁移错误,
Please set Warehouse in Woocommerce Settings,请在Woocommerce设置中设置Warehouse,
Row {0}: Delivery Warehouse ({1}) and Customer Warehouse ({2}) can not be same,第{0}行：交货仓库（{1}）和客户仓库（{2}）不能相同,
Row {0}: Due Date in the Payment Terms table cannot be before Posting Date,第{0}行：“付款条款”表中的到期日期不能早于过帐日期,
Cannot find {} for item {}. Please set the same in Item Master or Stock Settings.,找不到项目{}的{}。请在“物料主数据”或“库存设置”中进行相同设置。,
Row #{0}: The batch {1} has already expired.,行＃{0}：批次{1}已过期。,
Start Year and End Year are mandatory,开始年和结束年是强制性的,
GL Entry,总账分录,
Cannot allocate more than {0} against payment term {1},付款期{1}不能分配超过{0},
The root account {0} must be a group,根帐户{0}必须是一个组,
Shipping rule not applicable for country {0} in Shipping Address,送货规则不适用于送货地址中的国家/地区{0},
Get Payments from,从中获取付款,
Set Shipping Address or Billing Address,设置送货地址或帐单地址,
Consultation Setup,咨询设置,
Fee Validity,费用有效期,
Laboratory Setup,实验室设置,
Dosage Form,剂型,
Records and History,记录和历史,
Patient Medical Record,病人医疗记录,
Rehabilitation,复原,
Exercise Type,锻炼类型,
Exercise Difficulty Level,运动难度等级,
Therapy Type,治疗类型,
Therapy Plan,治疗计划,
Therapy Session,治疗会议,
Motor Assessment Scale,运动评估量表,
[Important] [ERPNext] Auto Reorder Errors,[重要] [ERPNext]自动重新排序错误,
"Regards,",问候，,
The following {0} were created: {1},已创建以下{0}：{1},
Work Orders,工作订单,
The {0} {1} created sucessfully,{0} {1}成功创建,
Work Order cannot be created for following reason: <br> {0},由于以下原因，无法创建工作订单：<br> {0},
Add items in the Item Locations table,在“项目位置”表中添加项目,
Update Current Stock,更新当前库存,
"{0} Retain Sample is based on batch, please check Has Batch No to retain sample of item",{0}保留样品基于批次，请检查是否具有批次号以保留项目样品,
Empty,空的,
Currently no stock available in any warehouse,目前在任何仓库中都没有库存,
BOM Qty,BOM数量,
Time logs are required for {0} {1},{0} {1}需要时间日志,
Total Completed Qty,完成总数量,
Qty to Manufacture,生产数量,
Social Media Campaigns,社交媒体活动,
From Date can not be greater than To Date,起始日期不能大于截止日期,
Please set a Customer linked to the Patient,请设置与患者链接的客户,
Customer Not Found,找不到客户,
Please Configure Clinical Procedure Consumable Item in ,请在以下位置配置临床程序消耗品,
Missing Configuration,缺少配置,
Out Patient Consulting Charge Item,不住院患者咨询费用项目,
Inpatient Visit Charge Item,住院访问费用项目,
OP Consulting Charge,OP咨询费,
Inpatient Visit Charge,住院访问费用,
Appointment Status,预约状态,
Test: ,测试：,
Collection Details: ,集合详细信息：,
{0} out of {1},{1}中的{0},
Select Therapy Type,选择治疗类型,
{0} sessions completed,{0}个会话已完成,
{0} session completed,{0}个会话已完成,
 out of {0},在{0}中,
Therapy Sessions,治疗会议,
Add Exercise Step,添加运动步骤,
Edit Exercise Step,编辑运动步骤,
Patient Appointments,预约病人,
Item with Item Code {0} already exists,物料代码为{0}的物料已存在,
Registration Fee cannot be negative or zero,注册费不能为负或零,
Configure a service Item for {0},为{0}配置服务项目,
Temperature: ,温度：,
Pulse: ,脉冲：,
Respiratory Rate: ,呼吸频率：,
BP: ,血压：,
BMI: ,体重指数：,
Note: ,注意：,
Check Availability,检查可用性,
Please select Patient first,请先选择患者,
Please select a Mode of Payment first,请先选择付款方式,
Please set the Paid Amount first,请先设置付款金额,
Not Therapies Prescribed,没有规定的疗法,
There are no Therapies prescribed for Patient {0},没有为患者{0}规定任何疗法,
Appointment date and Healthcare Practitioner are Mandatory,预约日期和医疗从业者为必填项,
No Prescribed Procedures found for the selected Patient,未找到所选患者的处方程序,
Please select a Patient first,请先选择一个病人,
There are no procedure prescribed for ,没有规定的程序,
Prescribed Therapies,订明疗法,
Appointment overlaps with ,约会与,
{0} has appointment scheduled with {1} at {2} having {3} minute(s) duration.,{0}已与{1}安排了{3}分钟的{2}约会。,
Appointments Overlapping,约会重叠,
Consulting Charges: {0},咨询费用：{0},
Appointment Cancelled. Please review and cancel the invoice {0},预约已取消。请查看并取消发票{0},
Appointment Cancelled.,预约已取消。,
Fee Validity {0} updated.,费用有效期{0}已更新。,
Practitioner Schedule Not Found,找不到医生时间表,
{0} is on a Half day Leave on {1},{0}休假半天，{1},
{0} is on Leave on {1},{0}正在休假{1},
{0} does not have a Healthcare Practitioner Schedule. Add it in Healthcare Practitioner,{0}没有医疗保健从业人员时间表。将其添加到医疗保健从业者中,
Healthcare Service Units,医疗服务单位,
Complete and Consume,完成并消耗,
Complete {0} and Consume Stock?,完成{0}并消耗库存吗？,
Complete {0}?,完成{0}吗？,
Stock quantity to start the Procedure is not available in the Warehouse {0}. Do you want to record a Stock Entry?,仓库{0}中没有启动该过程的库存数量。您是否要记录库存条目？,
{0} as on {1},{0}与{1}相同,
Clinical Procedure ({0}):,临床程序（{0}）：,
Please set Customer in Patient {0},请在“患者{0}”中设置“客户”,
Item {0} is not active,项目{0}无效,
Therapy Plan {0} created successfully.,治疗计划{0}已成功创建。,
Symptoms: ,症状：,
No Symptoms,无症状,
Diagnosis: ,诊断：,
No Diagnosis,没有诊断,
Drug(s) Prescribed.,处方药。,
Test(s) Prescribed.,规定的测试。,
Procedure(s) Prescribed.,规定的程序。,
Counts Completed: {0},已完成的计数：{0},
Patient Assessment,患者评估,
Assessments,评估,
Heads (or groups) against which Accounting Entries are made and balances are maintained.,标题（或者组），以此会计分录建立和余额保存的,
Account Name,科目名称,
Inter Company Account,关联公司间交易科目,
Parent Account,上级账号,
Setting Account Type helps in selecting this Account in transactions.,设置科目类型有助于在交易中选择该科目。,
Chargeable,可记账的,
Rate at which this tax is applied,应用此税率的单价,
Frozen,已冻结,
"If the account is frozen, entries are allowed to restricted users.",如果科目被冻结，则只有特定用户才能创建分录。,
Balance must be,余额必须是,
Lft,左脚,
Rgt,Rgt,
Old Parent,旧上级,
Include in gross,包括毛,
Auditor,审计员,
Accounting Dimension,会计维度,
Dimension Name,尺寸名称,
Dimension Defaults,尺寸默认值,
Accounting Dimension Detail,会计维度明细,
Default Dimension,默认尺寸,
Mandatory For Balance Sheet,资产负债表必备,
Mandatory For Profit and Loss Account,对于损益账户必须提供,
Accounting Period,会计期间,
Period Name,期间名称,
Closed Documents,已关闭的文件,
Accounts Settings,会计设置,
Settings for Accounts,科目设置,
Make Accounting Entry For Every Stock Movement,为每个库存变动创建会计分录,
Users with this role are allowed to set frozen accounts and create / modify accounting entries against frozen accounts,拥有此角色的用户可以设置冻结科目，创建/修改冻结科目的会计凭证,
Determine Address Tax Category From,确定地址税类别,
Over Billing Allowance (%),超过结算津贴（％）,
Credit Controller,信用控制人,
Check Supplier Invoice Number Uniqueness,检查供应商费用清单编号唯一性,
Make Payment via Journal Entry,通过手工凭证进行付款,
Unlink Payment on Cancellation of Invoice,取消费用清单时去掉关联的付款,
Book Asset Depreciation Entry Automatically,自动生成固定资产折旧凭证,
Automatically Add Taxes and Charges from Item Tax Template,从项目税模板自动添加税费,
Automatically Fetch Payment Terms,自动获取付款条款,
Show Payment Schedule in Print,在打印中显示付款工时单,
Currency Exchange Settings,外币汇率设置,
Allow Stale Exchange Rates,允许使用历史汇率,
Stale Days,信用证有效期天数,
Report Settings,报表设置,
Use Custom Cash Flow Format,使用自定义现金流量格式,
Allowed To Transact With,允许与。。。交易,
SWIFT number,环球银行金融电信协会代码,
Branch Code,分行代码,
Address and Contact,地址和联系方式,
Address HTML,地址HTML,
Contact HTML,联系HTML,
Data Import Configuration,数据导入配置,
Bank Transaction Mapping,银行交易映射,
Plaid Access Token,格子访问令牌,
Company Account,公司帐号,
Account Subtype,帐户子类型,
Is Default Account,是默认帐户,
Is Company Account,是公司帐户,
Party Details,往来单位详细信息,
Account Details,科目信息,
IBAN,IBAN,
Bank Account No,银行帐号,
Integration Details,集成细节,
Integration ID,集成ID,
Last Integration Date,上次整合日期,
Change this date manually to setup the next synchronization start date,手动更改此日期以设置下一个同步开始日期,
Mask,面具,
Bank Account Subtype,银行帐户子类型,
Bank Account Type,银行账户类型,
Bank Guarantee,银行担保,
Bank Guarantee Type,银行担保类型,
Receiving,接收,
Providing,提供,
Reference Document Name,参考文件名称,
Validity in Days,有效天数,
Bank Account Info,银行账户信息,
Clauses and Conditions,条款和条件,
Other Details,其他详情,
Bank Guarantee Number,银行担保编号,
Name of Beneficiary,受益人姓名,
Margin Money,保证金,
Charges Incurred,产生的费用,
Fixed Deposit Number,定期存款编号,
Account Currency,科目币种,
Select the Bank Account to reconcile.,选择要对帐的银行帐户。,
Include Reconciled Entries,包括核销分录,
Get Payment Entries,获取付款项,
Payment Entries,付款项,
Update Clearance Date,更新清算日期,
Bank Reconciliation Detail,银行对帐信息,
Cheque Number,支票号码,
Cheque Date,支票日期,
Statement Header Mapping,对帐单抬头对照关系,
Statement Headers,对帐单抬头,
Transaction Data Mapping,交易数据映射,
Mapped Items,已映射的项目,
Bank Statement Settings Item,银行对账单设置项,
Mapped Header,已映射的标题,
Bank Header,银行标题,
Bank Statement Transaction Entry,银行对账单交易分录,
Bank Transaction Entries,银行交易分录,
New Transactions,新交易,
Match Transaction to Invoices,将交易记录与费用清单匹配,
Create New Payment/Journal Entry,创建新的付款/日记账分录,
Submit/Reconcile Payments,提交/核销付款,
Matching Invoices,匹配费用清单,
Payment Invoice Items,付款要求,
Reconciled Transactions,已核对的交易,
Bank Statement Transaction Invoice Item,银行对账单交易费用清单条目,
Payment Description,付款说明,
Invoice Date,费用清单日期,
invoice,发票,
Bank Statement Transaction Payment Item,银行对账单交易付款项目,
outstanding_amount,未付金额,
Payment Reference,付款凭据,
Bank Statement Transaction Settings Item,银行对账单交易设置项目,
Bank Data,银行数据,
Mapped Data Type,已映射数据类型,
Mapped Data,已映射数据,
Bank Transaction,银行交易,
ACC-BTN-.YYYY.-,ACC-BTN-.YYYY.-,
Transaction ID,交易ID,
Unallocated Amount,未分配金额,
Field in Bank Transaction,银行交易中的字段,
Column in Bank File,银行文件中的列,
Bank Transaction Payments,银行交易付款,
Control Action,控制行动,
Applicable on Material Request,适用于物料申请,
Action if Annual Budget Exceeded on MR,年度预算超出MR的行动,
Warn,警告,
Ignore,忽略,
Action if Accumulated Monthly Budget Exceeded on MR,如果累计每月预算超过MR，则采取行动,
Applicable on Purchase Order,适用于采购订单,
Action if Annual Budget Exceeded on PO,年度预算超出采购订单时采取的行动,
Action if Accumulated Monthly Budget Exceeded on PO,采购订单上累计每月预算超出时的操作,
Applicable on booking actual expenses,适用于预订实际费用,
Action if Annual Budget Exceeded on Actual,年度预算超出实际的行动,
Action if Accumulated Monthly Budget Exceeded on Actual,累计每月预算超出实际的行动,
Budget Accounts,预算科目,
Budget Account,预算科目,
Budget Amount,预算额,
C-Form,C-表,
ACC-CF-.YYYY.-,ACC-CF-.YYYY.-,
C-Form No,C-表编号,
Received Date,收到日期,
Quarter,季,
I,我,
II,二,
III,III,
IV,IV,
C-Form Invoice Detail,C-Form费用清单明细,
Invoice No,费用清单号码,
Cash Flow Mapper,现金流量映射器,
Section Name,部分名称,
Section Header,章节标题,
Section Leader,科长,
e.g Adjustments for:,例如调整：,
Section Subtotal,部分小计,
Section Footer,章节页脚,
Cash Flow Mapping,现金流量映射,
Select Maximum Of 1,选择最多1个,
Is Finance Cost,财务成本,
Is Working Capital,是营运资本,
Is Finance Cost Adjustment,财务成本调整,
Is Income Tax Liability,是所得税责任,
Is Income Tax Expense,是所得税费用？,
Cash Flow Mapping Accounts,现金流量表科目,
account,科目,
Cash Flow Mapping Template,现金流量映射模板,
Cash Flow Mapping Template Details,现金流量映射模板细节,
POS-CLO-,POS-CLO-,
Custody,保管,
Net Amount,净额,
Cashier Closing Payments,收银员结算付款,
Chart of Accounts Importer,会计科目表,
Import Chart of Accounts from a csv file,从csv文件导入科目表,
Attach custom Chart of Accounts file,附加自定义会计科目表文件,
Chart Preview,图表预览,
Chart Tree,图表树,
Cheque Print Template,支票打印模板,
Has Print Format,有打印格式,
Primary Settings,主要设置,
Cheque Size,支票大小,
Regular,定期,
Starting position from top edge,起价顶边位置,
Cheque Width,支票宽度,
Cheque Height,支票高度,
Scanned Cheque,支票扫描,
Is Account Payable,为应付账款,
Distance from top edge,从顶边的距离,
Distance from left edge,从左侧边缘的距离,
Message to show,信息显示,
Date Settings,日期设定,
Starting location from left edge,从左边起始位置,
Payer Settings,付款人设置,
Width of amount in word,文字表示的金额输出宽度,
Line spacing for amount in words,行距文字量,
Amount In Figure,量图,
Signatory Position,签署的位置,
Closed Document,已关闭文件,
Track separate Income and Expense for product verticals or divisions.,跟踪独立收入和支出进行产品垂直或部门。,
Cost Center Name,成本中心名称,
Parent Cost Center,上级成本中心,
lft,Lft,
rgt,RGT,
Coupon Code,优惠券代码,
Coupon Name,优惠券名称,
"e.g. ""Summer Holiday 2019 Offer 20""",例如“ 2019年暑假特惠20”,
Coupon Type,优惠券类型,
Promotional,促销性,
Gift Card,礼物卡,
unique e.g. SAVE20  To be used to get discount,独特的，例如SAVE20用于获得折扣,
Validity and Usage,有效性和用法,
Valid From,有效期自,
Valid Upto,有效期至,
Maximum Use,最大使用量,
Used,用过的,
Coupon Description,优惠券说明,
Discounted Invoice,特价发票,
Debit to,借给,
Exchange Rate Revaluation,汇率重估,
Get Entries,获取条目,
Exchange Rate Revaluation Account,汇率重估科目,
Total Gain/Loss,总收益/损失,
Balance In Account Currency,科目币别余额,
Current Exchange Rate,当前汇率,
Balance In Base Currency,本币余额,
New Exchange Rate,新汇率,
New Balance In Base Currency,本币新余额,
Gain/Loss,收益/损失,
**Fiscal Year** represents a Financial Year. All accounting entries and other major transactions are tracked against **Fiscal Year**.,**财年**表示财务年度。所有的会计分录和其他重大交易将根据**财年**跟踪。,
Year Name,年度名称,
"For e.g. 2012, 2012-13",对例如2012，2012-13,
Year Start Date,年度起始日期,
Year End Date,年度结束日期,
Companies,企业,
Auto Created,自动创建,
Stock User,库存用户,
Fiscal Year Company,公司财务年度,
Debit Amount,借方金额,
Credit Amount,信贷金额,
Debit Amount in Account Currency,科目币别借方金额,
Credit Amount in Account Currency,科目币别贷方金额,
Voucher Detail No,凭证信息编号,
Is Opening,开帐分录？,
Is Advance,是否预付款,
To Rename,要重命名,
GST Account,GST科目,
CGST Account,CGST账户,
SGST Account,SGST账户,
IGST Account,IGST科目,
CESS Account,CESS科目,
Loan Start Date,贷款开始日期,
Loan Period (Days),贷款期限（天）,
Loan End Date,贷款结束日期,
Bank Charges,银行收费,
Short Term Loan Account,短期贷款账户,
Bank Charges Account,银行手续费账户,
Accounts Receivable Credit Account,应收帐款信用帐户,
Accounts Receivable Discounted Account,应收帐款折扣帐户,
Accounts Receivable Unpaid Account,应收帐款未付帐户,
Item Tax Template,物品税模板,
Tax Rates,税率,
Item Tax Template Detail,项目税模板详细信息,
Entry Type,凭证类型,
Inter Company Journal Entry,Inter公司手工凭证,
Bank Entry,银行凭证,
Cash Entry,现金分录,
Credit Card Entry,信用卡分录,
Contra Entry,对销分录,
Excise Entry,Excise分录,
Write Off Entry,销帐分录,
Opening Entry,开帐凭证,
ACC-JV-.YYYY.-,ACC-JV-.YYYY.-,
Accounting Entries,会计分录,
Total Debit,总借方金额,
Total Credit,总贷方金额,
Difference (Dr - Cr),差异(借方-贷方),
Make Difference Entry,创建差异分录,
Total Amount Currency,总金额币种,
Total Amount in Words,大写的总金额,
Remark,备注,
Paid Loan,付费贷款,
Inter Company Journal Entry Reference,关联公司业务手工凭证参考,
Write Off Based On,销帐基于,
Get Outstanding Invoices,获取未付费用清单,
Write Off Amount,冲销金额,
Printing Settings,打印设置,
Pay To / Recd From,支付/ 收款自,
Payment Order,付款单,
Subscription Section,重复,
Journal Entry Account,手工凭证帐号,
Account Balance,科目余额,
Party Balance,往来单位余额,
Accounting Dimensions,会计维度,
If Income or Expense,收入或支出,
Exchange Rate,汇率,
Debit in Company Currency,借记卡在公司货币,
Credit in Company Currency,基于公司本货的信用额度,
Payroll Entry,工资计算,
Employee Advance,员工预支,
Reference Due Date,参考到期日,
Loyalty Program Tier,忠诚度计划层级,
Redeem Against,兑换,
Expiry Date,到期时间,
Loyalty Point Entry Redemption,忠诚度积分兑换,
Redemption Date,赎回日期,
Redeemed Points,兑换积分,
Loyalty Program Name,忠诚计划名称,
Loyalty Program Type,忠诚度计划类型,
Single Tier Program,单层计划,
Multiple Tier Program,多层计划,
Customer Territory,客户地区,
Auto Opt In (For all customers),自动选择（适用于所有客户）,
Collection Tier,收集层,
Collection Rules,收集规则,
Redemption,赎回,
Conversion Factor,转换系数,
1 Loyalty Points = How much base currency?,1忠诚度积分=多少基本币？,
Expiry Duration (in days),到期时间（天）,
Help Section,帮助部分,
Loyalty Program Help,忠诚度计划帮助,
Loyalty Program Collection,忠诚度计划集,
Tier Name,等级名称,
Minimum Total Spent,最低总支出,
Collection Factor (=1 LP),收集因子（= 1 LP）,
For how much spent = 1 Loyalty Point,花费多少= 1忠诚点,
Mode of Payment Account,付款方式默认科目,
Default Account,默认科目,
Default account will be automatically updated in POS Invoice when this mode is selected.,选择此模式后，默认科目将在POS费用清单中自动更新。,
**Monthly Distribution** helps you distribute the Budget/Target across months if you have seasonality in your business.,**月度分配**帮助你分配预算/目标跨越几个月，如果你在你的业务有季节性。,
Distribution Name,分配名称,
Name of the Monthly Distribution,月度分布名称,
Monthly Distribution Percentages,月度分布比例,
Monthly Distribution Percentage,月度分布比例,
Percentage Allocation,核销百分比,
Create Missing Party,创建丢失的单位,
Create missing customer or supplier.,创建已丢失的客户或供应商,
Opening Invoice Creation Tool Item,费用清单创建工具项,
Temporary Opening Account,临时开账科目,
Party Account,往来单位科目,
Type of Payment,付款类型,
ACC-PAY-.YYYY.-,ACC-PAY-.YYYY.-,
Receive,收款,
Internal Transfer,内部转账,
Payment Order Status,付款订单状态,
Payment Ordered,付款订购,
Payment From / To,支付自/至,
Company Bank Account,公司银行账户,
Party Bank Account,党银行账户,
Account Paid From,收款科目,
Account Paid To,付款科目,
Paid Amount (Company Currency),已支付的金额（公司货币）,
Received Amount,收到的金额,
Received Amount (Company Currency),收到的款项（公司币种）,
Get Outstanding Invoice,获得优秀发票,
Payment References,付款参考,
Writeoff,注销,
Total Allocated Amount,总已分配金额,
Total Allocated Amount (Company Currency),总拨款额（公司币种）,
Set Exchange Gain / Loss,设置汇兑损益,
Difference Amount (Company Currency),差异金额（公司币种）,
Write Off Difference Amount,销帐差异金额,
Deductions or Loss,扣除或损失,
Payment Deductions or Loss,付款扣除或损失,
Cheque/Reference Date,支票/参考日期,
Payment Entry Deduction,输入付款扣除,
Payment Entry Reference,付款凭证参考,
Allocated,已分配,
Payment Gateway Account,支付网关账户,
Payment Account,付款帐号,
Default Payment Request Message,默认的付款申请消息,
PMO-,PMO-,
Payment Order Type,付款订单类型,
Payment Order Reference,付款订单参考,
Bank Account Details,银行账户明细,
Payment Reconciliation,付款对账,
Receivable / Payable Account,应收/应付账款,
Bank / Cash Account,银行/现金账户,
From Invoice Date,从费用清单日期,
To Invoice Date,费用清单日期,
Minimum Invoice Amount,最小费用清单金额,
Maximum Invoice Amount,最高费用清单金额,
System will fetch all the entries if limit value is zero.,如果限制值为零，系统将获取所有条目。,
Get Unreconciled Entries,获取未对帐/结清分录,
Unreconciled Payment Details,未核销付款信息,
Invoice/Journal Entry Details,费用清单/手工凭证详细信息,
Payment Reconciliation Invoice,付款发票对账,
Invoice Number,费用清单号码,
Payment Reconciliation Payment,付款方式付款对账,
Reference Row,引用行,
Allocated amount,已核销金额,
Payment Request Type,付款申请类型,
Outward,向外,
Inward,向内的,
ACC-PRQ-.YYYY.-,ACC-PRQ-.YYYY.-,
Transaction Details,交易明细,
Amount in customer's currency,量客户的货币,
Is a Subscription,是订阅,
Transaction Currency,交易货币,
Subscription Plans,订阅计划,
SWIFT Number,SWIFT号码,
Recipient Message And Payment Details,收件人邮件和付款细节,
Make Sales Invoice,创建销售费用清单,
Mute Email,静音电子邮件,
payment_url,支付_链接,
Payment Gateway Details,支付网关信息,
Payment Schedule,付款工时单,
Invoice Portion,费用清单占比,
Payment Amount,付款金额,
Payment Term Name,付款条款名称,
Due Date Based On,到期日基于,
Day(s) after invoice date,费用清单日期后的天数,
Day(s) after the end of the invoice month,费用清单月份结束后的一天,
Month(s) after the end of the invoice month,费用清单月份结束后的月份,
Credit Days,信用期,
Credit Months,信贷月份,
Allocate Payment Based On Payment Terms,根据付款条件分配付款,
"If this checkbox is checked, paid amount will be splitted and allocated as per the amounts in payment schedule against each payment term",如果选中此复选框，则将根据每个付款期限的付款时间表中的金额来拆分和分配付款金额,
Payment Terms Template Detail,付款条款模板细节,
Closing Fiscal Year,结算财年,
Closing Account Head,结算科目,
"The account head under Liability or Equity, in which Profit/Loss will be booked",负债或权益下的科目，其中利润/亏损将被黄牌警告,
POS Customer Group,销售终端客户群,
POS Field,POS场,
POS Item Group,销售终端物料组,
Company Address,公司地址,
Update Stock,更新库存,
Ignore Pricing Rule,忽略定价规则,
Applicable for Users,适用于用户,
Sales Invoice Payment,销售发票付款,
Item Groups,物料组,
Only show Items from these Item Groups,仅显示这些项目组中的项目,
Customer Groups,客户群,
Only show Customer of these Customer Groups,仅显示这些客户组的客户,
Write Off Account,销帐科目,
Write Off Cost Center,销帐成本中心,
Account for Change Amount,零钱科目,
Taxes and Charges,税/费,
Apply Discount On,申请折扣,
POS Profile User,POS配置文件用户,
Apply On,应用于,
Price or Product Discount,价格或产品折扣,
Apply Rule On Item Code,在物品代码上应用规则,
Apply Rule On Item Group,在项目组上应用规则,
Apply Rule On Brand,在品牌上应用规则,
Mixed Conditions,混合条件,
Conditions will be applied on all the selected items combined. ,条件将适用于所有选定项目的组合。,
Is Cumulative,是累积的,
Coupon Code Based,基于优惠券代码,
Discount on Other Item,其他物品的折扣,
Apply Rule On Other,在其他方面适用规则,
Party Information,党的信息,
Quantity and Amount,数量和金额,
Min Qty,最小数量,
Max Qty,最大数量,
Min Amt,Min Amt,
Max Amt,Max Amt,
Period Settings,期间设置,
Margin,利润,
Margin Type,保证金类型,
Margin Rate or Amount,保证金税率或税额,
Price Discount Scheme,价格折扣计划,
Rate or Discount,价格或折扣,
Discount Percentage,折扣百分比,
Discount Amount,折扣金额,
For Price List,价格清单,
Product Discount Scheme,产品折扣计划,
Same Item,相同的项目,
Free Item,免费物品,
Threshold for Suggestion,建议的门槛,
System will notify to increase or decrease quantity or amount ,系统将通知增加或减少数量或金额,
"Higher the number, higher the priority",数字越大，优先级越高,
Apply Multiple Pricing Rules,应用多个定价规则,
Apply Discount on Rate,应用折扣率,
Validate Applied Rule,验证应用规则,
Rule Description,规则说明,
Pricing Rule Help,定价规则说明,
Promotional Scheme Id,促销计划ID,
Promotional Scheme,促销计划,
Pricing Rule Brand,定价规则品牌,
Pricing Rule Detail,定价规则细节,
Child Docname,儿童医生名称,
Rule Applied,适用规则,
Pricing Rule Item Code,定价规则项目代码,
Pricing Rule Item Group,定价规则项目组,
Price Discount Slabs,价格折扣板,
Promotional Scheme Price Discount,促销计划价格折扣,
Product Discount Slabs,产品折扣板,
Promotional Scheme Product Discount,促销计划产品折扣,
Min Amount,最低金额,
Max Amount,最大金额,
Discount Type,折扣类型,
ACC-PINV-.YYYY.-,ACC-PINV-.YYYY.-,
Tax Withholding Category,预扣税类别,
Edit Posting Date and Time,修改记帐日期与时间,
Is Paid,已付款？,
Is Return (Debit Note),是退货（借记卡）,
Apply Tax Withholding Amount,申请预扣税金额,
Accounting Dimensions ,会计维度,
Supplier Invoice Details,供应商费用清单信息,
Supplier Invoice Date,供应商费用清单日期,
Return Against Purchase Invoice,基于采购费用清单退货,
Select Supplier Address,选择供应商地址,
Contact Person,联络人,
Select Shipping Address,选择销售出货地址,
Currency and Price List,货币和价格清单,
Price List Currency,价格清单货币,
Price List Exchange Rate,价格清单汇率,
Set Accepted Warehouse,设置接受的仓库,
Rejected Warehouse,拒收仓库,
Warehouse where you are maintaining stock of rejected items,拒收物料的仓库,
Raw Materials Supplied,已提供的原材料,
Supplier Warehouse,供应商仓库,
Pricing Rules,定价规则,
Supplied Items,供应的物料,
Total (Company Currency),总金额（公司货币）,
Net Total (Company Currency),总净金额（公司货币）,
Total Net Weight,总净重,
Shipping Rule,配送规则,
Purchase Taxes and Charges Template,进项税/费模板,
Purchase Taxes and Charges,购置税/费,
Tax Breakup,税收分解,
Taxes and Charges Calculation,税费计算,
Taxes and Charges Added (Company Currency),税/费已添加（公司货币）,
Taxes and Charges Deducted (Company Currency),已扣除税费（公司货币）,
Total Taxes and Charges (Company Currency),总税/费（公司货币）,
Taxes and Charges Added,已添加的税费,
Taxes and Charges Deducted,已扣除税费,
Total Taxes and Charges,总税/费,
Additional Discount,额外折扣,
Apply Additional Discount On,额外折扣基于,
Additional Discount Amount (Company Currency),额外折扣金额（公司货币）,
Additional Discount Percentage,额外折扣率,
Additional Discount Amount,额外折扣金额,
Grand Total (Company Currency),总计（公司货币）,
Rounding Adjustment (Company Currency),四舍五入调整（公司货币）,
Rounded Total (Company Currency),圆整后金额（公司货币）,
In Words (Company Currency),大写金额（公司货币）,
Rounding Adjustment,舍入调整,
In Words,大写金额,
Total Advance,总预收额,
Disable Rounded Total,禁用圆整后金额,
Cash/Bank Account,现金/银行科目,
Write Off Amount (Company Currency),销帐金额（公司货币）,
Set Advances and Allocate (FIFO),设置进度和分配（FIFO）,
Get Advances Paid,获取已付预付款,
Advances,进展,
Terms,条款,
Terms and Conditions1,条款和条件1,
Group same items,合并相同物料,
Print Language,打印语言,
"Once set, this invoice will be on hold till the set date",一旦设置，该费用清单将被保留至设定的日期,
Credit To,贷记,
Party Account Currency,往来单位科目币种,
Against Expense Account,针对的费用账目,
Inter Company Invoice Reference,公司之间费用清单参考,
Is Internal Supplier,是内部供应商,
Start date of current invoice's period,当前费用清单周期的起始日期,
End date of current invoice's period,当前费用清单周期的结束日期,
Update Auto Repeat Reference,更新自动重复参考,
Purchase Invoice Advance,采购费用清单预付,
Purchase Invoice Item,采购费用清单项,
Quantity and Rate,数量和价格,
Received Qty,收到数量,
Accepted Qty,接受数量,
Rejected Qty,拒收数量,
UOM Conversion Factor,计量单位换算系数,
Discount on Price List Rate (%),基于价格清单价格的折扣（％）,
Price List Rate (Company Currency),价格清单单价（公司货币）,
Rate ,单价,
Rate (Company Currency),单价（公司货币）,
Amount (Company Currency),金额（公司货币）,
Is Free Item,是免费物品,
Net Rate,净单价,
Net Rate (Company Currency),净单价（公司货币）,
Net Amount (Company Currency),净金额（公司货币）,
Item Tax Amount Included in Value,物品税金额包含在价值中,
Landed Cost Voucher Amount,到岸成本凭证金额,
Raw Materials Supplied Cost,已提供的原材料成本,
Accepted Warehouse,已确认的仓库,
Serial No,序列号,
Rejected Serial No,拒收序列号,
Expense Head,总支出,
Is Fixed Asset,是固定的资产,
Asset Location,资产位置,
Deferred Expense,递延费用,
Deferred Expense Account,递延费用帐户,
Service Stop Date,服务停止日期,
Enable Deferred Expense,启用延期费用,
Service Start Date,服务开始日期,
Service End Date,服务结束日期,
Allow Zero Valuation Rate,允许评估价为0,
Item Tax Rate,物料税率,
Tax detail table fetched from item master as a string and stored in this field.\nUsed for Taxes and Charges,从物料主数据中取得税项详细信息表并转换为字符串存入此字段内。用作税金和费用。,
Purchase Order Item,采购订单项,
Purchase Receipt Detail,采购收货明细,
Item Weight Details,物料重量,
Weight Per Unit,每单位重量,
Total Weight,总重量,
Weight UOM,重量计量单位,
Page Break,分页符,
Consider Tax or Charge for,用途,
Valuation and Total,库存评估价与总计,
Valuation,库存评估价,
Add or Deduct,添加或扣除,
Deduct,扣除,
On Previous Row Amount,基于前一行的金额,
On Previous Row Total,基于前一行的总计,
On Item Quantity,关于物品数量,
Reference Row #,参考行＃,
Is this Tax included in Basic Rate?,此税项是否包含在基本价格中？,
"If checked, the tax amount will be considered as already included in the Print Rate / Print Amount",如果勾选，税将被包括在打印的单价/总额内了。,
Account Head,帐号头,
Tax Amount After Discount Amount,税额折后金额,
Item Wise Tax Detail ,明智的税项明细,
"Standard tax template that can be applied to all Purchase Transactions. This template can contain list of tax heads and also other expense heads like ""Shipping"", ""Insurance"", ""Handling"" etc.\n\n#### Note\n\nThe tax rate you define here will be the standard tax rate for all **Items**. If there are **Items** that have different rates, they must be added in the **Item Tax** table in the **Item** master.\n\n#### Description of Columns\n\n1. Calculation Type: \n    - This can be on **Net Total** (that is the sum of basic amount).\n    - **On Previous Row Total / Amount** (for cumulative taxes or charges). If you select this option, the tax will be applied as a percentage of the previous row (in the tax table) amount or total.\n    - **Actual** (as mentioned).\n2. Account Head: The Account ledger under which this tax will be booked\n3. Cost Center: If the tax / charge is an income (like shipping) or expense it needs to be booked against a Cost Center.\n4. Description: Description of the tax (that will be printed in invoices / quotes).\n5. Rate: Tax rate.\n6. Amount: Tax amount.\n7. Total: Cumulative total to this point.\n8. Enter Row: If based on ""Previous Row Total"" you can select the row number which will be taken as a base for this calculation (default is the previous row).\n9. Consider Tax or Charge for: In this section you can specify if the tax / charge is only for valuation (not a part of total) or only for total (does not add value to the item) or for both.\n10. Add or Deduct: Whether you want to add or deduct the tax.",标准税项模板可应用到所有采购交易中。此模板可以包含多个税项及其他的费用项(例如“运费”，“保险费”，“处理费”等)。\n###需要注意的是，这里指定的是试用于相关单据中所有物料的标准税费率。如果某些物料的税费率与标准税费率不同，则您必须在“物料主数据”内的“物料税项”内添加。\n####列说明\n1. 计算类型：-“净总计”(即基本总额)；-“基于前一行的总计/金额”(用于累计税费率)，选择此项则税费将基于前一行的总计/金额按百分比计算；-“实际”(按实际输入)。\n2. 一级科目： 此税费对应的会计分类帐。\n3. 成本中心： 如果此税费为收益或支出，那么必须指定一个成本中心。\n4. 说明：税费项的说明，会被用于如发票/报价的打印中。\n5. 税率：税项的比率\n6. 金额：税项金额\n7. 总计：到这里为止的累计\n8. 输入行： 如果选择了“基于前一行的总计/金额”，你可以选择此税项基于的行数(默认为前一行)。\n9. 税费应用于：你可以在此部分指定此税费影响库存计价(即不再是总计的一部分)， 或者仅作为总计的一部分，而不单独附加到每个物料上，或两者。\n10. 添加或扣除： 添加还是扣除此税费。,
ACC-SINV-.YYYY.-,ACC-SINV-.YYYY.-,
Include Payment (POS),直接付款（POS订单）,
Offline POS Name,离线POS名称,
Is Return (Credit Note),是退货？（退款单）,
Return Against Sales Invoice,基于销售费用清单退货,
Update Billed Amount in Sales Order,更新销售订单中的结算金额,
Customer PO Details,客户PO详细信息,
Customer's Purchase Order,客户采购订单,
Customer's Purchase Order Date,客户的采购订单日期,
Customer Address,客户地址,
Shipping Address Name,销售出货地址,
Company Address Name,公司地址名称,
Rate at which Customer Currency is converted to customer's base currency,客户货币转换为客户的本币后的单价,
Rate at which Price list currency is converted to customer's base currency,价格清单货币转换成客户的本币后的单价,
Set Source Warehouse,设置源仓库（出货仓）,
Packing List,包装清单,
Packed Items,已打包物料,
Product Bundle Help,产品包帮助,
Time Sheet List,时间表列表,
Time Sheets,时间表,
Total Billing Amount,总结算金额,
Sales Taxes and Charges Template,销项税/费模板,
Sales Taxes and Charges,销售税费,
Loyalty Points Redemption,忠诚积分兑换,
Redeem Loyalty Points,兑换忠诚度积分,
Redemption Account,赎回账户,
Redemption Cost Center,赎回成本中心,
In Words will be visible once you save the Sales Invoice.,大写金额将在销售费用清单保存后显示。,
Allocate Advances Automatically (FIFO),自动分配进度（FIFO）,
Get Advances Received,获取已收预付款,
Base Change Amount (Company Currency),基地涨跌额（公司币种）,
Write Off Outstanding Amount,注销未付金额,
Terms and Conditions Details,条款和条件信息,
Is Internal Customer,是内部客户,
Is Discounted,打折,
Unpaid and Discounted,无偿和折扣,
Overdue and Discounted,逾期和折扣,
Accounting Details,会计细节,
Debit To,科目（应收帐款）,
Is Opening Entry,是否期初分录,
C-Form Applicable,C-表格适用,
Commission Rate (%),佣金率（％）,
Sales Team1,销售团队1,
Against Income Account,针对的收益账目,
Sales Invoice Advance,销售费用清单预付款,
Advance amount,预付款总额,
Sales Invoice Item,销售费用清单项目,
Customer's Item Code,客户的物料代码,
Brand Name,品牌名称,
Qty as per Stock UOM,按库存计量单位数量,
Discount and Margin,折扣与边际利润,
Rate With Margin,利率保证金,
Discount (%) on Price List Rate with Margin,价格上涨率与贴现率的折扣（％）,
Rate With Margin (Company Currency),利率保证金（公司货币）,
Delivered By Supplier,交付供应商,
Deferred Revenue,递延收入,
Deferred Revenue Account,递延收入科目,
Enable Deferred Revenue,启用延期收入,
Stock Details,库存详细信息,
Customer Warehouse (Optional),客户仓库（可选）,
Available Batch Qty at Warehouse,仓库中可用的批次数量,
Available Qty at Warehouse,库存可用数量,
Delivery Note Item,销售出货单项,
Base Amount (Company Currency),基本金额（公司币种）,
Sales Invoice Timesheet,销售费用清单工时单,
Time Sheet,时间表,
Billing Hours,计入账单的小时,
Timesheet Detail,时间表详细信息,
Tax Amount After Discount Amount (Company Currency),扣除折扣后税额（公司货币）,
Item Wise Tax Detail,物料税/费信息,
Parenttype,上级类型,
"Standard tax template that can be applied to all Sales Transactions. This template can contain list of tax heads and also other expense / income heads like ""Shipping"", ""Insurance"", ""Handling"" etc.\n\n#### Note\n\nThe tax rate you define here will be the standard tax rate for all **Items**. If there are **Items** that have different rates, they must be added in the **Item Tax** table in the **Item** master.\n\n#### Description of Columns\n\n1. Calculation Type: \n    - This can be on **Net Total** (that is the sum of basic amount).\n    - **On Previous Row Total / Amount** (for cumulative taxes or charges). If you select this option, the tax will be applied as a percentage of the previous row (in the tax table) amount or total.\n    - **Actual** (as mentioned).\n2. Account Head: The Account ledger under which this tax will be booked\n3. Cost Center: If the tax / charge is an income (like shipping) or expense it needs to be booked against a Cost Center.\n4. Description: Description of the tax (that will be printed in invoices / quotes).\n5. Rate: Tax rate.\n6. Amount: Tax amount.\n7. Total: Cumulative total to this point.\n8. Enter Row: If based on ""Previous Row Total"" you can select the row number which will be taken as a base for this calculation (default is the previous row).\n9. Is this Tax included in Basic Rate?: If you check this, it means that this tax will not be shown below the item table, but will be included in the Basic Rate in your main item table. This is useful where you want give a flat price (inclusive of all taxes) price to customers.",标准税项模板可应用到所有销售交易中。此模板可以包含多个税项及其他的开支/收益项(例如“运费”，“保险费”，“处理费”等)。\n###需要注意的是，这里指定的是适用于整张单据所有物料的标准税费率。如果某些物料的税费率与这里指定的标准税费率，则您必须在“物料主数据”内的“物料税项”内添加。\n####列说明\n1. 计算类型：-“净总计”(即基本总额)；-“基于前一行的总计/金额”(用于累计税费率)，选择此项则税费将基于前一行的总计/金额按百分比计算；-“实际”(按实际输入)。\n2. 科目： 此税费对应的会计分类帐。\n3. 成本中心： 如果此税费为收益或支出，那么必须指定一个成本中心。\n4. 说明：税费项的说明，会被用于如发票/报价的打印中。\n5. 税率：税项的比率\n6. 金额：税项金额\n7. 总计：到这里为止的累计\n8. 输入行： 如果选择了“基于前一行的总计/金额”，你可以选择此税项基于的行数(默认为前一行)。\n9. 价内税？：勾选此项则意味着此税项不会再单独出现在销售订单物料项清单下方，此税费率会体现再物料售价中。如果您需要向客户提供打包价而不是详细价格(即包含所有税费)，此选项就会很有用。,
* Will be calculated in the transaction.,*将被计算在该交易内。,
From No,来自No,
To No,至No,
Is Company,是公司？,
Current State,当前状态,
Purchased,已采购,
From Shareholder,来自股东,
From Folio No,来自对开本No.,
To Shareholder,给股东,
To Folio No,对开本No,
Equity/Liability Account,权益/负债科目,
Asset Account,资产科目,
(including),（包含）,
ACC-SH-.YYYY.-,ACC-SH-.YYYY.-,
Folio no.,对开本页码.,
Address and Contacts,地址和联系方式,
Contact List,联系人列表,
Hidden list maintaining the list of contacts linked to Shareholder,保存链接到股东的联系人列表的隐藏的列表,
Specify conditions to calculate shipping amount,指定用来计算运费金额的条件,
Shipping Rule Label,配送规则标签,
example: Next Day Shipping,例如：次日发货,
Shipping Rule Type,运输规则类型,
Shipping Account,销售出货账户,
Calculate Based On,基于...的计算,
Fixed,固定,
Net Weight,净重,
Shipping Amount,发货金额,
Shipping Rule Conditions,配送规则条件,
Restrict to Countries,限制到国家,
Valid for Countries,有效的国家,
Shipping Rule Condition,配送规则条件,
A condition for a Shipping Rule,发货规则的一个条件,
From Value,起始值,
To Value,To值,
Shipping Rule Country,航运规则国家,
Subscription Period,订阅期,
Subscription Start Date,订阅开始日期,
Cancelation Date,取消日期,
Trial Period Start Date,试用期开始日期,
Trial Period End Date,试用期结束日期,
Current Invoice Start Date,当前费用清单开始日期,
Current Invoice End Date,当前费用清单结束日期,
Days Until Due,天至期限,
Number of days that the subscriber has to pay invoices generated by this subscription,用户必须支付此订阅生成的费用清单的天数,
Cancel At End Of Period,在期末取消,
Generate Invoice At Beginning Of Period,在期初生成费用清单,
Plans,计划,
Discounts,折扣,
Additional DIscount Percentage,额外折扣百分比,
Additional DIscount Amount,额外的折扣金额,
Subscription Invoice,订阅费用清单,
Subscription Plan,订阅计划,
Cost,成本,
Billing Interval,计费间隔,
Billing Interval Count,计费间隔计数,
"Number of intervals for the interval field e.g if Interval is 'Days' and Billing Interval Count is 3, invoices will be generated every 3 days",间隔字段的间隔数，例如，如果间隔为'天数'并且计费间隔计数为3，则会每3天生成一次费用清单,
Payment Plan,付款计划,
Subscription Plan Detail,订阅计划信息,
Plan,计划,
Subscription Settings,订阅设置,
Grace Period,宽限期,
Number of days after invoice date has elapsed before canceling subscription or marking subscription as unpaid,在取消循环凭证或将循环凭证标记为未付之前，费用清单日期之后的天数已过,
Prorate,按比例分配,
Tax Rule,税务规则,
Tax Type,税收类型,
Use for Shopping Cart,使用的购物车,
Billing City,结算城市,
Billing County,开票县,
Billing State,计费状态,
Billing Zipcode,计费邮编,
Billing Country,结算国家,
Shipping City,起运市,
Shipping County,起运县,
Shipping State,运输状态,
Shipping Zipcode,运输邮编,
Shipping Country,起运国家,
Tax Withholding Account,代扣税款科目,
Tax Withholding Rates,预扣税率,
Rates,价格,
Tax Withholding Rate,税收预扣税率,
Single Transaction Threshold,单一交易阈值,
Cumulative Transaction Threshold,累积交易阈值,
Agriculture Analysis Criteria,农业分析标准,
Linked Doctype,链接的文档类型,
Water Analysis,水分析,
Soil Analysis,土壤分析,
Plant Analysis,植物分析,
Fertilizer,肥料,
Soil Texture,土壤纹理,
Weather,天气,
Agriculture Manager,农业经理,
Agriculture User,农业用户,
Agriculture Task,农业任务,
Task Name,任务名称,
Start Day,开始日,
End Day,结束日期,
Holiday Management,度假管理,
Ignore holidays,忽略假期,
Previous Business Day,前一个营业日,
Next Business Day,下一个营业日,
Urgent,加急,
Crop Name,作物名称,
Scientific Name,科学名称,
"You can define all the tasks which need to carried out for this crop here. The day field is used to mention the day on which the task needs to be carried out, 1 being the 1st day, etc.. ",你可以在这里定义所有需要进行的作业。日场是用来提及任务需要执行的日子，1日是第一天等。,
Crop Spacing,作物间距,
Crop Spacing UOM,裁剪间隔UOM,
Row Spacing,行间距,
Row Spacing UOM,行距UOM,
Perennial,多年生,
Biennial,双年展,
Planting UOM,种植UOM,
Planting Area,种植面积,
Yield UOM,产量UOM,
Materials Required,所需材料,
Produced Items,生产物料,
Produce,生产,
Byproducts,副产品,
Linked Location,链接位置,
A link to all the Locations in which the Crop is growing,指向作物生长的所有位置的链接,
This will be day 1 of the crop cycle,这将是作物周期的第一天,
ISO 8601 standard,ISO 8601标准,
Cycle Type,循环类型,
Less than a year,不到一年,
The minimum length between each plant in the field for optimum growth,每个工厂之间的最小长度为最佳的增长,
The minimum distance between rows of plants for optimum growth,植株之间的最小距离，以获得最佳生长,
Detected Diseases,检测到的疾病,
List of diseases detected on the field. When selected it'll automatically add a list of tasks to deal with the disease ,在现场检测到的疾病清单。当选择它会自动添加一个任务清单处理疾病,
Detected Disease,检测到的疾病,
LInked Analysis,链接的分析,
Disease,疾病,
Tasks Created,创建的任务,
Common Name,通用名称,
Treatment Task,治疗任务,
Treatment Period,治疗期,
Fertilizer Name,肥料名称,
Density (if liquid),密度（如果是液体）,
Fertilizer Contents,肥料含量,
Fertilizer Content,肥料含量,
Linked Plant Analysis,链接的工厂分析,
Linked Soil Analysis,连接的土壤分析,
Linked Soil Texture,连接的土壤纹理,
Collection Datetime,收集日期时间,
Laboratory Testing Datetime,实验室测试日期时间,
Result Datetime,结果日期时间,
Plant Analysis Criterias,植物分析标准,
Plant Analysis Criteria,植物分析标准,
Minimum Permissible Value,最小允许值,
Maximum Permissible Value,最大允许值,
Ca/K,钙/钾,
Ca/Mg,钙/镁,
Mg/K,镁/ K,
(Ca+Mg)/K,（钙+镁）/ K,
Ca/(K+Ca+Mg),钙 /（钾 +钙+镁）,
Soil Analysis Criterias,土壤分析标准,
Soil Analysis Criteria,土壤分析标准,
Soil Type,土壤类型,
Loamy Sand,泥沙,
Sandy Loam,桑迪Loam,
Loam,壤土,
Silt Loam,淤泥粘土,
Sandy Clay Loam,桑迪粘土壤土,
Clay Loam,粘土沃土,
Silty Clay Loam,泥土粘土,
Sandy Clay,桑迪粘土,
Silty Clay,粉泥,
Clay Composition (%),粘土成分（％）,
Sand Composition (%),沙成分（％）,
Silt Composition (%),粉尘成分（％）,
Ternary Plot,三元剧情,
Soil Texture Criteria,土壤质地标准,
Type of Sample,样品类型,
Container,容器,
Origin,起源,
Collection Temperature ,收集温度,
Storage Temperature,储存温度,
Appearance,外观,
Person Responsible,负责人,
Water Analysis Criteria,水分析标准,
Weather Parameter,天气参数,
ACC-ASS-.YYYY.-,ACC-ASS-.YYYY.-,
Asset Owner,资产所有者,
Asset Owner Company,资产所有者公司,
Custodian,保管人,
Disposal Date,处置日期,
Journal Entry for Scrap,手工凭证报废,
Available-for-use Date,可供使用的日期,
Calculate Depreciation,计算折旧,
Allow Monthly Depreciation,允许每月折旧,
Number of Depreciations Booked,预订折旧数,
Finance Books,账簿,
Straight Line,直线,
Double Declining Balance,双倍余额递减,
Manual,手册,
Value After Depreciation,折旧后值,
Total Number of Depreciations,折旧总数,
Frequency of Depreciation (Months),折旧率（月）,
Next Depreciation Date,接下来折旧日期,
Depreciation Schedule,折旧计划,
Depreciation Schedules,折旧计划,
Insurance details,保险详情,
Policy number,保单号码,
Insurer,保险公司,
Insured value,保险价值,
Insurance Start Date,保险开始日期,
Insurance End Date,保险终止日期,
Comprehensive Insurance,综合保险,
Maintenance Required,需要维护,
Check if Asset requires Preventive Maintenance or Calibration,检查资产是否需要预防性维护或校准,
Booked Fixed Asset,预订的固定资产,
Purchase Receipt Amount,采购收货单金额,
Default Finance Book,默认账簿,
Quality Manager,质量经理,
Asset Category Name,资产类别名称,
Depreciation Options,折旧选项,
Enable Capital Work in Progress Accounting,启用资本在建会计,
Finance Book Detail,账簿信息,
Asset Category Account,资产类别的科目,
Fixed Asset Account,固定资产科目,
Accumulated Depreciation Account,累计折旧科目,
Depreciation Expense Account,折旧费用科目,
Capital Work In Progress Account,在途资本科目,
Asset Finance Book,资产资金账簿,
Written Down Value,账面净值,
Expected Value After Useful Life,期望值使用寿命结束后,
Rate of Depreciation,折旧率,
In Percentage,百分比,
Maintenance Team,维修队,
Maintenance Manager Name,维护经理姓名,
Maintenance Tasks,维护任务,
Manufacturing User,生产用户,
Asset Maintenance Log,资产维护日志,
ACC-AML-.YYYY.-,ACC-AML-.YYYY.-,
Maintenance Type,维护类型,
Maintenance Status,维护状态,
Planned,计划,
Has Certificate ,有证书,
Certificate,证书,
Actions performed,已执行的操作,
Asset Maintenance Task,资产维护任务,
Maintenance Task,维护任务,
Preventive Maintenance,预防性的维护,
Calibration,校准,
2 Yearly,每年2次,
Certificate Required,证书要求,
Assign to Name,分配给名称,
Next Due Date,下一个到期日,
Last Completion Date,最后完成日期,
Asset Maintenance Team,资产维护团队,
Maintenance Team Name,维护组名称,
Maintenance Team Members,维护团队成员,
Purpose,目的,
Stock Manager,库存管理,
Asset Movement Item,资产变动项目,
Source Location,来源地点,
From Employee,来自员工,
Target Location,目标位置,
To Employee,给员工,
Asset Repair,资产修复,
ACC-ASR-.YYYY.-,ACC-ASR-.YYYY.-,
Failure Date,失败日期,
Assign To Name,分配到名称,
Repair Status,维修状态,
Error Description,错误说明,
Downtime,停工期,
Repair Cost,修理费用,
Manufacturing Manager,生产经理,
Current Asset Value,流动资产价值,
New Asset Value,新资产价值,
Make Depreciation Entry,创建计算折旧凭证,
Finance Book Id,账簿ID,
Location Name,地点名称,
Parent Location,上级位置,
Is Container,是容器,
Check if it is a hydroponic unit,检查它是否是水培单位,
Location Details,位置详情,
Latitude,纬度,
Longitude,经度,
Area,区,
Area UOM,区基础单位,
Tree Details,树详细信息,
Maintenance Team Member,维护团队成员,
Team Member,团队成员,
Maintenance Role,维护角色,
Buying Settings,采购设置,
Settings for Buying Module,采购模块的设置,
Supplier Naming By,供应商命名方式,
Default Supplier Group,默认供应商组,
Default Buying Price List,默认采购价格清单,
Backflush Raw Materials of Subcontract Based On,基于CRM的分包合同反向原材料,
Material Transferred for Subcontract,为转包合同材料转移,
Over Transfer Allowance (%),超过转移津贴（％）,
Percentage you are allowed to transfer more against the quantity ordered. For example: If you have ordered 100 units. and your Allowance is 10% then you are allowed to transfer 110 units.,允许您根据订购数量转移更多的百分比。例如：如果您订购了100个单位。你的津贴是10％，那么你可以转让110个单位。,
PUR-ORD-.YYYY.-,PUR-ORD-.YYYY.-,
Get Items from Open Material Requests,从未完成物料申请获取物料,
Fetch items based on Default Supplier.,根据默认供应商获取项目。,
Required By,必选,
Order Confirmation No,订单确认号,
Order Confirmation Date,订单确认日期,
Customer Mobile No,客户手机号码,
Customer Contact Email,客户联系电子邮件,
Set Target Warehouse,设置目标仓库（收货仓）,
Sets 'Warehouse' in each row of the Items table.,在“项目”表的每一行中设置“仓库”。,
Supply Raw Materials,供应原材料,
Purchase Order Pricing Rule,采购订单定价规则,
Set Reserve Warehouse,设置储备仓库,
In Words will be visible once you save the Purchase Order.,大写金额将在采购订单保存后显示。,
Advance Paid,已支付的预付款,
Tracking,追踪,
% Billed,% 已记账,
% Received,％已收货,
Ref SQ,参考SQ,
Inter Company Order Reference,公司间订单参考,
Supplier Part Number,供应商零件编号,
Billed Amt,已开票金额,
Warehouse and Reference,仓库及参考,
To be delivered to customer,将出货给客户,
Material Request Item,材料申请项目,
Supplier Quotation Item,供应商报价物料,
Against Blanket Order,反对一揽子订单,
Blanket Order,总括订单,
Blanket Order Rate,总括订单单价,
Returned Qty,退货数量,
Purchase Order Item Supplied,采购订单外发物料,
BOM Detail No,物料清单信息编号,
Stock Uom,库存计量单位,
Raw Material Item Code,原材料物料编号,
Supplied Qty,供应的数量,
Purchase Receipt Item Supplied,外包订单外发物料,
Current Stock,当前库存,
PUR-RFQ-.YYYY.-,PUR-RFQ-.YYYY.-,
For individual supplier,单个供应商,
Link to Material Requests,链接到物料请求,
Message for Supplier,消息供应商,
Request for Quotation Item,询价项目,
Required Date,需求日期,
Request for Quotation Supplier,询价供应商,
Send Email,发送电子邮件,
Quote Status,报价状态,
Download PDF,下载PDF,
Supplier of Goods or Services.,提供商品或服务的供应商。,
Name and Type,名称和类型,
SUP-.YYYY.-,SUP-.YYYY.-,
Default Bank Account,默认银行科目,
Is Transporter,是承运商,
Represents Company,代表公司,
Supplier Type,供应商类型,
Allow Purchase Invoice Creation Without Purchase Order,允许创建没有采购订单的采购发票,
Allow Purchase Invoice Creation Without Purchase Receipt,允许在没有收货的情况下创建采购发票,
Warn RFQs,警告RFQs,
Warn POs,警告PO,
Prevent RFQs,防止RFQ,
Prevent POs,防止PO,
Billing Currency,结算货币,
Default Payment Terms Template,默认付款条款模板,
Block Supplier,块供应商,
Hold Type,暂缓处理类型,
Leave blank if the Supplier is blocked indefinitely,如果供应商被无限期封锁，请留空,
Default Payable Accounts,默认应付账户(多个),
Mention if non-standard payable account,如使用非标准应付科目，应提及,
Default Tax Withholding Config,预设税款预扣配置,
Supplier Details,供应商信息,
Statutory info and other general information about your Supplier,你的供应商的注册信息和其他一般信息,
PUR-SQTN-.YYYY.-,PUR-SQTN-.YYYY.-,
Supplier Address,供应商地址,
Link to material requests,链接到物料申请,
Rounding Adjustment (Company Currency,四舍五入调整（公司货币）,
Auto Repeat Section,自动重复部分,
Is Subcontracted,是否外包,
Lead Time in days,交期（天）,
Supplier Score,供应商分数,
Indicator Color,指示灯颜色,
Evaluation Period,评估期,
Per Week,每个星期,
Per Month,每月,
Per Year,每年,
Scoring Setup,得分设置,
Weighting Function,加权函数,
"Scorecard variables can be used, as well as:\n{total_score} (the total score from that period),\n{period_number} (the number of periods to present day)\n",可以使用记分卡变量，以及：{total_score}（该期间的总分数），{period_number}（到当前时间段的数量）,
Scoring Standings,得分排名,
Criteria Setup,条件设置,
Load All Criteria,加载所有标准,
Scoring Criteria,评分标准,
Scorecard Actions,记分卡操作,
Warn for new Request for Quotations,警告新的报价请求,
Warn for new Purchase Orders,警告新的采购订单,
Notify Supplier,通知供应商,
Notify Employee,通知员工,
Supplier Scorecard Criteria,供应商记分卡标准,
Criteria Name,标准名称,
Max Score,最高分数,
Criteria Formula,标准配方,
Criteria Weight,标准重量,
Supplier Scorecard Period,供应商记分卡期,
PU-SSP-.YYYY.-,PU-SSP-.YYYY.-,
Period Score,期间得分,
Calculations,计算,
Criteria,标准,
Variables,变量,
Supplier Scorecard Setup,供应商记分卡设置,
Supplier Scorecard Scoring Criteria,供应商记分卡评分标准,
Score,得分了,
Supplier Scorecard Scoring Standing,供应商记分卡,
Standing Name,常务名称,
Purple,紫色,
Yellow,黄色,
Orange,橙子,
Min Grade,最小成绩,
Max Grade,最高等级,
Warn Purchase Orders,警告采购订单,
Prevent Purchase Orders,防止采购订单,
Employee ,员工,
Supplier Scorecard Scoring Variable,供应商记分卡评分变量,
Variable Name,变量名,
Parameter Name,参数名称,
Supplier Scorecard Standing,供应商记分卡当前评分,
Notify Other,通知其他,
Supplier Scorecard Variable,供应商记分卡变数,
Call Log,通话记录,
Received By,收到的,
Caller Information,来电者信息,
Contact Name,联系人姓名,
Lead ,铅,
Lead Name,线索姓名,
Ringing,铃声,
Missed,错过,
Call Duration in seconds,呼叫持续时间（秒）,
Recording URL,录制网址,
Communication Medium,通信介质,
Communication Medium Type,通信媒体类型,
Voice,语音,
Catch All,抓住一切,
"If there is no assigned timeslot, then communication will be handled by this group",如果没有分配的时间段，则该组将处理通信,
Timeslots,时隙,
Communication Medium Timeslot,通信媒体时隙,
Employee Group,员工组,
Appointment,约定,
Scheduled Time,计划的时间,
Unverified,未验证,
Customer Details,客户详细信息,
Phone Number,电话号码,
Skype ID,Skype帐号,
Linked Documents,链接文件,
Appointment With,预约,
Calendar Event,日历活动,
Appointment Booking Settings,预约预约设置,
Enable Appointment Scheduling,启用约会计划,
Agent Details,代理商详细信息,
Availability Of Slots,插槽的可用性,
Number of Concurrent Appointments,并发预约数,
Agents,代理商,
Appointment Details,预约详情,
Appointment Duration (In Minutes),预约时间（以分钟为单位）,
Notify Via Email,通过电子邮件通知,
Notify customer and agent via email on the day of the appointment.,在约会当天通过电子邮件通知客户和代理商。,
Number of days appointments can be booked in advance,可以提前预约的天数,
Success Settings,成功设定,
Success Redirect URL,成功重定向网址,
"Leave blank for home.\nThis is relative to site URL, for example ""about"" will redirect to ""https://yoursitename.com/about""",留空在家。这是相对于网站URL的，例如“ about”将重定向到“ https://yoursitename.com/about”,
Appointment Booking Slots,预约订位,
Day Of Week,星期几,
From Time ,起始时间,
Campaign Email Schedule,Campaign电子邮件计划,
Send After (days),发送后（天）,
Signed,签,
Party User,往来单位用户,
Unsigned,无符号,
Fulfilment Status,履行状态,
N/A,N / A,
Unfulfilled,未完成的,
Partially Fulfilled,部分实现,
Fulfilled,达到,
Lapsed,间隔,
Contract Period,合同期,
Signee Details,签名信息,
Signee,签署人,
Signed On,签名,
Contract Details,合同细节,
Contract Template,合同模板,
Contract Terms,合同条款,
Fulfilment Details,履行细节,
Requires Fulfilment,需要履行,
Fulfilment Deadline,履行截止日期,
Fulfilment Terms,履行条款,
Contract Fulfilment Checklist,合同履行清单,
Requirement,需求,
Contract Terms and Conditions,合同条款和条件,
Fulfilment Terms and Conditions,履行条款和条件,
Contract Template Fulfilment Terms,合同模板履行条款,
Email Campaign,电邮广告系列,
Email Campaign For ,电子邮件活动,
Lead is an Organization,商机是一个组织,
CRM-LEAD-.YYYY.-,CRM-LEAD-.YYYY.-,
Person Name,姓名,
Lost Quotation,遗失的报价,
Interested,当事的,
Converted,已转换,
Do Not Contact,请勿打扰,
From Customer,源客户,
Campaign Name,活动名称,
Follow Up,跟进,
Next Contact By,下次联络人,
Next Contact Date,下次联络日期,
Ends On,结束于,
Address & Contact,地址及联系方式,
Mobile No.,手机号码,
Lead Type,线索类型,
Channel Partner,渠道合作伙伴,
Consultant,顾问,
Market Segment,市场分类,
Industry,行业,
Request Type,需求类型,
Product Enquiry,产品查询,
Request for Information,索取资料,
Suggestions,建议,
Blog Subscriber,博客订阅者,
LinkedIn Settings,LinkedIn设置,
Company ID,公司编号,
OAuth Credentials,OAuth凭证,
Consumer Key,消费者密钥,
Consumer Secret,消费者的秘密,
User Details,使用者详细资料,
Person URN,人URN,
Session Status,会话状态,
Lost Reason Detail,丢失的原因细节,
Opportunity Lost Reason,机会失去理智,
Potential Sales Deal,潜在的销售交易,
CRM-OPP-.YYYY.-,CRM-OPP-.YYYY.-,
Opportunity From,机会来源,
Customer / Lead Name,客户/潜在客户名称,
Opportunity Type,机会类型,
Converted By,转换依据,
Sales Stage,销售阶段,
Lost Reason,遗失的原因,
Expected Closing Date,预计截止日期,
To Discuss,待讨论,
With Items,物料,
Probability (%),概率（％）,
Contact Info,联系方式,
Customer / Lead Address,客户/潜在客户地址,
Contact Mobile No,联系人手机号码,
Enter name of campaign if source of enquiry is campaign,如果询价的来源是活动的话请输入活动名称。,
Opportunity Date,日期机会,
Opportunity Item,机会项（行）,
Basic Rate,标准售价,
Stage Name,艺名,
Social Media Post,社交媒体帖子,
Post Status,发布状态,
Posted,发表于,
Share On,分享,
Twitter,推特,
LinkedIn,领英,
Twitter Post Id,Twitter的帖子ID,
LinkedIn Post Id,LinkedIn邮政编号,
Tweet,鸣叫,
Twitter Settings,Twitter设置,
API Secret Key,API密钥,
Term Name,术语名称,
Term Start Date,条款起始日期,
Term End Date,合同结束日期,
Academics User,学术界用户,
Academic Year Name,学年名称,
Article,文章,
LMS User,LMS用户,
Assessment Criteria Group,评估标准组,
Assessment Group Name,评估小组名称,
Parent Assessment Group,上级评估小组,
Assessment Name,评估名称,
Grading Scale,分级量表,
Examiner,检查员,
Examiner Name,考官名称,
Supervisor,监工,
Supervisor Name,主管名称,
Evaluate,评估,
Maximum Assessment Score,最大考核评分,
Assessment Plan Criteria,评估计划标准,
Maximum Score,最大比分,
Grade,职级,
Assessment Result Detail,评价结果详细,
Assessment Result Tool,评价结果工具,
Result HTML,结果HTML,
Content Activity,内容活动,
Last Activity ,上次活动,
Content Question,内容问题,
Question Link,问题链接,
Course Name,课程名,
Topics,话题,
Hero Image,英雄形象,
Default Grading Scale,默认等级规模,
Education Manager,教育经理,
Course Activity,课程活动,
Course Enrollment,课程报名,
Activity Date,活动日期,
Course Assessment Criteria,课程评价标准,
Weightage,权重,
Course Content,课程内容,
Quiz,测验,
Program Enrollment,招生计划,
Enrollment Date,报名日期,
Instructor Name,导师姓名,
EDU-CSH-.YYYY.-,EDU-CSH-.YYYY.-,
Course Scheduling Tool,排课工具,
Course Start Date,课程开始日期,
To TIme,要时间,
Course End Date,课程结束日期,
Course Topic,课程主题,
Topic Name,主题名称,
Education Settings,教育设置,
Current Academic Year,当前学年,
Current Academic Term,当前学术期限,
Attendance Freeze Date,出勤冻结日期,
Validate Batch for Students in Student Group,验证学生组学生的批次,
"For Batch based Student Group, the Student Batch will be validated for every Student from the Program Enrollment.",对于基于批次的学生组，学生批次将由课程注册中的每位学生进行验证。,
Validate Enrolled Course for Students in Student Group,验证学生组学生入学课程,
"For Course based Student Group, the Course will be validated for every Student from the enrolled Courses in Program Enrollment.",对于基于课程的学生组，课程将从入学课程中的每个学生确认。,
Make Academic Term Mandatory,使学术期限为强制项,
"If enabled, field Academic Term will be Mandatory in Program Enrollment Tool.",如果启用，则在学期注册工具中，字段学术期限将是强制性的。,
Skip User creation for new Student,跳过为新学生创建的用户,
"By default, a new User is created for every new Student. If enabled, no new User will be created when a new Student is created.",默认情况下，为每个新学生创建一个新用户。如果启用，则在创建新学生时将不会创建新用户。,
Instructor Records to be created by,导师记录由,
Employee Number,员工编号,
Fee Category,收费类别,
Fee Component,收费组件,
Fees Category,费用类别,
Fee Schedule,收费表,
Fee Structure,费用结构,
EDU-FSH-.YYYY.-,EDU-FSH-.YYYY.-,
Fee Creation Status,费用创建状态,
In Process,进行中,
Send Payment Request Email,发送付款申请电子邮件,
Student Category,学生组,
Fee Breakup for each student,每名学生的费用细分,
Total Amount per Student,每个学生的总金额,
Institution,机构,
Fee Schedule Program,费用计划计划,
Student Batch,学生批次,
Total Students,学生总数,
Fee Schedule Student Group,费用计划学生组,
EDU-FST-.YYYY.-,EDU-FST-.YYYY.-,
EDU-FEE-.YYYY.-,EDU-收费.YYYY.-,
Include Payment,包括付款,
Send Payment Request,发送付款申请,
Student Details,学生细节,
Student Email,学生电子邮件,
Grading Scale Name,分级标准名称,
Grading Scale Intervals,分级刻度间隔,
Intervals,间隔,
Grading Scale Interval,分级分度值,
Grade Code,等级代码,
Threshold,阈值,
Grade Description,等级说明,
Guardian,监护人,
Guardian Name,监护人姓名,
Alternate Number,备用号码,
Occupation,职业,
Work Address,工作地址,
Guardian Of ,...的监护人,
Students,学生们,
Guardian Interests,监护人利益,
Guardian Interest,监护人利益,
Interest,利息,
Guardian Student,学生监护人,
EDU-INS-.YYYY.-,EDU-INS-.YYYY.-,
Instructor Log,讲师日志,
Other details,其他详细信息,
Option,选项,
Is Correct,是正确的,
Program Name,程序名称,
Program Abbreviation,计划缩写,
Courses,课程,
Is Published,已发布,
Allow Self Enroll,允许自我注册,
Is Featured,精选,
Intro Video,介绍视频,
Program Course,课程计划,
School House,学校议院,
Boarding Student,寄宿学生,
Check this if the Student is residing at the Institute's Hostel.,如果学生住在学院的旅馆，勾选此项。,
Walking,步行,
Institute's Bus,学院的巴士,
Self-Driving Vehicle,自驾车,
Pick/Drop by Guardian,由守护者选择,
Enrolled courses,入学课程,
Program Enrollment Course,课程注册课程,
Program Enrollment Fee,计划注册费,
Program Enrollment Tool,计划注册工具,
Get Students From,从... 选择学生,
Student Applicant,学生申请,
Get Students,让学生,
Enrollment Details,注册信息,
New Program,新程序,
New Student Batch,新学生批次,
Enroll Students,招生,
New Academic Year,新学年,
New Academic Term,新学期,
Program Enrollment Tool Student,计划注册学生工具,
Student Batch Name,学生批次名,
Program Fee,课程费用,
Question,题,
Single Correct Answer,单一正确答案,
Multiple Correct Answer,多个正确的答案,
Quiz Configuration,测验配置,
Passing Score,合格分数,
Score out of 100,得分100分,
Max Attempts,Max尝试,
Enter 0 to waive limit,输入0以放弃限制,
Grading Basis,评分基础,
Latest Highest Score,最新的最高分,
Latest Attempt,最新尝试,
Quiz Activity,测验活动,
Enrollment,注册,
Pass,通过,
Quiz Question,测验问题,
Quiz Result,测验结果,
Selected Option,选择的选项,
Correct,正确,
Wrong,错误,
Room Name,房间名称,
Room Number,房间号,
Seating Capacity,座位数,
House Name,房名,
EDU-STU-.YYYY.-,EDU-STU-.YYYY.-,
Student Mobile Number,学生手机号码,
Blood Group,血型,
A+,A +,
A-,A-,
B+,B +,
B-,B-,
O+,O +,
O-,O-,
AB+,AB +,
AB-,AB-,
Nationality,国籍,
Home Address,主页地址,
Guardian Details,监护人详细信息,
Guardians,守护者,
Sibling Details,兄弟姐妹信息,
Siblings,兄弟姐妹,
Exit,离职,
Date of Leaving,离开日期,
Leaving Certificate Number,毕业证书号码,
Reason For Leaving,离开的原因,
Student Admission,学生入学,
Admission Start Date,准入开始日期,
Admission End Date,准入结束日期,
Eligibility and Details,资格和细节,
Student Admission Program,学生入学计划,
Minimum Age,最低年龄,
Maximum Age,最大年龄,
Application Fee,报名费,
Naming Series (for Student Applicant),名录（面向学生申请人）,
LMS Only,仅限LMS,
EDU-APP-.YYYY.-,EDU-APP-.YYYY.-,
Application Date,申请日期,
Student Attendance Tool,学生考勤工具,
Group Based On,分组依据,
Students HTML,学生HTML,
Group Based on,基于组,
Student Group Name,学生组名称,
Max Strength,最大力量,
Set 0 for no limit,为不限制设为0,
Instructors,教师,
Student Group Creation Tool,学生组创建工具,
Leave blank if you make students groups per year,如果您每年制作学生团体，请留空,
Get Courses,获取课程,
Separate course based Group for every Batch,为每个批次分离基于课程的组,
Leave unchecked if you don't want to consider batch while making course based groups. ,如果您不想在制作基于课程的组时考虑批量，请不要选中。,
Student Group Creation Tool Course,学生组创建工具课程,
Course Code,课程代码,
Student Group Instructor,学生组教导,
Student Group Student,学生组学生,
Group Roll Number,组卷编号,
Student Guardian,学生家长,
Relation,关系,
Mother,母亲,
Father,父亲,
Student Language,学生语言,
Student Leave Application,学生请假申请,
Mark as Present,标记为现,
Student Log,学生登录,
Academic,学术的,
Achievement,已完成的,
Student Report Generation Tool,学生报表生成工具,
Include All Assessment Group,包括所有评估小组,
Show Marks,显示标记,
Add letterhead,添加信头,
Print Section,打印部分,
Total Parents Teacher Meeting,总计家长教师会议,
Attended by Parents,父母代出席,
Assessment Terms,评估条款,
Student Sibling,学生兄弟,
Studying in Same Institute,就读于同一研究所,
NO,没有,
YES,是,
Student Siblings,学生兄弟姐妹,
Topic Content,主题内容,
Amazon MWS Settings,亚马逊MWS设置,
ERPNext Integrations,ERPNext集成,
Enable Amazon,启用亚马逊,
MWS Credentials,MWS凭证,
Seller ID,卖家ID,
AWS Access Key ID,AWS访问密钥ID,
MWS Auth Token,MWS 验证令牌,
Market Place ID,市场ID,
AE,自动曝光,
AU,AU,
BR,BR,
CA,钙,
CN,CN,
DE,DE,
ES,ES,
FR,FR,
JP,J.P,
IT,IT,
MX,MX,
UK,英国,
US,我们,
Customer Type,客户类型,
Market Place Account Group,市场账户组,
After Date,日期之后,
Amazon will synch data updated after this date,亚马逊将同步在此日期之后更新的数据,
Sync Taxes and Charges,同步税费,
Get financial breakup of Taxes and charges data by Amazon ,获取亚马逊的税收和收费数据的财务细分,
Sync Products,同步产品,
Always sync your products from Amazon MWS before synching the Orders details,同步订单详细信息之前，请始终从Amazon MWS同步您的产品,
Sync Orders,同步订单,
Click this button to pull your Sales Order data from Amazon MWS.,单击此按钮可从亚马逊MWS中提取销售订单数据。,
Enable Scheduled Sync,启用预定同步,
Check this to enable a scheduled Daily synchronization routine via scheduler,选中此选项可通过调度程序启用计划的每日同步例程,
Max Retry Limit,最大重试限制,
Exotel Settings,Exotel设置,
Account SID,帐户SID,
API Token,API令牌,
GoCardless Mandate,GoCardless任务,
Mandate,要求,
GoCardless Customer,GoCardless客户,
GoCardless Settings,GoCardless设置,
Webhooks Secret,Webhooks的秘密,
Plaid Settings,格子设置,
Synchronize all accounts every hour,每小时同步所有帐户,
Plaid Client ID,格子客户端ID,
Plaid Secret,格子秘密,
Plaid Environment,格子环境,
sandbox,沙盒,
development,发展,
production,生产,
QuickBooks Migrator,QuickBooks Migrator,
Application Settings,应用程序设置,
Token Endpoint,令牌端点,
Scope,范围,
Authorization Settings,授权设置,
Authorization Endpoint,授权端点,
Authorization URL,授权URL,
Quickbooks Company ID,Quickbooks公司ID,
Company Settings,公司设置,
Default Shipping Account,默认运输帐户,
Default Warehouse,默认仓库,
Default Cost Center,默认成本中心,
Undeposited Funds Account,未存入资金账户,
Shopify Log,Shopify日志,
Shopify Settings,Shopify设置,
status html,状态HTML,
Enable Shopify,启用Shopify,
App Type,应用类型,
Last Sync Datetime,上次同步日期时间,
Shop URL,商店网址,
eg: frappe.myshopify.com,例如：frappe.myshopify.com,
Shared secret,共享秘密,
Webhooks Details,Webhooks详细信息,
Webhooks,网络挂接,
Customer Settings,客户设置,
Default Customer,默认客户,
Customer Group will set to selected group while syncing customers from Shopify,客户组将在同步Shopify客户的同时设置为选定的组,
For Company,对公司,
Cash Account will used for Sales Invoice creation,现金科目将用于创建销售费用清单,
Update Price from Shopify To ERPNext Price List,将Shopify更新到ERPNext价格清单,
Default Warehouse to to create Sales Order and Delivery Note,默认仓库到创建销售订单和交货单,
Sales Order Series,销售订单系列,
Import Delivery Notes from Shopify on Shipment,在发货时从Shopify导入交货单,
Delivery Note Series,销售出货单系列,
Import Sales Invoice from Shopify if Payment is marked,如果付款已标记，则从Shopify导入销售费用清单,
Sales Invoice Series,销售费用清单系列,
Shopify Tax Account,Shopify税收科目,
Shopify Tax/Shipping Title,Shopify税/运输标题,
ERPNext Account,ERPNext科目,
Shopify Webhook Detail,Shopify Webhook详细信息,
Webhook ID,Webhook ID,
Tally Migration,理货迁移,
Master Data,主要的数据,
"Data exported from Tally that consists of the Chart of Accounts, Customers, Suppliers, Addresses, Items and UOMs",从Tally导出的数据包括科目表，客户，供应商，地址，物料和UOM,
Is Master Data Processed,主数据是否已处理,
Is Master Data Imported,是否导入主数据,
Tally Creditors Account,理货债权人账户,
Creditors Account set in Tally,Tally中设置的债权人帐户,
Tally Debtors Account,理货债务人账户,
Debtors Account set in Tally,负债表中设置的债务人帐户,
Tally Company,理货公司,
Company Name as per Imported Tally Data,根据导入的理货数据的公司名称,
Default UOM,默认UOM,
UOM in case unspecified in imported data,如果未在导入的数据中指定UOM,
ERPNext Company,ERPNext公司,
Your Company set in ERPNext,您的公司在ERPNext中设置,
Processed Files,已处理的文件,
Parties,派对,
UOMs,计量单位,
Vouchers,优惠券,
Round Off Account,四舍五入科目,
Day Book Data,日簿数据,
Day Book Data exported from Tally that consists of all historic transactions,从Tally导出的包含所有历史交易的日簿数据,
Is Day Book Data Processed,是否处理了日记簿数据,
Is Day Book Data Imported,是否导入了日记簿数据,
Woocommerce Settings,Woocommerce设置,
Enable Sync,启用同步,
Woocommerce Server URL,Woocommerce服务器URL,
Secret,秘密,
API consumer key,应用程序界面消费者密钥,
API consumer secret,应用程序界面消费者秘密,
Tax Account,税收科目,
Freight and Forwarding Account,货运和转运科目,
Creation User,创作用户,
"The user that will be used to create Customers, Items and Sales Orders. This user should have the relevant permissions.",将用于创建客户，项目和销售订单的用户。该用户应具有相关权限。,
"This warehouse will be used to create Sales Orders. The fallback warehouse is ""Stores"".",该仓库将用于创建销售订单。后备仓库是“商店”。,
"The fallback series is ""SO-WOO-"".",后备系列是“SO-WOO-”。,
This company will be used to create Sales Orders.,该公司将用于创建销售订单。,
Delivery After (Days),交货后（天）,
This is the default offset (days) for the Delivery Date in Sales Orders. The fallback offset is 7 days from the order placement date.,这是销售订单中交货日期的默认偏移量（天）。后备偏移量是从下单日期算起的7天。,
"This is the default UOM used for items and Sales orders. The fallback UOM is ""Nos"".",这是用于商品和销售订单的默认UOM。后备UOM是“不”。,
Endpoints,端点,
Endpoint,端点,
Antibiotic Name,抗生素名称,
Healthcare Administrator,医疗管理员,
Laboratory User,实验室用户,
Is Inpatient,住院病人,
Default Duration (In Minutes),默认持续时间（以分钟为单位）,
Body Part,身体的一部分,
Body Part Link,身体部位链接,
HLC-CPR-.YYYY.-,HLC-CPR-.YYYY.-,
Procedure Template,程序模板,
Procedure Prescription,程序处方,
Service Unit,服务单位,
Consumables,耗材,
Consume Stock,消费库存,
Invoice Consumables Separately,发票耗材分开,
Consumption Invoiced,消费发票,
Consumable Total Amount,耗材总量,
Consumption Details,消费明细,
Nursing User,护理用户,
Clinical Procedure Item,临床流程项目,
Invoice Separately as Consumables,作为耗材单独费用清单,
Transfer Qty,转移数量,
Actual Qty (at source/target),实际数量（源/目标）,
Is Billable,是可计费的,
Allow Stock Consumption,允许库存消耗,
Sample UOM,样本单位,
Collection Details,收集细节,
Change In Item,项目变更,
Codification Table,编纂表,
Complaints,投诉,
Dosage Strength,剂量强度,
Strength,强度,
Drug Prescription,药物处方,
Drug Name / Description,药物名称/说明,
Dosage,剂量,
Dosage by Time Interval,剂量按时间间隔,
Interval,间隔,
Interval UOM,间隔UOM,
Hour,小时,
Update Schedule,更新时间排程,
Exercise,行使,
Difficulty Level,难度级别,
Counts Target,计算目标,
Counts Completed,计数完成,
Assistance Level,协助等级,
Active Assist,主动辅助,
Exercise Name,练习名称,
Body Parts,身体部位,
Exercise Instructions,练习说明,
Exercise Video,运动视频,
Exercise Steps,锻炼步骤,
Steps Table,步骤表,
Exercise Type Step,运动类型步骤,
Max number of visit,最大访问次数,
Visited yet,已访问,
Reference Appointments,参考预约,
Valid till,有效期至,
Fee Validity Reference,费用有效期参考,
Basic Details,基本细节,
HLC-PRAC-.YYYY.-,HLC-PRAC-.YYYY.-,
Mobile,手机号,
Phone (R),电话（R）,
Phone (Office),电话（办公室）,
Employee and User Details,员工和用户详细信息,
Hospital,医院,
Appointments,约会,
Practitioner Schedules,从业者时间表,
Charges,费用,
Out Patient Consulting Charge,门诊咨询费,
Default Currency,默认货币,
Healthcare Schedule Time Slot,医疗保健计划时间槽,
Parent Service Unit,上级服务单位,
Service Unit Type,服务单位类型,
Allow Appointments,允许任命,
Allow Overlap,允许重叠,
Inpatient Occupancy,住院病人入住率,
Occupancy Status,职业状况,
Vacant,空的,
Occupied,占据,
Item Details,品目详细信息,
UOM Conversion in Hours,UOM按小时转换,
Rate / UOM,费率/ UOM,
Change in Item,项目的更改,
Out Patient Settings,不住院患者设置,
Patient Name By,病人姓名By,
Patient Name,患者姓名,
Link Customer to Patient,将客户与患者联系起来,
"If checked, a customer will be created, mapped to Patient.\nPatient Invoices will be created against this Customer. You can also select existing Customer while creating Patient.",如果选中，将创建一个客户，映射到患者。将针对该客户创建病人费用清单。您也可以在创建患者时选择现有客户。,
Default Medical Code Standard,默认医疗代码标准,
Collect Fee for Patient Registration,收取病人登记费,
Checking this will create new Patients with a Disabled status by default and will only be enabled after invoicing the Registration Fee.,选中此选项将默认创建具有“禁用”状态的新患者，并且仅在开具注册费发票后才能启用。,
Registration Fee,注册费用,
Automate Appointment Invoicing,自动预约发票,
Manage Appointment Invoice submit and cancel automatically for Patient Encounter,管理预约费用清单的提交和自动取消以满足患者的需求,
Enable Free Follow-ups,启用免费跟进,
Number of Patient Encounters in Valid Days,有效天数中的患者人数,
The number of free follow ups (Patient Encounters in valid days) allowed,允许的免费跟进次数（患者在有效期内遇到）,
Valid Number of Days,有效天数,
Time period (Valid number of days) for free consultations,免费咨询的时间段（有效天数）,
Default Healthcare Service Items,默认医疗服务项目,
"You can configure default Items for billing consultation charges, procedure consumption items and inpatient visits",您可以为帐单咨询费用，程序消耗项目和住院访问配置默认项目,
Clinical Procedure Consumable Item,临床程序消耗品,
Default Accounts,默认账户,
Default income accounts to be used if not set in Healthcare Practitioner to book Appointment charges.,如果未在医生执业者中设置预约费用，则使用默认收入帐户。,
Default receivable accounts to be used to book Appointment charges.,默认的应收帐款将用于预订约会费用。,
Out Patient SMS Alerts,不住院病人短信,
Patient Registration,病人登记,
Registration Message,注册信息,
Confirmation Message,确认讯息,
Avoid Confirmation,避免确认,
Do not confirm if appointment is created for the same day,不要确认是否在同一天创建约会,
Appointment Reminder,预约提醒,
Reminder Message,提醒信息,
Laboratory Settings,实验室设置,
Create Lab Test(s) on Sales Invoice Submission,在销售发票提交上创建实验室测试,
Checking this will create Lab Test(s) specified in the Sales Invoice on submission.,选中此复选框将创建提交时在销售发票中指定的实验室测试。,
Create Sample Collection document for Lab Test,创建样本收集文档以进行实验室测试,
Checking this will create a Sample Collection document  every time you create a Lab Test,每次创建实验室测试时，选中此项都会创建一个“样品收集”文档,
Employee name and designation in print,打印出来显示的员工姓名和职位,
Check this if you want the Name and Designation of the Employee associated with the User who submits the document to be printed in the Lab Test Report.,如果您希望与提交文档的用户相关联的员工姓名和职务名称被打印在实验室测试报告中。,
Do not print or email Lab Tests without Approval,未经批准请勿打印或通过电子邮件发送实验室测试,
Checking this will restrict printing and emailing of Lab Test documents unless they have the status as Approved.,除非状态为“已批准”，否则选中此选项将限制打印和通过电子邮件发送实验室测试文档。,
Custom Signature in Print,自定义签名打印,
Laboratory SMS Alerts,实验室短信提醒,
Result Printed Message,结果打印消息,
Result Emailed Message,结果通过电子邮件发送,
Check In,报到,
Check Out,退出,
HLC-INP-.YYYY.-,HLC-INP-.YYYY.-,
A Positive,积极的,
A Negative,一个负面的,
AB Positive,AB积极,
AB Negative,AB阴性,
B Positive,B积极,
B Negative,B负面,
O Positive,O积极,
O Negative,O负面,
Date of birth,出生日期,
Admission Scheduled,计划的准入时间,
Discharge Scheduled,预定的卸货,
Discharged,已卸货,
Admission Schedule Date,准入时间表日期,
Admitted Datetime,准入的日期时间,
Expected Discharge,预期的卸货,
Discharge Date,出院日期,
Lab Prescription,实验室处方,
Lab Test Name,实验室测试名称,
Test Created,测试已创建,
Submitted Date,提交日期,
Approved Date,批准日期,
Sample ID,样品编号,
Lab Technician,实验室技术员,
Report Preference,报表偏好,
Test Name,测试名称,
Test Template,测试模板,
Test Group,测试组,
Custom Result,自定义结果,
LabTest Approver,实验室检测审批者,
Add Test,添加测试,
Normal Range,普通范围,
Result Format,结果格式,
Single,单身,
Compound,复合,
Descriptive,描述的,
Grouped,分组,
No Result,没有结果,
This value is updated in the Default Sales Price List.,该值在默认销售价格清单中更新。,
Lab Routine,实验室常规,
Result Value,结果值,
Require Result Value,需要结果值,
Normal Test Template,正常测试模板,
Patient Demographics,患者人口统计学,
HLC-PAT-.YYYY.-,HLC-PAT-.YYYY.-,
Middle Name (optional),中间名（可选）,
Inpatient Status,住院状况,
"If ""Link Customer to Patient"" is checked in Healthcare Settings and an existing Customer is not selected then, a Customer will be created for this Patient for recording transactions in Accounts module.",如果在“医疗保健设置”中选中了“将客户链接到患者”，并且未选择现有客户，则将为此患者创建一个客户，以在“帐户”模块中记录交易。,
Personal and Social History,个人和社会史,
Marital Status,婚姻状况,
Married,已婚,
Divorced,离异,
Widow,寡妇,
Patient Relation,患者关系,
"Allergies, Medical and Surgical History",过敏，医疗和外科史,
Allergies,过敏,
Medication,药物治疗,
Medical History,医学史,
Surgical History,手术史,
Risk Factors,风险因素,
Occupational Hazards and Environmental Factors,职业危害与环境因素,
Other Risk Factors,其他风险因素,
Patient Details,患者细节,
Additional information regarding the patient,有关患者的其他信息,
HLC-APP-.YYYY.-,HLC-APP-.YYYY.-,
Patient Age,患者年龄,
Get Prescribed Clinical Procedures,获取规定的临床程序,
Therapy,治疗,
Get Prescribed Therapies,获取处方疗法,
Appointment Datetime,约会日期时间,
Duration (In Minutes),持续时间（以分钟为单位）,
Reference Sales Invoice,参考销售发票,
More Info,更多信息,
Referring Practitioner,转介医生,
HLC-PA-.YYYY.-,HLC-PA-.YYYY.-,
Assessment Template,评估模板,
Assessment Datetime,评估日期时间,
Assessment Description,评估说明,
Assessment Sheet,评估表,
Total Score Obtained,获得总分,
Scale Min,最小刻度,
Scale Max,最大规模,
Patient Assessment Detail,患者评估详情,
Assessment Parameter,评估参数,
Patient Assessment Parameter,患者评估参数,
Patient Assessment Sheet,患者评估表,
Patient Assessment Template,患者评估模板,
Assessment Parameters,评估参数,
Parameters,参数,
Assessment Scale,评估量表,
Scale Minimum,最小规模,
Scale Maximum,最大规模,
HLC-ENC-.YYYY.-,HLC-ENC-.YYYY.-,
Encounter Date,遇到日期,
Encounter Time,遇到时间,
Encounter Impression,遇到印象,
Symptoms,病征,
In print,已打印,
Medical Coding,医学编码,
Procedures,程序,
Therapies,疗法,
Review Details,评论细节,
Patient Encounter Diagnosis,病人遭遇诊断,
Patient Encounter Symptom,病人遭遇症状,
HLC-PMR-.YYYY.-,HLC-PMR-.YYYY.-,
Attach Medical Record,附加病历,
Spouse,配偶,
Family,家庭,
Schedule Details,时间表详情,
Schedule Name,计划名称,
Time Slots,时隙,
Practitioner Service Unit Schedule,从业者服务单位时间表,
Procedure Name,程序名称,
Appointment Booked,约定已设定,
Procedure Created,程序已创建,
HLC-SC-.YYYY.-,HLC-SC-.YYYY.-,
Collected By,收藏者,
Particulars,细节,
Result Component,结果组件,
HLC-THP-.YYYY.-,HLC-THP-.YYYY.-,
Therapy Plan Details,治疗计划详情,
Total Sessions,总会议,
Total Sessions Completed,总会议完成,
Therapy Plan Detail,治疗计划详情,
No of Sessions,会话数,
Sessions Completed,会议完成,
Tele,远程,
Exercises,练习题,
Therapy For,疗法,
Add Exercises,添加练习,
Body Temperature,体温,
Presence of a fever (temp &gt; 38.5 °C/101.3 °F or sustained temp &gt; 38 °C/100.4 °F),发烧（温度&gt; 38.5°C / 101.3°F或持续温度&gt; 38°C / 100.4°F）,
Heart Rate / Pulse,心率/脉搏,
Adults' pulse rate is anywhere between 50 and 80 beats per minute.,成年人的脉率在每分钟50到80次之间。,
Respiratory rate,呼吸频率,
Normal reference range for an adult is 16–20 breaths/minute (RCP 2012),成人的正常参考范围是16-20次呼吸/分钟（RCP 2012）,
Tongue,舌,
Coated,有涂层的,
Very Coated,涂层很厚,
Normal,正常,
Furry,毛茸茸,
Cuts,削减,
Abdomen,腹部,
Bloated,胀,
Fluid,流体,
Constipated,便秘,
Reflexes,反射,
Hyper,超,
Very Hyper,非常兴奋,
One Sided,单面,
Blood Pressure (systolic),血压（收缩期）,
Blood Pressure (diastolic),血压（舒张）,
Blood Pressure,血压,
"Normal resting blood pressure in an adult is approximately 120 mmHg systolic, and 80 mmHg diastolic, abbreviated ""120/80 mmHg""",成年人的正常静息血压约为收缩期120mmHg，舒张压80mmHg，缩写为“120 / 80mmHg”,
Nutrition Values,营养价值观,
Height (In Meter),身高（米）,
Weight (In Kilogram),体重（公斤）,
BMI,BMI,
Hotel Room,旅馆房间,
Hotel Room Type,酒店房间类型,
Capacity,容量,
Extra Bed Capacity,加床容量,
Hotel Manager,酒店经理,
Hotel Room Amenity,酒店客房舒适,
Billable,可开票,
Hotel Room Package,酒店客房配套,
Amenities,设施,
Hotel Room Pricing,酒店房间价格,
Hotel Room Pricing Item,酒店房间定价项目,
Hotel Room Pricing Package,酒店房间价格套餐,
Hotel Room Reservation,酒店房间预订,
Guest Name,客人姓名,
Late Checkin,延迟入住,
Booked,已预订,
Hotel Reservation User,酒店预订用户,
Hotel Room Reservation Item,酒店房间预订项目,
Hotel Settings,酒店设置,
Default Taxes and Charges,默认税费,
Default Invoice Naming Series,默认费用清单名录,
HR,HR,
Date on which this component is applied,应用此组件的日期,
Salary Slip,工资单,
HR User,HR用户,
Job Applicant,求职者,
Body,身体,
Appraisal Template,评估模板,
Parent Department,上级部门,
Leave Block List,禁止休假日列表,
Days for which Holidays are blocked for this department.,此部门的禁离日,
Leave Approver,休假审批人,
Expense Approver,费用审批人,
Required Skills,所需技能,
Skills,技能,
Driver,司机,
HR-DRI-.YYYY.-,HR-DRI-.YYYY.-,
Suspended,暂停,
Transporter,承运商,
Applicable for external driver,适用于外部驱动器,
Cellphone Number,手机号码,
License Details,许可证信息,
License Number,许可证号,
Issuing Date,发行日期,
Driving License Categories,驾驶执照类别,
Driving License Category,驾驶执照类别,
Fleet Manager,车队经理,
Driver licence class,驾驶执照等级,
HR-EMP-,HR-EMP-,
Employment Type,员工类别,
Emergency Contact,紧急联络人,
Emergency Contact Name,紧急联络名字,
Emergency Phone,紧急电话,
ERPNext User,ERPNext用户,
"System User (login) ID. If set, it will become default for all HR forms.",系统用户的（登录）ID，将作为人力资源表单的默认ID。,
Create User Permission,创建用户权限,
This will restrict user access to other employee records,这将限制用户访问其他员工记录,
Joining Details,入职信息,
Offer Date,录用日期,
Confirmation Date,确认日期,
Contract End Date,合同结束日期,
Notice (days),通告（天）,
Date Of Retirement,退休日期,
Department and Grade,部门和职级,
Reports to,上级主管,
Attendance and Leave Details,出勤和离职详情,
Attendance Device ID (Biometric/RF tag ID),考勤设备ID（生物识别/ RF标签ID）,
Applicable Holiday List,适用于该员工的假期列表,
Default Shift,默认Shift,
Salary Mode,工资发放方式,
Bank A/C No.,银行账号,
Health Insurance,医保,
Health Insurance Provider,保险公司,
Health Insurance No,保单号,
Prefered Email,首选电子邮件,
Personal Email,个人电子邮件,
Permanent Address Is,永久地址,
Rented,租,
Owned,已有所有者,
Permanent Address,永久地址,
Prefered Contact Email,首选联系邮箱,
Company Email,企业邮箱,
Provide Email Address registered in company,提供公司注册邮箱地址,
Current Address Is,当前地址性质,
Current Address,当前地址,
Personal Bio,个人履历,
Bio / Cover Letter,履历/求职信,
Short biography for website and other publications.,在网站或其他出版物使用的个人简介,
Passport Number,护照号码,
Date of Issue,签发日期,
Place of Issue,签发地点,
Widowed,丧偶,
Family Background,家庭背景,
"Here you can maintain family details like name and occupation of parent, spouse and children",这里可以保存家庭详细信息，如姓名，父母、配偶及子女的职业等,
Health Details,健康信息,
"Here you can maintain height, weight, allergies, medical concerns etc",这里可以保存身高，体重，是否对某药物过敏等,
Educational Qualification,学历,
Previous Work Experience,以前工作经验,
External Work History,外部就职经历,
History In Company,公司内履历,
Internal Work History,内部工作经历,
Resignation Letter Date,辞职信日期,
Relieving Date,离职日期,
Reason for Leaving,离职原因,
Leave Encashed?,假期已折现？,
Encashment Date,折现日期,
New Workplace,新工作地点,
Returned Amount,退货金额,
Advance Account,预支科目,
Benefits Applied,已实施的福利,
Benefit Type and Amount,福利类型和金额,
Task Weight,任务权重,
Employee Education,员工教育,
School/University,学校/大学,
Graduate,研究生,
Post Graduate,研究生,
Under Graduate,本科,
Year of Passing,年份,
Class / Percentage,类/百分比,
Major/Optional Subjects,主修/选修科目,
Employee External Work History,员工外部就职经历,
Total Experience,总经验,
Default Leave Policy,默认休假政策,
Employee Group Table,员工组表,
ERPNext User ID,ERPNext用户ID,
Employee Internal Work History,员工内部就职经历,
Activities,活动,
Employee Onboarding Activity,员工入职活动,
Employee Promotion Detail,员工升职信息,
Employee Transfer Property,员工变动属性,
Unclaimed amount,未申报金额,
Holiday List Name,假期列表名称,
Total Holidays,总假期,
Add Weekly Holidays,添加每周假期,
Weekly Off,周末,
Add to Holidays,加入到假期,
Holidays,假期,
Clear Table,清除表格,
Retirement Age,退休年龄,
Enter retirement age in years,输入退休年龄,
Stop Birthday Reminders,停止生日提醒,
Leave Settings,保留设置,
Accepted,已接受,
Printing Details,打印设置,
Job Title,职位,
Allocation,分配,
Select Employees,选择员工,
Allocate,分配,
Max Leaves Allowed,允许最大休假,
Maximum Continuous Days Applicable,单次最长连续休假天数,
Select Payroll Period,选择工资名单的时间段,
Abbr,缩写,
Condition and Formula,条件和公式,
Total Working Hours,总的工作时间,
Hour Rate,时薪,
Bank Account No.,银行账号,
Earning & Deduction,收入及扣除,
Loan repayment,偿还借款,
Employee Loan,员工贷款,
Total Principal Amount,贷款本金总额,
Total Interest Amount,利息总额,
Total Loan Repayment,总贷款还款,
net pay info,净工资信息,
Gross Pay - Total Deduction - Loan Repayment,工资总额 - 扣除总额 - 贷款还款,
Net Pay (in words) will be visible once you save the Salary Slip.,保存工资单后会显示净支付金额(大写)。,
Staffing Plan Details,人员配置计划信息,
Optional,可选的,
Costing,成本核算,
Vehicle,车辆,
License Plate,牌照,
Odometer Value (Last),里程表值（最后）,
Acquisition Date,采集日期,
Chassis No,底盘号,
Vehicle Value,汽车价值,
Insurance Details,保险信息,
Insurance Company,保险公司,
Policy No,政策：,
Additional Details,额外细节,
Fuel Type,燃料类型,
Petrol,汽油,
Diesel,柴油机,
Natural Gas,天然气,
Electric,电动,
Fuel UOM,燃油计量单位,
Last Carbon Check,最后炭检查,
Wheels,车轮,
Doors,门,
last Odometer Value ,上一个里程表值,
Service Detail,服务细节,
Hub Tracked Item,集线器跟踪的物料,
Hub Node,集线器节点,
Image List,图像列表,
Item Manager,物料经理,
Hub User,集线器用户,
Hub Password,集线器密码,
Hub Users,集线器用户,
Marketplace Settings,市场设置,
Disable Marketplace,禁用市场,
Marketplace URL (to hide and update label),市场URL（隐藏和更新标签）,
Registered,已注册,
Sync in Progress,同步进行中,
Hub Seller Name,集线器卖家名称,
Custom Data,自定义数据,
Repay From Salary,从工资偿还,
Regular Payment,定期付款,
Loan Closure,贷款结清,
Rate of Interest (%) Yearly,利息率的比例（％）年,
MAT-MSH-.YYYY.-,MAT-MSH-.YYYY.-,
Generate Schedule,生成工时单,
Schedules,计划任务,
Maintenance Schedule Detail,维护计划细节,
Scheduled Date,计划日期,
Actual Date,实际日期,
Maintenance Schedule Item,维护计划物料,
Random,随机,
No of Visits,访问数量,
MAT-MVS-.YYYY.-,MAT-MVS-.YYYY.-,
Maintenance Date,维护日期,
Maintenance Time,维护时间,
Completion Status,完成状态,
Partially Completed,部分完成,
Fully Completed,全部完成,
Unscheduled,计划外,
Breakdown,细目,
Purposes,用途,
Customer Feedback,客户反馈,
Maintenance Visit Purpose,维护访问目的,
Work Done,已完成工作,
Against Document No,针对的文档编号,
Against Document Detail No,针对的对文档信息编号,
MFG-BLR-.YYYY.-,MFG-BLR-.YYYY.-,
Order Type,订单类型,
Blanket Order Item,总括订单项目,
Ordered Quantity,已下单数量,
Item to be manufactured or repacked,待生产或者重新包装的物料,
Quantity of item obtained after manufacturing / repacking from given quantities of raw materials,原材料被生产/重新打包后得到的物料数量,
Set rate of sub-assembly item based on BOM,基于BOM设置子组合项目的速率,
Allow Alternative Item,允许替代物料,
Item UOM,物料计量单位,
Conversion Rate,转换率,
Rate Of Materials Based On,基于以下的物料单价,
With Operations,带工艺,
Manage cost of operations,管理成本,
Transfer Material Against,转移材料,
Routing,路由,
Materials,物料,
Quality Inspection Required,需要质量检查,
Quality Inspection Template,质量检验模板,
Scrap,废料,
Scrap Items,废品,
Operating Cost,营业成本,
Raw Material Cost,原材料成本,
Scrap Material Cost,废料成本,
Operating Cost (Company Currency),营业成本（公司货币）,
Raw Material Cost (Company Currency),原材料成本（公司货币）,
Scrap Material Cost(Company Currency),废料成本（公司货币）,
Total Cost,总成本,
Total Cost (Company Currency),总成本（公司货币）,
Materials Required (Exploded),所需物料（正展开）,
Exploded Items,爆炸物品,
Show in Website,在网站上显示,
Item Image (if not slideshow),物料图片(如果没有演示文稿),
Thumbnail,缩略图,
Website Specifications,网站规格,
Show Items,显示物料,
Show Operations,显示操作,
Website Description,显示在网站商的详细说明，可文字，图文，多媒体混排,
BOM Explosion Item,BOM展开物料,
Qty Consumed Per Unit,每单位消耗数量,
Include Item In Manufacturing,包括制造业中的项目,
BOM Item,物料清单项目,
Item operation,物品操作,
Rate & Amount,价格和金额,
Basic Rate (Company Currency),库存评估价（公司货币）,
Scrap %,折旧％,
Original Item,原物料,
BOM Operation,物料清单操作,
Operation Time ,运作时间,
In minutes,在几分钟内,
Batch Size,批量大小,
Base Hour Rate(Company Currency),基数小时率（公司货币）,
Operating Cost(Company Currency),营业成本（公司货币）,
BOM Scrap Item,物料清单废料,
Basic Amount (Company Currency),基本金额（公司币种）,
BOM Update Tool,物料清单更新工具,
"Replace a particular BOM in all other BOMs where it is used. It will replace the old BOM link, update cost and regenerate ""BOM Explosion Item"" table as per new BOM.\nIt also updates latest price in all the BOMs.",替换使用所有其他BOM的特定BOM。它将替换旧的BOM链接，更新成本，并按照新的BOM重新生成“BOM爆炸项目”表。它还更新了所有BOM中的最新价格。,
Replace BOM,更换BOM,
Current BOM,当前BOM,
The BOM which will be replaced,此物料清单将被替换,
The new BOM after replacement,更换后的新物料清单,
Replace,更换,
Update latest price in all BOMs,更新所有BOM的最新价格,
BOM Website Item,物料清单网站项目,
BOM Website Operation,物料清单网站运营,
Operation Time,操作时间,
PO-JOB.#####,PO-JOB。#####,
Timing Detail,时间细节,
Time Logs,时间日志,
Total Time in Mins,分钟总时间,
Operation ID,操作编号,
Transferred Qty,已发料数量,
Job Started,工作开始了,
Started Time,开始时间,
Current Time,当前时间,
Job Card Item,工作卡项目,
Job Card Time Log,工作卡时间日志,
Time In Mins,分钟时间,
Completed Qty,已完成数量,
Manufacturing Settings,生产设置,
Raw Materials Consumption,原材料消耗,
Allow Multiple Material Consumption,允许多种材料消耗,
Backflush Raw Materials Based On,基于..进行原物料倒扣账,
Material Transferred for Manufacture,材料移送用于制造,
Capacity Planning,容量规划,
Disable Capacity Planning,禁用容量规划,
Allow Overtime,允许加班,
Allow Production on Holidays,允许在假日生产,
Capacity Planning For (Days),容量规划的期限（天）,
Default Warehouses for Production,默认生产仓库,
Default Work In Progress Warehouse,默认运行中的仓库,
Default Finished Goods Warehouse,默认成品仓库,
Default Scrap Warehouse,默认废料仓库,
Overproduction Percentage For Sales Order,销售订单超额生产百分比,
Overproduction Percentage For Work Order,工作订单的超订单生产量的百分比,
Other Settings,其他设置,
Update BOM Cost Automatically,自动更新BOM成本,
Material Request Plan Item,材料申请计划项目,
Material Request Type,材料申请类型,
Material Issue,发料,
Customer Provided,由客户提供,
Minimum Order Quantity,最小起订量,
Default Workstation,默认工作台,
Production Plan,生产计划,
MFG-PP-.YYYY.-,MFG-PP-.YYYY.-,
Get Items From,从...获取物料,
Get Sales Orders,获取销售订单,
Material Request Detail,材料申请信息,
Get Material Request,获取物料需求,
Material Requests,材料需求,
Get Items For Work Order,为工单获取物料,
Material Request Planning,材料申请计划,
Include Non Stock Items,包含非库存物料,
Include Subcontracted Items,包括转包物料,
Ignore Existing Projected Quantity,忽略现有的预计数量,
"To know more about projected quantity, <a href=""https://erpnext.com/docs/user/manual/en/stock/projected-quantity"" style=""text-decoration: underline;"" target=""_blank"">click here</a>.","要了解有关预计数量的更多信息， <a href=""https://erpnext.com/docs/user/manual/en/stock/projected-quantity"" style=""text-decoration: underline;"" target=""_blank"">请单击此处</a> 。",
Download Required Materials,下载所需资料,
Get Raw Materials For Production,获取生产用原材料,
Total Planned Qty,总计划数量,
Total Produced Qty,总生产数量,
Material Requested,需要的材料,
Production Plan Item,生产计划项,
Make Work Order for Sub Assembly Items,制作子装配件的工作订单,
"If enabled, system will create the work order for the exploded items against which BOM is available.",如果启用，系统将为BOM可用的爆炸项目创建工作订单。,
Planned Start Date,计划开始日期,
Quantity and Description,数量和描述,
material_request_item,材料_需求_物料,
Product Bundle Item,产品包物料,
Production Plan Material Request,生产计划申请材料,
Production Plan Sales Order,生产计划销售订单,
Sales Order Date,销售订单日期,
Routing Name,路由名称,
MFG-WO-.YYYY.-,MFG-WO-.YYYY.-,
Item To Manufacture,待生产物料,
Material Transferred for Manufacturing,材料移送用于制造,
Manufactured Qty,已生产数量,
Use Multi-Level BOM,采用多级物料清单,
Plan material for sub-assemblies,计划材料为子组件,
Skip Material Transfer to WIP Warehouse,跳过物料转移到WIP仓库,
Check if material transfer entry is not required,检查是否不需要材料转移条目,
Backflush Raw Materials From Work-in-Progress Warehouse,从在制品库中反冲原料,
Update Consumed Material Cost In Project,更新项目中的消耗材料成本,
Warehouses,仓库,
This is a location where raw materials are available.,这是可获取原材料的地方。,
Work-in-Progress Warehouse,在制品仓库,
This is a location where operations are executed.,这是执行操作的位置。,
This is a location where final product stored.,这是存放最终产品的位置。,
Scrap Warehouse,废料仓库,
This is a location where scraped materials are stored.,这是存放刮擦材料的位置。,
Required Items,所需物料,
Actual Start Date,实际开始日期,
Planned End Date,计划的结束日期,
Actual End Date,实际结束日期,
Operation Cost,运营成本,
Planned Operating Cost,计划运营成本,
Actual Operating Cost,实际运行成本,
Additional Operating Cost,额外的运营成本,
Total Operating Cost,总营运成本,
Manufacture against Material Request,针对材料需求的生产,
Work Order Item,工单项,
Available Qty at Source Warehouse,源仓库可用数量,
Available Qty at WIP Warehouse,在WIP仓库可用的数量,
Work Order Operation,工单操作,
Operation Description,操作说明,
Operation completed for how many finished goods?,已为多少成品操作完成？,
Work in Progress,在制品,
Estimated Time and Cost,预计时间和成本,
Planned Start Time,计划开始时间,
Planned End Time,计划结束时间,
in Minutes,以分钟为单位,
Actual Time and Cost,实际时间和成本,
Actual Start Time,实际开始时间,
Actual End Time,实际结束时间,
Updated via 'Time Log',通过“时间日志”更新,
Actual Operation Time,实际操作时间,
in Minutes\nUpdated via 'Time Log',单位为分钟，通过“时间日志”更新,
(Hour Rate / 60) * Actual Operation Time,（小时率/ 60）*实际操作时间,
Workstation Name,工作站名称,
Production Capacity,生产能力,
Operating Costs,运营成本,
Electricity Cost,电力成本,
per hour,每小时,
Consumable Cost,耗材成本,
Rent Cost,租金成本,
Wages,工资,
Wages per hour,时薪,
Net Hour Rate,净小时价格,
Workstation Working Hour,工作站工作时间,
Certification Application,认证申请,
Name of Applicant,申请人名称,
Certification Status,认证状态,
Yet to appear,尚未出现,
Certified,认证,
Not Certified,未认证,
USD,美元,
INR,INR,
Certified Consultant,认证顾问,
Name of Consultant,顾问的名字,
Certification Validity,认证有效性,
Discuss ID,讨论ID,
GitHub ID,GitHub ID,
Non Profit Manager,非营利经理,
Chapter Head,章节开头,
Meetup Embed HTML,Meetup嵌入的HTML,
chapters/chapter_name\nleave blank automatically set after saving chapter.,保留空白的章节/章节名称在保存章节后会自动设置。,
Chapter Members,章节成员,
Members,会员,
Chapter Member,章会员,
Website URL,网站网址,
Leave Reason,离开原因,
Donor Name,捐助者名称,
Donor Type,捐助者类型,
Withdrawn,取消,
Grant Application Details ,授予申请细节,
Grant Description,授予说明,
Requested Amount,请求金额,
Has any past Grant Record,有过去的赠款记录吗？,
Show on Website,在网站上显示,
Assessment  Mark (Out of 10),评估标记（满分10分）,
Assessment  Manager,评估经理,
Email Notification Sent,电子邮件通知已发送,
NPO-MEM-.YYYY.-,NPO-MEM-.YYYY.-,
Membership Expiry Date,会员到期日,
Razorpay Details,Razorpay详细信息,
Subscription ID,订阅编号,
Customer ID,顾客ID,
Subscription Activated,订阅已激活,
Subscription Start ,订阅开始,
Subscription End,订阅结束,
Non Profit Member,非盈利会员,
Membership Status,成员身份,
Member Since,自...成为会员,
Payment ID,付款编号,
Membership Settings,会员设置,
Enable RazorPay For Memberships,为会员启用RazorPay,
RazorPay Settings,RazorPay设置,
Billing Cycle,结算周期,
Billing Frequency,帐单频率,
"The number of billing cycles for which the customer should be charged. For example, if a customer is buying a 1-year membership that should be billed on a monthly basis, this value should be 12.",应向客户收费的计费周期数。例如，如果客户购买的1年会员资格应按月计费，则此值应为12。,
Razorpay Plan ID,Razorpay计划编号,
Volunteer Name,志愿者姓名,
Volunteer Type,志愿者类型,
Availability and Skills,可用性和技能,
Availability,可用性,
Weekends,周末,
Availability Timeslot,可用时间段,
Morning,早上,
Afternoon,下午,
Evening,晚间,
Anytime,任何时候,
Volunteer Skills,志愿者技能,
Volunteer Skill,志愿者技能,
Homepage,主页,
Hero Section Based On,基于英雄的英雄部分,
Homepage Section,主页部分,
Hero Section,英雄科,
Tag Line,标语,
Company Tagline for website homepage,公司标语的网站主页,
Company Description for website homepage,在网站的首页公司介绍,
Homepage Slideshow,主页幻灯片,
"URL for ""All Products""",网址“所有产品”,
Products to be shown on website homepage,在网站首页中显示的产品,
Homepage Featured Product,首页推荐产品,
route,路线,
Section Based On,基于的部分,
Section Cards,部分卡片,
Number of Columns,列数,
Number of columns for this section. 3 cards will be shown per row if you select 3 columns.,此部分的列数。如果选择3列，每行将显示3张卡片。,
Section HTML,部分HTML,
Use this field to render any custom HTML in the section.,使用此字段在该部分中呈现任何自定义HTML。,
Section Order,部分顺序,
"Order in which sections should appear. 0 is first, 1 is second and so on.",应该出现哪些部分的顺序。 0是第一个，1是第二个，依此类推。,
Homepage Section Card,主页卡片,
Subtitle,字幕,
Products Settings,产品设置,
Home Page is Products,首页是产品,
"If checked, the Home page will be the default Item Group for the website",如果选中，主页将是网站的默认项目组,
Show Availability Status,显示可用性状态,
Product Page,产品页面,
Products per Page,每页产品,
Enable Field Filters,启用字段过滤器,
Item Fields,项目字段,
Enable Attribute Filters,启用属性过滤器,
Attributes,属性,
Hide Variants,隐藏变体,
Website Attribute,网站属性,
Attribute,属性,
Website Filter Field,网站过滤字段,
Activity Cost,活动费用,
Billing Rate,结算利率,
Costing Rate,成本率,
title,标题,
Projects User,工程用户,
Default Costing Rate,默认成本核算率,
Default Billing Rate,默认开票单价,
Dependent Task,相关任务,
Project Type,项目类型,
% Complete Method,完成百分比法,
Task Completion,任务完成,
Task Progress,任务进度,
% Completed,% 已完成,
From Template,来自模板,
Project will be accessible on the website to these users,这些用户可在网站上访问该项目,
Copied From,复制自,
Start and End Dates,开始和结束日期,
Actual Time in Hours (via Timesheet),实际时间（以小时为单位）,
Costing and Billing,成本核算和计费,
Total Costing Amount (via Timesheet),总成本金额（通过工时单）,
Total Expense Claim (via Expense Claim),总费用报销（通过费用报销）,
Total Purchase Cost (via Purchase Invoice),总采购成本（通过采购费用清单）,
Total Sales Amount (via Sales Order),总销售额（通过销售订单）,
Total Billable Amount (via Timesheet),总可计费用金额（通过工时单）,
Total Billed Amount (via Sales Invoice),总账单金额（通过销售费用清单）,
Total Consumed Material Cost (via Stock Entry),总物料消耗成本（通过手工库存移动）,
Gross Margin,毛利,
Gross Margin %,毛利率％,
Monitor Progress,监控进度,
Collect Progress,收集进度,
Frequency To Collect Progress,采集进度信息的频率,
Twice Daily,每天两次,
First Email,第一封邮件,
Second Email,第二封邮件,
Time to send,发送时间,
Day to Send,发送日,
Message will be sent to the users to get their status on the Project,消息将发送给用户以获取其在项目中的状态,
Projects Manager,工程经理,
Project Template,项目模板,
Project Template Task,项目模板任务,
Begin On (Days),开始（天）,
Duration (Days),持续时间（天）,
Project Update,项目更新,
Project User,项目用户,
View attachments,查看附件,
Projects Settings,工程设置,
Ignore Workstation Time Overlap,忽略工作站时间重叠,
Ignore User Time Overlap,忽略用户时间重叠,
Ignore Employee Time Overlap,忽略员工时间重叠,
Weight,重量,
Parent Task,上级任务,
Timeline,时间线,
Expected Time (in hours),预期时间（以小时计）,
% Progress,％进展,
Is Milestone,里程碑,
Task Description,任务描述,
Dependencies,依赖,
Dependent Tasks,相关任务,
Depends on Tasks,前置任务,
Actual Start Date (via Timesheet),实际开始日期（通过工时单）,
Actual Time in Hours (via Timesheet),实际时间（小时）,
Actual End Date (via Timesheet),实际结束日期（通过工时单）,
Total Expense Claim (via Expense Claim),总费用报销（通过费用报销）,
Review Date,评论日期,
Closing Date,结算日期,
Task Depends On,前置任务,
Task Type,任务类型,
TS-.YYYY.-,TS-.YYYY.-,
Employee Detail,员工详细信息,
Billing Details,开票（帐单）信息,
Total Billable Hours,总可计费时间,
Total Billed Hours,帐单总时间,
Total Costing Amount,总成本计算金额,
Total Billable Amount,总可结算金额,
Total Billed Amount,总可开单金额,
% Amount Billed,（％）金额帐单,
Hrs,小时,
Costing Amount,成本核算金额,
Corrective/Preventive,纠正/预防,
Corrective,纠正的,
Preventive,预防,
Resolution,决议,
Resolutions,决议,
Quality Action Resolution,质量行动决议,
Quality Feedback Parameter,质量反馈参数,
Quality Feedback Template Parameter,质量反馈模板参数,
Quality Goal,质量目标,
Monitoring Frequency,监测频率,
Weekday,平日,
Objectives,目标,
Quality Goal Objective,质量目标,
Objective,目的,
Agenda,议程,
Minutes,分钟,
Quality Meeting Agenda,质量会议议程,
Quality Meeting Minutes,质量会议纪要,
Minute,分钟,
Parent Procedure,父程序,
Processes,流程,
Quality Procedure Process,质量程序流程,
Process Description,进度解析,
Link existing Quality Procedure.,链接现有的质量程序。,
Additional Information,附加信息,
Quality Review Objective,质量审查目标,
DATEV Settings,DATEV设置,
Regional,区域性,
Consultant ID,顾问编号,
GST HSN Code,GST HSN代码,
HSN Code,HSN代码,
GST Settings,GST设置,
GST Summary,消费税总结,
GSTIN Email Sent On,发送GSTIN电子邮件,
GST Accounts,GST科目,
B2C Limit,B2C限制,
Set Invoice Value for B2C. B2CL and B2CS calculated based on this invoice value.,设置B2C的费用清单值。 B2CL和B2CS根据此费用清单值计算。,
GSTR 3B Report,GSTR 3B报告,
January,一月,
February,二月,
March,游行,
April,四月,
August,八月,
September,九月,
October,十月,
November,十一月,
December,十二月,
JSON Output,JSON输出,
Invoices with no Place Of Supply,没有供应地的发票,
Import Supplier Invoice,进口供应商发票,
Invoice Series,发票系列,
Upload XML Invoices,上载XML发票,
Zip File,压缩文件,
Import Invoices,进口发票,
Click on Import Invoices button once the zip file has been attached to the document. Any errors related to processing will be shown in the Error Log.,将zip文件附加到文档后，单击“导入发票”按钮。与处理相关的任何错误将显示在错误日志中。,
Lower Deduction Certificate,降低扣除证明,
Certificate Details,证书详细信息,
194A,194A,
194C,194C,
194D,194D,
194H,194小时,
194I,194I,
194J,194J,
194LA,194LA,
194LBB,194磅,
194LBC,194LBC,
Certificate No,证书号码,
Deductee Details,受扣人详细信息,
PAN No,PAN号,
Validity Details,有效性详细信息,
Rate Of TDS As Per Certificate,根据证书的TDS费率,
Certificate Limit,证书限制,
Invoice Series Prefix,费用清单系列前缀,
Active Menu,活动菜单,
Restaurant Menu,餐厅菜单,
Price List (Auto created),价格清单（自动创建）,
Restaurant Manager,餐厅经理,
Restaurant Menu Item,餐厅菜单项,
Restaurant Order Entry,餐厅订单录入,
Restaurant Table,餐桌,
Click Enter To Add,点击输入以添加,
Last Sales Invoice,上次销售费用清单,
Current Order,当前订单,
Restaurant Order Entry Item,餐厅订单录入项目,
Served,曾任,
Restaurant Reservation,餐厅预订,
Waitlisted,轮候,
No Show,没有出现,
No of People,人数,
Reservation Time,预订时间,
Reservation End Time,预订结束时间,
No of Seats,座位数,
Minimum Seating,最小的座位,
"Keep Track of Sales Campaigns. Keep track of Leads, Quotations, Sales Order etc from Campaigns to gauge Return on Investment. ",追踪促销活动。追踪来自营销活动的线索，报价，销售订单等，统计投资回报率。,
SAL-CAM-.YYYY.-,SAL-CAM-.YYYY.-,
Campaign Schedules,活动时间表,
Buyer of Goods and Services.,产品和服务采购者。,
CUST-.YYYY.-,CUST-.YYYY.-,
Default Company Bank Account,默认公司银行帐户,
From Lead,来自潜在客户,
Account Manager,客户经理,
Allow Sales Invoice Creation Without Sales Order,允许创建无销售订单的销售发票,
Allow Sales Invoice Creation Without Delivery Note,允许在没有交货单的情况下创建销售发票,
Default Price List,默认价格清单,
Primary Address and Contact Detail,主要地址和联系人信息,
"Select, to make the customer searchable with these fields",选择，使客户可以使用这些字段进行搜索,
Customer Primary Contact,客户主要联系人,
"Reselect, if the chosen contact is edited after save",重新选择，如果所选联系人在保存后被编辑,
Customer Primary Address,客户主要地址,
"Reselect, if the chosen address is edited after save",重新选择，如果所选地址在保存后被编辑,
Primary Address,主要地址,
Mention if non-standard receivable account,如需记账到非标准应收账款科目应提及,
Credit Limit and Payment Terms,信用额度和付款条款,
Additional information regarding the customer.,该客户的其他信息。,
Sales Partner and Commission,销售合作伙伴及佣金,
Commission Rate,佣金率,
Sales Team Details,销售团队信息,
Customer POS id,客户POS ID,
Customer Credit Limit,客户信用额度,
Bypass Credit Limit Check at Sales Order,不在销售订单做信用额度检查,
Industry Type,行业类型,
MAT-INS-.YYYY.-,MAT-INS-.YYYY.-,
Installation Date,安装日期,
Installation Time,安装时间,
Installation Note Item,安装单项,
Installed Qty,已安装数量,
Lead Source,线索来源,
Period Start Date,期间开始日期,
Period End Date,期末结束日期,
Cashier,出纳员,
Difference,区别,
Modes of Payment,付款方式,
Linked Invoices,链接的费用清单,
POS Closing Voucher Details,销售终端关闭凭证详细信息,
Collected Amount,收集金额,
Expected Amount,预期金额,
POS Closing Voucher Invoices,销售pos终端关闭凭证费用清单,
Quantity of Items,物料数量,
"Aggregate group of **Items** into another **Item**. This is useful if you are bundling a certain **Items** into a package and you maintain stock of the packed **Items** and not the aggregate **Item**. \n\nThe package **Item** will have ""Is Stock Item"" as ""No"" and ""Is Sales Item"" as ""Yes"".\n\nFor Example: If you are selling Laptops and Backpacks separately and have a special price if the customer buys both, then the Laptop + Backpack will be a new Product Bundle Item.\n\nNote: BOM = Bill of Materials",将一组物料集合到另外一种物料。如果将物料组合打包/包装，然后维护这个组合后的物料的库存而不是集合物料。包装物料将有一种属性：“库存条目”（取值“否”），或“销售条目”（取值“是”）。例如：你分别销售笔记本电脑和背包，并且如果有顾客采购两种而使用单独的价格，那么笔记本电脑+背包将是一个新的产品包项目。注：物料BOM =Bill of Materials,
Parent Item,上级项目,
List items that form the package.,本包装内的物料列表。,
SAL-QTN-.YYYY.-,SAL-QTN-.YYYY.-,
Quotation To,报价对象,
Rate at which customer's currency is converted to company's base currency,客户的货币转换为公司的本币后的单价,
Rate at which Price list currency is converted to company's base currency,价格清单货币转换为公司的本币后的单价,
Additional Discount and Coupon Code,附加折扣和优惠券代码,
Referral Sales Partner,推荐销售合作伙伴,
In Words will be visible once you save the Quotation.,大写金额将在报价单保存后显示。,
Term Details,条款信息,
Quotation Item,报价物料,
Against Doctype,针对的文档类型,
Against Docname,针对的文档名称,
Additional Notes,补充说明,
SAL-ORD-.YYYY.-,SAL-ORD-.YYYY.-,
Skip Delivery Note,跳过交货单,
In Words will be visible once you save the Sales Order.,大写金额将在销售订单保存后显示。,
Track this Sales Order against any Project,对任何工程追踪该销售订单,
Billing and Delivery Status,账单和交货状态,
Not Delivered,未交付,
Fully Delivered,完全交付,
Partly Delivered,部分交付,
Not Applicable,不适用,
%  Delivered,％已交付,
% of materials delivered against this Sales Order,此销售订单% 的物料已交货。,
% of materials billed against this Sales Order,此销售订单%的物料已开票。,
Not Billed,未开票,
Fully Billed,完全开票,
Partly Billed,部分开票,
Ensure Delivery Based on Produced Serial No,确保基于生产的序列号的交货,
Supplier delivers to Customer,供应商直接出货给客户,
Delivery Warehouse,交货仓库,
Planned Quantity,计划数量,
For Production,生产,
Work Order Qty,工单数量,
Produced Quantity,生产的产品数量,
Used for Production Plan,用于生产计划,
Sales Partner Type,销售伙伴类型,
Contact No.,联络人电话,
Contribution (%),贡献（％）,
Contribution to Net Total,贡献净总计,
Selling Settings,销售设置,
Settings for Selling Module,销售模块的设置,
Customer Naming By,客户命名方式,
Campaign Naming By,活动命名：,
Default Customer Group,默认客户群组,
Default Territory,默认地区,
Close Opportunity After Days,几天后关闭机会,
Default Quotation Validity Days,默认报价有效天数,
Sales Update Frequency,销售更新频率,
Each Transaction,每笔交易,
SMS Center,短信中心,
Send To,发送到,
All Contact,所有联系人,
All Customer Contact,所有客户联系方式,
All Supplier Contact,所有供应商联系人,
All Sales Partner Contact,所有的销售合作伙伴联系人,
All Lead (Open),所有潜在客户（开放）,
All Employee (Active),所有员工（活动）,
All Sales Person,所有的销售人员,
Create Receiver List,创建接收人列表,
Receiver List,接收人列表,
Messages greater than 160 characters will be split into multiple messages,大于160个字符的消息将被分割为多条消息,
Total Characters,总字符,
Total Message(s),总信息（s ）,
Authorization Control,授权控制,
Authorization Rule,授权规则,
Average Discount,平均折扣,
Customerwise Discount,客户折扣,
Itemwise Discount,物料级的折扣,
Customer or Item,客户或物料,
Customer / Item Name,客户/物料名称,
Authorized Value,授权值,
Applicable To (Role),适用于(角色),
Applicable To (Employee),适用于(员工),
Applicable To (User),适用于(用户),
Applicable To (Designation),适用于(职位),
Approving Role (above authorized value),批准角色（上述授权值）,
Approving User  (above authorized value),批准的用户（上述授权值）,
Brand Defaults,品牌默认值,
Legal Entity / Subsidiary with a separate Chart of Accounts belonging to the Organization.,属于本机构的，带独立科目表的法人/附属机构。,
Change Abbreviation,更改缩写,
Parent Company,母公司,
Default Values,默认值,
Default Holiday List,默认假期列表,
Default Selling Terms,默认销售条款,
Default Buying Terms,默认购买条款,
Create Chart Of Accounts Based On,基于...创建科目表,
Standard Template,标准模板,
Existing Company,现有公司,
Chart Of Accounts Template,科目表模板,
Existing Company ,现有的公司,
Date of Establishment,成立时间,
Sales Settings,销售设置,
Monthly Sales Target,每月销售目标,
Sales Monthly History,销售月历,
Transactions Annual History,交易年历,
Total Monthly Sales,每月销售总额,
Default Cash Account,默认现金科目,
Default Receivable Account,默认应收科目,
Round Off Cost Center,四舍五入成本中心,
Discount Allowed Account,折扣允许的帐户,
Discount Received Account,折扣收到的帐户,
Exchange Gain / Loss Account,汇兑损益科目,
Unrealized Exchange Gain/Loss Account,未实现汇兑损益科目,
Allow Account Creation Against Child Company,允许针对儿童公司创建帐户,
Default Payable Account,默认应付科目,
Default Employee Advance Account,默认员工预支科目,
Default Cost of Goods Sold Account,默认销货成本科目,
Default Income Account,默认收入科目,
Default Deferred Revenue Account,默认递延收入科目,
Default Deferred Expense Account,默认递延费用帐户,
Default Payroll Payable Account,默认应付职工薪资科目,
Default Expense Claim Payable Account,默认费用索赔应付帐款,
Stock Settings,库存设置,
Enable Perpetual Inventory,启用永续库存功能（每次库存移动实时生成会计凭证）,
Default Inventory Account,默认存货科目,
Stock Adjustment Account,库存调整科目,
Fixed Asset Depreciation Settings,固定资产折旧设置,
Series for Asset Depreciation Entry (Journal Entry),固定资产折旧凭证命名序列（手工凭证）,
Gain/Loss Account on Asset Disposal,资产处置收益/损失科目,
Asset Depreciation Cost Center,资产折旧成本中心,
Budget Detail,预算信息,
Exception Budget Approver Role,例外预算审批人角色,
Company Info,公司信息,
For reference only.,仅供参考。,
Company Logo,公司标志,
Date of Incorporation,注册成立日期,
Date of Commencement,开始日期,
Phone No,电话号码,
Company Description,公司介绍,
Registration Details,注册详细信息,
Company registration numbers for your reference. Tax numbers etc.,公司注册号码，供大家参考。税务号码等,
Delete Company Transactions,删除正式上线前的测试数据,
Currency Exchange,外币汇率,
Specify Exchange Rate to convert one currency into another,指定外币汇率的汇率,
From Currency,源货币,
To Currency,以货币,
For Buying,待采购,
For Selling,出售,
Customer Group Name,客户群组名称,
Parent Customer Group,父（上级）客户群组,
Only leaf nodes are allowed in transaction,只有叶节点中允许交易,
Mention if non-standard receivable account applicable,如果不规范应收账款适用的话应提及,
Credit Limits,信用额度,
Email Digest,邮件摘要,
Send regular summary reports via Email.,通过电子邮件发送定期汇总报表。,
Email Digest Settings,邮件摘要设置,
How frequently?,多快的频率？,
Next email will be sent on:,下次邮件发送时间：,
Note: Email will not be sent to disabled users,注意：邮件不会发送给已禁用用户,
Profit & Loss,利润损失,
New Income,新的收入,
New Expenses,新的费用,
Annual Income,年收入,
Annual Expenses,年度支出,
Bank Balance,银行存款余额,
Bank Credit Balance,银行信贷余额,
Receivables,应收款,
Payables,应付账款,
Sales Orders to Bill,应开账单的的销售订单,
Purchase Orders to Bill,需要开具账单的采购订单,
New Sales Orders,新建销售订单,
New Purchase Orders,新建采购订单,
Sales Orders to Deliver,要交付的销售订单,
Purchase Orders to Receive,要收货的采购订单,
New Purchase Invoice,新购买发票,
New Quotations,新报价,
Open Quotations,打开报价单,
Open Issues,开放式问题,
Open Projects,公开项目,
Purchase Orders Items Overdue,采购订单项目逾期,
Upcoming Calendar Events,即将进行的日历活动,
Open To Do,开放做,
Add Quote,添加报价,
Global Defaults,全局默认值,
Default Company,默认公司,
Current Fiscal Year,当前财年,
Default Distance Unit,默认距离单位,
Hide Currency Symbol,隐藏货币符号,
Do not show any symbol like $ etc next to currencies.,不要在货币旁显示货币代号，例如$等。,
"If disable, 'Rounded Total' field will not be visible in any transaction",如果禁用，“圆整后金额”字段将不在任何交易中显示,
Disable In Words,禁用词,
"If disable, 'In Words' field will not be visible in any transaction",如果禁用“，在词”字段不会在任何交易可见,
Item Classification,物料分类,
General Settings,常规设置,
Item Group Name,物料群组名称,
Parent Item Group,父（上级）项目组,
Item Group Defaults,项目组默认值,
Item Tax,物料税项,
Check this if you want to show in website,要在网站上展示，请勾选此项。,
Show this slideshow at the top of the page,在页面顶部显示此幻灯片,
HTML / Banner that will show on the top of product list.,HTML或横幅，将显示在产品列表的顶部。,
Set prefix for numbering series on your transactions,为交易设置编号系列的前缀,
Setup Series,设置系列,
Update Series,更新系列,
Change the starting / current sequence number of an existing series.,更改现有系列的起始/当前序列号。,
Quotation Lost Reason,报价遗失原因,
A third party distributor / dealer / commission agent / affiliate / reseller who sells the companies products for a commission.,被授权销售公司产品以赚取佣金的第三方分销商/经销商/授权代理商/分支机构/转销商,
Sales Partner Name,销售合作伙伴名称,
Partner Type,合作伙伴类型,
Address & Contacts,地址及联系方式,
Address Desc,地址倒序,
Contact Desc,联系人倒序,
Sales Partner Target,销售合作伙伴目标,
Targets,目标,
Show In Website,在网站上展示,
Referral Code,推荐码,
To Track inbound purchase,跟踪入站购买,
Logo,徽标,
Partner website,合作伙伴网站,
All Sales Transactions can be tagged against multiple **Sales Persons** so that you can set and monitor targets.,所有的销售交易都可以标记多个**销售人员**，方便你设置和监控目标。,
Name and Employee ID,姓名和员工ID,
Sales Person Name,销售人员姓名,
Parent Sales Person,母公司销售人员,
Select company name first.,请先选择公司名称。,
Sales Person Targets,销售人员目标,
Set targets Item Group-wise for this Sales Person.,为该销售人员设置目标物料组别,
Supplier Group Name,供应商群组名称,
Parent Supplier Group,上级供应商组,
Target Detail,目标详细信息,
Target Qty,目标数量,
Target  Amount,目标金额,
Target Distribution,目标分布,
"Standard Terms and Conditions that can be added to Sales and Purchases.\n\nExamples:\n\n1. Validity of the offer.\n1. Payment Terms (In Advance, On Credit, part advance etc).\n1. What is extra (or payable by the Customer).\n1. Safety / usage warning.\n1. Warranty if any.\n1. Returns Policy.\n1. Terms of shipping, if applicable.\n1. Ways of addressing disputes, indemnity, liability, etc.\n1. Address and Contact of your Company.",可以添加至销售或采购的标准条款和条件。例如：1. 报价有效期。 2.付款条件(预付款，赊购，部分预付款)。3.其他，例如安全/使用警告，退货政策，配送条款，争议/赔偿/责任仲裁方式，贵公司的地址和联系方式。,
Applicable Modules,适用模块,
Terms and Conditions Help,条款和条件帮助,
Classification of Customers by region,客户按区域分类,
Territory Name,区域名称,
Parent Territory,上级领域,
Territory Manager,区域经理,
For reference,供参考,
Territory Targets,区域目标,
Set Item Group-wise budgets on this Territory. You can also include seasonality by setting the Distribution.,为此区域设置物料群组特定的预算。你还可以设置“分布”，为预算启动季节性。,
UOM Name,计量单位名称,
Check this to disallow fractions. (for Nos),要对编号禁止分数，请勾选此项。,
Website Item Group,网站物料组,
Cross Listing of Item in multiple groups,多个群组物料交叉显示,
Default settings for Shopping Cart,购物车的默认设置,
Enable Shopping Cart,启用购物车,
Display Settings,显示设置,
Show Public Attachments,显示公共附件,
Show Price,显示价格,
Show Stock Availability,显示库存可用性,
Show Contact Us Button,显示联系我们按钮,
Show Stock Quantity,显示库存数量,
Show Apply Coupon Code,显示申请优惠券代码,
Allow items not in stock to be added to cart,允许将无库存的商品添加到购物车,
Prices will not be shown if Price List is not set,价格将不会显示如果没有设置价格,
Quotation Series,报价系列,
Checkout Settings,结帐设置,
Enable Checkout,启用结帐,
Payment Success Url,付款成功URL,
After payment completion redirect user to selected page.,支付完成后将用户转到所选网页。,
Batch Details,批次明细,
Batch ID,批次ID,
image,图片,
Parent Batch,上级批次,
Manufacturing Date,生产日期,
Batch Quantity,批次数量,
Batch UOM,批量UOM,
Source Document Type,源文档类型,
Source Document Name,源文档名称,
Batch Description,批次说明,
Bin,储位,
Reserved Quantity,保留数量,
Actual Quantity,实际数量,
Requested Quantity,需求数量,
Reserved Qty for sub contract,用于外包的预留数量,
Moving Average Rate,移动平均价格,
FCFS Rate,FCFS率,
Customs Tariff Number,海关税则号,
Tariff Number,税则号,
Delivery To,交货对象,
MAT-DN-.YYYY.-,MAT-DN-.YYYY.-,
Is Return,退货？,
Issue Credit Note,发行信用票据,
Return Against Delivery Note,基于销售出货单退货,
Customer's Purchase Order No,客户的采购订单号,
Billing Address Name,帐单地址名称,
Required only for sample item.,只针对样品物料。,
"If you have created a standard template in Sales Taxes and Charges Template, select one and click on the button below.",如果您已经创建了销项税/费标准模板，选择一个，然后点击下面的按钮。,
In Words will be visible once you save the Delivery Note.,大写金额将在销售出货单保存后显示。,
In Words (Export) will be visible once you save the Delivery Note.,大写金额（出口）将在销售出货单保存后显示。,
Transporter Info,承运商信息,
Driver Name,司机姓名,
Track this Delivery Note against any Project,对任何工程跟踪此销售出货单,
Inter Company Reference,公司间参考,
Print Without Amount,打印量不,
% Installed,％已安装,
% of materials delivered against this Delivery Note,此出货单% 的材料已交货。,
Installation Status,安装状态,
Excise Page Number,Excise页码,
Instructions,说明,
From Warehouse,源仓库,
Against Sales Order,针对的销售订单,
Against Sales Order Item,针对的销售订单项,
Against Sales Invoice,针对的销售费用清单,
Against Sales Invoice Item,针对的销售费用清单项,
Available Batch Qty at From Warehouse,源仓库可用的批次数量,
Available Qty at From Warehouse,源仓库可用数量,
Delivery Settings,交货设置,
Dispatch Settings,发货设置,
Dispatch Notification Template,派遣通知模板,
Dispatch Notification Attachment,发货通知附件,
Leave blank to use the standard Delivery Note format,留空以使用标准的交货单格式,
Send with Attachment,发送附件,
Delay between Delivery Stops,交货停止之间的延迟,
Delivery Stop,交付停止,
Lock,锁,
Visited,已访问,
Order Information,订单信息,
Contact Information,联系信息,
Email sent to,电子邮件发送给,
Dispatch Information,发货信息,
Estimated Arrival,预计抵达时间,
MAT-DT-.YYYY.-,MAT-DT-.YYYY.-,
Initial Email Notification Sent,初始电子邮件通知已发送,
Delivery Details,交货细节,
Driver Email,司机电邮,
Driver Address,司机地址,
Total Estimated Distance,总估计距离,
Distance UOM,距离UOM,
Departure Time,出发时间,
Delivery Stops,交货站点,
Calculate Estimated Arrival Times,计算预计到达时间,
Use Google Maps Direction API to calculate estimated arrival times,使用Google Maps Direction API计算预计到达时间,
Optimize Route,优化路线,
Use Google Maps Direction API to optimize route,使用Google Maps Direction API优化路线,
In Transit,运输中,
Fulfillment User,履行用户,
"A Product or a Service that is bought, sold or kept in stock.",库存中已被采购，销售或保留的一个产品或服务,
STO-ITEM-.YYYY.-,STO-ITEM-.YYYY.-,
Variant Of,的变体,
"If item is a variant of another item then description, image, pricing, taxes etc will be set from the template unless explicitly specified",如果物料为另一物料的变体，那么它的描述，图片，价格，税率等将从模板自动设置。你也可以手动设置。,
Is Item from Hub,是来自集线器的组件,
Default Unit of Measure,默认计量单位,
Maintain Stock,管理库存,
Standard Selling Rate,标准售价,
Auto Create Assets on Purchase,自动创建购买资产,
Asset Naming Series,资产名录,
Over Delivery/Receipt Allowance (%),超过交货/收据津贴（％）,
Barcodes,条形码,
Shelf Life In Days,保质期天数,
End of Life,寿命结束,
Default Material Request Type,默认物料申请类型,
Valuation Method,估值方法,
FIFO,先进先出,
Moving Average,移动平均,
Warranty Period (in days),保修期限（天数）,
Auto re-order,自动重订货,
Reorder level based on Warehouse,根据仓库订货点水平,
Will also apply for variants unless overrridden,除非手动指定，否则会同时应用于变体,
Units of Measure,计量单位,
Will also apply for variants,会同时应用于变体,
Serial Nos and Batches,序列号和批号,
Has Batch No,有批号,
Automatically Create New Batch,自动创建新批,
Batch Number Series,批号系列,
"Example: ABCD.#####. If series is set and Batch No is not mentioned in transactions, then automatic batch number will be created based on this series. If you always want to explicitly mention Batch No for this item, leave this blank. Note: this setting will take priority over the Naming Series Prefix in Stock Settings.",例如：ABCD。#####。如果系列已设置且交易中未输入批号，则将根据此系列创建自动批号。如果您始终想要明确提及此料品的批号，请将此留为空白。注意：此设置将优先于库存设置中的名录前缀。,
Has Expiry Date,有过期日期,
Retain Sample,保留样品,
Max Sample Quantity,最大样品量,
Maximum sample quantity that can be retained,可以保留的最大样品数量,
Has Serial No,有序列号,
Serial Number Series,序列号系列,
"Example: ABCD.#####\nIf series is set and Serial No is not mentioned in transactions, then automatic serial number will be created based on this series. If you always want to explicitly mention Serial Nos for this item. leave this blank.",例如：ABCD ##### \n如果设置了序列但是没有在交易中输入序列号，那么系统会根据序列自动生产序列号。如果要强制手动输入序列号，请不要勾选此项。,
Variants,变量,
Has Variants,有变体,
"If this item has variants, then it cannot be selected in sales orders etc.",如果此物料为模板物料（有变体），就不能直接在销售订单中使用，请使用变体物料,
Variant Based On,变量基于,
Item Attribute,物料属性,
"Sales, Purchase, Accounting Defaults",销售，采购，会计违约,
Item Defaults,物料默认值,
"Purchase, Replenishment Details",采购，补货细节,
Is Purchase Item,可采购？,
Default Purchase Unit of Measure,默认采购单位,
Minimum Order Qty,最小起订量,
Minimum quantity should be as per Stock UOM,最小数量应按照库存单位,
Average time taken by the supplier to deliver,供应商交货时间的平均值,
Is Customer Provided Item,客户提供物品,
Delivered by Supplier (Drop Ship),由供应商交货（直接发运）,
Supplier Items,供应商物料,
Foreign Trade Details,外贸详细,
Country of Origin,原产地,
Sales Details,销售信息,
Default Sales Unit of Measure,默认销售单位,
Is Sales Item,可销售？,
Max Discount (%),最大折扣(%),
No of Months,没有几个月,
Customer Items,客户物料,
Inspection Criteria,检验标准,
Inspection Required before Purchase,需进行来料检验,
Inspection Required before Delivery,需进行出货检验,
Default BOM,默认的BOM,
Supply Raw Materials for Purchase,采购时提供原材料给供应商,
If subcontracted to a vendor,针对外包给供应商的情况,
Customer Code,客户代码,
Default Item Manufacturer,默认项目制造商,
Default Manufacturer Part No,默认制造商零件号,
Show in Website (Variant),在网站上展示（变体）,
Items with higher weightage will be shown higher,具有较高权重的物料会优先显示在清单的上面,
Show a slideshow at the top of the page,在页面顶部显示幻灯片,
Website Image,网站图片,
Website Warehouse,网站仓库,
"Show ""In Stock"" or ""Not in Stock"" based on stock available in this warehouse.",根据此仓库显示“有库存”或“无库存”状态。,
Website Item Groups,网站物料组,
List this Item in multiple groups on the website.,在网站上的多个组中显示此物料,
Copy From Item Group,从物料组复制,
Website Content,网站内容,
You can use any valid Bootstrap 4 markup in this field. It will be shown on your Item Page.,您可以在此字段中使用任何有效的Bootstrap 4标记。它将显示在您的项目页面上。,
Total Projected Qty,预计总数量,
Hub Publishing Details,集线器发布细节,
Publish in Hub,在集散中心发布,
Publish Item to hub.erpnext.com,发布项目到hub.erpnext.com,
Hub Category to Publish,集线器类别的发布,
Hub Warehouse,中心仓库,
"Publish ""In Stock"" or ""Not in Stock"" on Hub based on stock available in this warehouse.",基于仓库中的库存，在Hub上发布“库存”或“不在库存”。,
Synced With Hub,与Hub同步,
Item Alternative,替换物料,
Alternative Item Code,替代物料代码,
Two-way,双向,
Alternative Item Name,替代物料名称,
Attribute Name,属性名称,
Numeric Values,数字值,
From Range,从范围,
Increment,增量,
To Range,为了范围,
Item Attribute Values,物料属性值,
Item Attribute Value,物料属性值,
Attribute Value,属性值,
Abbreviation,缩写,
"This will be appended to the Item Code of the variant. For example, if your abbreviation is ""SM"", and the item code is ""T-SHIRT"", the item code of the variant will be ""T-SHIRT-SM""",这将追加到物料代码变量。例如，如果你的英文缩写为“SM”，而该物料代码是“T-SHIRT”，该变式的物料代码将是“T-SHIRT-SM”,
Item Barcode,物料条码,
Barcode Type,条码类型,
EAN,EAN,
UPC-A,UPC-A,
Item Customer Detail,物料客户信息,
"For the convenience of customers, these codes can be used in print formats like Invoices and Delivery Notes",为方便客户，这些代码可以在打印格式(如费用清单和销售出货单)中使用,
Ref Code,参考代码,
Item Default,物料默认值,
Purchase Defaults,采购默认值,
Default Buying Cost Center,默认采购成本中心,
Default Supplier,默认供应商,
Default Expense Account,默认费用科目,
Sales Defaults,销售默认值,
Default Selling Cost Center,默认销售成本中心,
Item Manufacturer,产品制造商,
Item Price,物料价格,
Packing Unit,包装单位,
Quantity  that must be bought or sold per UOM,每个UOM必须购买或出售的数量,
Item Quality Inspection Parameter,物料质量检验参数,
Acceptance Criteria,验收标准,
Item Reorder,物料重新排序,
Check in (group),检查（组）,
Request for,需求目的,
Re-order Level,重订货水平,
Re-order Qty,再订货数量,
Item Supplier,物料供应商,
Item Variant,物料变体,
Item Variant Attribute,产品规格属性,
Do not update variants on save,不要在保存时更新变体,
Fields will be copied over only at time of creation.,字段将仅在创建时复制。,
Allow Rename Attribute Value,允许重命名属性值,
Rename Attribute Value in Item Attribute.,在物料属性中重命名属性值。,
Copy Fields to Variant,将字段复制到变量,
Item Website Specification,网站上显示的物料详细规格,
Table for Item that will be shown in Web Site,将在网站显示的物料表,
Landed Cost Item,到岸成本物料,
Receipt Document Type,收据凭证类型,
Receipt Document,收到文件,
Applicable Charges,适用费用,
Purchase Receipt Item,采购入库项,
Landed Cost Purchase Receipt,到岸成本采购收货单,
Landed Cost Taxes and Charges,到岸成本税费,
Landed Cost Voucher,到岸成本凭证,
MAT-LCV-.YYYY.-,MAT-LCV-.YYYY.-,
Purchase Receipts,采购收货,
Purchase Receipt Items,采购收据项,
Get Items From Purchase Receipts,从采购收货单获取物料,
Distribute Charges Based On,费用分配基于,
Landed Cost Help,到岸成本帮助,
Manufacturers used in Items,在项目中使用制造商,
Limited to 12 characters,限12个字符,
MAT-MR-.YYYY.-,MAT-MR-.YYYY.-,
Partially Ordered,部分订购,
Transferred,已转移,
% Ordered,%  已排序,
Terms and Conditions Content,条款和条件内容,
Quantity and Warehouse,数量和仓库,
Lead Time Date,交货时间日期,
Min Order Qty,最小订货量,
Packed Item,盒装产品,
To Warehouse (Optional),至仓库（可选）,
Actual Batch Quantity,实际批次数量,
Prevdoc DocType,Prevdoc的文件类型,
Parent Detail docname,上级详细文件名,
"Generate packing slips for packages to be delivered. Used to notify package number, package contents and its weight.",生成要发货物料的装箱单，包括包号，内容和重量。,
Indicates that the package is a part of this delivery (Only Draft),表示该打包是这个交付的一部分（仅草稿）,
MAT-PAC-.YYYY.-,MAT-PAC-.YYYY.-,
From Package No.,起始包号,
Identification of the package for the delivery (for print),打包物料的标志（用于打印）,
To Package No.,以包号,
If more than one package of the same type (for print),如果有多个同样类型的打包（用于打印）,
Package Weight Details,包装重量信息,
The net weight of this package. (calculated automatically as sum of net weight of items),此包装的净重。（根据内容物料的净重自动计算）,
Net Weight UOM,净重计量单位,
Gross Weight,毛重,
The gross weight of the package. Usually net weight + packaging material weight. (for print),包装的毛重。通常是净重+包装材料的重量。 （用于打印）,
Gross Weight UOM,毛重计量单位,
Packing Slip Item,装箱单项,
DN Detail,销售出货单信息,
STO-PICK-.YYYY.-,STO-PICK-.YYYY.-,
Material Transfer for Manufacture,材料移送用于制造,
Qty of raw materials will be decided based on the qty of the Finished Goods Item,原材料的数量将根据成品的数量来确定,
Parent Warehouse,上级仓库,
Items under this warehouse will be suggested,将建议此仓库下的项目,
Get Item Locations,获取物品位置,
Item Locations,物品位置,
Pick List Item,选择清单项目,
Picked Qty,挑选数量,
Price List Master,价格清单主数据,
Price List Name,价格列表名称,
Price Not UOM Dependent,价格不是UOM依赖,
Applicable for Countries,适用于国家,
Price List Country,价格清单国家,
MAT-PRE-.YYYY.-,MAT-PRE-.YYYY.-,
Supplier Delivery Note,供应商送货单,
Time at which materials were received,收到物料的时间,
Return Against Purchase Receipt,基于外购收货退货,
Rate at which supplier's currency is converted to company's base currency,供应商的货币转换为公司的本币后的单价,
Sets 'Accepted Warehouse' in each row of the items table.,在项目表的每一行中设置“可接受的仓库”。,
Sets 'Rejected Warehouse' in each row of the items table.,在项目表的每一行中设置“拒绝仓库”。,
Raw Materials Consumed,原材料消耗,
Get Current Stock,获取当前库存,
Consumed Items,消耗品,
Add / Edit Taxes and Charges,添加/编辑税金及费用,
Auto Repeat Detail,自动重复细节,
Transporter Details,运输信息,
Vehicle Number,车号,
Vehicle Date,车辆日期,
Received and Accepted,收到并接受,
Accepted Quantity,已接受数量,
Rejected Quantity,拒收数量,
Accepted Qty as per Stock UOM,根据库存单位数量的可接受数量,
Sample Quantity,样品数量,
Rate and Amount,单价及小计,
MAT-QA-.YYYY.-,MAT-QA-.YYYY.-,
Report Date,报表日期,
Inspection Type,检验类型,
Item Serial No,物料序列号,
Sample Size,样本大小,
Inspected By,验货人,
Readings,检验结果,
Quality Inspection Reading,质量检验报表,
Reading 1,检验结果1,
Reading 2,检验结果2,
Reading 3,检验结果3,
Reading 4,检验结果4,
Reading 5,检验结果5,
Reading 6,检验结果6,
Reading 7,检验结果7,
Reading 8,检验结果8,
Reading 9,检验结果9,
Reading 10,检验结果10,
Quality Inspection Template Name,质量检验模板名称,
Quick Stock Balance,快速库存平衡,
Available Quantity,可用数量,
Distinct unit of an Item,物料的不同的计量单位,
Warehouse can only be changed via Stock Entry / Delivery Note / Purchase Receipt,仓库信息只能通过手工库存移动/销售出货/采购收货来修改,
Purchase / Manufacture Details,采购/制造详细信息,
Creation Document Type,创建文件类型,
Creation Document No,创建文档编号,
Creation Date,创建日期,
Creation Time,创建时间,
Asset Details,资产信息,
Asset Status,资产状态,
Delivery Document Type,交货文档类型,
Delivery Document No,交货文档编号,
Delivery Time,交货时间,
Invoice Details,费用清单信息,
Warranty / AMC Details,保修/ 年度保养合同信息,
Warranty Expiry Date,保修到期日,
AMC Expiry Date,AMC到期时间,
Under Warranty,在保修期内,
Out of Warranty,超出保修期,
Under AMC,在年度保养合同中,
Out of AMC,出资产管理公司,
Warranty Period (Days),保修期限（天数）,
Serial No Details,序列号信息,
MAT-STE-.YYYY.-,MAT-STE-.YYYY.-,
Stock Entry Type,库存进入类型,
Stock Entry (Outward GIT),库存进入（外向GIT）,
Material Consumption for Manufacture,生产所需的材料消耗,
Repack,包装,
Send to Subcontractor,发送给分包商,
Delivery Note No,销售出货单编号,
Sales Invoice No,销售发票编号,
Purchase Receipt No,采购收据号码,
Inspection Required,需要检验,
From BOM,来自物料清单,
For Quantity,数量,
As per Stock UOM,按库存的基础单位,
Including items for sub assemblies,包括下层组件物料,
Default Source Warehouse,默认源仓库,
Source Warehouse Address,来源仓库地址,
Default Target Warehouse,默认目标仓库,
Target Warehouse Address,目标仓库地址,
Update Rate and Availability,更新存货评估价和可用数量,
Total Incoming Value,总收到金额,
Total Outgoing Value,总待付款价值,
Total Value Difference (Out - In),总价值差（输出 - ）,
Additional Costs,额外费用,
Total Additional Costs,总额外费用,
Customer or Supplier Details,客户或供应商详细信息,
Per Transferred,每次转移,
Stock Entry Detail,手工库存移动信息,
Basic Rate (as per Stock UOM),库存评估价（按库存计量单位）,
Basic Amount,基本金额,
Additional Cost,额外费用,
Serial No / Batch,序列号/批次,
BOM No. for a Finished Good Item,成品物料的物料清单编号,
Material Request used to make this Stock Entry,创建此手工库存移动的材料申请,
Subcontracted Item,外包物料,
Against Stock Entry,反对库存进入,
Stock Entry Child,库存入境儿童,
PO Supplied Item,PO提供的物品,
Reference Purchase Receipt,参考购买收据,
Stock Ledger Entry,库存分类帐分录,
Outgoing Rate,出库库存评估价,
Actual Qty After Transaction,交易过帐后实际数量,
Stock Value Difference,库存值差异,
Stock Queue (FIFO),库存队列(先进先出),
Is Cancelled,是否注销,
Stock Reconciliation,库存盘点,
This tool helps you to update or fix the quantity and valuation of stock in the system. It is typically used to synchronise the system values and what actually exists in your warehouses.,此工具可帮助您更新或修复系统中的库存数量和价值。它通常被用于同步系统值和实际存在于您的仓库的库存。,
MAT-RECO-.YYYY.-,MAT-RECO-.YYYY.-,
Reconciliation JSON,基于JSON格式对账,
Stock Reconciliation Item,库存盘点物料,
Before reconciliation,在对账前,
Current Serial No,目前的序列号,
Current Valuation Rate,当前评估价,
Current Amount,电流量,
Quantity Difference,数量差异,
Amount Difference,金额差异,
Item Naming By,物料命名字段,
Default Item Group,默认物料群组,
Default Stock UOM,默认库存计量单位,
Sample Retention Warehouse,样品保留仓库,
Default Valuation Method,默认估值方法,
Show Barcode Field,显示条形码字段,
Convert Item Description to Clean HTML,将项目描述转换为清理HTML,
Allow Negative Stock,允许负库存,
Automatically Set Serial Nos based on FIFO,自动设置序列号的基础上FIFO,
Auto Material Request,自动材料需求,
Inter Warehouse Transfer Settings,仓库间转移设置,
Freeze Stock Entries,冻结库存移动凭证,
Stock Frozen Upto,库存冻结止,
Batch Identification,批次标识,
Use Naming Series,使用名录,
Naming Series Prefix,名录前缀,
UOM Category,UOM类别,
UOM Conversion Detail,计量单位换算信息,
Variant Field,变量字段,
A logical Warehouse against which stock entries are made.,库存进项记录对应的逻辑仓库。,
Warehouse Detail,仓库详细信息,
Warehouse Name,仓库名称,
Warehouse Contact Info,仓库联系方式,
PIN,销,
ISS-.YYYY.-,ISS-.YYYY.-,
Raised By (Email),提出（电子邮件）,
Issue Type,发行类型,
Issue Split From,问题拆分,
Service Level,服务水平,
Response By,回应,
Response By Variance,按方差回应,
Ongoing,不断的,
Resolution By,分辨率,
Resolution By Variance,按方差分辨率,
Service Level Agreement Creation,服务水平协议创建,
First Responded On,首次回复时间,
Resolution Details,详细解析,
Opening Date,问题提交日期,
Opening Time,开放时间,
Resolution Date,决议日期,
Via Customer Portal,通过客户门户,
Support Team,支持团队,
Issue Priority,问题优先,
Service Day,服务日,
Workday,劳动日,
Default Priority,默认优先级,
Priorities,优先级,
Support Hours,支持小时,
Support and Resolution,支持和解决,
Default Service Level Agreement,默认服务水平协议,
Entity,实体,
Agreement Details,协议细节,
Response and Resolution Time,响应和解决时间,
Service Level Priority,服务水平优先,
Resolution Time,解决时间,
Support Search Source,支持搜索源,
Source Type,来源类型,
Query Route String,查询路由字符串,
Search Term Param Name,搜索字词Param Name,
Response Options,响应选项,
Response Result Key Path,响应结果关键路径,
Post Route String,邮政路线字符串,
Post Route Key List,发布路由密钥列表,
Post Title Key,帖子标题密钥,
Post Description Key,发布说明密钥,
Link Options,链接选项,
Source DocType,源文件类型,
Result Title Field,结果标题字段,
Result Preview Field,结果预览字段,
Result Route Field,结果路由字段,
Service Level Agreements,服务等级协定,
Track Service Level Agreement,跟踪服务水平协议,
Allow Resetting Service Level Agreement,允许重置服务水平协议,
Close Issue After Days,关闭问题天后,
Auto close Issue after 7 days,7天之后自动关闭问题,
Support Portal,支持门户,
Get Started Sections,入门部分,
Show Latest Forum Posts,显示最新的论坛帖子,
Forum Posts,论坛帖子,
Forum URL,论坛URL,
Get Latest Query,获取最新查询,
Response Key List,响应密钥列表,
Post Route Key,邮政路线密钥,
Search APIs,搜索API,
SER-WRN-.YYYY.-,SER-WRN-.YYYY.-,
Issue Date,问题日期,
Item and Warranty Details,物料和保修,
Warranty / AMC Status,保修/ 年度保养合同状态,
Resolved By,议决,
Service Address,服务地址,
If different than customer address,如果客户地址不同的话,
Raised By,提出,
From Company,源公司,
Rename Tool,重命名工具,
Utilities,公用事业,
Type of document to rename.,需重命名的文件类型。,
File to Rename,文件重命名,
"Attach .csv file with two columns, one for the old name and one for the new name",附加.csv文件有两列，一为旧名称，一个用于新名称,
Rename Log,重命名日志,
SMS Log,短信日志,
Sender Name,发件人名称,
Sent On,发送日期,
No of Requested SMS,请求短信数量,
Requested Numbers,请求号码,
No of Sent SMS,发送短信数量,
Sent To,发给,
Absent Student Report,缺勤学生报表,
Assessment Plan Status,评估计划状态,
Asset Depreciation Ledger,资产折旧总帐,
Asset Depreciations and Balances,资产折旧和余额,
Available Stock for Packing Items,库存可用打包物料,
Bank Clearance Summary,银行清帐汇总报表,
Batch Item Expiry Status,物料批号到期状态,
Batch-Wise Balance History,物料批号结余数量历史记录,
BOM Explorer,BOM Explorer,
BOM Search,物料清单搜索,
BOM Stock Calculated,物料清单库存计算,
BOM Variance Report,物料清单差异报表,
Campaign Efficiency,促销活动效率,
Cash Flow,现金流量表,
Completed Work Orders,完成的工单,
To Produce,以生产,
Produced,生产,
Consolidated Financial Statement,合并财务报表,
Course wise Assessment Report,课程明智的评估报表,
Customer Acquisition and Loyalty,客户获得和忠诚度,
Customer Credit Balance,客户贷方余额,
Customer Ledger Summary,客户分类帐摘要,
Customer-wise Item Price,客户明智的物品价格,
Customers Without Any Sales Transactions,没有任何销售交易的客户,
Daily Timesheet Summary,每日工时单摘要,
DATEV,DATEV,
Delayed Item Report,延迟物品报告,
Delayed Order Report,延迟订单报告,
Delivered Items To Be Billed,待开费用清单已出货物料,
Delivery Note Trends,销售出货趋势,
Electronic Invoice Register,电子发票登记,
Employee Billing Summary,员工账单摘要,
Employee Birthday,员工生日,
Employee Information,员工资料,
Employee Leave Balance,员工休假余额（天数）,
Employee Leave Balance Summary,员工休假余额摘要,
Eway Bill,Eway Bill,
Expiring Memberships,即将到期的会员,
Fichier des Ecritures Comptables [FEC],Fichier des Ecritures Comptables [FEC],
Final Assessment Grades,最终评估等级,
Fixed Asset Register,固定资产登记册,
Gross and Net Profit Report,毛利润和净利润报告,
GST Itemised Purchase Register,GST物料采购台帐,
GST Itemised Sales Register,消费税商品销售登记册,
GST Purchase Register,采购台账（GST),
GST Sales Register,销售台账（GST),
GSTR-1,GSTR-1,
GSTR-2,GSTR-2,
Hotel Room Occupancy,酒店客房入住率,
HSN-wise-summary of outward supplies,HSN明智的向外供应摘要,
Inactive Customers,非活跃客户,
Inactive Sales Items,非活动销售项目,
IRS 1099,IRS 1099,
Issued Items Against Work Order,发料到工单,
Projected Quantity as Source,基于预期可用库存,
Item Balance (Simple),物料余额（简单）,
Item Price Stock,物料价格与库存,
Item Prices,物料价格,
Item Shortage Report,缺料报表,
Item Variant Details,物料变体详细信息,
Item-wise Price List Rate,物料价格清单单价,
Item-wise Purchase History,物料采购历史,
Item-wise Purchase Register,物料采购台帐,
Item-wise Sales History,产品销售历史记录,
Item-wise Sales Register,物料销售台帐,
Items To Be Requested,待申请物料,
Reserved,保留的,
Itemwise Recommended Reorder Level,建议的物料重订货点,
Lead Details,商机信息,
Lead Owner Efficiency,线索负责人效率,
Lost Opportunity,失去的机会,
Maintenance Schedules,维护计划,
Material Requests for which Supplier Quotations are not created,无供应商报价的材料申请,
Open Work Orders,打开工单,
Qty to Deliver,交付数量,
Patient Appointment Analytics,患者预约分析,
Payment Period Based On Invoice Date,基于费用清单日期的付款期间,
Pending SO Items For Purchase Request,针对采购申请的待处理销售订单行,
Procurement Tracker,采购跟踪器,
Product Bundle Balance,产品包余额,
Production Analytics,生产Analytics（分析）,
Profit and Loss Statement,损益表,
Profitability Analysis,盈利能力分析,
Project Billing Summary,项目开票摘要,
Project wise Stock Tracking,项目明智的库存跟踪,
Project wise Stock Tracking ,工程级库存追踪,
Prospects Engaged But Not Converted,展望未成熟,
Purchase Analytics,采购分析,
Purchase Invoice Trends,采购费用清单趋势,
Qty to Receive,接收数量,
Received Qty Amount,收到的数量,
Billed Qty,开票数量,
Purchase Order Trends,采购订单趋势,
Purchase Receipt Trends,采购收货趋势,
Purchase Register,采购台帐,
Quotation Trends,报价趋势,
Received Items To Be Billed,待开费用清单已收货物料,
Qty to Order,待下单数量,
Requested Items To Be Transferred,已申请待移转物料,
Qty to Transfer,转移数量,
Sales Analytics,销售分析,
Sales Invoice Trends,销售费用清单趋势,
Sales Order Trends,销售订单趋势,
Sales Partner Commission Summary,销售合作伙伴佣金摘要,
Sales Partner Target Variance based on Item Group,销售合作伙伴基于项目组的目标差异,
Sales Partner Transaction Summary,销售合作伙伴交易摘要,
Sales Partners Commission,销售合作伙伴佣金,
Invoiced Amount (Exclusive Tax),发票金额（不含税）,
Average Commission Rate,平均佣金率,
Sales Payment Summary,销售付款摘要,
Sales Person Commission Summary,销售人员委员会摘要,
Sales Person Target Variance Based On Item Group,基于项目组的销售人员目标差异,
Sales Person-wise Transaction Summary,各销售人员业务汇总,
Sales Register,销售台帐,
Serial No Service Contract Expiry,序列号/年度保养合同过期,
Serial No Status,序列号状态,
Serial No Warranty Expiry,序列号/保修到期,
Stock Ageing,库存账龄,
Stock and Account Value Comparison,库存和账户价值比较,
Stock Projected Qty,预期可用库存,
Student and Guardian Contact Details,学生和监护人联系方式,
Student Batch-Wise Attendance,学生按批考勤,
Student Fee Collection,学生费征收,
Student Monthly Attendance Sheet,学生每月考勤表,
Subcontracted Item To Be Received,要转包的分包物品,
Subcontracted Raw Materials To Be Transferred,分包原材料将被转让,
Supplier Ledger Summary,供应商分类帐摘要,
Supplier-Wise Sales Analytics,供应商直出客户的贸易业务销售分析,
Support Hour Distribution,支持小时分配,
TDS Computation Summary,TDS计算摘要,
TDS Payable Monthly,TDS应付月度,
Territory Target Variance Based On Item Group,基于项目组的地域目标差异,
Territory-wise Sales,区域销售,
Total Stock Summary,总库存总结,
Trial Balance,试算平衡表,
Trial Balance (Simple),试算结余（简单）,
Trial Balance for Party,往来单位试算平衡表,
Warehouse wise Item Balance Age and Value,仓库级物料库龄和金额报表,
Work Order Stock Report,工单库存报表,
Work Orders in Progress,工单正在进行中,
Automatically Process Deferred Accounting Entry,自动处理递延会计分录,
Bank Clearance,银行清算,
Bank Clearance Detail,银行清算详细信息,
Update Cost Center Name / Number,更新成本中心名称/编号,
Journal Entry Template,日记条目模板,
Template Title,模板标题,
Journal Entry Type,日记帐分录类型,
Journal Entry Template Account,日记帐分录模板帐户,
Process Deferred Accounting,递延会计处理,
Manual entry cannot be created! Disable automatic entry for deferred accounting in accounts settings and try again,无法创建手动输入！禁用自动输入帐户设置中的递延会计，然后重试,
End date cannot be before start date,结束日期不能早于开始日期,
Total Counts Targeted,目标总数,
Total Counts Completed,总计数已完成,
Counts Targeted: {0},目标计数：{0},
Material Request Warehouse,物料请求仓库,
Select warehouse for material requests,选择物料需求仓库,
Transfer Materials For Warehouse {0},仓库{0}的转移物料,
Production Plan Material Request Warehouse,生产计划物料申请仓库,
Sets 'Source Warehouse' in each row of the items table.,在项目表的每一行中设置“源仓库”。,
Sets 'Target Warehouse' in each row of the items table.,在项目表的每一行中设置“目标仓库”。,
Show Cancelled Entries,显示已取消的条目,
Backdated Stock Entry,回溯的库存输入,
Row #{}: Currency of {} - {} doesn't matches company currency.,第＃{}行：{}-{}的货币与公司货币不符。,
{} Assets created for {},{}为{}创建的资产,
{0} Number {1} is already used in {2} {3},{0}数字{1}已在{2} {3}中使用,
Update Bank Clearance Dates,更新银行清算日期,
Healthcare Practitioner: ,医疗保健从业者：,
Lab Test Conducted: ,进行的实验室测试：,
Lab Test Event: ,实验室测试事件：,
Lab Test Result: ,实验室测试结果：,
Clinical Procedure conducted: ,进行的临床程序：,
Therapy Session Charges: {0},疗程费用：{0},
Therapy: ,治疗：,
Therapy Plan: ,治疗计划：,
Total Counts Targeted: ,目标总数：,
Total Counts Completed: ,已完成的总数：,
Is Mandatory,是强制性的,
Service Received But Not Billed,服务已收到但未计费,
Deferred Accounting Settings,递延会计设置,
Book Deferred Entries Based On,基于的图书递延分录,
Days,天,
Months,月数,
Book Deferred Entries Via Journal Entry,通过日记帐分录递延分录,
Submit Journal Entries,提交日记帐分录,
If this is unchecked Journal Entries will be saved in a Draft state and will have to be submitted manually,如果未选中，则日记帐分录将保存为草稿状态，并且必须手动提交,
Enable Distributed Cost Center,启用分布式成本中心,
Distributed Cost Center,分布式成本中心,
Dunning,催款,
DUNN-.MM.-.YY.-,邓恩-.MM .-。YY.-,
Overdue Days,逾期,
Dunning Type,催款类型,
Dunning Fee,催款费,
Dunning Amount,催款金额,
Resolved,解决,
Unresolved,未解决,
Printing Setting,打印设定,
Body Text,主体,
Closing Text,结束语,
Resolve,解决,
Dunning Letter Text,催款信文字,
Is Default Language,是默认语言,
Letter or Email Body Text,信件或电子邮件正文,
Letter or Email Closing Text,信件或电子邮件结束文字,
Body and Closing Text Help,正文和结束语帮助,
Overdue Interval,逾期间隔,
Dunning Letter,催款信,
"This section allows the user to set the Body and Closing text of the Dunning Letter for the Dunning Type based on language, which can be used in Print.",该部分允许用户根据语言设置催款类型的催款信的正文和关闭文本，可以在打印中使用。,
Reference Detail No,参考详细信息,
Custom Remarks,自订备注,
Please select a Company first.,请先选择一个公司。,
"Row #{0}: Reference Document Type must be one of Sales Order, Sales Invoice, Journal Entry or Dunning",行＃{0}：参考单据类型必须是销售订单，销售发票，日记帐分录或催款中的一种,
POS Closing Entry,POS结账单,
POS Opening Entry,POS入口条目,
POS Transactions,POS交易,
POS Closing Entry Detail,POS关闭输入明细,
Opening Amount,期初金额,
Closing Amount,结算金额,
POS Closing Entry Taxes,POS结关进项税,
POS Invoice,POS发票,
ACC-PSINV-.YYYY.-,ACC-PSINV-.YYYY.-,
Consolidated Sales Invoice,合并销售发票,
Return Against POS Invoice,退回POS发票,
Consolidated,合并,
POS Invoice Item,POS发票项目,
POS Invoice Merge Log,POS发票合并日志,
POS Invoices,POS发票,
Consolidated Credit Note,合并贷方通知单,
POS Invoice Reference,POS发票参考,
Set Posting Date,设定过帐日期,
Opening Balance Details,期初余额明细,
POS Opening Entry Detail,POS入口条目详细信息,
POS Payment Method,POS付款方式,
Payment Methods,支付方式,
Process Statement Of Accounts,流程帐目表,
General Ledger Filters,总帐过滤器,
Customers,顾客,
Select Customers By,选择客户依据,
Fetch Customers,获取客户,
Send To Primary Contact,发送给主要联系人,
Print Preferences,打印首选项,
Include Ageing Summary,包括帐龄摘要,
Enable Auto Email,启用自动电子邮件,
Filter Duration (Months),筛选时间（月）,
CC To,抄送,
Help Text,帮助文字,
Emails Queued,电子邮件已排队,
Process Statement Of Accounts Customer,流程帐目客户,
Billing Email,帐单电邮,
Primary Contact Email,主要联系人电子邮件,
PSOA Cost Center,PSOA成本中心,
PSOA Project,PSOA项目,
ACC-PINV-RET-.YYYY.-,ACC-PINV-RET-.YYYY.-,
Supplier GSTIN,供应商GSTIN,
Place of Supply,供货地点,
Select Billing Address,选择帐单地址,
GST Details,GST详细信息,
GST Category,消费税类别,
Registered Regular,注册普通,
Registered Composition,注册组成,
Unregistered,未注册,
SEZ,经济特区,
Overseas,海外,
UIN Holders,UIN持有人,
With Payment of Tax,交税,
Without Payment of Tax,免税,
Invoice Copy,发票副本,
Original for Recipient,收件人原件,
Duplicate for Transporter,复制转运蛋白,
Duplicate for Supplier,供方重复,
Triplicate for Supplier,供应商一式三份,
Reverse Charge,反向充电,
Y,ÿ,
N,ñ,
E-commerce GSTIN,电子商务GSTIN,
Reason For Issuing document,签发文件的原因,
01-Sales Return,01销售退货,
02-Post Sale Discount,02-售后折扣,
03-Deficiency in services,03-服务不足,
04-Correction in Invoice,04-发票更正,
05-Change in POS,05-更改POS,
06-Finalization of Provisional assessment,06-临时评估完成,
07-Others,07-其他,
Eligibility For ITC,ITC资格,
Input Service Distributor,输入服务分销商,
Import Of Service,服务导入,
Import Of Capital Goods,资本货物进口,
Ineligible,不合格,
All Other ITC,所有其他ITC,
Availed ITC Integrated Tax,ITC综合税,
Availed ITC Central Tax,ITC中央税可用,
Availed ITC State/UT Tax,ITC州/ UT可用税,
Availed ITC Cess,ITC Cess可用,
Is Nil Rated or Exempted,零分或免税,
Is Non GST,是非消费税,
ACC-SINV-RET-.YYYY.-,ACC-SINV-RET-.YYYY.-,
E-Way Bill No.,电子通单号,
Is Consolidated,已合并,
Billing Address GSTIN,帐单地址GSTIN,
Customer GSTIN,客户GSTIN,
GST Transporter ID,GST运输者ID,
Distance (in km),距离（公里）,
Road,路,
Air,空气,
Rail,轨,
Ship,船,
GST Vehicle Type,GST车辆类型,
Over Dimensional Cargo (ODC),超尺寸货物（ODC）,
Consumer,消费者,
Deemed Export,被视为出口,
Port Code,港口代码,
 Shipping Bill Number,发货单号,
Shipping Bill Date,发货单日期,
Subscription End Date,订阅结束日期,
Follow Calendar Months,跟随日历月,
If this is checked subsequent new invoices will be created on calendar  month and quarter start dates irrespective of current invoice start date,如果选中此选项，则无论当前发票的开始日期如何，都将在日历月和季度开始日期创建后续的新发票。,
Generate New Invoices Past Due Date,生成过期的新发票,
New invoices will be generated as per schedule even if current invoices are unpaid or past due date,即使当前发票未付款或过期，也会按照计划生成新发票,
Document Type ,文件类型,
Subscription Price Based On,订阅价格基于,
Fixed Rate,固定利率,
Based On Price List,根据价目表,
Monthly Rate,月费,
Cancel Subscription After Grace Period,宽限期后取消订阅,
Source State,源状态,
Is Inter State,是州际,
Purchase Details,采购明细,
Depreciation Posting Date,折旧过帐日期,
"By default, the Supplier Name is set as per the Supplier Name entered. If you want Suppliers to be named by a  ",默认情况下，根据输入的供应商名称设置供应商名称。如果您希望供应商以,
 choose the 'Naming Series' option.,选择“命名系列”选项。,
Configure the default Price List when creating a new Purchase transaction. Item prices will be fetched from this Price List.,在创建新的采购交易时配置默认的价目表。项目价格将从此价格表中获取。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Purchase Invoice or Receipt without creating a Purchase Order first. This configuration can be overridden for a particular supplier by enabling the 'Allow Purchase Invoice Creation Without Purchase Order' checkbox in the Supplier master.",如果将此选项配置为“是”，ERPNext将阻止您创建采购发票或收据而无需先创建采购订单。通过启用供应商主数据中的“允许创建无购买订单的发票”复选框，可以为特定供应商覆盖此配置。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Purchase Invoice without creating a Purchase Receipt first. This configuration can be overridden for a particular supplier by enabling the 'Allow Purchase Invoice Creation Without Purchase Receipt' checkbox in the Supplier master.",如果将此选项配置为“是”，则ERPNext将阻止您创建采购发票而不先创建采购收据。通过启用供应商主数据中的“允许创建没有购买收据的购买发票”复选框，可以为特定供应商覆盖此配置。,
Quantity & Stock,数量和库存,
Call Details,通话详情,
Authorised By,授权人,
Signee (Company),签收人（公司）,
Signed By (Company),签名人（公司）,
First Response Time,第一次响应时间,
Request For Quotation,要求报价,
Opportunity Lost Reason Detail,机会丧失原因详细信息,
Access Token Secret,访问令牌机密,
Add to Topics,添加到主题,
...Adding Article to Topics,...在主题中添加文章,
Add Article to Topics,将文章添加到主题,
This article is already added to the existing topics,本文已添加到现有主题,
Add to Programs,添加到程序,
Programs,程式,
...Adding Course to Programs,...将课程添加到程序中,
Add Course to Programs,将课程添加到程序,
This course is already added to the existing programs,该课程已被添加到现有程序中,
Learning Management System Settings,学习管理系统设置,
Enable Learning Management System,启用学习管理系统,
Learning Management System Title,学习管理系统标题,
...Adding Quiz to Topics,...将测验添加到主题,
Add Quiz to Topics,将测验添加到主题,
This quiz is already added to the existing topics,该测验已添加到现有主题中,
Enable Admission Application,启用入学申请,
EDU-ATT-.YYYY.-,EDU-ATT-.YYYY.-,
Marking attendance,考勤,
Add Guardians to Email Group,将监护人添加到电子邮件组,
Attendance Based On,出勤依据,
Check this to mark the student as present in case the student is not attending the institute to participate or represent the institute in any event.\n\n,在任何情况下，如果学生不参加学院参加或代表学院，请选中该复选框以将学生标记为在场。,
Add to Courses,添加到课程,
...Adding Topic to Courses,...为课程添加主题,
Add Topic to Courses,向课程添加主题,
This topic is already added to the existing courses,该主题已被添加到现有课程中,
"If Shopify does not have a customer in the order, then while syncing the orders, the system will consider the default customer for the order",如果Shopify的订单中没有客户，则在同步订单时，系统将考虑该订单的默认客户,
The accounts are set by the system automatically but do confirm these defaults,帐户由系统自动设置，但请确认这些默认设置,
Default Round Off Account,默认四舍五入帐户,
Failed Import Log,导入日志失败,
Fixed Error Log,固定错误日志,
Company {0} already exists. Continuing will overwrite the Company and Chart of Accounts,公司{0}已存在。继续将覆盖公司和会计科目表,
Meta Data,元数据,
Unresolve,未解决,
Create Document,建立文件,
Mark as unresolved,标记为未解决,
TaxJar Settings,TaxJar设置,
Sandbox Mode,沙盒模式,
Enable Tax Calculation,启用税收计算,
Create TaxJar Transaction,创建TaxJar交易,
Credentials,证书,
Live API Key,实时API密钥,
Sandbox API Key,沙盒API密钥,
Configuration,组态,
Tax Account Head,税务科目主管,
Shipping Account Head,运送帐户负责人,
Practitioner Name,执业者姓名,
Enter a name for the Clinical Procedure Template,输入临床程序模板的名称,
Set the Item Code which will be used for billing the Clinical Procedure.,设置将用于计费临床程序的项目代码。,
Select an Item Group for the Clinical Procedure Item.,为临床程序项目选择一个项目组。,
Clinical Procedure Rate,临床程序率,
Check this if the Clinical Procedure is billable and also set the rate.,如果临床过程是可计费的，请检查此项目并设置费率。,
Check this if the Clinical Procedure utilises consumables. Click ,如果临床程序使用消耗品，请检查此项目。请点击,
 to know more,了解更多,
"You can also set the Medical Department for the template. After saving the document, an Item will automatically be created for billing this Clinical Procedure. You can then use this template while creating Clinical Procedures for Patients. Templates save you from filling up redundant data every single time. You can also create templates for other operations like Lab Tests, Therapy Sessions, etc.",您也可以为模板设置医疗部门。保存文档后，将自动创建一个项目，以为此临床程序开票。然后，您可以在创建患者临床程序时使用此模板。模板使您不必每次都填充冗余数据。您还可以为其他操作（如实验室测试，治疗会议等）创建模板。,
Descriptive Test Result,描述性测试结果,
Allow Blank,允许空白,
Descriptive Test Template,描述性测试模板,
"If you want to track Payroll and other HRMS operations for a Practitoner, create an Employee and link it here.",如果要跟踪从业人员的薪资和其他HRMS操作，请创建一个Employee并将其链接到此处。,
Set the Practitioner Schedule you just created. This will be used while booking appointments.,设置您刚刚创建的从业者时间表。这将在预订约会时使用。,
Create a service item for Out Patient Consulting.,为门诊咨询创建服务项目。,
"If this Healthcare Practitioner works for the In-Patient Department, create a service item for Inpatient Visits.",如果此医疗保健从业者在门诊部工作，请为住院患者就诊创建服务项目。,
Set the Out Patient Consulting Charge for this Practitioner.,设置该从业者的门诊咨询费用。,
"If this Healthcare Practitioner also works for the In-Patient Department, set the inpatient visit charge for this Practitioner.",如果该医疗保健从业者还为门诊部工作，请为该从业者设定住院费用。,
"If checked, a customer will be created for every Patient. Patient Invoices will be created against this Customer. You can also select existing Customer while creating a Patient. This field is checked by default.",如果选中，将为每个患者创建一个客户。将针对该客户创建患者发票。您还可以在创建患者时选择现有客户。默认情况下选中此字段。,
Collect Registration Fee,收取注册费,
"If your Healthcare facility bills registrations of Patients, you can check this and set the Registration Fee in the field below. Checking this will create new Patients with a Disabled status by default and will only be enabled after invoicing the Registration Fee.",如果您的医疗保健机构对患者的注册开具账单，则可以进行检查并在下面的字段中设置注册费。选中此选项将默认创建具有“禁用”状态的新患者，并且仅在开具注册费发票后才能启用。,
Checking this will automatically create a Sales Invoice whenever an appointment is booked for a Patient.,选中此选项将在为患者预约约会时自动创建销售发票。,
Healthcare Service Items,医疗服务项目,
"You can create a service item for Inpatient Visit Charge and set it here. Similarly, you can set up other Healthcare Service Items for billing in this section. Click ",您可以为“住院访问费用”创建服务项目并在此处进行设置。同样，您可以在此部分中设置其他医疗服务项目以进行计费。请点击,
Set up default Accounts for the Healthcare Facility,为医疗保健设施设置默认帐户,
"If you wish to override default accounts settings and configure the Income and Receivable accounts for Healthcare, you can do so here.",如果您希望覆盖默认帐户设置并为医疗保健配置收入帐户和应收帐款帐户，则可以在此处进行。,
Out Patient SMS alerts,门诊短信提醒,
"If you want to send SMS alert on Patient Registration, you can enable this option. Similary, you can set up Out Patient SMS alerts for other functionalities in this section. Click ",如果要发送有关患者挂号的SMS警报，则可以启用此选项。同样，您可以在本节中为其他功能设置门诊SMS警报。请点击,
Admission Order Details,入场订单详细信息,
Admission Ordered For,订购入场,
Expected Length of Stay,预计停留时间,
Admission Service Unit Type,入学服务单位类型,
Healthcare Practitioner (Primary),保健医生（小学）,
Healthcare Practitioner (Secondary),保健医生（中学）,
Admission Instruction,入学须知,
Chief Complaint,首席投诉,
Medications,药物治疗,
Investigations,调查,
Discharge Detials,放电细节,
Discharge Ordered Date,卸货订购日期,
Discharge Instructions,卸货说明,
Follow Up Date,跟进日期,
Discharge Notes,出院注意事项,
Processing Inpatient Discharge,处理住院病人出院,
Processing Patient Admission,处理患者入院,
Check-in time cannot be greater than the current time,入住时间不能大于当前时间,
Process Transfer,流程转移,
HLC-LAB-.YYYY.-,HLC-LAB-.YYYY.-,
Expected Result Date,预期结果日期,
Expected Result Time,预期结果时间,
Printed on,印于,
Requesting Practitioner,要求从业者,
Requesting Department,要求部门,
Employee (Lab Technician),员工（实验室技术员）,
Lab Technician Name,实验室技术员姓名,
Lab Technician Designation,实验室技术员指定,
Compound Test Result,复合测试结果,
Organism Test Result,生物测试结果,
Sensitivity Test Result,灵敏度测试结果,
Worksheet Print,工作表打印,
Worksheet Instructions,工作表说明,
Result Legend Print,结果图例打印,
Print Position,打印位置,
Both,都,
Result Legend,结果图例,
Lab Tests,实验室测试,
No Lab Tests found for the Patient {0},找不到针对患者{0}的实验室测试,
"Did not send SMS, missing patient mobile number or message content.",没有发送短信，缺少患者的手机号码或消息内容。,
No Lab Tests created,未创建实验室测试,
Creating Lab Tests...,创建实验室测试...,
Lab Test Group Template,实验室测试组模板,
Add New Line,添加新行,
Secondary UOM,次要UOM,
"<b>Single</b>: Results which require only a single input.\n<br>\n<b>Compound</b>: Results which require multiple event inputs.\n<br>\n<b>Descriptive</b>: Tests which have multiple result components with manual result entry.\n<br>\n<b>Grouped</b>: Test templates which are a group of other test templates.\n<br>\n<b>No Result</b>: Tests with no results, can be ordered and billed but no Lab Test will be created. e.g.. Sub Tests for Grouped results",<b>单项</b>：仅需一个输入的结果。<br><b>复合</b>：需要多个事件输入的结果。<br><b>描述性</b>：具有多个结果成分且带有手动结果输入的测试。<br><b>分组</b>：测试模板，它是一组其他测试模板。<br><b>无结果</b>：可以订购<b>无结果的</b>测试，可以订购和计费，但不会创建实验室测试。例如。分组结果的子测试,
"If unchecked, the item will not be available in Sales Invoices for billing but can be used in group test creation. ",如果未选中，则该项目在“销售发票”中将不可用，但可用于创建组测试。,
Description ,描述,
Descriptive Test,描述性测试,
Group Tests,小组测试,
Instructions to be printed on the worksheet,在工作表上打印的说明,
"Information to help easily interpret the test report, will be printed as part of the Lab Test result.",帮助您轻松解释测试报告的信息将作为实验室测试结果的一部分进行打印。,
Normal Test Result,正常测试结果,
Secondary UOM Result,次要UOM结果,
Italic,斜体,
Underline,强调,
Organism,生物,
Organism Test Item,生物测试项目,
Colony Population,殖民地人口,
Colony UOM,殖民地UOM,
Tobacco Consumption (Past),烟草消费量（过去）,
Tobacco Consumption (Present),烟草消费（现在）,
Alcohol Consumption (Past),饮酒量（过去）,
Alcohol Consumption (Present),饮酒量（当前）,
Billing Item,开票项目,
Medical Codes,医疗法规,
Clinical Procedures,临床程序,
Order Admission,订单入场,
Scheduling Patient Admission,安排病人入院,
Order Discharge,订单卸货,
Sample Details,样品细节,
Collected On,收集于,
No. of prints,印刷数量,
Number of prints required for labelling the samples,标记样品所需的打印数量,
HLC-VTS-.YYYY.-,HLC-VTS-.YYYY.-,
Payroll Cost Center,工资成本中心,
Approvers,批准人,
The first Approver in the list will be set as the default Approver.,列表中的第一个批准人将被设置为默认批准人。,
Shift Request Approver,轮班请求批准人,
Provident Fund Account,公积金帐户,
MICR Code,MICR代码,
Repay unclaimed amount from salary,从工资中偿还无人认领的金额,
Deduction from salary,从工资中扣除,
If this is not checked the loan by default will be considered as a Demand Loan,如果未选中此选项，则默认情况下该贷款将被视为需求贷款,
This account is used for booking loan repayments from the borrower and also disbursing loans to the borrower,此帐户用于预订借款人的还款，也用于向借款人发放贷款,
This account is capital account which is used to allocate capital for loan disbursal account ,该帐户是资本帐户，用于为贷款支付帐户分配资本,
This account will be used for booking loan interest accruals,此帐户将用于预订应计贷款利息,
This account will be used for booking penalties levied due to delayed repayments,该帐户将用于因延迟还款而收取的订舱罚款,
Variant BOM,变体BOM,
Template Item,模板项目,
Select template item,选择模板项目,
Select variant item code for the template item {0},选择模板项目{0}的变体项目代码,
Downtime Entry,停机时间输入,
DT-,DT-,
Workstation / Machine,工作站/机器,
Operator,操作员,
In Mins,分钟,
Downtime Reason,停机原因,
Stop Reason,停止原因,
Excessive machine set up time,机器设置时间过多,
Unplanned machine maintenance,计划外的机器维护,
On-machine press checks,机上印刷检查,
Machine operator errors,机器操作员错误,
Machine malfunction,机器故障,
Electricity down,断电,
Operation Row Number,操作行号,
Operation {0} added multiple times in the work order {1},操作{0}已在工作订单{1}中多次添加,
"If ticked, multiple materials can be used for a single Work Order. This is useful if one or more time consuming products are being manufactured.",如果选中，则可以将多个物料用于单个工单。如果要生产一种或多种耗时的产品，这将很有用。,
Backflush Raw Materials,反冲原料,
"The Stock Entry of type 'Manufacture' is known as backflush. Raw materials being consumed to manufacture finished goods is known as backflushing. <br><br> When creating Manufacture Entry, raw-material items are backflushed based on BOM of production item. If you want raw-material items to be backflushed based on Material Transfer entry made against that Work Order instead, then you can set it under this field.",类型“制造”的库存分录称为反冲。生产成品所消耗的原材料称为反冲。<br><br>创建生产分录时，将根据生产物料的物料清单对物料物料进行反冲。如果您希望根据针对该工单的物料转移条目来回算原始物料，则可以在此字段下进行设置。,
Work In Progress Warehouse,在制品仓库,
This Warehouse will be auto-updated in the Work In Progress Warehouse field of Work Orders.,该仓库将在工作单的“进行中的仓库”字段中自动更新。,
Finished Goods Warehouse,成品仓库,
This Warehouse will be auto-updated in the Target Warehouse field of Work Order.,该仓库将在工作单的目标仓库字段中自动更新。,
"If ticked, the BOM cost will be automatically updated based on Valuation Rate / Price List Rate / last purchase rate of raw materials.",如果选中，则物料清单成本将根据评估价/价目表价格/原材料的最后购买价自动更新。,
Source Warehouses (Optional),源仓库（可选）,
"System will pickup the materials from the selected warehouses. If not specified, system will create material request for purchase.",系统将从选定的仓库提取物料。如果未指定，系统将创建采购物料申请。,
Lead Time,交货时间,
PAN Details,PAN详细信息,
Create Customer,建立客户,
Invoicing,开票,
Enable Auto Invoicing,启用自动开票,
Send Membership Acknowledgement,发送会员确认,
Send Invoice with Email,通过电子邮件发送发票,
Membership Print Format,会员打印格式,
Invoice Print Format,发票打印格式,
Revoke <Key></Key>,撤消&lt;Key&gt;&lt;/Key&gt;,
You can learn more about memberships in the manual. ,您可以在手册中了解有关会员资格的更多信息。,
ERPNext Docs,ERPNext文档,
Regenerate Webhook Secret,重新生成Webhook秘密,
Generate Webhook Secret,生成Webhook秘密,
Copy Webhook URL,复制Webhook URL,
Linked Item,关联项目,
Feedback By,反馈者,
Manufacturing Section,制造部,
"By default, the Customer Name is set as per the Full Name entered. If you want Customers to be named by a ",默认情况下，根据输入的全名设置客户名。如果您希望客户以,
Configure the default Price List when creating a new Sales transaction. Item prices will be fetched from this Price List.,在创建新的销售交易时配置默认的价目表。项目价格将从此价格表中获取。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Sales Invoice or Delivery Note without creating a Sales Order first. This configuration can be overridden for a particular Customer by enabling the 'Allow Sales Invoice Creation Without Sales Order' checkbox in the Customer master.",如果将此选项配置为“是”，则ERPNext将阻止您创建销售发票或交货单，而无需先创建销售订单。通过启用“客户”主数据中的“允许在没有销售订单的情况下创建销售发票”复选框，可以为特定客户覆盖此配置。,
"If this option is configured 'Yes', ERPNext will prevent you from creating a Sales Invoice without creating a Delivery Note first. This configuration can be overridden for a particular Customer by enabling the 'Allow Sales Invoice Creation Without Delivery Note' checkbox in the Customer master.",如果将此选项配置为“是”，则ERPNext将阻止您创建销售发票而不先创建交货单。通过启用客户主数据中的“允许创建没有交货单的销售发票”复选框，可以为特定客户覆盖此配置。,
Default Warehouse for Sales Return,默认退货仓库,
Default In Transit Warehouse,默认在途仓库,
Enable Perpetual Inventory For Non Stock Items,为非库存项目启用永久库存,
HRA Settings,HRA设定,
Basic Component,基本组成,
HRA Component,HRA组件,
Arrear Component,Arrear组件,
Please enter the company name to confirm,请输入公司名称进行确认,
Quotation Lost Reason Detail,报价丢失原因明细,
Enable Variants,启用变体,
Save Quotations as Draft,将报价另存为草稿,
MAT-DN-RET-.YYYY.-,MAT-DN-RET-.YYYY.-,
Please Select a Customer,请选择一个客户,
Against Delivery Note Item,针对交货单项目,
Is Non GST ,是非消费税,
Image Description,图片描述,
Transfer Status,转移状态,
MAT-PR-RET-.YYYY.-,MAT-PR-RET-.YYYY.-,
Track this Purchase Receipt against any Project,针对任何项目跟踪此采购收据,
Please Select a Supplier,请选择供应商,
Add to Transit,添加到公交,
Set Basic Rate Manually,手动设置基本费率,
"By default, the Item Name is set as per the Item Code entered. If you want Items to be named by a ",默认情况下，根据输入的项目代码设置项目名称。如果您希望项目以,
Set a Default Warehouse for Inventory Transactions. This will be fetched into the Default Warehouse in the Item master.,为库存交易设置默认仓库。这将被提取到物料主数据中的默认仓库中。,
"This will allow stock items to be displayed in negative values. Using this option depends on your use case. With this option unchecked, the system warns before obstructing a transaction that is causing negative stock.",这将使库存项目显示为负值。使用此选项取决于您的用例。取消选中此选项，系统会在阻止导致负库存的交易之前发出警告。,
Choose between FIFO and Moving Average Valuation Methods. Click ,在FIFO和移动平均估值方法之间选择。请点击,
 to know more about them.,进一步了解他们。,
Show 'Scan Barcode' field above every child table to insert Items with ease.,在每个子表上方显示“扫描条形码”字段，以轻松插入项目。,
"Serial numbers for stock will be set automatically based on the Items entered based on first in first out in transactions like Purchase/Sales Invoices, Delivery Notes, etc.",库存的序列号将根据在采购/销售发票，交货单等交易中基于先进先出输入的项目自动设置。,
"If blank, parent Warehouse Account or company default will be considered in transactions",如果为空白，则将在交易中考虑父仓库帐户或公司默认值,
Service Level Agreement Details,服务水平协议详细信息,
Service Level Agreement Status,服务水平协议状态,
On Hold Since,暂停后,
Total Hold Time,总保持时间,
Response Details,回应详情,
Average Response Time,平均响应时间,
User Resolution Time,用户解析时间,
SLA is on hold since {0},自{0}起，SLA处于保留状态,
Pause SLA On Status,暂停状态中的SLA,
Pause SLA On,暂停SLA,
Greetings Section,问候部分,
Greeting Title,问候标题,
Greeting Subtitle,问候字幕,
Youtube ID,YouTube ID,
Youtube Statistics,YouTube统计,
Views,观看次数,
Dislikes,不喜欢,
Video Settings,影片设定,
Enable YouTube Tracking,启用YouTube跟踪,
30 mins,30分钟,
1 hr,1小时,
6 hrs,6小时,
Patient Progress,患者进展,
Targetted,有针对性,
Score Obtained,获得分数,
Sessions,届会,
Average Score,平均分,
Select Assessment Template,选择评估模板,
 out of ,在......之外,
Select Assessment Parameter,选择评估参数,
Gender: ,性别：,
Contact: ,联系：,
Total Therapy Sessions: ,总治疗会议：,
Monthly Therapy Sessions: ,每月治疗会议：,
Patient Profile,患者简介,
Point Of Sale,销售点,
Email sent successfully.,电子邮件发送成功。,
Search by invoice id or customer name,按发票编号或客户名称搜索,
Invoice Status,发票状态,
Filter by invoice status,按发票状态过滤,
Select item group,选择项目组,
No items found. Scan barcode again.,未找到任何项目。再次扫描条形码。,
"Search by customer name, phone, email.",通过客户名称，电话，电子邮件进行搜索。,
Enter discount percentage.,输入折扣百分比。,
Discount cannot be greater than 100%,折扣不能大于100％,
Enter customer's email,输入客户的电子邮件,
Enter customer's phone number,输入客户的电话号码,
Customer contact updated successfully.,客户联系人已成功更新。,
Item will be removed since no serial / batch no selected.,由于未选择序列/批次，因此将删除项目。,
Discount (%),优惠（％）,
You cannot submit the order without payment.,您必须先付款才能提交订单。,
You cannot submit empty order.,您不能提交空订单。,
To Be Paid,要支付,
Create POS Opening Entry,创建POS开幕条目,
Please add Mode of payments and opening balance details.,请添加付款方式和期初余额详细信息。,
Toggle Recent Orders,切换最近的订单,
Save as Draft,保存为草稿,
You must add atleast one item to save it as draft.,您必须添加至少一项才能将其保存为草稿。,
There was an error saving the document.,保存文档时出错。,
You must select a customer before adding an item.,在添加项目之前，您必须选择一个客户。,
Please Select a Company,请选择公司,
Active Leads,主动线索,
Please Select a Company.,请选择一个公司。,
BOM Operations Time,BOM操作时间,
BOM ID,物料清单编号,
BOM Item Code,BOM项目代码,
Time (In Mins),时间（分钟）,
Sub-assembly BOM Count,子组件BOM计数,
View Type,查看类型,
Total Delivered Amount,总交付量,
Downtime Analysis,停机时间分析,
Machine,机,
Downtime (In Hours),停机时间（小时）,
Employee Analytics,员工分析,
"""From date"" can not be greater than or equal to ""To date""",“起始日期”不能大于或等于“起始日期”,
Exponential Smoothing Forecasting,指数平滑预测,
First Response Time for Issues,问题的第一响应时间,
First Response Time for Opportunity,机会的第一响应时间,
Depreciatied Amount,折旧额,
Period Based On,期间基于,
Date Based On,日期依据,
{0} and {1} are mandatory,{0}和{1}是强制性的,
Consider Accounting Dimensions,考虑会计维度,
Reserved Quantity for Production,预留生产量,
Projected Quantity,预计数量,
 Total Sales Amount,总销售金额,
Job Card Summary,工作卡摘要,
Id,ID,
Time Required (In Mins),所需时间（分钟）,
From Posting Date,从发布日期开始,
To Posting Date,到发布日期,
No records found,没有找到记录,
Customer/Lead Name,客户/姓氏,
Production Planning Report,生产计划报告,
Order Qty,订货量,
Raw Material Code,原材料代码,
Raw Material Name,原料名称,
Allotted Qty,分配数量,
Expected Arrival Date,预计到达日期,
Arrival Quantity,到达数量,
Raw Material Warehouse,原料仓库,
Order By,订购依据,
Include Sub-assembly Raw Materials,包括子装配原材料,
Program wise Fee Collection,程序明智的收费,
Fees Collected,收费,
Project Summary,项目总结,
Total Tasks,总任务,
Tasks Completed,任务完成,
Tasks Overdue,任务逾期,
Completion,完成时间,
Purchase Order Analysis,采购订单分析,
From and To Dates are required.,必须提供“自”和“至”日期,
To Date cannot be before From Date.,截止日期不能早于截止日期。,
Qty to Bill,账单数量,
Group by Purchase Order,按采购订单分组,
 Purchase Value,购买价值,
Total Received Amount,收款总额,
Quality Inspection Summary,质量检验总结,
 Quoted Amount,报价金额,
Lead Time (Days),交货时间（天）,
Include Expired,包括过期,
Requested Items to Order and Receive,要求订购和接收的物品,
Sales Order Analysis,销售订单分析,
Amount Delivered,交付金额,
Delay (in Days),延误（天）,
Group by Sales Order,按销售订单分组,
 Sales Value,销售价值,
Stock Qty vs Serial No Count,库存数量vs序列号不计,
Serial No Count,序列号,
Work Order Summary,工作单摘要,
Produce Qty,产生数量,
Lead Time (in mins),交货时间（以分钟为单位）,
Charts Based On,基于的图表,
YouTube Interactions,YouTube互动,
Published Date,发布日期,
Barnch,倒钩,
Select a Company,选择公司,
Opportunity {0} created,机会{0}已创建,
Kindly select the company first,请首先选择公司,
Please enter From Date and To Date to generate JSON,请输入起始日期和截止日期以生成JSON,
Download DATEV File,下载DATEV文件,
Numero has not set in the XML file,Numero尚未在XML文件中设置,
Inward Supplies(liable to reverse charge),内向耗材（易于反向充电）,
This is based on the course schedules of this Instructor,这是基于该教师的课程表,
Course and Assessment,课程与评估,
Course {0} has been added to all the selected programs successfully.,课程{0}已成功添加到所有选定程序。,
Programs updated,程序更新,
Program and Course,课程与课程,
{0} or {1} is mandatory,{0}或{1}是强制性的,
Mandatory Fields,必须填写,
Student {0}: {1} does not belong to Student Group {2},学生{0}：{1}不属于学生组{2},
Student Attendance record {0} already exists against the Student {1},针对学生{1}的学生出勤记录{0}已存在,
Course and Fee,课程和费用,
Not eligible for the admission in this program as per Date Of Birth,根据出生日期，不符合该计划的入学条件,
Topic {0} has been added to all the selected courses successfully.,主题{0}已成功添加到所有所选课程。,
Courses updated,课程更新,
{0} {1} has been added to all the selected topics successfully.,{0} {1}已成功添加到所有选定主题。,
Topics updated,主题已更新,
Academic Term and Program,学期和课程,
Please remove this item and try to submit again or update the posting time.,请删除该项目，然后尝试再次提交或更新发布时间。,
Failed to Authenticate the API key.,无法验证API密钥。,
Invalid Credentials,无效证件,
URL can only be a string,网址只能是一个字符串,
"Here is your webhook secret, this will be shown to you only once.",这是您的Webhook机密，此秘密仅显示给您一次。,
The payment for this membership is not paid. To generate invoice fill the payment details,未支付此会员资格的费用。要生成发票，请填写付款明细,
An invoice is already linked to this document,发票已链接到该文件,
No customer linked to member {},没有客户链接到成员{},
You need to set <b>Debit Account</b> in Membership Settings,您需要在会员设置中设置<b>借记帐户</b>,
You need to set <b>Default Company</b> for invoicing in Membership Settings,您需要在会员设置中设置<b>默认公司</b>以开发票,
You need to enable <b>Send Acknowledge Email</b> in Membership Settings,您需要在会员设置中启用<b>发送确认电子邮件</b>,
Error creating membership entry for {0},为{0}创建成员资格条目时出错,
A customer is already linked to this Member,客户已经链接到该会员,
End Date must not be lesser than Start Date,结束日期不得小于开始日期,
Employee {0} already has Active Shift {1}: {2},员工{0}已具有活动班次{1}：{2},
 from {0},来自{0},
 to {0},到{0},
Please set {0} for the Employee or for Department: {1},请为员工或部门设置{0}：{1},
Employee Onboarding: {0} is already for Job Applicant: {1},员工入职：{0}已适用于求职者：{1},
Asset Value Analytics,资产价值分析,
Category-wise Asset Value,类别资产价值,
Total Assets,总资产,
New Assets (This Year),新资产（今年）,
Row #{}: Depreciation Posting Date should not be equal to Available for Use Date.,第{}行：折旧过帐日期不应等于可用日期。,
Incorrect Date,日期不正确,
Invalid Gross Purchase Amount,无效的总购买金额,
There are active maintenance or repairs against the asset. You must complete all of them before cancelling the asset.,对资产进行了积极的维护或修理。您必须先完成所有操作，然后才能取消资产。,
% Complete,完成百分比,
Back to Course,回到课程,
Finish Topic,完成主题,
Mins,分钟,
by,通过,
Back to,回到,
Enrolling...,正在注册...,
You have successfully enrolled for the program ,您已成功注册该计划,
Enrolled,已入学,
Watch Intro,观看介绍,
We're here to help!,我们在这里为您提供帮助！,
Frequently Read Articles,经常阅读的文章,
Please set a default company address,请设置默认公司地址,
{0} is not a valid state! Check for typos or enter the ISO code for your state.,{0}不是有效状态！检查拼写错误或输入您所在州的ISO代码。,
Error occured while parsing Chart of Accounts: Please make sure that no two accounts have the same name,解析会计科目表时发生错误：请确保没有两个帐户具有相同的名称,
Plaid invalid request error,格子无效的请求错误,
Please check your Plaid client ID and secret values,请检查您的格子客户ID和机密值,
Bank transaction creation error,银行交易创建错误,
Unit of Measurement,测量单位,
Fiscal Year {0} Does Not Exist,会计年度{0}不存在,
Row # {0}: Returned Item {1} does not exist in {2} {3},行号{0}：返回的项目{1}在{2} {3}中不存在,
Valuation type charges can not be marked as Inclusive,评估类型的费用不能标记为包含,
You do not have permissions to {} items in a {}.,您无权访问{}中的{}个项目。,
Insufficient Permissions,权限不足,
You are not allowed to update as per the conditions set in {} Workflow.,您无法按照{}工作流程中设置的条件进行更新。,
Expense Account Missing,费用帐户丢失,
{0} is not a valid Value for Attribute {1} of Item {2}.,{0}不是项{2}的属性{1}的有效值。,
Invalid Value,无效值,
The value {0} is already assigned to an existing Item {1}.,值{0}已分配给现有项{1}。,
"To still proceed with editing this Attribute Value, enable {0} in Item Variant Settings.",要继续编辑该属性值，请在“项目变式设置”中启用{0}。,
Edit Not Allowed,不允许编辑,
Row #{0}: Item {1} is already fully received in Purchase Order {2},第＃0行：采购订单{2}中已完全收到项目{1},
You cannot create or cancel any accounting entries with in the closed Accounting Period {0},您无法在关闭的会计期间{0}中创建或取消任何会计分录,
POS Invoice should have {} field checked.,POS发票应选中{}字段。,
Invalid Item,无效的项目,
Row #{}: You cannot add postive quantities in a return invoice. Please remove item {} to complete the return.,第{}行：您不能在退货发票中添加肯定数量。请删除项目{}以完成退货。,
The selected change account {} doesn't belongs to Company {}.,所选的更改帐户{}不属于公司{}。,
Atleast one invoice has to be selected.,必须选择至少一张发票。,
Payment methods are mandatory. Please add at least one payment method.,付款方式是强制性的。请添加至少一种付款方式。,
Please select a default mode of payment,请选择默认付款方式,
You can only select one mode of payment as default,您只能选择一种付款方式作为默认付款方式,
Missing Account,帐户遗失,
Customers not selected.,未选择客户。,
Statement of Accounts,决算报告,
Ageing Report Based On ,基于的账龄报告,
Please enter distributed cost center,请进入分布式成本中心,
Total percentage allocation for distributed cost center should be equal to 100,分布式成本中心的总百分比分配应等于100,
Cannot enable Distributed Cost Center for a Cost Center already allocated in another Distributed Cost Center,无法为已在另一个分布式成本中心中分配的成本中心启用分布式成本中心,
Parent Cost Center cannot be added in Distributed Cost Center,父成本中心不能添加到分布式成本中心中,
A Distributed Cost Center cannot be added in the Distributed Cost Center allocation table.,无法在“分布式成本中心”分配表中添加“分布式成本中心”。,
Cost Center with enabled distributed cost center can not be converted to group,启用了分布式成本中心的成本中心无法转换为组,
Cost Center Already Allocated in a Distributed Cost Center cannot be converted to group,无法将已分配在分布式成本中心中的成本中心转换为组,
Trial Period Start date cannot be after Subscription Start Date,试用期开始日期不能晚于订阅开始日期,
Subscription End Date must be after {0} as per the subscription plan,根据订阅计划，订阅结束日期必须在{0}之后,
Subscription End Date is mandatory to follow calendar months,订阅结束日期必须遵循日历月,
Row #{}: POS Invoice {} is not against customer {},第＃{}行：POS发票{}不针对客户{},
Row #{}: POS Invoice {} is not submitted yet,第{}行：尚未提交POS发票{},
Row #{}: POS Invoice {} has been {},第＃{}行：POS发票{}已被{},
No Supplier found for Inter Company Transactions which represents company {0},找不到代表公司{0}的公司间交易的供应商,
No Customer found for Inter Company Transactions which represents company {0},找不到代表公司{0}的公司间交易的客户,
Invalid Period,无效期间,
Selected POS Opening Entry should be open.,所选的POS入口条目应打开。,
Invalid Opening Entry,无效的开幕词,
Please set a Company,请设置公司,
"Sorry, this coupon code's validity has not started",抱歉，此优惠券代码的有效性尚未开始,
"Sorry, this coupon code's validity has expired",抱歉，此优惠券代码的有效性已过期,
"Sorry, this coupon code is no longer valid",抱歉，此优惠券代码不再有效,
For the 'Apply Rule On Other' condition the field {0} is mandatory,对于“其他应用规则”条件，字段{0}是必填字段,
{1} Not in Stock,{1}无库存,
Only {0} in Stock for item {1},项目{1}的库存中只有{0},
Please enter a coupon code,请输入优惠券代码,
Please enter a valid coupon code,请输入有效的优惠券代码,
Invalid Child Procedure,无效的子程序,
Import Italian Supplier Invoice.,导入意大利供应商发票。,
"Valuation Rate for the Item {0}, is required to do accounting entries for {1} {2}.",要为{1} {2}进行会计分录，必须使用项目{0}的评估率。,
 Here are the options to proceed:,以下是继续进行的选项：,
"If the item is transacting as a Zero Valuation Rate item in this entry, please enable 'Allow Zero Valuation Rate' in the {0} Item table.",如果该项在此条目中正在作为零评估率项目进行交易，请在{0}项表中启用“允许零评估率”。,
"If not, you can Cancel / Submit this entry ",如果没有，您可以取消/提交此条目,
 performing either one below:,执行以下任一操作：,
Create an incoming stock transaction for the Item.,创建物料的进货库存交易。,
Mention Valuation Rate in the Item master.,物料主数据中的提及评估率。,
Valuation Rate Missing,估价率缺失,
Serial Nos Required,需要序列号,
Quantity Mismatch,数量不匹配,
"Please Restock Items and Update the Pick List to continue. To discontinue, cancel the Pick List.",请重新入库并更新选择清单以继续。要中止，取消选择列表。,
Out of Stock,缺货,
{0} units of Item {1} is not available.,项目{1}的{0}个单位不可用。,
Item for row {0} does not match Material Request,第{0}行的项目与物料请求不匹配,
Warehouse for row {0} does not match Material Request,第{0}行的仓库与物料请求不匹配,
Accounting Entry for Service,服务会计分录,
All items have already been Invoiced/Returned,所有商品均已开票/退货,
All these items have already been Invoiced/Returned,所有这些物品已经开票/退货,
Stock Reconciliations,库存对帐,
Merge not allowed,不允许合并,
The following deleted attributes exist in Variants but not in the Template. You can either delete the Variants or keep the attribute(s) in template.,以下已删除的属性存在于变式中，但不在模板中。您可以删除变体，也可以将属性保留在模板中。,
Variant Items,变体物品,
Variant Attribute Error,变体属性错误,
The serial no {0} does not belong to item {1},序列号{0}不属于项目{1},
There is no batch found against the {0}: {1},找不到针对{0}的批次：{1},
Completed Operation,完成作业,
Work Order Analysis,工单分析,
Quality Inspection Analysis,质量检验分析,
Pending Work Order,待处理的工作单,
Last Month Downtime Analysis,上个月停机时间分析,
Work Order Qty Analysis,工单数量分析,
Job Card Analysis,工作卡分析,
Monthly Total Work Orders,每月总工单,
Monthly Completed Work Orders,每月完成的工作单,
Ongoing Job Cards,持续的工作卡,
Monthly Quality Inspections,每月质量检查,
(Forecast),（预测）,
Total Demand (Past Data),总需求（过去数据）,
Total Forecast (Past Data),总预测（过去数据）,
Total Forecast (Future Data),总预测（未来数据）,
Based On Document,基于文件,
Based On Data ( in years ),基于数据（以年为单位）,
Smoothing Constant,平滑常数,
Please fill the Sales Orders table,请填写销售订单表,
Sales Orders Required,需要销售订单,
Please fill the Material Requests table,请填写材料申请表,
Material Requests Required,所需材料要求,
Items to Manufacture are required to pull the Raw Materials associated with it.,需要制造的物品才能拉动与其关联的原材料。,
Items Required,必填项,
Operation {0} does not belong to the work order {1},操作{0}不属于工作订单{1},
Print UOM after Quantity,数量后打印UOM,
Set default {0} account for perpetual inventory for non stock items,为非库存项目的永久库存设置默认的{0}帐户,
Row #{0}: Child Item should not be a Product Bundle. Please remove Item {1} and Save,第＃{0}行：子项不应是产品捆绑包。请删除项目{1}并保存,
Credit limit reached for customer {0},客户{0}已达到信用额度,
Could not auto create Customer due to the following missing mandatory field(s):,由于缺少以下必填字段，因此无法自动创建客户：,
Please create Customer from Lead {0}.,请根据潜在客户{0}创建客户。,
Mandatory Missing,必填项,
From Date can not be greater than To Date.,起始日期不能大于截止日期。,
Row #{0}: Please enter the result value for {1},第＃0行：请输入{1}的结果值,
Mandatory Results,强制性结果,
Sales Invoice or Patient Encounter is required to create Lab Tests,创建实验室测试需要销售发票或患者En,
Insufficient Data,资料不足,
Lab Test(s) {0} created successfully,实验测试{0}已成功创建,
Test :,测试：,
Sample Collection {0} has been created,样品采集{0}已创建,
Normal Range: ,普通范围：,
Row #{0}: Check Out datetime cannot be less than Check In datetime,第＃{0}行：签出日期时间不能小于签入日期时间,
"Missing required details, did not create Inpatient Record",缺少必需的详细信息，未创建住院记录,
Unbilled Invoices,未开票发票,
Standard Selling Rate should be greater than zero.,标准销售率应大于零。,
Conversion Factor is mandatory,转换因子为必填项,
Row #{0}: Conversion Factor is mandatory,第＃0行：转换因子为必填项,
Sample Quantity cannot be negative or 0,样品数量不能为负或0,
Invalid Quantity,无效数量,
"Please set defaults for Customer Group, Territory and Selling Price List in Selling Settings",请在销售设置中为客户组，地区和销售价格表设置默认值,
{0} on {1},{1}上的{0},
{0} with {1},{0}与{1},
Appointment Confirmation Message Not Sent,未发送约会确认消息,
"SMS not sent, please check SMS Settings",短信未发送，请检查短信设置,
Healthcare Service Unit Type cannot have both {0} and {1},医疗保健服务单位类型不能同时具有{0}和{1},
Healthcare Service Unit Type must allow atleast one among {0} and {1},医疗服务单位类型必须允许{0}和{1}中的至少一个,
Set Response Time and Resolution Time for Priority {0} in row {1}.,在行{1}中设置优先级{0}的响应时间和解决时间。,
Response Time for {0} priority in row {1} can't be greater than Resolution Time.,第{1}行中{0}优先级的响应时间不能大于“解决时间”。,
{0} is not enabled in {1},{1}中未启用{0},
Group by Material Request,按材料要求分组,
Email Sent to Supplier {0},通过电子邮件发送给供应商{0},
"The Access to Request for Quotation From Portal is Disabled. To Allow Access, Enable it in Portal Settings.",禁止从门户网站访问报价请求。要允许访问，请在门户设置中启用它。,
Supplier Quotation {0} Created,供应商报价{0}已创建,
Valid till Date cannot be before Transaction Date,有效期至日期不能早于交易日期,
Unlink Advance Payment on Cancellation of Order,取消订单时取消预付款链接,
"Simple Python Expression, Example: territory != 'All Territories'",简单的Python表达式，例如：region！=&#39;All Territories&#39;,
Sales Contributions and Incentives,销售贡献和激励,
Sourced by Supplier,由供应商采购,
Total weightage assigned should be 100%.<br>It is {0},分配的总重量应为100％。<br>是{0},
Account {0} exists in parent company {1}.,帐户{0}在母公司{1}中。,
"To overrule this, enable '{0}' in company {1}",要否决此问题，请在公司{1}中启用“ {0}”,
Invalid condition expression,条件表达式无效,
Please Select a Company First,请先选择公司,
Please Select Both Company and Party Type First,请首先选择公司和派对类型,
Provide the invoice portion in percent,提供发票百分比,
Give number of days according to prior selection,根据事先选择给出天数,
Email Details,电子邮件详细资料,
"Select a greeting for the receiver. E.g. Mr., Ms., etc.",选择收件人的问候语。例如先生，女士等,
Preview Email,预览电子邮件,
Please select a Supplier,请选择供应商,
Supplier Lead Time (days),供应商交货时间（天）,
"Home, Work, etc.",家庭，工作等,
Exit Interview Held On,退出面试举行,
Sets 'Target Warehouse' in each row of the Items table.,在“物料”表的每一行中设置“目标仓库”。,
Sets 'Source Warehouse' in each row of the Items table.,在“物料”表的每一行中设置“源仓库”。,
POS Register,POS收银机,
"Can not filter based on POS Profile, if grouped by POS Profile",如果按POS配置文件分组，则无法基于POS配置文件进行过滤,
"Can not filter based on Customer, if grouped by Customer",如果按客户分组，则无法根据客户进行过滤,
"Can not filter based on Cashier, if grouped by Cashier",如果按收银员分组，则无法根据收银员进行过滤,
Payment Method,付款方法,
"Can not filter based on Payment Method, if grouped by Payment Method",如果按付款方式分组，则无法基于付款方式进行过滤,
Supplier Quotation Comparison,供应商报价比较,
Price per Unit (Stock UOM),单价（库存单位）,
Group by Supplier,按供应商分组,
Group by Item,按项目分组,
Remember to set {field_label}. It is required by {regulation}.,请记住设置{field_label}。 {regulation}要求它。,
Enrollment Date cannot be before the Start Date of the Academic Year {0},入学日期不能早于学年的开始日期{0},
Enrollment Date cannot be after the End Date of the Academic Term {0},入学日期不能晚于学期结束日期{0},
Enrollment Date cannot be before the Start Date of the Academic Term {0},入学日期不能早于学期开始日期{0},
Future Posting Not Allowed,不允许将来发布,
"To enable Capital Work in Progress Accounting, ",要启用基本工程进度会计，,
you must select Capital Work in Progress Account in accounts table,您必须在帐户表中选择正在进行的资本工程帐户,
You can also set default CWIP account in Company {},您还可以在公司{}中设置默认的CWIP帐户,
The Request for Quotation can be accessed by clicking on the following button,点击以下按钮可以访问报价请求,
Regards,问候,
Please click on the following button to set your new password,请点击以下按钮设置新密码,
Update Password,更新密码,
Row #{}: Selling rate for item {} is lower than its {}. Selling {} should be atleast {},第＃{}行：商品{}的销售价格低于其{}。出售{}应该至少{},
You can alternatively disable selling price validation in {} to bypass this validation.,您也可以在{}中禁用售价验证，以绕过此验证。,
Invalid Selling Price,无效的售价,
Address needs to be linked to a Company. Please add a row for Company in the Links table.,地址需要链接到公司。请在“链接”表中为“公司”添加一行。,
Company Not Linked,公司未链接,
Import Chart of Accounts from CSV / Excel files,从CSV / Excel文件导入会计科目表,
Completed Qty cannot be greater than 'Qty to Manufacture',完成的数量不能大于“制造数量”,
"Row {0}: For Supplier {1}, Email Address is Required to send an email",第{0}行：对于供应商{1}，需要电子邮件地址才能发送电子邮件,
"If enabled, the system will post accounting entries for inventory automatically",如果启用，系统将自动过帐库存的会计分录,
Accounts Frozen Till Date,帐户冻结日期,
Accounting entries are frozen up to this date. Nobody can create or modify entries except users with the role specified below,截止到此日期，会计条目被冻结。除具有以下指定角色的用户外，任何人都无法创建或修改条目,
Role Allowed to Set Frozen Accounts and Edit Frozen Entries,允许角色设置冻结帐户和编辑冻结条目,
Address used to determine Tax Category in transactions,用于确定交易中税种的地址,
"The percentage you are allowed to bill more against the amount ordered. For example, if the order value is $100 for an item and tolerance is set as 10%, then you are allowed to bill up to $110 ",您可以针对所订购的金额开具更多费用的百分比。例如，如果某商品的订单价值为$ 100且容差设置为10％，则您最多可收取$ 110的费用,
This role is allowed to submit transactions that exceed credit limits,允许该角色提交超出信用额度的交易,
"If ""Months"" is selected, a fixed amount will be booked as deferred revenue or expense for each month irrespective of the number of days in a month. It will be prorated if deferred revenue or expense is not booked for an entire month",如果选择“月”，则固定金额将记为每月的递延收入或费用，而与一个月中的天数无关。如果递延的收入或费用没有整月预定，则将按比例分配,
"If this is unchecked, direct GL entries will be created to book deferred revenue or expense",如果未选中此复选框，则将创建直接总帐分录以预定递延收入或费用,
Show Inclusive Tax in Print,在打印中显示含税,
Only select this if you have set up the Cash Flow Mapper documents,仅在设置了现金流量映射器文档后才选择此选项,
Payment Channel,付款渠道,
Is Purchase Order Required for Purchase Invoice & Receipt Creation?,采购发票和收货创建是否需要采购订单？,
Is Purchase Receipt Required for Purchase Invoice Creation?,创建采购发票是否需要采购收据？,
Maintain Same Rate Throughout the Purchase Cycle,在整个购买周期中保持相同的费率,
Allow Item To Be Added Multiple Times in a Transaction,允许在事务中多次添加项目,
Suppliers,供应商,
Send Emails to Suppliers,发送电子邮件给供应商,
Select a Supplier,选择供应商,
Cannot mark attendance for future dates.,无法标记出将来的日期。,
Do you want to update attendance? <br> Present: {0} <br> Absent: {1},您要更新出勤率吗？<br>目前：{0}<br>缺席：{1},
Mpesa Settings,Mpesa设置,
Initiator Name,发起方名称,
Till Number,耕种数,
Sandbox,沙盒,
 Online PassKey,在线密码,
Security Credential,安全凭证,
Get Account Balance,获取帐户余额,
Please set the initiator name and the security credential,请设置发起方名称和安全凭证,
Inpatient Medication Entry,住院药物输入,
HLC-IME-.YYYY.-,HLC-IME-.YYYY.-,
Item Code (Drug),物品代码（药品）,
Medication Orders,药物订单,
Get Pending Medication Orders,获取待处理的药物订单,
Inpatient Medication Orders,住院用药单,
Medication Warehouse,药物仓库,
Warehouse from where medication stock should be consumed,应从那里消耗药品库存的仓库,
Fetching Pending Medication Orders,提取待处理的药物订单,
Inpatient Medication Entry Detail,住院药物输入详细信息,
Medication Details,用药细节,
Drug Code,药品代码,
Drug Name,药品名称,
Against Inpatient Medication Order,反对住院用药令,
Against Inpatient Medication Order Entry,反对住院药物订单输入,
Inpatient Medication Order,住院用药令,
HLC-IMO-.YYYY.-,HLC-IMO-.YYYY.-,
Total Orders,订单总数,
Completed Orders,已完成的订单,
Add Medication Orders,添加药物订单,
Adding Order Entries,添加订单条目,
{0} medication orders completed,{0}个药物订单已完成,
{0} medication order completed,{0}个药物订单已完成,
Inpatient Medication Order Entry,住院药物订单输入,
Is Order Completed,订单完成了吗,
Employee Records to Be Created By,要创建的员工记录,
Employee records are created using the selected field,使用所选字段创建员工记录,
Don't send employee birthday reminders,不要发送员工生日提醒,
Restrict Backdated Leave Applications,限制回请假申请,
Sequence ID,序列号,
Sequence Id,序列编号,
Allow multiple material consumptions against a Work Order,允许根据工单消耗多种物料,
Plan time logs outside Workstation working hours,计划工作站工作时间以外的时间日志,
Plan operations X days in advance,提前X天计划运营,
Time Between Operations (Mins),间隔时间（分钟）,
Default: 10 mins,默认值：10分钟,
Overproduction for Sales and Work Order,销售和工单的生产过剩,
"Update BOM cost automatically via scheduler, based on the latest Valuation Rate/Price List Rate/Last Purchase Rate of raw materials",根据最新的评估价/清单价格/原材料的最新购买价，通过计划程序自动更新BOM成本,
Purchase Order already created for all Sales Order items,已经为所有销售订单项目创建了采购订单,
Select Items,选择项目,
Against Default Supplier,针对默认供应商,
Auto close Opportunity after the no. of days mentioned above,否后自动关闭机会。上述天数,
Is Sales Order Required for Sales Invoice & Delivery Note Creation?,创建销售发票和交货单是否需要销售订单？,
Is Delivery Note Required for Sales Invoice Creation?,创建销售发票是否需要交货单？,
How often should Project and Company be updated based on Sales Transactions?,应根据销售交易更新项目和公司的频率？,
Allow User to Edit Price List Rate in Transactions,允许用户编辑交易中的价目表价格,
Allow Item to Be Added Multiple Times in a Transaction,允许在事务中多次添加项目,
Allow Multiple Sales Orders Against a Customer's Purchase Order,允许针对客户的采购订单的多个销售订单,
Validate Selling Price for Item Against Purchase Rate or Valuation Rate,根据购买率或评估率验证项目的售价,
Hide Customer's Tax ID from Sales Transactions,从销售交易中隐藏客户的税号,
"The percentage you are allowed to receive or deliver more against the quantity ordered. For example, if you have ordered 100 units, and your Allowance is 10%, then you are allowed to receive 110 units.",相对于订购数量，您可以接收或交付更多的百分比。例如，如果您订购了100个单位，而您的津贴为10％，那么您将获得110个单位。,
Action If Quality Inspection Is Not Submitted,未提交质量检验的措施,
Auto Insert Price List Rate If Missing,缺少时自动插入价目表价格,
Automatically Set Serial Nos Based on FIFO,基于FIFO自动设置序列号,
Set Qty in Transactions Based on Serial No Input,根据无序列号输入设置交易数量,
Raise Material Request When Stock Reaches Re-order Level,库存达到再订购水平时提高物料请求,
Notify by Email on Creation of Automatic Material Request,通过电子邮件通知创建自动物料请求,
Allow Material Transfer from Delivery Note to Sales Invoice,允许物料从交货单转移到销售发票,
Allow Material Transfer from Purchase Receipt to Purchase Invoice,允许从收货到采购发票的物料转移,
Freeze Stocks Older Than (Days),冻结大于（天）的库存,
Role Allowed to Edit Frozen Stock,允许角色编辑冻结库存,
The unallocated amount of Payment Entry {0} is greater than the Bank Transaction's unallocated amount,付款条目{0}的未分配金额大于银行交易的未分配金额,
Payment Received,已收到付款,
Attendance cannot be marked outside of Academic Year {0},无法在学年{0}以外标记出勤,
Student is already enrolled via Course Enrollment {0},已经通过课程注册{0}来注册学生,
Attendance cannot be marked for future dates.,无法标记出将来的出勤日期。,
Please add programs to enable admission application.,请添加程序以启用入学申请。,
The following employees are currently still reporting to {0}:,以下员工目前仍在向{0}报告：,
Please make sure the employees above report to another Active employee.,请确保上述员工向另一位在职员工报告。,
Cannot Relieve Employee,无法解雇员工,
Please enter {0},请输入{0},
Please select another payment method. Mpesa does not support transactions in currency '{0}',请选择其他付款方式。 Mpesa不支持使用货币“ {0}”的交易,
Transaction Error,交易错误,
Mpesa Express Transaction Error,Mpesa Express交易错误,
"Issue detected with Mpesa configuration, check the error logs for more details",使用Mpesa配置检测到问题，请查看错误日志以获取更多详细信息,
Mpesa Express Error,Mpesa Express错误,
Account Balance Processing Error,帐户余额处理错误,
Please check your configuration and try again,请检查您的配置，然后重试,
Mpesa Account Balance Processing Error,Mpesa帐户余额处理错误,
Balance Details,余额明细,
Current Balance,当前余额,
Available Balance,可用余额,
Reserved Balance,预留余额,
Uncleared Balance,未结余额,
Payment related to {0} is not completed,与{0}相关的付款尚未完成,
Row #{}: Item Code: {} is not available under warehouse {}.,第{}行：项目代码：{}在仓库{}下不可用。,
Row #{}: Stock quantity not enough for Item Code: {} under warehouse {}. Available quantity {}.,第＃{}行：仓库{}下的库存数量不足以用于项目代码{}。可用数量{}。,
Row #{}: Please select a serial no and batch against item: {} or remove it to complete transaction.,第{}行：请选择一个序列号，并针对{}进行批处理或将其删除以完成交易。,
Row #{}: No serial number selected against item: {}. Please select one or remove it to complete transaction.,行＃{}：未针对项目{}选择序列号。请选择一项或将其删除以完成交易。,
Row #{}: No batch selected against item: {}. Please select a batch or remove it to complete transaction.,行＃{}：未针对项目{}选择批次。请选择一个批次或将其删除以完成交易。,
Payment amount cannot be less than or equal to 0,付款金额不能小于或等于0,
Please enter the phone number first,请先输入电话号码,
Row #{}: {} {} does not exist.,第＃{}行：{} {}不存在。,
Row #{0}: {1} is required to create the Opening {2} Invoices,行＃{0}：创建期初{2}发票需要{1},
You had {} errors while creating opening invoices. Check {} for more details,创建期初发票时出现{}个错误。检查{}了解更多详细信息,
Error Occured,发生了错误,
Opening Invoice Creation In Progress,进行中的开立发票创建,
Creating {} out of {} {},在{} {}中创建{},
(Serial No: {0}) cannot be consumed as it's reserverd to fullfill Sales Order {1}.,（序列号：{0}）无法使用，因为它是完成销售订单{1}的保留。,
Item {0} {1},项目{0} {1},
Last Stock Transaction for item {0} under warehouse {1} was on {2}.,仓库{1}下项目{0}的上次库存交易在{2}上。,
Stock Transactions for Item {0} under warehouse {1} cannot be posted before this time.,在此之前，不能过帐仓库{1}下物料{0}的库存交易。,
Posting future stock transactions are not allowed due to Immutable Ledger,由于总帐不可变，不允许过帐未来的库存交易,
A BOM with name {0} already exists for item {1}.,项目{1}的名称为{0}的BOM已存在。,
{0}{1} Did you rename the item? Please contact Administrator / Tech support,{0} {1}您是否重命名了该项目？请联系管理员/技术支持,
At row #{0}: the sequence id {1} cannot be less than previous row sequence id {2},在第{0}行：序列ID {1}不能小于上一行的序列ID {2},
The {0} ({1}) must be equal to {2} ({3}),{0}（{1}）必须等于{2}（{3}）,
"{0}, complete the operation {1} before the operation {2}.",{0}，在操作{2}之前完成操作{1}。,
Cannot ensure delivery by Serial No as Item {0} is added with and without Ensure Delivery by Serial No.,无法确保按序列号交货，因为添加和不保证按序列号交货都添加了项目{0}。,
Item {0} has no Serial No. Only serilialized items can have delivery based on Serial No,物料{0}没有序列号。只有序列化的物料才能根据序列号交货,
No active BOM found for item {0}. Delivery by Serial No cannot be ensured,找不到项目{0}的活动BOM。无法确保按序列号交货,
No pending medication orders found for selected criteria,找不到符合所选条件的待处理药物订单,
From Date cannot be after the current date.,起始日期不能晚于当前日期。,
To Date cannot be after the current date.,截止日期不能晚于当前日期。,
From Time cannot be after the current time.,“开始时间”不能晚于当前时间。,
To Time cannot be after the current time.,到时间不能晚于当前时间。,
Stock Entry {0} created and ,创建库存条目{0}并,
Inpatient Medication Orders updated successfully,住院用药单已成功更新,
Row {0}: Cannot create Inpatient Medication Entry against cancelled Inpatient Medication Order {1},第{0}行：无法针对已取消的住院药物命令{1}创建住院药物分录,
Row {0}: This Medication Order is already marked as completed,第{0}行：此药物订单已被标记为已完成,
Quantity not available for {0} in warehouse {1},仓库{1}中{0}不可用的数量,
Please enable Allow Negative Stock in Stock Settings or create Stock Entry to proceed.,请启用“允许库存设置中的负库存”或创建“库存输入”以继续。,
No Inpatient Record found against patient {0},找不到针对患者{0}的住院记录,
An Inpatient Medication Order {0} against Patient Encounter {1} already exists.,针对患者遭遇{1}的住院药物命令{0}已存在。,
Allow In Returns,允许退货,
Hide Unavailable Items,隐藏不可用的物品,
Apply Discount on Discounted Rate,对折现率应用折扣,
Therapy Plan Template,治疗计划模板,
Fetching Template Details,提取模板详细信息,
Linked Item Details,链接项目详细信息,
Therapy Types,治疗类型,
Therapy Plan Template Detail,治疗计划模板详细信息,
Non Conformance,不合格,
Process Owner,流程负责人,
Corrective Action,纠正措施,
Preventive Action,预防措施,
Problem,问题,
Responsible,负责任的,
Completion By,完成方式,
Process Owner Full Name,流程所有者全名,
Right Index,正确的索引,
Left Index,左索引,
Sub Procedure,子程序,
Passed,已通过,
Print Receipt,打印收据,
Edit Receipt,编辑收据,
Focus on search input,专注于搜索输入,
Focus on Item Group filter,专注于项目组过滤器,
Checkout Order / Submit Order / New Order,结帐订单/提交订单/新订单,
Add Order Discount,添加订单折扣,
Item Code: {0} is not available under warehouse {1}.,项目代码：{0}在仓库{1}下不可用。,
Serial numbers unavailable for Item {0} under warehouse {1}. Please try changing warehouse.,仓库{1}下项目{0}的序列号不可用。请尝试更换仓库。,
Fetched only {0} available serial numbers.,仅获取了{0}个可用序列号。,
Switch Between Payment Modes,在付款模式之间切换,
Enter {0} amount.,输入{0}金额。,
You don't have enough points to redeem.,您的积分不足以兑换。,
You can redeem upto {0}.,您最多可以兑换{0}。,
Enter amount to be redeemed.,输入要兑换的金额。,
You cannot redeem more than {0}.,您最多只能兑换{0}个。,
Open Form View,打开表单视图,
POS invoice {0} created succesfully,成功创建POS发票{0},
Stock quantity not enough for Item Code: {0} under warehouse {1}. Available quantity {2}.,仓库{1}下的存货数量不足以用于物料代码{0}。可用数量{2}。,
Serial No: {0} has already been transacted into another POS Invoice.,序列号：{0}已被交易到另一个POS发票中。,
Balance Serial No,天平序列号,
Warehouse: {0} does not belong to {1},仓库：{0}不属于{1},
Please select batches for batched item {0},请为批次项目{0}选择批次,
Please select quantity on row {0},请在第{0}行上选择数量,
Please enter serial numbers for serialized item {0},请输入序列化项目{0}的序列号,
Batch {0} already selected.,已选择批次{0}。,
Please select a warehouse to get available quantities,请选择一个仓库以获取可用数量,
"For transfer from source, selected quantity cannot be greater than available quantity",对于从源转移，所选数量不能大于可用数量,
Cannot find Item with this Barcode,用此条形码找不到物品,
{0} is mandatory. Maybe Currency Exchange record is not created for {1} to {2},{0}是必需的。也许没有为{1}至{2}创建货币兑换记录,
{} has submitted assets linked to it. You need to cancel the assets to create purchase return.,{}已提交与其关联的资产。您需要取消资产以创建购买退货。,
Cannot cancel this document as it is linked with submitted asset {0}. Please cancel it to continue.,由于该文档与已提交的资产{0}链接，因此无法取消。请取消它以继续。,
Row #{}: Serial No. {} has already been transacted into another POS Invoice. Please select valid serial no.,第{}行：序列号{}已被交易到另一个POS发票中。请选择有效的序列号,
Row #{}: Serial Nos. {} has already been transacted into another POS Invoice. Please select valid serial no.,第{}行：序列号{}已被交易到另一个POS发票中。请选择有效的序列号,
Item Unavailable,物品不可用,
Row #{}: Serial No {} cannot be returned since it was not transacted in original invoice {},第＃{}行：由于未在原始发票{}中进行交易，因此无法返回序列号{},
Please set default Cash or Bank account in Mode of Payment {},请在付款方式{}中设置默认的现金或银行帐户,
Please set default Cash or Bank account in Mode of Payments {},请在付款方式{}中设置默认的现金或银行帐户,
Please ensure {} account is a Balance Sheet account. You can change the parent account to a Balance Sheet account or select a different account.,请确保{}帐户是资产负债表帐户。您可以将父帐户更改为资产负债表帐户，也可以选择其他帐户。,
Please ensure {} account is a Payable account. Change the account type to Payable or select a different account.,请确保{}帐户是应付帐户。将帐户类型更改为“应付帐款”或选择其他帐户。,
Row {}: Expense Head changed to {} ,第{}行：费用总目已更改为{},
because account {} is not linked to warehouse {} ,因为帐户{}未链接到仓库{},
or it is not the default inventory account,或它不是默认的库存帐户,
Expense Head Changed,费用总目已更改,
because expense is booked against this account in Purchase Receipt {},因为费用是在采购收据{}中为此帐户预订的,
as no Purchase Receipt is created against Item {}. ,因为没有针对物料{}创建采购收据。,
This is done to handle accounting for cases when Purchase Receipt is created after Purchase Invoice,这样做是为了处理在采购发票后创建采购收货的情况,
Purchase Order Required for item {},项目{}所需的采购订单,
To submit the invoice without purchase order please set {} ,要提交不含采购订单的发票，请设置{},
as {} in {},如{}中的{},
Mandatory Purchase Order,强制性采购订单,
Purchase Receipt Required for item {},项目{}的采购收据,
To submit the invoice without purchase receipt please set {} ,要提交没有购买收据的发票，请设置{},
Mandatory Purchase Receipt,强制性收货,
POS Profile {} does not belongs to company {},POS个人资料{}不属于公司{},
User {} is disabled. Please select valid user/cashier,用户{}被禁用。请选择有效的用户/出纳员,
Row #{}: Original Invoice {} of return invoice {} is {}. ,第＃{}行：退货发票{}的原始发票{}为{}。,
Original invoice should be consolidated before or along with the return invoice.,原始发票应在退货发票之前或与之合并。,
You can add original invoice {} manually to proceed.,您可以手动添加原始发票{}以继续。,
Please ensure {} account is a Balance Sheet account. ,请确保{}帐户是资产负债表帐户。,
You can change the parent account to a Balance Sheet account or select a different account.,您可以将父帐户更改为资产负债表帐户，也可以选择其他帐户。,
Please ensure {} account is a Receivable account. ,请确保{}帐户是应收帐款帐户。,
Change the account type to Receivable or select a different account.,将帐户类型更改为“应收帐款”或选择其他帐户。,
{} can't be cancelled since the Loyalty Points earned has been redeemed. First cancel the {} No {},由于所赚取的忠诚度积分已被兑换，因此无法取消{}。首先取消{}否{},
already exists,已经存在,
POS Closing Entry {} against {} between selected period,选定期间之间的POS关闭条目{}对{},
POS Invoice is {},POS发票为{},
POS Profile doesn't matches {},POS个人资料与{}不匹配,
POS Invoice is not {},POS发票不是{},
POS Invoice isn't created by user {},POS发票不是由用户{}创建的,
Row #{}: {},第＃{}行：{},
Invalid POS Invoices,无效的POS发票,
Please add the account to root level Company - {},请将帐户添加到根级别的公司-{},
"While creating account for Child Company {0}, parent account {1} not found. Please create the parent account in corresponding COA",为子公司{0}创建帐户时，找不到父帐户{1}。请在相应的COA中创建上级帐户,
Account Not Found,找不到帐户,
"While creating account for Child Company {0}, parent account {1} found as a ledger account.",在为子公司{0}创建帐户时，发现父帐户{1}是分类帐。,
Please convert the parent account in corresponding child company to a group account.,请将相应子公司中的母公司帐户转换为组帐户。,
Invalid Parent Account,无效的上级帐户,
"Renaming it is only allowed via parent company {0}, to avoid mismatch.",重命名仅允许通过母公司{0}进行，以避免不匹配。,
"If you {0} {1} quantities of the item {2}, the scheme {3} will be applied on the item.",如果您{0} {1}数量的项目{2}，则方案{3}将应用于该项目。,
"If you {0} {1} worth item {2}, the scheme {3} will be applied on the item.",如果您{0} {1}值得项目{2}，则方案{3}将应用于该项目。,
"As the field {0} is enabled, the field {1} is mandatory.",当启用字段{0}时，字段{1}是必填字段。,
"As the field {0} is enabled, the value of the field {1} should be more than 1.",启用字段{0}时，字段{1}的值应大于1。,
Cannot deliver Serial No {0} of item {1} as it is reserved to fullfill Sales Order {2},无法交付物料{1}的序列号{0}，因为已保留该物料以填写销售订单{2},
"Sales Order {0} has reservation for the item {1}, you can only deliver reserved {1} against {0}.",销售订单{0}对物料{1}有保留，您只能针对{0}交付保留的{1}。,
{0} Serial No {1} cannot be delivered,{0}序列号{1}无法传递,
Row {0}: Subcontracted Item is mandatory for the raw material {1},第{0}行：原材料{1}必须使用转包物料,
"As there are sufficient raw materials, Material Request is not required for Warehouse {0}.",由于有足够的原材料，因此仓库{0}不需要“物料请求”。,
" If you still want to proceed, please enable {0}.",如果仍然要继续，请启用{0}。,
The item referenced by {0} - {1} is already invoiced,{0}-{1}引用的商品已开票,
Therapy Session overlaps with {0},治疗会话与{0}重叠,
Therapy Sessions Overlapping,治疗会议重叠,
Therapy Plans,治疗计划,
"Item Code, warehouse, quantity are required on row {0}",在第{0}行中需要提供物料代码，仓库，数量,
Get Items from Material Requests against this Supplier,从针对此供应商的物料请求中获取物料,
Enable European Access,启用欧洲访问,
Creating Purchase Order ...,创建采购订单...,
"Select a Supplier from the Default Suppliers of the items below. On selection, a Purchase Order will be made against items belonging to the selected Supplier only.",从以下各项的默认供应商中选择供应商。选择后，将针对仅属于所选供应商的项目下达采购订单。,
Row #{}: You must select {} serial numbers for item {}.,行号{}：您必须为项目{}选择{}序列号。,
Add Comment,添加评论,
More...,更多...,
Notes,便签,
Payment Gateway,支付网关,
Payment Gateway Name,支付网关名称,
Payments,付款,
Plan Name,计划名称,
Portal,门户,
Scan Barcode,扫条码,
Some information is missing,一些信息缺失,
Successful,成功,
Tools,工具,
Use Sandbox,使用沙盒,
Busy,忙,
Completed By,由...完成,
Payment Failed,支付失败,
Column {0},列{0},
Field Mapping,场图,
Not Specified,未标明,
Update Type,更新类型,
Dr,借方,
End Time,结束时间,
Fetching...,正在获取...,
"It seems that there is an issue with the server's stripe configuration. In case of failure, the amount will get refunded to your account.",看起来服务器的条带配置存在问题。如果失败，这笔款项将退还给您的账户。,
Looks like someone sent you to an incomplete URL. Please ask them to look into it.,貌似有人送你一个不完整的URL。请让他们寻找到它。,
Master,大师,
Pay,支付,
You can also copy-paste this link in your browser,您也可以复制粘贴此链接到您的浏览器地址栏中,
Verified By,认证,
Invalid naming series (. missing) for {0},{0}的无效命名系列（。丢失）,
Phone Number,电话号码,
Account SID,帐户SID,
Global Defaults,全局默认值,
Is Mandatory,是强制性的,
WhatsApp,WhatsApp的,
Make a call,打个电话,
Approve,同意,
Reject,拒绝,
