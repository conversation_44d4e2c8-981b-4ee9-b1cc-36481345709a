[project]
name = "erpnext"
authors = [
    { name = "Frappe Technologies Pvt Ltd", email = "<EMAIL>"}
]
description = "Open Source ERP"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    # Core dependencies
    "pycountry~=22.3.5",
    "Unidecode~=1.3.6",
    "barcodenumber~=0.5.0",
    "rapidfuzz~=2.15.0",
    "holidays~=0.28",

    # integration dependencies
    "googlemaps",
    "plaid-python~=7.2.1",
    "python-youtube~=0.8.0",

    # Not used directly - required by PyQRCode for PNG generation
    "pypng~=0.20220715.0",
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.black]
line-length = 99

[tool.isort]
line_length = 99
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
indent = "\t"

[tool.bench.frappe-dependencies]
frappe = ">=15.10.0,<16.0.0"
