# import frappe
# import os
# import json

# def install_workspaces():
#     """
#     تثبيت ملفات الـ workspace من مجلد workspace
#     """
#     workspace_path = os.path.join(frappe.get_app_path("erp_optimizer_ui"), "erp_optimizer_ui", "workspace")
    
#     # تحقق من وجود المجلد
#     if not os.path.exists(workspace_path):
#         frappe.log_error(f"مجلد workspace غير موجود: {workspace_path}")
#         return
    
#     # قائمة بملفات JSON في المجلد
#     json_files = [f for f in os.listdir(workspace_path) if f.endswith('.json')]
    
#     if not json_files:
#         frappe.log_error(f"لا توجد ملفات JSON في المجلد: {workspace_path}")
#         return
    
#     # تثبيت كل ملف workspace
#     for filename in json_files:
#         file_path = os.path.join(workspace_path, filename)
#         try:
#             with open(file_path, 'r', encoding='utf-8') as f:
#                 data = json.load(f)
                
#                 # تحقق من وجود الـ workspace
#                 workspace_name = data.get("name")
#                 if not workspace_name:
#                     frappe.log_error(f"اسم الـ workspace غير موجود في الملف: {filename}")
#                     continue
                
#                 # تحقق من وجود الـ workspace في قاعدة البيانات
#                 if frappe.db.exists("Workspace", workspace_name):
#                     # تحديث الـ workspace الموجود
#                     doc = frappe.get_doc("Workspace", workspace_name)
#                 else:
#                     # إنشاء workspace جديد
#                     doc = frappe.new_doc("Workspace")
#                     doc.name = workspace_name
                
#                 # تحديث البيانات
#                 doc.label = data.get("label")
#                 doc.module = data.get("module")
#                 doc.is_standard = data.get("is_standard", 1)
                
#                 # تحويل محتوى الـ workspace إلى JSON string إذا كان قائمة
#                 content = data.get("content")
#                 if isinstance(content, list):
#                     doc.content = json.dumps(content)
#                 else:
#                     doc.content = content
                
#                 # حفظ التغييرات
#                 doc.save()
#                 frappe.db.commit()
#                 print(f"تم تثبيت/تحديث workspace: {workspace_name}")
                
#         except Exception as e:
#             frappe.log_error(f"خطأ في تثبيت workspace {filename}: {str(e)}")
#             print(f"خطأ في تثبيت workspace {filename}: {str(e)}")

# if __name__ == "__main__":
#     install_workspaces()
import frappe
import os
import json

def install_workspaces():
    """
    تثبيت ملفات Workspace من مجلد workspace داخل تطبيق erp_optimizer_ui
    مع منع التكرار عند وجود نفس المحتوى.
    """
    app_name = "erp_optimizer_ui"
    workspace_path = os.path.join(frappe.get_app_path(app_name), app_name, "workspace")

    if not os.path.isdir(workspace_path):
        frappe.log_error(f"مجلد workspace غير موجود: {workspace_path}", "Install Workspaces")
        return

    json_files = [f for f in os.listdir(workspace_path) if f.endswith('.json')]

    if not json_files:
        frappe.log_error(f"لا توجد ملفات JSON في المجلد: {workspace_path}", "Install Workspaces")
        return

    for filename in json_files:
        file_path = os.path.join(workspace_path, filename)

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            workspace_name = data.get("name")
            if not workspace_name:
                frappe.log_error(f"اسم workspace غير موجود في الملف: {filename}", "Install Workspaces")
                continue

            content_json = json.dumps(data.get("content", []), ensure_ascii=False, indent=2)

            if frappe.db.exists("Workspace", workspace_name):
                doc = frappe.get_doc("Workspace", workspace_name)

                # تحقق من التطابق
                existing_content = doc.content.strip() if doc.content else ""
                new_content = content_json.strip()

                if existing_content == new_content:
                    print(f"⏭️ لا تغيير في Workspace: {workspace_name} (تم التجاوز)")
                    continue  # لا حاجة للتحديث

                # تحديث المحتوى فقط عند التغيير
                doc.content = new_content
                doc.label = data.get("label", workspace_name)
                doc.module = data.get("module", "")
                doc.is_standard = data.get("is_standard", 1)
                doc.public = data.get("public", 1)
                doc.restrict_to_domain = data.get("restrict_to_domain", "")
                doc.hide_custom = data.get("hide_custom", 0)

                doc.save(ignore_permissions=True)
                frappe.db.commit()
                print(f"🔄 تم تحديث Workspace: {workspace_name}")

            else:
                doc = frappe.new_doc("Workspace")
                doc.name = workspace_name
                doc.label = data.get("label", workspace_name)
                doc.module = data.get("module", "")
                doc.is_standard = data.get("is_standard", 1)
                doc.public = data.get("public", 1)
                doc.restrict_to_domain = data.get("restrict_to_domain", "")
                doc.hide_custom = data.get("hide_custom", 0)
                doc.content = content_json

                doc.insert(ignore_permissions=True)
                frappe.db.commit()
                print(f"✅ تم إنشاء Workspace جديد: {workspace_name}")

        except Exception as e:
            frappe.log_error(title=f"Install Workspace Error: {filename}", message=str(e))
            print(f"❌ خطأ في تثبيت Workspace {filename}: {str(e)}")

def run():
    install_workspaces()
