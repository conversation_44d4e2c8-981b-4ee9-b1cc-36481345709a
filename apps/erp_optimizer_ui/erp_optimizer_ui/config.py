import frappe
import os
import json

def apply_workspace_customizations():
    """
    تطبيق التخصيصات على الـ workspaces
    """
    # قائمة بالـ workspaces التي نريد تخصيصها
    workspaces = [
        "المبيعات",
        "الشراء",
        "المخزون",
        "الحسابات",
        "التصنيع",
        "الموارد البشرية",
        "إدارة العملاء",
        "الأصول"
    ]
    
    # التحقق من وجود الـ workspaces وتحديثها
    for workspace_name in workspaces:
        try:
            # التحقق من وجود الـ workspace في قاعدة البيانات
            if frappe.db.exists("Workspace", workspace_name):
                # تحديث الـ workspace
                doc = frappe.get_doc("Workspace", workspace_name)
                doc.is_standard = 1
                doc.save()
                frappe.db.commit()
                print(f"تم تحديث workspace: {workspace_name}")
            else:
                # البحث عن ملف الـ workspace
                workspace_path = os.path.join(frappe.get_app_path("erp_optimizer_ui"), "erp_optimizer_ui", "workspace", f"{workspace_name}.json")
                
                if os.path.exists(workspace_path):
                    with open(workspace_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        
                        # إنشاء workspace جديد
                        doc = frappe.new_doc("Workspace")
                        doc.name = workspace_name
                        doc.label = data.get("label")
                        doc.module = data.get("module")
                        doc.is_standard = data.get("is_standard", 1)
                        
                        # تحويل محتوى الـ workspace إلى JSON string إذا كان قائمة
                        content = data.get("content")
                        if isinstance(content, list):
                            doc.content = json.dumps(content)
                        else:
                            doc.content = content
                        
                        # حفظ التغييرات
                        doc.save()
                        frappe.db.commit()
                        print(f"تم إنشاء workspace: {workspace_name}")
                else:
                    print(f"ملف workspace غير موجود: {workspace_name}")
        except Exception as e:
            frappe.log_error(f"خطأ في تحديث workspace {workspace_name}: {str(e)}")
            print(f"خطأ في تحديث workspace {workspace_name}: {str(e)}")

def execute():
    """
    تنفيذ التخصيصات
    """
    apply_workspace_customizations()