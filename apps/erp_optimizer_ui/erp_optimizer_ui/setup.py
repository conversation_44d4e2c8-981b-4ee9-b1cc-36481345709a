import frappe
from frappe import _

def after_install():
    """
    تنفيذ الإجراءات بعد تثبيت التطبيق
    """
    # استيراد الـ workspaces
    import_workspaces()
    
    # تفعيل التخصيصات
    setup_customizations()
    
    frappe.msgprint(_("تم تثبيت تطبيق واجهات ERP العصرية بنجاح"))

def import_workspaces():
    """
    استيراد ملفات الـ workspace
    """
    import os
    import json
    
    workspace_path = frappe.get_app_path("erp_optimizer_ui", "erp_optimizer_ui", "workspace")
    for filename in os.listdir(workspace_path):
        if filename.endswith('.json'):
            file_path = os.path.join(workspace_path, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                try:
                    data = json.load(f)
                    # تحقق من وجود الـ workspace
                    workspace_name = data.get("name")
                    if not frappe.db.exists("Workspace", workspace_name):
                        doc = frappe.new_doc("Workspace")
                        doc.name = workspace_name
                        doc.label = data.get("label")
                        doc.module = data.get("module")
                        doc.is_standard = data.get("is_standard", 1)
                        doc.content = json.dumps(data.get("content", []))
                        doc.save()
                        frappe.db.commit()
                        print(f"تم إنشاء workspace: {workspace_name}")
                    else:
                        # تحديث الـ workspace الموجود
                        doc = frappe.get_doc("Workspace", workspace_name)
                        doc.label = data.get("label")
                        doc.module = data.get("module")
                        doc.is_standard = data.get("is_standard", 1)
                        doc.content = json.dumps(data.get("content", []))
                        doc.save()
                        frappe.db.commit()
                        print(f"تم تحديث workspace: {workspace_name}")
                except Exception as e:
                    frappe.log_error(f"خطأ في استيراد workspace {filename}: {str(e)}")

def setup_customizations():
    """
    تفعيل التخصيصات الأخرى
    """
    # تفعيل ملفات CSS و JS
    update_hooks()
    
    # تحديث الإعدادات
    frappe.db.set_value("Website Settings", "Website Settings", "app_name", "نظام ERP العصري")
    
    # مسح ذاكرة التخزين المؤقت
    frappe.clear_cache()

def update_hooks():
    """
    التأكد من تفعيل hooks.py
    """
    # يمكن إضافة أي تحديثات إضافية هنا إذا لزم الأمر
    pass