
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

body {
    font-family: 'Cairo', sans-serif !important;
}

/* تخصيص الألوان الرئيسية */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* تخصيص الخلفية */
.page-container {
    background-color: var(--light-color);
}

/* تخصيص الأزرار */
.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-secondary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

/* تخصيص القوائم */
.navbar-light {
    background-color: white !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* تخصيص الجداول */
.grid-row {
    border-radius: 4px;
}

.grid-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* تخصيص الـ workspace */
.desk-sidebar .standard-sidebar-item.selected {
    background-color: var(--primary-color) !important;
}

.desk-sidebar .standard-sidebar-item:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

/* تخصيص الـ cards */
.widget.links-widget-box {
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.widget.links-widget-box:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* تخصيص الـ form */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تخصيص الـ tabs */
.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

/* تخصيص الـ workspace */
.workspace-sidebar-item.selected {
    background-color: var(--primary-color) !important;
    color: white !important;
}
