
frappe.ui.form.on('Sales Invoice', {
    customer: function(frm) {
        if (frm.doc.customer) {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Sales Invoice",
                    filters: { customer: frm.doc.customer },
                    fields: ["name"]
                },
                callback: function(r) {
                    let invoice_count = r.message.length;
                    frappe.call({
                        method: "frappe.client.get_list",
                        args: {
                            doctype: "Payment Entry",
                            filters: { party: frm.doc.customer },
                            fields: ["name"]
                        },
                        callback: function(res) {
                            let payment_count = res.message.length;
                            frappe.msgprint({
                                title: "معلومات العميل",
                                indicator: "blue",
                                message: `🔔 <b>${frm.doc.customer}</b><br>
                                    🧾 عدد الفواتير: <b>${invoice_count}</b><br>
                                    💰 عدد السندات: <b>${payment_count}</b><br>
                                    <br><a href='/app/sales-invoice?customer=${frm.doc.customer}' target='_blank'>عرض الفواتير</a><br>
                                    <a href='/app/payment-entry?party=${frm.doc.customer}' target='_blank'>عرض السندات</a>`
                            });
                        }
                    });
                }
            });
        }
    }
});
