// تخصيص الـ workspace
frappe.provide('erp_optimizer_ui');

erp_optimizer_ui.customize_workspace = function() {
    // تطبيق التخصيصات على الـ workspace
    $(document).on('page-change', function() {
        if (frappe.get_route()[0] === 'workspace') {
            setTimeout(function() {
                // تخصيص العناوين
                $('.workspace-title').css({
                    'font-family': 'Cairo, sans-serif',
                    'font-weight': 'bold',
                    'color': '#007bff'
                });
                
                // تخصيص الأيقونات
                $('.shortcut-widget-box .widget-title').css({
                    'font-family': 'Cairo, sans-serif',
                    'font-weight': 'bold'
                });
                
                // تخصيص البطاقات
                $('.links-widget-box').css({
                    'border-radius': '8px',
                    'box-shadow': '0 2px 5px rgba(0,0,0,0.05)',
                    'transition': 'all 0.3s ease'
                });
                
                $('.links-widget-box').hover(
                    function() {
                        $(this).css({
                            'box-shadow': '0 5px 15px rgba(0,0,0,0.1)',
                            'transform': 'translateY(-2px)'
                        });
                    },
                    function() {
                        $(this).css({
                            'box-shadow': '0 2px 5px rgba(0,0,0,0.05)',
                            'transform': 'translateY(0)'
                        });
                    }
                );
                
                // تخصيص القائمة الجانبية
                $('.desk-sidebar .standard-sidebar-item.selected').css({
                    'background-color': '#007bff',
                    'color': 'white'
                });
                
                // إضافة أيقونات للروابط
                $('.widget-content .link-item').each(function() {
                    let text = $(this).text().trim();
                    let icon = '';
                    
                    if (text.includes('فاتورة') || text.includes('Invoice')) {
                        icon = '📄 ';
                    } else if (text.includes('عميل') || text.includes('Customer')) {
                        icon = '👤 ';
                    } else if (text.includes('مورد') || text.includes('Supplier')) {
                        icon = '🏭 ';
                    } else if (text.includes('صنف') || text.includes('Item')) {
                        icon = '📦 ';
                    } else if (text.includes('مخزون') || text.includes('Stock')) {
                        icon = '🏪 ';
                    } else if (text.includes('تقرير') || text.includes('Report')) {
                        icon = '📊 ';
                    } else if (text.includes('دفع') || text.includes('Payment')) {
                        icon = '💰 ';
                    } else {
                        icon = '🔗 ';
                    }
                    
                    $(this).html(icon + text);
                });
            }, 500);
        }
    });
};

// تنفيذ التخصيصات عند تحميل الصفحة
$(document).ready(function() {
    erp_optimizer_ui.customize_workspace();
});