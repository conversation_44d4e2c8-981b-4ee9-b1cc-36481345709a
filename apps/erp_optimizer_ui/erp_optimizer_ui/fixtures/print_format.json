[{"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "Purchase Order", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": "<div class=\"page-break\">\n    <div class=\"print-heading\">\n\t\t<h2>Purchase Order<br>\n\t        <small>{{doc.name}}</small>\n        </h2>\n    </div>\n    <div class=\"row section-break\">\n        <div class=\"col-xs-6 column-break\">\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Supplier Name</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.supplier_name}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t{% if doc.address_display %}\n\t                <div class=\"row\">\n\t\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t\t<label>Address</label>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t\t{{doc.address_display}}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t{%- endif -%}\n\t\t\t\t{% if doc.contact_display %}\n\t                <div class=\"row\">\n\t\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t\t<label>Contact</label>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t\t{{doc.contact_display}}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t{%- endif -%}\n\t\t\t\t{% if doc.contact_mobile %}\n\t                <div class=\"row\">\n\t\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t\t<label>Mobile No</label>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t\t{{doc.contact_mobile}}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t{%- endif -%}\n        </div>\n        \n        <div class=\"col-xs-6 column-break\">\n            <div class=\"row\">\n\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t<label>Date</label>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t{{doc.transaction_date}}\n\t\t\t\t</div>\n\t\t\t</div>\n            <div class=\"row\">\n\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t<label>Customer Name</label>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t{{doc.customer_name}}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t{% if doc.shipping_address_display %}\n\t            <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Customer Address</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.shipping_address_display}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t{%- endif -%}\n\t\t\t{% if doc.customer_contact_display %}\n\t            <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Customer Contact</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.customer_contact_display}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t{%- endif -%}\n\t\t\t{% if doc.customer_contact_mobile %}\n\t            <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Customer Mobile No</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7  value\">\n\t\t\t\t\t\t{{doc.customer_contact_mobile}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t{%- endif -%}\n        </div>\n    </div>\n\t<table class=\"table table-bordered\">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<th>Sr</th>\n\t\t\t\t<th>Item Name</th>\n\t\t\t\t<th>Description</th>\n\t\t\t\t<th class=\"text-right\">Qty</th>\n\t\t\t\t<th class=\"text-right\">Rate</th>\n\t\t\t\t<th class=\"text-right\">Amount</th>\n\t\t\t</tr>\n\t\t\t{%- for row in doc.items -%}\n\t\t\t<tr>\n\t\t\t\t<td style=\"width: 3%;\">{{ row.idx }}</td>\n\t\t\t\t<td style=\"width: 20%;\">\n\t\t\t\t\t{{ row.item_name }}\n\t\t\t\t\t{% if row.item_code != row.item_name -%}\n\t\t\t\t\t<br>Item Code: {{ row.item_code}}\n\t\t\t\t\t{%- endif %}\n\t\t\t\t</td>\n\t\t\t\t<td style=\"width: 37%;\">\n\t\t\t\t\t<div style=\"border: 0px;\">{{ row.description }}</div></td>\n\t\t\t\t<td style=\"width: 10%; text-align: right;\">{{ row.qty }} {{ row.uom or row.stock_uom }}</td>\n\t\t\t\t<td style=\"width: 15%; text-align: right;\">{{\n\t\t\t\t\trow.get_formatted(\"rate\", doc) }}</td>\n\t\t\t\t<td style=\"width: 15%; text-align: right;\">{{\n\t\t\t\t\trow.get_formatted(\"amount\", doc) }}</td>\n\t\t\t</tr>\n\t\t\t{%- endfor -%}\n\t\t</tbody>\n\t</table>\n    <div class=\"row section-break\">\n        <div class=\"col-xs-6 column-break\">\n        </div>\n        <div class=\"col-xs-6 column-break\">\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Total</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7 text-right value\">\n\t\t\t\t\t\t{{doc.total}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>Grand Total</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7 text-right value\">\n\t\t\t\t\t\t{{doc.grand_total}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n                <div class=\"row\">\n\t\t\t\t\t<div class=\"col-xs-5 text-right\">\n\t\t\t\t\t\t<label>In Words</label>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"col-xs-7 text-right value\">\n\t\t\t\t\t\t{{doc.in_words}}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n        </div>\n    </div>\n</div>", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2016-03-07 20:49:39.443328", "module": "Buying", "name": "Drop Shipping Format", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": "<style>\n\t.print-format table, .print-format tr, \n\t.print-format td, .print-format div, .print-format p {\n\t\tfont-family: Monospace;\n\t\tline-height: 200%;\n\t\tvertical-align: middle;\n\t}\n\t@media screen {\n\t\t.print-format {\n\t\t\twidth: 4in;\n\t\t\tpadding: 0.25in;\n\t\t\tmin-height: 8in;\n\t\t}\n\t}\n</style>\n\n<p class=\"text-center\">\n\t{{ company }}<br>\n\t{{  __(\"POS No : \") }} {{ offline_pos_name }}<br>\n</p>\n<p>\n\t<b>{{ __(\"Customer\") }}:</b> {{ customer }}<br>\n</p>\n\n<p>\n\t<b>{{ __(\"Date\") }}:</b> {{ dateutil.global_date_format(posting_date) }}<br>\n</p>\n\n<hr>\n<table class=\"table table-condensed cart no-border\">\n\t<thead>\n\t\t<tr>\n\t\t\t<th width=\"50%\">{{ __(\"Item\") }}</b></th>\n\t\t\t<th width=\"25%\" class=\"text-right\">{{ __(\"Qty\") }}</th>\n\t\t\t<th width=\"25%\" class=\"text-right\">{{ __(\"Amount\") }}</th>\n\t\t</tr>\n\t</thead>\n\t<tbody>\n\t\t{% for item in items %}\n\t\t<tr>\n\t\t\t<td>\n\t\t\t\t{{ item.item_name }}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">{{ format_number(item.qty, null,precision(\"difference\")) }}<br>@ {{ format_currency(item.rate, currency) }}</td>\n\t\t\t<td class=\"text-right\">{{ format_currency(item.amount, currency) }}</td>\n\t\t</tr>\n\t\t{% endfor %}\n\t</tbody>\n</table>\n\n<table class=\"table table-condensed no-border\">\n\t<tbody>\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t{{ __(\"Net Total\") }}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ format_currency(total, currency) }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{% for row in taxes %}\n\t\t{% if not row.included_in_print_rate %}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t{{ row.description }}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ format_currency(row.tax_amount, currency) }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{% endif %}\n\t\t{% endfor %}\n\t\t{% if discount_amount %}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t{{ __(\"Discount\") }}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ format_currency(discount_amount, currency) }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{% endif %}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ __(\"Grand Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ format_currency(grand_total, currency) }}\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ __(\"Paid Amount\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ format_currency(paid_amount, currency) }}\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ __(\"Qty Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ qty_total }}\n\t\t\t</td>\n\t\t</tr>\n\t</tbody>\n</table>\n\n\n<hr>\n<p>{{ terms }}</p>\n<p class=\"text-center\">{{ __(\"Thank you, please visit again.\") }}</p>", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-09-05 17:20:30.726659", "module": "Accounts", "name": "Point of Sale", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "JS", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 1, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>TAX Invoice<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name\", \"label\": \"Customer Name\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name_in_arabic\", \"label\": \"Customer Name in Arabic\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"posting_date\", \"label\": \"Date\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company\", \"label\": \"Company\"}, {\"print_hide\": 0, \"fieldname\": \"company_trn\", \"label\": \"Company TRN\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company_address_display\", \"label\": \"Company Address\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"item_code\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"200px\"}, {\"print_hide\": 0, \"fieldname\": \"uom\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_code\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"items\", \"label\": \"Items\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"label\": \"Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"charge_type\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"row_id\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"account_head\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"cost_center\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"300px\"}, {\"print_hide\": 0, \"fieldname\": \"rate\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"item_wise_tax_detail\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"taxes\", \"label\": \"Sales Taxes and Charges\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"grand_total\", \"label\": \"Grand Total\"}, {\"print_hide\": 0, \"fieldname\": \"rounded_total\", \"label\": \"Rounded Total\"}, {\"print_hide\": 0, \"fieldname\": \"in_words\", \"align\": \"left\", \"label\": \"In Words\"}]", "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2017-12-15 11:57:20.498724", "module": "Regional", "name": "Simplified Tax Invoice", "page_number": null, "pdf_generator": null, "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 1, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>TAX Invoice<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name\", \"label\": \"Customer Name\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name_in_arabic\", \"label\": \"Customer Name in Arabic\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"posting_date\", \"label\": \"Date\"}, {\"print_hide\": 0, \"fieldname\": \"project\", \"align\": \"left\", \"label\": \"Reverse Charge Applicable\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"address_display\", \"label\": \"Address\"}, {\"print_hide\": 0, \"fieldname\": \"tax_id\", \"label\": \"Tax Id\"}, {\"print_hide\": 0, \"fieldname\": \"contact_display\", \"label\": \"Contact\"}, {\"print_hide\": 0, \"fieldname\": \"contact_mobile\", \"label\": \"Mobile No\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company\", \"label\": \"Company\"}, {\"print_hide\": 0, \"fieldname\": \"company_address_display\", \"label\": \"Company Address\"}, {\"print_hide\": 0, \"fieldname\": \"company_trn\", \"label\": \"Company TRN\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"item_code\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"200px\"}, {\"print_hide\": 0, \"fieldname\": \"image\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"qty\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"uom\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"rate\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_code\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"items\", \"label\": \"Items\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"label\": \"Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"charge_type\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"row_id\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"account_head\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"cost_center\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"300px\"}, {\"print_hide\": 0, \"fieldname\": \"rate\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"item_wise_tax_detail\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"taxes\", \"label\": \"Sales Taxes and Charges\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"grand_total\", \"label\": \"Grand Total\"}, {\"print_hide\": 0, \"fieldname\": \"rounded_total\", \"label\": \"Rounded Total\"}, {\"print_hide\": 0, \"fieldname\": \"in_words\", \"align\": \"left\", \"label\": \"In Words\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"other_charges_calculation\", \"label\": \"Taxes and Charges Calculation\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Terms\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"terms\", \"label\": \"Terms and Conditions Details\"}]", "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2017-12-28 13:39:26.068659", "module": "Regional", "name": "Detailed Tax Invoice", "page_number": null, "pdf_generator": null, "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 1, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>TAX Invoice<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name\", \"label\": \"Customer Name\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name_in_arabic\", \"label\": \"Customer Name in Arabic\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"posting_date\", \"label\": \"Date\"}, {\"print_hide\": 0, \"fieldname\": \"project\", \"align\": \"left\", \"label\": \"Reverse Charge Applicable\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"address_display\", \"label\": \"Address\"}, {\"print_hide\": 0, \"fieldname\": \"tax_id\", \"label\": \"Tax Id\"}, {\"print_hide\": 0, \"fieldname\": \"contact_display\", \"label\": \"Contact\"}, {\"print_hide\": 0, \"fieldname\": \"contact_mobile\", \"label\": \"Mobile No\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company\", \"label\": \"Company\"}, {\"print_hide\": 0, \"fieldname\": \"company_address_display\", \"label\": \"Company Address\"}, {\"print_hide\": 0, \"fieldname\": \"company_trn\", \"label\": \"Company TRN\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"item_code\", \"print_width\": \"50px\"}, {\"print_hide\": 0, \"fieldname\": \"tax_code\", \"print_width\": \"50px\"}, {\"print_hide\": 0, \"fieldname\": \"qty\", \"print_width\": \"30px\"}, {\"print_hide\": 0, \"fieldname\": \"uom\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"rate\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_rate\", \"print_width\": \"10px\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"total_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"delivery_date\", \"print_width\": \"30px\"}], \"print_hide\": 0, \"fieldname\": \"items\", \"label\": \"Items\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"label\": \"Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"charge_type\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"row_id\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"account_head\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"cost_center\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"300px\"}, {\"print_hide\": 0, \"fieldname\": \"rate\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"item_wise_tax_detail\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"taxes\", \"label\": \"Sales Taxes and Charges\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"grand_total\", \"label\": \"Grand Total\"}, {\"print_hide\": 0, \"fieldname\": \"rounded_total\", \"label\": \"Rounded Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Terms\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"terms\", \"label\": \"Terms and Conditions Details\"}]", "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2017-12-27 16:05:27.785367", "module": "Regional", "name": "Tax Invoice", "page_number": null, "pdf_generator": null, "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-02-14 14:42:35.151611", "module": "Accounts", "name": "Purchase Auditing Voucher", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "Payment Entry", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-02-15 11:49:08.608619", "module": "Accounts", "name": "Bank and Cash Payment Voucher", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "Journal Entry", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-02-15 14:13:05.721784", "module": "Accounts", "name": "Journal Auditing Voucher", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-02-15 15:02:51.454754", "module": "Accounts", "name": "Sales Auditing Voucher", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 1, "css": null, "custom_format": 0, "default_print_language": "en-US", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": "", "line_breaks": 1, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-07-24 20:13:30.259953", "module": "Accounts", "name": "Sales Invoice Return", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 1, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 1, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 0, "doc_type": "Pick List", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>Pick List<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company\", \"label\": \"Company\"}, {\"print_hide\": 0, \"fieldname\": \"customer\", \"label\": \"Customer\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"purpose\", \"label\": \"Purpose\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"item_name\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"warehouse\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"qty\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"stock_qty\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"serial_no\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"batch_no\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"locations\", \"label\": \"Item Locations\"}]", "html": null, "line_breaks": 1, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-08-30 15:58:27.807219", "module": "Stock", "name": "Pick List", "page_number": null, "pdf_generator": null, "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 1, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 1, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>Purchase Invoice<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nCedente/prestatore (fornitore)\\n</th>\\n<th>\\nCessionario/committente (cliente)\\n</th>\\n</head>\\n<body>\\n<tr>\\n<td style=\\\"width: 50%;white-space:nowrap;\\\">\\n<p>Identificstivo fiscale ai fini IVA: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"tax_id\\\")}}</p>\\n<p>Codice fiscale: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"fiscal_code\\\")}}</p>\\n<p>Denominazione: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"supplier_name\\\")}}</p>\\n<p>Regime fiscale: {{frappe.db.get_value(\\\"Supplier\\\", doc.supplier, \\\"fiscal_regime\\\")}}</p>\\n<p>Indrizo: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"address_line1\\\")}}</p>\\n<p>Commune: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"city\\\")}} Provincia: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"state_code\\\")}}</p>\\n<p>Cap: {{(frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"pincode\\\")) or \\\" \\\"}} Nazione: {{frappe.db.get_value(\\\"Address\\\", doc.supplier_address, \\\"country\\\")}}</p>\\n</td>\\n<td style=\\\"width: 50%;white-space:nowrap;\\\">\\n<p>Identificstivo fiscale ai fini IVA: {{frappe.db.get_value(\\\"Company\\\", doc.company, \\\"tax_id\\\")}}</p>\\n<p>Codice fiscale: {{frappe.db.get_value(\\\"Company\\\", doc.company, \\\"fiscal_code\\\")}}</p>\\n<p>Denominazione: {{doc.company}}</p>\\n<p>Indrizo: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"address_line1\\\")}}</p>\\n<p>Commune: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"city\\\")}} Provincia: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"state_code\\\")}}</p>\\n<p>Cap: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"pincode\\\")}} Nazione: {{frappe.db.get_value(\\\"Address\\\", doc.shipping_address, \\\"country\\\")}}</p>\\n</td>\\n</td>\\n</tr>\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nTipologla\\n</th>\\n<th>\\nArt. 73\\n</th>\\n<th>\\nNumero documento\\n</th>\\n<th>\\nData documento\\n</th>\\n<th>\\nCodice destinatario\\n</th>\\n</head>\\n<body>\\n<tr>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.document_type or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.bill_no or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.get_formatted(\\\"bill_date\\\") or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 30%;white-space:nowrap;\\\">\\n{{doc.destination_code or \\\" \\\"}}\\n</td>\\n</tr>\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nDescrizione\\n</th>\\n<th>\\nQuantita\\n</th>\\n<th>\\nPrezzo unitario\\n</th>\\n<th>\\nUM\\n</th>\\n<th>\\n%IVA\\n</th>\\n<th>\\nPrezzo totale\\n</th>\\n</head>\\n\\n<body>\\n{%- for row in doc.items -%}\\n<tr>\\n<td style=\\\"width: 30%;\\\">\\n{{row.description or \\\" \\\"}}\\n</td>\\n<td style=\\\"width: 15%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"qty\\\", doc)}}\\n</td>\\n<td style=\\\"width: 15%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"rate\\\", doc)}}\\n</td>\\n<td style=\\\"width: 5%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"uom\\\", doc)}}\\n</td>\\n<td style=\\\"width: 15%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"tax_rate\\\", doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"amount\\\", doc)}}\\n</td>\\n{%- endfor -%}\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nesigibilita immediata / riferimenti normativi\\n</th>\\n<th>\\n%IVA\\n</th>\\n<th>\\nSpese accessorie\\n</th>\\n<th>\\nArr.\\n</th>\\n<th>\\nTotale imponibile\\n</th>\\n<th>\\nTotale Imposta\\n</th>\\n</head>\\n\\n<body>\\n{%- for row in doc.taxes -%}\\n<tr>\\n<td style=\\\"width: 30%;white-space:nowrap;\\\">\\n{% if 'None' in row.description %}\\n    {{ \\\" \\\" }}\\n{% else %}\\n{{row.description}}\\n{% endif %}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"tax_rate\\\", doc)}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\"0,00\\\"}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{doc.get_formatted(\\\"base_net_total\\\")}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"tax_amount\\\", doc)}}\\n</td>\\n{%- endfor -%}\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nImporto bolio\\n</th>\\n<th>\\nSconto/Magglorazione\\n</th>\\n<th>\\nArr.\\n</th>\\n<th>\\nTotale documento\\n</th>\\n</head>\\n\\n<body>\\n<tr>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 30%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 10%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 40%;white-space:nowrap;\\\">\\n{{doc.get_formatted(\\\"base_grand_total\\\")}}\\n</td>\\n</body>\\n</table>\\n<br>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table border=\\\"1\\\" width=\\\"100%\\\">\\n<head>\\n<th>\\nModalita pagamento\\n</th>\\n<th>\\nIBAN\\n</th>\\n<th>\\nInstituto\\n</th>\\n<th>\\nData scadenza\\n</th>\\n<th>\\nImporto\\n</th>\\n</head>\\n\\n<body>\\n{%- for row in doc.payment_schedule -%}\\n<tr>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"mode_of_payment_code\\\",doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"bank_account_iban\\\",doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{\\\" \\\"}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"due_date\\\",doc)}}\\n</td>\\n<td style=\\\"width: 20%;white-space:nowrap;\\\">\\n{{row.get_formatted(\\\"payment_amount\\\",doc)}}\\n</td>\\n{%- endfor -%}\\n</body>\\n</table>\"}]", "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-10-16 23:32:37.709344", "module": "Regional", "name": "Purchase eInvoice", "page_number": null, "pdf_generator": null, "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": ".print-format th {\n    background-color: transparent !important;\n    border-bottom: 1px solid !important;\n    border-top: none !important;\n}\n.print-format .ql-editor {\n    padding-left: 0px;\n    padding-right: 0px;\n}\n\n.print-format table {\n    margin-bottom: 0px;\n    }\n.print-format .table-data tr:last-child { \n    border-bottom: 1px solid !important;\n}\n\n.print-format .table-inner tr:last-child {\n    border-bottom:none !important;\n}\n.print-format .table-inner {\n    margin: 0px 0px;\n}\n\n.print-format .table-data ul li { \n    color:#787878 !important;\n}\n\n.no-top-border {\n    border-top:none !important;\n}\n\n.table-inner td {\n    padding-left: 0px !important;    \n    padding-top: 1px !important;\n    padding-bottom: 1px !important;\n    color:#787878 !important;\n}\n\n.total {\n    background-color: lightgrey !important;\n    padding-top: 4px !important;\n    padding-bottom: 4px !important;\n}\n", "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "<PERSON><PERSON>", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON>", "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<b>{{doc.customer_name}}</b> <br />\\n{{doc.address_display}}\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<div style=\\\"text-align:left;\\\">\\n<div style=\\\"font-size:24px; text-transform:uppercase;\\\">{{_(doc.dunning_type)}}</div>\\n<div style=\\\"font-size:16px;padding-bottom:5px;\\\">{{ doc.name }}</div>\\n</div>\"}, {\"fieldname\": \"posting_date\", \"print_hide\": 0, \"label\": \"Date\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"body_text\", \"print_hide\": 0, \"label\": \"Body Text\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"overdue_payments\", \"print_hide\": 0, \"label\": \"Overdue Payments\", \"visible_columns\": [{\"fieldname\": \"sales_invoice\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"dunning_level\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"due_date\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"overdue_days\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"invoice_portion\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"outstanding\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"interest\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total_outstanding\", \"print_hide\": 0, \"label\": \"Total Outstanding\"}, {\"fieldname\": \"dunning_fee\", \"print_hide\": 0, \"label\": \"Dunning Fee\"}, {\"fieldname\": \"total_interest\", \"print_hide\": 0, \"label\": \"Total Interest\"}, {\"fieldname\": \"grand_total\", \"print_hide\": 0, \"label\": \"Grand Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"closing_text\", \"print_hide\": 0, \"label\": \"Closing Text\"}]", "html": null, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2021-09-30 10:22:02.603871", "module": "Accounts", "name": "Dunning Letter", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": "en", "disabled": 0, "doc_type": "POS Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": null, "html": "<style>\n\t.print-format table, .print-format tr, \n\t.print-format td, .print-format div, .print-format p {\n\t\tfont-family: Tahoma, sans-serif;\n\t\tline-height: 150%;\n\t\tvertical-align: middle;\n\t}\n\t@media screen {\n\t\t.print-format {\n\t\t\twidth: 4in;\n\t\t\tpadding: 0.25in;\n\t\t\tmin-height: 8in;\n\t\t}\n\t}\n</style>\n\n{% if letter_head %}\n    {{ letter_head }}\n{% endif %}\n\n<p class=\"text-center\" style=\"margin-bottom: 1rem\">\n\t{{ doc.company }}<br>\n\t{{ doc.select_print_heading or _(\"Return Invoice\") }}<br>\n</p>\n<p>\n\t<b>{{ _(\"Receipt No\") }}:</b> {{ doc.name }}<br>\n\t<b>{{ _(\"Original Invoice\") }}:</b> {{ doc.return_against }}<br>\n\t<b>{{ _(\"Date\") }}:</b> {{ doc.get_formatted(\"posting_date\") }}<br>\n\t<b>{{ _(\"Customer\") }}:</b> {{ doc.customer_name }}\n</p>\n\n<hr>\n<table class=\"table table-condensed cart no-border\">\n\t<thead>\n\t\t<tr>\n\t\t\t<th width=\"50%\">{{ _(\"Item\") }}</th>\n\t\t\t<th width=\"25%\" class=\"text-right\">{{ _(\"Qty\") }}</th>\n\t\t\t<th width=\"25%\" class=\"text-right\">{{ _(\"Amount\") }}</th>\n\t\t</tr>\n\t</thead>\n\t<tbody>\n\t\t{%- for item in doc.items -%}\n\t\t<tr>\n\t\t\t<td>\n\t\t\t\t{{ item.item_code }}\n\t\t\t\t{%- if item.item_name != item.item_code -%}\n\t\t\t\t\t<br>{{ item.item_name }}\n\t\t\t\t{%- endif -%}\n\t\t\t\t{%- if item.serial_no -%}\n\t\t\t\t\t<br><b>{{ _(\"SR.No\") }}:</b><br>\n\t\t\t\t\t{{ item.serial_no | replace(\"\\n\", \", \") }}\n\t\t\t\t{%- endif -%}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">{{ item.qty }}<br>@ {{ item.get_formatted(\"rate\") }}</td>\n\t\t\t<td class=\"text-right\">{{ item.get_formatted(\"amount\") }}</td>\n\t\t</tr>\n\t\t{%- endfor -%}\n\t</tbody>\n</table>\n<table class=\"table table-condensed no-border\">\n\t<tbody>\n\t\t<tr>\n\t\t\t{% if doc.flags.show_inclusive_tax_in_print %}\n\t\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t\t{{ _(\"Total Excl. Tax\") }}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ doc.get_formatted(\"net_total\", doc) }}\n\t\t\t\t</td>\n\t\t\t{% else %}\n\t\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t\t{{ _(\"Total\") }}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ doc.get_formatted(\"total\", doc) }}\n\t\t\t\t</td>\n\t\t\t{% endif %}\n\t\t</tr>\n\t\t{%- for row in doc.taxes -%}\n\t\t  {%- if not row.included_in_print_rate or doc.flags.show_inclusive_tax_in_print -%}\n\t\t\t<tr>\n\t\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t    {% if '%' in row.description %}\n\t\t\t\t\t    {{ row.description }}\n\t\t\t\t\t{% else %}\n\t\t\t\t\t    {{ row.description }}@{{ row.rate }}%\n\t\t\t\t\t{% endif %}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ row.get_formatted(\"tax_amount\", doc)}}\n\t\t\t\t</td>\n\t\t\t<tr>\n\t\t  {%- endif -%}\n\t\t{%- endfor -%}\n\n\t\t{%- if doc.discount_amount -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t{{ _(\"Discount\") }}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"discount_amount\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- endif -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ _(\"Grand Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"grand_total\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- if doc.rounded_total -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ _(\"Rounded Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"rounded_total\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- endif -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ _(\"Paid Amount\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"paid_amount\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- if doc.change_amount -%}\n\t\t\t<tr>\n\t\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t\t<b>{{ _(\"Change Amount\") }}</b>\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ doc.get_formatted(\"change_amount\")}}\n\t\t\t\t</td>\n\t\t\t</tr>\n\t\t{%- endif -%}\n\t</tbody>\n</table>\n<hr>\n<p>{{ doc.terms or \"\" }}</p>\n<p class=\"text-center\">{{ _(\"Thank you, please visit again.\") }}</p>", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2020-05-14 17:13:29.354015", "module": "Selling", "name": "Return POS Invoice", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": "", "custom_format": 1, "default_print_language": "en", "disabled": 0, "doc_type": "Supplier", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>TAX Invoice<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name\", \"label\": \"Customer Name\"}, {\"print_hide\": 0, \"fieldname\": \"customer_name_in_arabic\", \"label\": \"Customer Name in Arabic\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"posting_date\", \"label\": \"Date\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company\", \"label\": \"Company\"}, {\"print_hide\": 0, \"fieldname\": \"company_trn\", \"label\": \"Company TRN\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"company_address_display\", \"label\": \"Company Address\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"item_code\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"200px\"}, {\"print_hide\": 0, \"fieldname\": \"uom\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_code\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"items\", \"label\": \"Items\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"label\": \"Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"visible_columns\": [{\"print_hide\": 0, \"fieldname\": \"charge_type\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"row_id\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"account_head\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"cost_center\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"description\", \"print_width\": \"300px\"}, {\"print_hide\": 0, \"fieldname\": \"rate\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_total\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"base_tax_amount_after_discount_amount\", \"print_width\": \"\"}, {\"print_hide\": 0, \"fieldname\": \"item_wise_tax_detail\", \"print_width\": \"\"}], \"print_hide\": 0, \"fieldname\": \"taxes\", \"label\": \"Sales Taxes and Charges\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"print_hide\": 0, \"fieldname\": \"grand_total\", \"label\": \"Grand Total\"}, {\"print_hide\": 0, \"fieldname\": \"rounded_total\", \"label\": \"Rounded Total\"}, {\"print_hide\": 0, \"fieldname\": \"in_words\", \"align\": \"left\", \"label\": \"In Words\"}]", "html": "<div id=\"copy_a\" style=\"position: relative; top:0cm; width:17cm;height:28.0cm;\">\n  <table>\n    <tbody>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:86mm\" colspan=\"4\" ; rowspan=\"3\">PAYER'S name, street address,\n          city or town, state or province, country, ZIP<br>or foreign postal code, and telephone no.<br>\n          {{ company or \"\" }}<br>\n          {{ payer_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">1 Rents</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:25mm\" rowspan=\"2\">OMB No. 1545-0115<br>\n          <yone>{{ fiscal_year[:2] }}</yone>\n          <ytwo>{{ fiscal_year[-2:] }}</ytwo><br>Form 1099-MISC\n        </td>\n        <td class=\"lbs bbs\" style=\"width:38mm\" colspan=\"2\" rowspan=\"2\">Miscellaneous Income</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">2 Royalties</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">3 Other Income<br>{{ payments or \"\" }}</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">4 Federal Income tax withheld</td>\n        <td class=\"tbs lbs bbs\" style=\"width:29mm\" rowspan=\"2\">Copy A<br>For<br>Internal Revenue<br>Service\n          Center<br><br>File with Form 1096</td>\n      </tr>\n      <tr style=\"height:16mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:43mm\">PAYER'S TIN<br>{{ company_tin or \"\" }}</td>\n\n        <td class=\"tbs rbs lbs bbs\" colspan=\"3\">RECIPIENT'S TIN<br><br>{{ tax_id or \"None\" }}</td>\n        <td class=\"tbs rbs lbs bbs\">Fishing boat proceeds</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">6 Medical and health care payments</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\">RECIPIENT'S name <br>{{ supplier or \"\" }}</td>\n        <td class=\"tbs rbs lbs bbs\">7 Nonemployee compensation<br>\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Substitute payments in lieu of dividends or interest</td>\n        <td class=\"tbs lbs bbs\" rowspan=\"6\">For Privacy Act<br>and Paperwork<br>Reduction Act<br>Notice, see\n          the<br>2018 General<br>Instructions for<br>Certain<br>Information<br>Returns.</td>\n      </tr>\n      <tr style=\"height:6mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">Street address (including apt. no.)<br>\n          {{ recipient_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\">$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:7mm\">\n        <td class=\"tbs rbs lbs bbs\" rowspan=\"2\">9 Payer made direct sales of<br>$5,000 or more of consumer\n          products<br>to a buyer<br>(recipient) for resale</td>\n        <td class=\"tbs rbs lbs\" colspan=\"2\">10 Crop insurance proceeds</td>\n      </tr>\n      <tr style=\"height:5mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">City or town, state or province, country, and ZIP or\n          foreign postal code<br>\n          {{ recipient_city_state or \"\" }}\n        </td>\n        <td style=\"vertical-align:bottom\" class=\" rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">11</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=2>12</td>\n      </tr>\n      <tr style=\"height:13mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Account number (see instructions)</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:16mm\">FACTA filing<br>requirement</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:14mm\">2nd TIN not.</td>\n        <td class=\"tbs rbs lbs bbs\">13 Excess golden parachute payments<br>$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">14 Gross proceeds paid to an<br>attorney<br>$___________</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs \">15a Section 409A deferrals</td>\n        <td class=\"tbs rbs lbs \" colspan=\"3\">15b Section 409 income</td>\n        <td class=\"tbs rbs lbs \">16 State tax withheld</td>\n        <td class=\"tbs rbs lbs \" colspan=\"2\">17 State/Payer's state no.</td>\n        <td class=\"tbs lbs\">18 State income</td>\n      </tr>\n      <tr>\n        <td class=\"lbs rbs bbs\">$</td>\n        <td class=\"lbs rbs bbs\" colspan=\"3\">$</td>\n        <td class=\"lbs rbs bbs tbd\">$</td>\n        <td class=\"lbs rbs bbs tbd\" colspan=\"2\"></td>\n        <td class=\"lbs bbs tbd\">$</td>\n      </tr>\n\n      <tr style=\"height:8mm\">\n        <td class=\"tbs\" colspan=\"8\">Form 1099-MISC Cat. No. 14425J www.irs.gov/Form1099MISC Department of the\n          Treasury - Internal Revenue Service</td>\n      </tr>\n\n    </tbody>\n  </table>\n</div>\n<div id=\"copy_1\" style=\"position: relative; top:0cm; width:17cm;height:28.0cm;\">\n  <table>\n    <tbody>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:86mm\" colspan=\"4\" ; rowspan=\"3\">PAYER'S name, street address,\n          city or town, state or province, country, ZIP<br>or foreign postal code, and telephone no.<br>\n          {{ company or \"\"}}<b r>\n          {{ payer_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">1 Rents</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:25mm\" rowspan=\"2\">OMB No. 1545-0115<br>\n          <yone>{{ fiscal_year[:2] }}</yone>\n          <ytwo>{{ fiscal_year[-2:] }}</ytwo><br>Form 1099-MISC\n        </td>\n        <td class=\"lbs bbs\" style=\"width:38mm\" colspan=\"2\" rowspan=\"2\">Miscellaneous Income</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:35mm\">2 Royalties</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">3 Other Income<br>\n          {{ payments or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">4 Federal Income tax withheld</td>\n        <td class=\"tbs lbs bbs\" style=\"width:29mm\" rowspan=\"2\">Copy 1<br>For State Tax<br>Department</td>\n      </tr>\n      <tr style=\"height:16mm\">\n        <td class=\"tbs rbs lbs bbs\" style=\"width:43mm\">PAYER'S TIN<br>\n          {{ company_tin or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"3\">RECIPIENT'S TIN<br>\n          {{ tax_id or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\">Fishing boat proceeds</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">6 Medical and health care payments</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\">RECIPIENT'S name</td>\n        {{ supplier or \"\" }}\n        <td class=\"tbs rbs lbs bbs\">7 Nonemployee compensation<br>\n        </td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Substitute payments in lieu of dividends or interest</td>\n        <td class=\"tbs lbs bbs\" rowspan=\"6\"></td>\n      </tr>\n      <tr style=\"height:6mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">Street address (including apt. no.)<br>\n          {{ recipient_street_address or \"\" }}\n        </td>\n        <td class=\"tbs rbs lbs bbs\">$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:7mm\">\n        <td class=\"tbs rbs lbs bbs\" rowspan=\"2\">9 Payer made direct sales of<br>$5,000 or more of consumer\n          products<br>to a buyer<br>(recipient) for resale</td>\n        <td class=\"tbs rbs lbs\" colspan=\"2\">10 Crop insurance proceeds</td>\n      </tr>\n      <tr style=\"height:5mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"4\" rowspan=\"2\">City or town, state or province, country, and ZIP or\n          foreign postal code<br>\n          {{ recipient_city_state or \"\" }}\n        </td>\n        <td style=\"vertical-align:bottom\" class=\" rbs lbs bbs\" colspan=\"2\">$___________</td>\n      </tr>\n      <tr style=\"height:9mm\">\n        <td class=\"tbs rbs lbs bbs\">11</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=2>12</td>\n      </tr>\n      <tr style=\"height:13mm\">\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">Account number (see instructions)</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:16mm\">FACTA filing<br>requirement</td>\n        <td class=\"tbs rbs lbs bbs\" style=\"width:14mm\">2nd TIN not.</td>\n        <td class=\"tbs rbs lbs bbs\">13 Excess golden parachute payments<br>$___________</td>\n        <td class=\"tbs rbs lbs bbs\" colspan=\"2\">14 Gross proceeds paid to an<br>attorney<br>$___________</td>\n      </tr>\n      <tr style=\"height:12mm\">\n        <td class=\"tbs rbs lbs \">15a Section 409A deferrals</td>\n        <td class=\"tbs rbs lbs \" colspan=\"3\">15b Section 409 income</td>\n        <td class=\"tbs rbs lbs \">16 State tax withheld</td>\n        <td class=\"tbs rbs lbs \" colspan=\"2\">17 State/Payer's state no.</td>\n        <td class=\"tbs lbs\">18 State income</td>\n      </tr>\n      <tr>\n        <td class=\"lbs rbs bbs\">$</td>\n        <td class=\"lbs rbs bbs\" colspan=\"3\">$</td>\n        <td class=\"lbs rbs bbs tbd\">$</td>\n        <td class=\"lbs rbs bbs tbd\" colspan=\"2\"></td>\n        <td class=\"lbs bbs tbd\">$</td>\n      </tr>\n\n      <tr style=\"height:8mm\">\n        <td class=\"tbs\" colspan=\"8\">Form 1099-MISC Cat. No. 14425J www.irs.gov/Form1099MISC Department of the\n          Treasury - Internal Revenue Service</td>\n      </tr>\n\n    </tbody>\n  </table>\n</div>\n<style>\n  body {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 5.66pt;\n  }\n\n  yone {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 14pt;\n    color: black;\n    -webkit-text-fill-color: white;\n    /* Will override color (regardless of order) */\n    -webkit-text-stroke-width: 1px;\n    -webkit-text-stroke-color: black;\n  }\n\n  ytwo {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 14pt;\n    color: black;\n    -webkit-text-stroke-width: 1px;\n    -webkit-text-stroke-color: black;\n  }\n\n  table,\n  th,\n  td {\n    font-family: 'Helvetica', sans-serif;\n    font-size: 5.66pt;\n    border: none;\n  }\n\n  .tbs {\n    border-top: 1px solid black;\n  }\n\n  .bbs {\n    border-bottom: 1px solid black;\n  }\n\n  .lbs {\n    border-left: 1px solid black;\n  }\n\n  .rbs {\n    border-right: 1px solid black;\n  }\n\n  .allBorder {\n    border-top: 1px solid black;\n    border-right: 1px solid black;\n    border-left: 1px solid black;\n    border-bottom: 1px solid black;\n  }\n\n  .bottomBorderOnlyDashed {\n    border-bottom: 1px dashed black;\n  }\n\n  .tbd {\n    border-top: 1px dashed black;\n  }\n\n  .address {\n    vertical-align: bottom;\n  }\n</style>", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2021-01-19 07:25:16.333666", "module": "Regional", "name": "IRS 1099 Form", "page_number": null, "pdf_generator": null, "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "No"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 0, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 14, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2><div>Purchase Receipt</div><br><small class=\\\"sub-heading\\\">{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"supplier_name\", \"print_hide\": 0, \"label\": \"Supplier Name\"}, {\"fieldname\": \"supplier_delivery_note\", \"print_hide\": 0, \"label\": \"Supplier Delivery Note\"}, {\"fieldname\": \"rack\", \"print_hide\": 0, \"label\": \"Rack\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"posting_date\", \"print_hide\": 0, \"label\": \"Date\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"apply_putaway_rule\", \"print_hide\": 0, \"label\": \"Apply Putaway Rule\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Accounting Dimensions\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"region\", \"print_hide\": 0, \"label\": \"Region\"}, {\"fieldname\": \"function\", \"print_hide\": 0, \"label\": \"Function\"}, {\"fieldname\": \"depot\", \"print_hide\": 0, \"label\": \"Depot\"}, {\"fieldname\": \"cost_center\", \"print_hide\": 0, \"label\": \"Cost Center\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"location\", \"print_hide\": 0, \"label\": \"Location\"}, {\"fieldname\": \"country\", \"print_hide\": 0, \"label\": \"Country\"}, {\"fieldname\": \"project\", \"print_hide\": 0, \"label\": \"Project\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Items\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"scan_barcode\", \"print_hide\": 0, \"label\": \"Scan Barcode\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"set_from_warehouse\", \"print_hide\": 0, \"label\": \"Set From Warehouse\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table class=\\\"table table-bordered\\\">\\n\\t<tbody>\\n\\t\\t<tr>\\n\\t\\t\\t<th>Sr</th>\\n\\t\\t\\t<th>Item Name</th>\\n\\t\\t\\t<th>Description</th>\\n\\t\\t\\t<th class=\\\"text-right\\\">Qty</th>\\n\\t\\t\\t<th class=\\\"text-right\\\">Rate</th>\\n\\t\\t\\t<th class=\\\"text-right\\\">Amount</th>\\n\\t\\t</tr>\\n\\t\\t{%- for row in doc.items -%}\\n\\t\\t<tr>\\n\\t\\t    {% set bundle_data = get_serial_or_batch_nos(row.serial_and_batch_bundle) %}\\n\\t\\t    {% set serial_nos = [] %}\\n            {% set batches = {} %}\\n\\n\\t\\t\\t<td style=\\\"width: 4%;\\\">{{ row.idx }}</td>\\n\\t\\t\\t<td style=\\\"width: 20%;\\\">\\n\\t\\t\\t\\t{{ row.item_name }}\\n\\t\\t\\t\\t{% if row.item_code != row.item_name -%}\\n\\t\\t\\t\\t<br>Item Code: {{ row.item_code}}\\n\\t\\t\\t\\t{%- endif %}\\n\\t\\t\\t</td>\\n\\t\\t\\t<td style=\\\"width: 30%;\\\">\\n\\t\\t\\t\\t<div style=\\\"border: 0px;\\\">{{ row.description }}</div></td>\\n\\t\\t\\t<td style=\\\"width: 10%; text-align: right;\\\">{{ row.qty }} {{ row.uom or row.stock_uom }}</td>\\n\\t\\t\\t<td style=\\\"width: 18%; text-align: right;\\\">{{\\n\\t\\t\\t\\trow.get_formatted(\\\"rate\\\", doc) }}</td>\\n\\t\\t\\t<td style=\\\"width: 18%; text-align: right;\\\">{{\\n\\t\\t\\t\\trow.get_formatted(\\\"amount\\\", doc) }}</td>\\n\\t\\t\\t\\n\\t\\t</tr>\\n\\t\\t{%- endfor -%}\\n\\t</tbody>\\n</table>\\n\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total_qty\", \"print_hide\": 0, \"label\": \"Total Quantity\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total\", \"print_hide\": 0, \"label\": \"Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"taxes\", \"print_hide\": 0, \"label\": \"Purchase Taxes and Charges\", \"visible_columns\": [{\"fieldname\": \"category\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"add_deduct_tax\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"charge_type\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"row_id\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"included_in_print_rate\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"included_in_paid_amount\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"account_head\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"description\", \"print_width\": \"300px\", \"print_hide\": 0}, {\"fieldname\": \"rate\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"region\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"function\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"location\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"cost_center\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"depot\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"country\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"account_currency\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"tax_amount\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"total\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Section Break\", \"label\": \"Totals\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"grand_total\", \"print_hide\": 0, \"label\": \"Grand Total\"}, {\"fieldname\": \"rounded_total\", \"print_hide\": 0, \"label\": \"Rounded Total\"}, {\"fieldname\": \"in_words\", \"print_hide\": 0, \"label\": \"In Words\"}, {\"fieldname\": \"disable_rounded_total\", \"print_hide\": 0, \"label\": \"Disable Rounded Total\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Supplier Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"address_display\", \"print_hide\": 0, \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"contact_display\", \"print_hide\": 0, \"label\": \"Contact\"}, {\"fieldname\": \"contact_mobile\", \"print_hide\": 0, \"label\": \"Mobile No\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Company Billing Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"billing_address\", \"print_hide\": 0, \"label\": \"Billing Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"billing_address_display\", \"print_hide\": 0, \"label\": \"Billing Address\"}, {\"fieldname\": \"terms\", \"print_hide\": 0, \"label\": \"Terms and Conditions\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table class=\\\"table table-bordered\\\">\\n\\t<tbody>\\n\\t\\t<tr>\\n\\t\\t\\t<th>Sr</th>\\n\\t\\t\\t<th>Item Name</th>\\n\\t\\t\\t<th>Qty</th>\\n\\t\\t\\t<th class=\\\"text-left\\\">Serial Nos</th>\\n\\t\\t\\t<th class=\\\"text-left\\\">Batch Nos (Qty)</th>\\n\\t\\t</tr>\\n\\t\\t{%- for row in doc.items -%}\\n\\t\\t<tr>\\n\\t\\t    {% set bundle_data = frappe.get_all(\\\"Serial and Batch Entry\\\", \\n\\t\\t        fields=[\\\"serial_no\\\", \\\"batch_no\\\", \\\"qty\\\"], \\n\\t\\t        filters={\\\"parent\\\": row.serial_and_batch_bundle}) %}\\n\\t\\t    {% set serial_nos = [] %}\\n            {% set batches = {} %}\\n            \\n            {% if bundle_data %}\\n\\t\\t\\t    {% for data in bundle_data %}\\n\\t\\t\\t        {% if data.serial_no %}\\n\\t\\t\\t            {{ serial_nos.append(data.serial_no) or \\\"\\\" }}\\n\\t\\t\\t        {% endif %}\\n\\t\\t\\t        \\n\\t\\t\\t        {% if data.batch_no %}\\n\\t\\t\\t            {{ batches.update({data.batch_no: data.qty}) or \\\"\\\" }}\\n\\t\\t\\t        {% endif %}\\n\\t\\t\\t    {% endfor %}\\n\\t\\t\\t{% endif %}\\n\\n\\t\\t\\t<td style=\\\"width: 3%;\\\">{{ row.idx }}</td>\\n\\t\\t\\t<td style=\\\"width: 20%;\\\">\\n\\t\\t\\t\\t{{ row.item_name }}\\n\\t\\t\\t\\t{% if row.item_code != row.item_name -%}\\n\\t\\t\\t\\t<br>Item Code: {{ row.item_code}}\\n\\t\\t\\t\\t{%- endif %}\\n\\t\\t\\t</td>\\n\\t\\t\\t<td style=\\\"width: 10%; text-align: right;\\\">{{ row.qty }} {{ row.uom or row.stock_uom }}</td>\\n\\t\\t\\t\\n\\t\\t\\t<td style=\\\"width: 30%; text-align: left;\\\">{{ serial_nos|join(',') }}</td>\\n\\t\\t\\t<td style=\\\"width: 30%;\\\">\\n\\t\\t\\t    {% if batches %}\\n                    {% for batch_no, qty in batches.items() %}\\n                        <p> {{batch_no}} : {{qty}} {{ row.uom or row.stock_uom }} </p>\\n                    {% endfor %}\\n                {% endif %}\\n\\t\\t\\t</td>\\n\\t\\t\\t\\n\\t\\t</tr>\\n\\t\\t{%- endfor -%}\\n\\t</tbody>\\n</table>\\n\"}]", "html": null, "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2023-06-26 14:51:20.609682", "module": "Stock", "name": "Purchase Receipt Serial and Batch Bundle Print", "page_number": "<PERSON>de", "pdf_generator": null, "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 1, "css": null, "custom_format": 1, "default_print_language": "ar", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 12, "format_data": null, "html": "\n    <div class=\"print-format\">\n        <style>\n            .print-format {\n                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n                margin: 0;\n                padding: 20px;\n                direction: rtl;\n                text-align: right;\n                color: #333;\n                line-height: 1.6;\n            }\n            .header {\n                text-align: center;\n                border-bottom: 3px solid #2c5aa0;\n                padding-bottom: 20px;\n                margin-bottom: 30px;\n                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n                color: white;\n                padding: 30px;\n                border-radius: 10px 10px 0 0;\n            }\n            .company-name {\n                font-size: 32px;\n                font-weight: bold;\n                margin-bottom: 10px;\n                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n            }\n            .invoice-title {\n                font-size: 26px;\n                color: #ffd700;\n                font-weight: bold;\n                margin: 15px 0;\n                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);\n            }\n            .invoice-number {\n                font-size: 16px;\n                background-color: rgba(255,255,255,0.2);\n                padding: 10px;\n                border-radius: 5px;\n                margin-top: 15px;\n            }\n            .invoice-details {\n                display: flex;\n                justify-content: space-between;\n                margin-bottom: 30px;\n                gap: 20px;\n            }\n            .customer-details, .invoice-info {\n                width: 48%;\n                background-color: #f8f9fa;\n                padding: 20px;\n                border-radius: 8px;\n                border: 1px solid #e9ecef;\n            }\n            .section-title {\n                font-size: 18px;\n                font-weight: bold;\n                color: #2c5aa0;\n                border-bottom: 2px solid #2c5aa0;\n                padding-bottom: 8px;\n                margin-bottom: 15px;\n                text-align: center;\n            }\n            .detail-item {\n                display: flex;\n                justify-content: space-between;\n                padding: 8px 0;\n                border-bottom: 1px solid #eee;\n            }\n            .detail-item:last-child {\n                border-bottom: none;\n            }\n            .car-details {\n                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n                padding: 25px;\n                border-radius: 10px;\n                margin-bottom: 25px;\n                border: 2px solid #2196f3;\n                box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n            }\n            .car-info-grid {\n                display: grid;\n                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n                gap: 15px;\n                margin-top: 15px;\n            }\n            .car-info-item {\n                display: flex;\n                justify-content: space-between;\n                padding: 12px;\n                background-color: white;\n                border-radius: 8px;\n                border: 1px solid #ddd;\n                box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n            }\n            .car-info-item strong {\n                color: #2c5aa0;\n            }\n            .items-table {\n                width: 100%;\n                border-collapse: collapse;\n                margin-bottom: 30px;\n                font-size: 14px;\n                box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n                border-radius: 8px;\n                overflow: hidden;\n            }\n            .items-table th, .items-table td {\n                border: 1px solid #ddd;\n                padding: 15px 12px;\n                text-align: center;\n            }\n            .items-table th {\n                background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);\n                color: white;\n                font-weight: bold;\n                font-size: 15px;\n            }\n            .items-table tr:nth-child(even) {\n                background-color: #f8f9fa;\n            }\n            .items-table tr:hover {\n                background-color: #e3f2fd;\n            }\n            .totals-section {\n                float: left;\n                width: 350px;\n                margin-top: 20px;\n                background-color: #f8f9fa;\n                border-radius: 8px;\n                overflow: hidden;\n                box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n            }\n            .total-row {\n                display: flex;\n                justify-content: space-between;\n                padding: 12px 20px;\n                border-bottom: 1px solid #ddd;\n                font-size: 15px;\n            }\n            .total-row:last-child {\n                border-bottom: none;\n            }\n            .grand-total {\n                background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);\n                color: white;\n                font-weight: bold;\n                font-size: 18px;\n            }\n            .footer {\n                margin-top: 60px;\n                border-top: 3px solid #2c5aa0;\n                padding-top: 30px;\n                text-align: center;\n            }\n            .signature-section {\n                display: flex;\n                justify-content: space-between;\n                margin-top: 50px;\n                gap: 20px;\n            }\n            .signature-box {\n                width: 250px;\n                text-align: center;\n                border-top: 2px solid #333;\n                padding-top: 15px;\n                font-weight: bold;\n            }\n            .tax-info {\n                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n                border: 2px solid #ffc107;\n                padding: 20px;\n                border-radius: 8px;\n                margin-bottom: 25px;\n                text-align: center;\n                font-weight: bold;\n            }\n            .thank-you {\n                font-size: 20px;\n                color: #2c5aa0;\n                font-weight: bold;\n                margin-bottom: 20px;\n            }\n            @media print {\n                .print-format {\n                    margin: 0;\n                    padding: 10px;\n                }\n                .header {\n                    -webkit-print-color-adjust: exact;\n                    color-adjust: exact;\n                }\n            }\n        </style>\n        \n        <div class=\"header\">\n            <div class=\"company-name\">{{ doc.company }}</div>\n            <div style=\"font-size: 16px; margin: 10px 0;\">\n                🚗 معرض السيارات الممتاز | Premium Car Showroom 🚗\n            </div>\n            <div class=\"invoice-title\">\n                📋 فاتورة ضريبية | Tax Invoice 📋\n            </div>\n            <div class=\"invoice-number\">\n                رقم الفاتورة: {{ doc.name }} | Invoice No: {{ doc.name }}\n            </div>\n        </div>\n\n        <div class=\"invoice-details\">\n            <div class=\"customer-details\">\n                <div class=\"section-title\">👤 بيانات العميل | Customer Details</div>\n                <div class=\"detail-item\">\n                    <strong>الاسم | Name:</strong>\n                    <span>{{ doc.customer_name }}</span>\n                </div>\n                <div class=\"detail-item\">\n                    <strong>كود العميل | Customer Code:</strong>\n                    <span>{{ doc.customer }}</span>\n                </div>\n                {% if doc.customer_address %}\n                <div class=\"detail-item\">\n                    <strong>العنوان | Address:</strong>\n                    <span>{{ doc.customer_address }}</span>\n                </div>\n                {% endif %}\n                {% if doc.contact_mobile %}\n                <div class=\"detail-item\">\n                    <strong>الهاتف | Phone:</strong>\n                    <span>{{ doc.contact_mobile }}</span>\n                </div>\n                {% endif %}\n                {% if doc.tax_id %}\n                <div class=\"detail-item\">\n                    <strong>الرقم الضريبي | Tax ID:</strong>\n                    <span>{{ doc.tax_id }}</span>\n                </div>\n                {% endif %}\n            </div>\n            <div class=\"invoice-info\">\n                <div class=\"section-title\">📄 بيانات الفاتورة | Invoice Info</div>\n                <div class=\"detail-item\">\n                    <strong>التاريخ | Date:</strong>\n                    <span>{{ frappe.utils.formatdate(doc.posting_date, \"dd-MM-yyyy\") }}</span>\n                </div>\n                <div class=\"detail-item\">\n                    <strong>الوقت | Time:</strong>\n                    <span>{{ doc.posting_time }}</span>\n                </div>\n                <div class=\"detail-item\">\n                    <strong>الحالة | Status:</strong>\n                    <span>{{ doc.status }}</span>\n                </div>\n                {% if doc.mode_of_payment %}\n                <div class=\"detail-item\">\n                    <strong>طريقة الدفع | Payment:</strong>\n                    <span>{{ doc.mode_of_payment }}</span>\n                </div>\n                {% endif %}\n                {% if doc.due_date %}\n                <div class=\"detail-item\">\n                    <strong>تاريخ الاستحقاق | Due Date:</strong>\n                    <span>{{ frappe.utils.formatdate(doc.due_date, \"dd-MM-yyyy\") }}</span>\n                </div>\n                {% endif %}\n            </div>\n        </div>\n\n        {% if doc.chassis_no or doc.engine_no or doc.car_color or doc.car_model or doc.manufacture_year %}\n        <div class=\"car-details\">\n            <div class=\"section-title\">🚗 تفاصيل السيارة | Car Details</div>\n            <div class=\"car-info-grid\">\n                {% if doc.chassis_no %}\n                <div class=\"car-info-item\">\n                    <strong>🔢 رقم الهيكل | Chassis:</strong>\n                    <span>{{ doc.chassis_no }}</span>\n                </div>\n                {% endif %}\n                {% if doc.engine_no %}\n                <div class=\"car-info-item\">\n                    <strong>⚙️ رقم المحرك | Engine:</strong>\n                    <span>{{ doc.engine_no }}</span>\n                </div>\n                {% endif %}\n                {% if doc.car_color %}\n                <div class=\"car-info-item\">\n                    <strong>🎨 اللون | Color:</strong>\n                    <span>{{ doc.car_color }}</span>\n                </div>\n                {% endif %}\n                {% if doc.car_model %}\n                <div class=\"car-info-item\">\n                    <strong>🚙 الموديل | Model:</strong>\n                    <span>{{ doc.car_model }}</span>\n                </div>\n                {% endif %}\n                {% if doc.manufacture_year %}\n                <div class=\"car-info-item\">\n                    <strong>📅 سنة الصنع | Year:</strong>\n                    <span>{{ doc.manufacture_year }}</span>\n                </div>\n                {% endif %}\n                {% if doc.showroom_name %}\n                <div class=\"car-info-item\">\n                    <strong>🏢 المعرض | Showroom:</strong>\n                    <span>{{ doc.showroom_name }}</span>\n                </div>\n                {% endif %}\n            </div>\n        </div>\n        {% endif %}\n\n        <table class=\"items-table\">\n            <thead>\n                <tr>\n                    <th style=\"width: 5%\">م | #</th>\n                    <th style=\"width: 15%\">كود الصنف | Item Code</th>\n                    <th style=\"width: 25%\">الوصف | Description</th>\n                    <th style=\"width: 8%\">الكمية | Qty</th>\n                    <th style=\"width: 12%\">السعر | Rate</th>\n                    <th style=\"width: 12%\">المبلغ | Amount</th>\n                    {% if doc.items[0].chassis_number %}\n                    <th style=\"width: 12%\">رقم الهيكل | Chassis</th>\n                    {% endif %}\n                    {% if doc.items[0].car_color_item %}\n                    <th style=\"width: 10%\">اللون | Color</th>\n                    {% endif %}\n                </tr>\n            </thead>\n            <tbody>\n                {% for item in doc.items %}\n                <tr>\n                    <td><strong>{{ loop.index }}</strong></td>\n                    <td>{{ item.item_code }}</td>\n                    <td style=\"text-align: right;\">{{ item.description or item.item_name }}</td>\n                    <td><strong>{{ item.qty }}</strong></td>\n                    <td>{{ \"{:,.2f}\".format(item.rate) }}</td>\n                    <td><strong>{{ \"{:,.2f}\".format(item.amount) }}</strong></td>\n                    {% if item.chassis_number %}\n                    <td>{{ item.chassis_number }}</td>\n                    {% endif %}\n                    {% if item.car_color_item %}\n                    <td>{{ item.car_color_item }}</td>\n                    {% endif %}\n                </tr>\n                {% endfor %}\n            </tbody>\n        </table>\n\n        <div class=\"totals-section\">\n            <div class=\"total-row\">\n                <span><strong>المجموع الفرعي | Subtotal:</strong></span>\n                <span><strong>{{ \"{:,.2f}\".format(doc.net_total) }}</strong></span>\n            </div>\n            {% if doc.total_taxes_and_charges %}\n            <div class=\"total-row\">\n                <span><strong>الضريبة | Tax ({{ doc.taxes[0].rate if doc.taxes else '15' }}%):</strong></span>\n                <span><strong>{{ \"{:,.2f}\".format(doc.total_taxes_and_charges) }}</strong></span>\n            </div>\n            {% endif %}\n            {% if doc.discount_amount %}\n            <div class=\"total-row\">\n                <span><strong>الخصم | Discount:</strong></span>\n                <span><strong>-{{ \"{:,.2f}\".format(doc.discount_amount) }}</strong></span>\n            </div>\n            {% endif %}\n            <div class=\"total-row grand-total\">\n                <span><strong>💰 المجموع الكلي | Grand Total:</strong></span>\n                <span><strong>{{ \"{:,.2f}\".format(doc.grand_total) }} ريال</strong></span>\n            </div>\n        </div>\n\n        <div style=\"clear: both;\"></div>\n\n        {% if doc.total_taxes_and_charges %}\n        <div class=\"tax-info\">\n            <strong>ℹ️ معلومات الضريبة | Tax Information</strong><br>\n            هذه فاتورة ضريبية صادرة وفقاً لأنظمة ضريبة القيمة المضافة في المملكة العربية السعودية\n            <br>\n            This is a tax invoice issued in accordance with VAT regulations in the Kingdom of Saudi Arabia\n        </div>\n        {% endif %}\n\n        <div class=\"footer\">\n            <div class=\"thank-you\">\n                🙏 شكراً لاختياركم معرضنا | Thank you for choosing our showroom 🙏\n            </div>\n            \n            <div class=\"signature-section\">\n                <div class=\"signature-box\">\n                    <div>✍️ توقيع العميل</div>\n                    <div>Customer Signature</div>\n                </div>\n                <div class=\"signature-box\">\n                    <div>✍️ توقيع المعرض</div>\n                    <div>Showroom Signature</div>\n                </div>\n            </div>\n            \n            <div style=\"margin-top: 40px; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 20px;\">\n                <strong>💻 تم إنشاء هذه الفاتورة بواسطة نظام ERPNext | Generated by ERPNext System</strong><br>\n                📧 للاستعلامات: <EMAIL> | For inquiries: <EMAIL><br>\n                📞 هاتف: +966-11-1234567 | Phone: +966-11-1234567\n            </div>\n        </div>\n    </div>\n    ", "line_breaks": 1, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-06-27 18:17:07.711466", "module": "Accounts", "name": "Car Showroom Tax Invoice", "page_number": "<PERSON>de", "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 1, "standard": "No"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "POS Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 0, "format_data": null, "html": "<style>\n\t.print-format table, .print-format tr, \n\t.print-format td, .print-format div, .print-format p {\n\t\tline-height: 150%;\n\t\tvertical-align: middle;\n\t}\n\t@media screen {\n\t\t.print-format {\n\t\t\twidth: 4in;\n\t\t\tpadding: 0.25in;\n\t\t\tmin-height: 8in;\n\t\t}\n\t}\n</style>\n\n{% if letter_head %}\n    {{ letter_head }}\n{% endif %}\n\n<p class=\"text-center\" style=\"margin-bottom: 1rem\">\n\t{{ doc.company }}<br>\n\t<b>{{ doc.select_print_heading or _(\"Invoice\") }}</b><br>\n</p>\n<p>\n\t<b>{{ _(\"Receipt No\") }}:</b> {{ doc.name }}<br>\n\t<b>{{ _(\"Cashier\") }}:</b> {{ doc.owner }}<br>\n\t<b>{{ _(\"Customer\") }}:</b> {{ doc.customer_name }}<br>\n\t<b>{{ _(\"Date\") }}:</b> {{ doc.get_formatted(\"posting_date\") }}<br>\n\t<b>{{ _(\"Time\") }}:</b> {{  doc.get_formatted(\"posting_time\") }}<br>\n</p>\n\n<hr>\n<table class=\"table table-condensed\">\n\t<thead>\n\t\t<tr>\n\t\t\t<th width=\"50%\">{{ _(\"Item\") }}</th>\n\t\t\t<th width=\"25%\" class=\"text-right\">{{ _(\"Qty\") }}</th>\n\t\t\t<th width=\"25%\" class=\"text-right\">{{ _(\"Amount\") }}</th>\n\t\t</tr>\n\t</thead>\n\t<tbody>\n\t\t{%- for item in doc.items -%}\n\t\t<tr>\n\t\t\t<td>\n\t\t\t\t{{ item.item_code }}\n\t\t\t\t{%- if item.item_name != item.item_code -%}\n\t\t\t\t\t<br>{{ item.item_name }}\n\t\t\t\t{%- endif -%}\n\t\t\t\t{%- if item.serial_no -%}\n\t\t\t\t\t<br><b>{{ _(\"SR.No\") }}:</b><br>\n\t\t\t\t\t{{ item.serial_no | replace(\"\\n\", \", \") }}\n\t\t\t\t{%- endif -%}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">{{ item.qty }}<br>@ {{ item.get_formatted(\"rate\") }}</td>\n\t\t\t<td class=\"text-right\">{{ item.get_formatted(\"amount\") }}</td>\n\t\t</tr>\n\t\t{%- endfor -%}\n\t</tbody>\n</table>\n<table class=\"table table-condensed no-border\">\n\t<tbody>\n\t\t<tr>\n\t\t\t{% if doc.flags.show_inclusive_tax_in_print %}\n\t\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t\t{{ _(\"Total Excl. Tax\") }}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ doc.get_formatted(\"net_total\", doc) }}\n\t\t\t\t</td>\n\t\t\t{% else %}\n\t\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t\t{{ _(\"Total\") }}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ doc.get_formatted(\"total\", doc) }}\n\t\t\t\t</td>\n\t\t\t{% endif %}\n\t\t</tr>\n\t\t{%- for row in doc.taxes -%}\n\t\t  {%- if not row.included_in_print_rate or doc.flags.show_inclusive_tax_in_print -%}\n\t\t\t<tr>\n\t\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t    {% if '%' in row.description %}\n\t\t\t\t\t    {{ row.description }}\n\t\t\t\t\t{% else %}\n\t\t\t\t\t    {{ row.description }}@{{ row.rate }}%\n\t\t\t\t\t{% endif %}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ row.get_formatted(\"tax_amount\", doc) }}\n\t\t\t\t</td>\n\t\t\t<tr>\n\t\t  {%- endif -%}\n\t\t{%- endfor -%}\n\n\t\t{%- if doc.discount_amount -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t{{ _(\"Discount\") }}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"discount_amount\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- endif -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ _(\"Grand Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"grand_total\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- if doc.rounded_total -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ _(\"Rounded Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"rounded_total\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- endif -%}\n\t\t{%- for row in doc.payments -%}\n\t\t\t<tr>\n\t\t\t\t<td class=\"text-right\" style=\"width: 70%\">\n\t\t\t\t    {{ row.mode_of_payment }}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ row.get_formatted(\"amount\", doc) }}\n\t\t\t\t</td>\n\t\t\t<tr>\n\t\t{%- endfor -%}\n\t\t<tr>\n\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t<b>{{ _(\"Paid Amount\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">\n\t\t\t\t{{ doc.get_formatted(\"paid_amount\") }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- if doc.change_amount -%}\n\t\t\t<tr>\n\t\t\t\t<td class=\"text-right\" style=\"width: 75%\">\n\t\t\t\t\t<b>{{ _(\"Change Amount\") }}</b>\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\">\n\t\t\t\t\t{{ doc.get_formatted(\"change_amount\") }}\n\t\t\t\t</td>\n\t\t\t</tr>\n\t\t{%- endif -%}\n\t</tbody>\n</table>\n<hr>\n<p>{{ doc.terms or \"\" }}</p>\n<p class=\"text-center\">{{ _(\"Thank you, please visit again.\") }}</p>", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2021-04-15 15:23:28.867135", "module": "Selling", "name": "POS Invoice", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "Journal Entry", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 0, "format_data": null, "html": "<div style=\"position: relative\">\n\n\t{%- from \"templates/print_formats/standard_macros.html\" import add_header -%}\n<div class=\"page-break\">\n    {%- if not doc.get(\"print_heading\") and not doc.get(\"select_print_heading\") \n        and doc.set(\"select_print_heading\", _(\"Payment Advice\")) -%}{%- endif -%}\n    {{ add_header(0, 1, doc, letter_head, no_letterhead) }}\n\n{%- for label, value in (\n        (_(\"Voucher Date\"), frappe.utils.formatdate(doc.voucher_date)),\n        (_(\"Reference / Cheque No.\"), doc.cheque_no),\n        (_(\"Reference / Cheque Date\"), frappe.utils.formatdate(doc.cheque_date))\n    ) -%}\n    <div class=\"row\">\n        <div class=\"col-xs-4\"><label class=\"text-right\">{{ label }}</label></div>\n        <div class=\"col-xs-8\">{{ value }}</div>\n    </div>\n{%- endfor -%}\n\t<hr>\n\t<p>{{ _(\"This amount is in full / part settlement of the listed bills\") }}:</p>\n{%- for label, value in (\n         (_(\"Amount\"), \"<strong>\" + doc.get_formatted(\"total_amount\") + \"</strong><br>\" + (doc.total_amount_in_words or \"\") + \"<br>\"),\n        (_(\"References\"), doc.remark)\n    ) -%}\n    <div class=\"row\">\n        <div class=\"col-xs-4\"><label class=\"text-right\">{{ label }}</label></div>\n        <div class=\"col-xs-8\">{{ value }}</div>\n    </div>\n    {%- endfor -%}\n    <hr>\n\t<div style=\"position: absolute; top: 14cm; left: 0cm;\">\n\t\tPrepared By</div>\n\t<div style=\"position: absolute; top: 14cm; left: 5.5cm;\">\n\t\tAuthorised Signatory</div>\n\t<div style=\"position: absolute; top: 14cm; left: 11cm;\">\n\t\tReceived Payment as Above</div>\n\t<div style=\"position: absolute; top: 16.4cm; left: 5.9cm;\">\n\t\t<strong>_____________</strong></div>\n\t<div style=\"position: absolute; top: 16.7cm; left: 6cm;\">\n\t\t<strong>A/C Payee</strong></div>\n\t<div style=\"position: absolute; top: 16.7cm; left: 5.9cm;\">\n\t\t<strong>_____________</strong></div>\n\t<div style=\"position: absolute; top: 16.9cm; left: 12cm;\">\n\t\t{{ frappe.utils.formatdate(doc.cheque_date) }}</div>\n\t<div style=\"position: absolute; top: 17.9cm; left: 1cm;\">\n\t\t{{ doc.pay_to_recd_from }}</div>\n\t<div style=\"position: absolute; top: 18.6cm; left: 1cm; width: 7cm;\">\n\t\t{{ doc.total_amount_in_words }}</div>\n\t<div style=\"position: absolute; top: 19.7cm; left: 12cm;\">\n\t\t{{ doc.get_formatted(\"total_amount\") }}</div>\n</div>", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2015-05-29 01:57:51.203850", "module": "Accounts", "name": "Cheque Printing Format", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 0, "doc_type": "Journal Entry", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 0, "format_data": null, "html": "", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2015-11-25 07:06:00.668141", "module": "Accounts", "name": "Payment Receipt Voucher", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 0, "doc_type": "Journal Entry", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 0, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2>Journal Entry<br><small>{{ doc.name }}</small>\\t\\t\\t\\t</h2></div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"voucher_type\", \"print_hide\": 0, \"label\": \"Entry Type\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"posting_date\", \"print_hide\": 0, \"label\": \"Posting Date\"}, {\"fieldname\": \"finance_book\", \"print_hide\": 0, \"label\": \"Finance Book\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"accounts\", \"print_hide\": 0, \"label\": \"Accounting Entries\", \"visible_columns\": [{\"fieldname\": \"account\", \"print_width\": \"250px\", \"print_hide\": 0}, {\"fieldname\": \"bank_account_no\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"party_type\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"party\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"debit_in_account_currency\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"credit_in_account_currency\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"reference_type\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"reference_name\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"reference_due_date\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"project\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"cheque_no\", \"print_hide\": 0, \"label\": \"Reference Number\"}, {\"fieldname\": \"cheque_date\", \"print_hide\": 0, \"label\": \"Reference Date\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"get_balance\", \"print_hide\": 0, \"label\": \"Make Difference Entry\"}, {\"fieldname\": \"total_amount\", \"print_hide\": 0, \"label\": \"Total Amount\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Reference\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"clearance_date\", \"print_hide\": 0, \"label\": \"Clearance Date\"}, {\"fieldname\": \"remark\", \"print_hide\": 0, \"label\": \"Remark\"}, {\"fieldname\": \"inter_company_journal_entry_reference\", \"print_hide\": 0, \"label\": \"Inter Company Journal Entry Reference\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"due_date\", \"print_hide\": 0, \"label\": \"Due Date\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Printing Settings\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"pay_to_recd_from\", \"print_hide\": 0, \"label\": \"Pay To / Recd From\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"letter_head\", \"print_hide\": 0, \"label\": \"Letter Head\"}, {\"fieldtype\": \"Section Break\", \"label\": \"More Information\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"mode_of_payment\", \"print_hide\": 0, \"label\": \"Mode of Payment\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"stock_entry\", \"print_hide\": 0, \"label\": \"Stock Entry\"}]", "html": "{%- from \"templates/print_formats/standard_macros.html\" import add_header -%}\n\n<div class=\"page-break\">\n    {%- if not doc.get(\"print_heading\") and not doc.get(\"select_print_heading\") \n        and doc.set(\"select_print_heading\", _(\"Credit Note\")) -%}{%- endif -%}\n    {{ add_header(0, 1, doc, letter_head, no_letterhead) }}\n\n    {%- for label, value in (\n        (_(\"Credit To\"), doc.pay_to_recd_from),\n        (_(\"Date\"), frappe.utils.formatdate(doc.voucher_date)),\n        (_(\"Amount\"), \"<strong>\" + doc.get_formatted(\"total_amount\") + \"</strong><br>\" + (doc.total_amount_in_words or \"\") + \"<br>\"),\n        (_(\"Remarks\"), doc.remark)\n    ) -%}\n\n    <div class=\"row\">\n        <div class=\"col-xs-3\"><label class=\"text-right\">{{ label }}</label></div>\n        <div class=\"col-xs-9\">{{ value }}</div>\n    </div>\n\n    {%- endfor -%}\n\n    <hr>\n    <br>\n    <p class=\"strong\">\n        {{ _(\"For\") }} {{ doc.company }},<br>\n        <br>\n        <br>\n        <br>\n        {{ _(\"Authorized Signatory\") }}\n    </p>\n</div>\n\n\n", "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2019-04-18 12:10:14.732269", "module": "Accounts", "name": "Credit Note", "page_number": null, "pdf_generator": null, "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 14, "format_data": null, "html": "\n<style>\n    body {\n        font-family: Arial, sans-serif;\n        direction: rtl;\n        font-size: 14px;\n        margin: 0;\n        padding: 0;\n    }\n    .header, .footer {\n        padding: 5px 10px;\n    }\n    .invoice-title {\n        text-align: center;\n        font-size: 18px;\n        font-weight: bold;\n        margin: 15px 0;\n    }\n    table {\n        width: 100%;\n        border-collapse: collapse;\n        margin-top: 5px;\n    }\n    th, td {\n        border: 1px solid #000;\n        padding: 5px;\n        text-align: center;\n    }\n    .totals {\n        margin-top: 10px;\n        width: 40%;\n        float: left;\n    }\n    .qr-code {\n        text-align: center;\n        margin: 10px 0;\n    }\n    .qr-code img {\n        max-width: 150px;\n        max-height: 150px;\n    }\n</style>\n\n<div class=\"header\">\n  <div style=\"display: flex; justify-content: space-between; align-items: center; padding: 5px 0; border-bottom: 2px solid black;\">\n    <div style=\"text-align: right; padding: 5px; width: 50%; font-size: 14px;\">\n      <h2 style=\"margin: 0;\">مؤسسة السرعة الفاخرة للسيارات</h2>\n      <p style=\"margin: 0;\">\n        <strong>سجل تجاري:</strong>7041878401 <br>\n        <strong>الرقم الضريبي:</strong> 301377763200003<br>\n        <strong>رقم الجوال:</strong> 0555522138\n      </p>\n    </div>\n    <div style=\"text-align: center; padding-bottom: 5px;\">\n      <img src=\"https://shamsfu.newsmart.tech/files/%D8%B4%D8%B9%D8%A7%D8%B1%20%D9%85%D8%B9%D8%B1%D8%B6%20%D8%A7%D9%84%D8%B3%D8%B1%D8%B9%D8%A9%20%D8%A7%D9%84%D9%81%D8%A7%D8%AE%D8%B1%D8%A9.png\" alt=\"شعار الشركة\" style=\"max-height: 200px;\">\n    </div>\n    <div style=\"text-align: left; padding: 5px; width: 50%; font-size: 13px;\">\n      <h2 style=\"margin: 0;\">Luxury Speed Establishment Cars</h2>\n      <p style=\"margin: 0;\">\n        <strong>C.R:</strong> 704187840<br>\n        <strong>VAT:</strong> 301377763200003<br>\n        <strong>PHONE:</strong> 0555522138\n      </p>\n    </div>\n  </div>\n</div>\n\n<div class=\"invoice-title\">فاتورة ضريبية مبسطة</div>\n\n<!-- بيانات العميل -->\n<div style=\"display: flex; justify-content: space-between; align-items: flex-start; margin-top: 10px; font-size: 13px;\">\n  <div style=\"text-align: right; width: 50%; padding: 5px;\">\n    <div><strong>العميل:</strong> {{ doc.customer_name }}</div>\n    <div style=\"margin-top: 5px;\"><strong>الرقم الضريبي للعميل:</strong> {{ doc.tax_id or 'غير محدد' }}</div>\n  </div>\n  <div style=\"text-align: left; width: 50%; padding: 5px;\">\n    <div><strong>رقم الفاتورة:</strong> {{ doc.name }}</div>\n    <div style=\"margin-top: 5px;\"><strong>التاريخ:</strong> {{ doc.posting_date }}</div>\n  </div>\n</div>\n\n<!-- QR Code -->\n<div class=\"qr-code\">\n  <div style=\"display: inline-block; border: 1px solid #ddd; padding: 10px; margin: 10px 0;\">\n    <div id=\"qr-code-container\">\n      {% if doc.qr_zatca_data %}\n        <canvas id=\"qr-canvas\"></canvas>\n      {% else %}\n        <div style=\"width: 128px; height: 128px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; font-size: 12px;\">\n          يرجى حفظ الفاتورة أولاً\n        </div>\n      {% endif %}\n    </div>\n    <div style=\"font-size: 12px; margin-top: 5px;\">QR Code للفاتورة</div>\n  </div>\n</div>\n\n<!-- جدول الأصناف -->\n<table>\n    <thead>\n        <tr>\n            <th>م</th>\n            <th>رقم الهيكل</th>\n            <th>الصنف</th>\n            <th>اللون</th>\n            <th>الموديل</th>\n            <th>الكمية</th>\n            <th>السعر</th>\n            <th>الإجمالي قبل الضريبة</th>\n            <th>الضريبة 15%</th>\n            <th>الإجمالي بعد الضريبة</th>\n        </tr>\n    </thead>\n    <tbody>\n        {% for item in doc.items %}\n        <tr>\n            <td>{{ loop.index }}</td>\n            <td>{{ item.custom_chassis_no or item.custom_engine_no or '' }}</td>\n            <td>{{ item.item_name }}</td>\n            <td>{{ item.custom_car_color or '' }}</td>\n            <td>{{ item.custom_car_model or '' }}</td>\n            <td>{{ item.qty }}</td>\n            <td>{{ frappe.utils.fmt_money(item.rate, currency=doc.currency) }}</td>\n            <td>{{ frappe.utils.fmt_money(item.amount, currency=doc.currency) }}</td>\n            <td>{{ frappe.utils.fmt_money(item.tax_amount or 0, currency=doc.currency) }}</td>\n            <td>{{ frappe.utils.fmt_money(item.amount + (item.tax_amount or 0), currency=doc.currency) }}</td>\n        </tr>\n        {% endfor %}\n    </tbody>\n</table>\n\n<!-- الإجماليات -->\n<div style=\"font-size: 13px; margin-top: 30px; width: 100%; direction: rtl;\">\n  <div style=\"display: flex; justify-content: space-between;\">\n    <div style=\"width: 48%;\">\n      {% set right_items = [\n        (\"الإجمالي قبل الضريبة\", doc.net_total),\n        (\"ضريبة القيمة المضافة 15%\", doc.total_taxes_and_charges),\n        (\"الإجمالي بعد الضريبة\", doc.grand_total),\n        (\"qيمة الفاتورة الضريبية\", doc.grand_total)\n      ] %}\n      {% for label, value in right_items %}\n      <div style=\"margin-bottom: 6px;\">\n        <div style=\"border-bottom: 1px solid black; display: inline-block; padding-bottom: 4px;\">\n          <span>{{ label }} : {{ frappe.utils.fmt_money(value, currency=doc.currency) }}</span>\n        </div>\n      </div>\n      {% endfor %}\n    </div>\n    <div style=\"width: 48%;\">\n      {% set left_items = [\n        (\"رسوم إصدار لوحات\", doc.custom_plate_fees or 0),\n        (\"المدفوع\", doc.paid_amount or 0),\n        (\"المتبقي\", doc.outstanding_amount or 0),\n        (\"قيمة الفاتورة النهائي\", doc.grand_total)\n      ] %}\n      {% for label, value in left_items %}\n      <div style=\"margin-bottom: 6px;\">\n        <div style=\"border-bottom: 1px solid black; display: inline-block; padding-bottom: 4px;\">\n          <span>{{ label }} : {{ frappe.utils.fmt_money(value, currency=doc.currency) }}</span>\n        </div>\n      </div>\n      {% endfor %}\n    </div>\n  </div>\n</div>\n\n<!-- ذيل الصفحة -->\n<div class=\"footer\" style=\"font-size: 11px; text-align: center; margin-top: 20px; padding-top: 10px; direction: rtl;\">\n  <div style=\"display: flex; justify-content: space-between; margin-bottom: 10px;\">\n      <div style=\"width: 30%; text-align: right;\">\n          <strong>الموظف</strong><br>\n          ___________________\n      </div>\n      <div style=\"width: 30%; text-align: center;\"></div>\n      <div style=\"width: 30%; text-align: left;\">\n          <strong>توقيع العميل</strong><br>\n          ___________________\n      </div>\n  </div>\n  <div style=\"margin: 10px 0;\">\n      لقد استلمت السيارة / السيارات المذكورة أعلاه سليمة، وفعالة، وفي حالة جيدة وكاملة العدة واللوازم، وغير منقوصة، ولا يوجد بها أي عيب من العيوب.<br>\n      ولستُ مسؤولًا عن هذه السيارة / السيارات، مسؤولية كاملة من الناحية الأمنية، وهذا إقرار مني بذلك، وعليه أوقع.\n  </div>\n  <div style=\"font-weight: bold; margin: 8px 0;\">\n      ** أي فاتورة لا تحمل ختم وتوقيع صاحب المعرض تعتبر لاغية **\n  </div>\n  <div style=\"background-color: #eee; padding: 6px; font-weight: bold;\">\n      المملكة العربية السعودية - جدة\n  </div>\n</div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    // توليد QR Code للفاتورة\n    generateInvoiceQR();\n});\n\nfunction generateInvoiceQR() {\n    try {\n        const qrData = '{{ doc.qr_zatca_data or \"\" }}';\n        \n        if (qrData && typeof QRCode !== 'undefined') {\n            // إنشاء QR Code من البيانات المحفوظة\n            const qrCode = new QRCode(document.getElementById('qr-canvas'), {\n                text: qrData,\n                width: 128,\n                height: 128,\n                colorDark: '#000000',\n                colorLight: '#ffffff',\n                correctLevel: QRCode.CorrectLevel.M\n            });\n        } else if (!qrData) {\n            // إذا لم توجد بيانات QR، إنشاء بيانات مؤقتة\n            const tempData = [\n                'مؤسسة السرعة الفاخرة للسيارات',\n                '301377763200003',\n                '{{ doc.posting_date }}T{{ doc.posting_time }}',\n                '{{ doc.grand_total }}',\n                '{{ doc.total_taxes_and_charges or 0 }}'\n            ].join('|');\n            \n            if (typeof QRCode !== 'undefined') {\n                new QRCode(document.getElementById('qr-canvas') || document.getElementById('qr-code-container'), {\n                    text: tempData,\n                    width: 128,\n                    height: 128,\n                    colorDark: '#000000',\n                    colorLight: '#ffffff'\n                });\n            }\n        }\n    } catch (error) {\n        console.error('خطأ في توليد QR Code:', error);\n    }\n}\n</script>\n\n<!-- تحميل مكتبة QR Code -->\n<script src=\"https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js\"></script>\n", "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 10.0, "margin_right": 10.0, "margin_top": 15.0, "modified": "2025-06-30 16:00:35.183378", "module": "Accounts", "name": "فاتورة ضريبية", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "No"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 0, "default_print_language": null, "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 14, "format_data": null, "html": "\n<style>\n    body {\n        font-family: Arial, sans-serif;\n        direction: rtl;\n        font-size: 14px;\n        margin: 0;\n        padding: 0;\n    }\n    .header, .footer {\n        padding: 5px 10px;\n    }\n    .invoice-title {\n        text-align: center;\n        font-size: 18px;\n        font-weight: bold;\n        margin: 15px 0;\n    }\n    table {\n        width: 100%;\n        border-collapse: collapse;\n        margin-top: 5px;\n    }\n    th, td {\n        border: 1px solid #000;\n        padding: 5px;\n        text-align: center;\n    }\n    .qr-code {\n        text-align: center;\n        margin: 10px 0;\n    }\n    @media print {\n        .no-print { display: none; }\n    }\n</style>\n\n<div class=\"header\">\n  <div style=\"display: flex; justify-content: space-between; align-items: center; padding: 5px 0; border-bottom: 2px solid black;\">\n    <div style=\"text-align: right; padding: 5px; width: 50%; font-size: 14px;\">\n      <h2 style=\"margin: 0;\">مؤسسة السرعة الفاخرة للسيارات</h2>\n      <p style=\"margin: 0;\">\n        <strong>سجل تجاري:</strong> 7041878401 <br>\n        <strong>الرقم الضريبي:</strong> 301377763200003<br>\n        <strong>رقم الجوال:</strong> 0555522138\n      </p>\n    </div>\n    <div style=\"text-align: center; padding-bottom: 5px;\">\n      <img src=\"https://shamsfu.newsmart.tech/files/%D8%B4%D8%B9%D8%A7%D8%B1%20%D9%85%D8%B9%D8%B1%D8%B6%20%D8%A7%D9%84%D8%B3%D8%B1%D8%B9%D8%A9%20%D8%A7%D9%84%D9%81%D8%A7%D8%AE%D8%B1%D8%A9.png\" alt=\"شعار الشركة\" style=\"max-height: 200px;\">\n    </div>\n    <div style=\"text-align: left; padding: 5px; width: 50%; font-size: 13px;\">\n      <h2 style=\"margin: 0;\">Luxury Speed Establishment Cars</h2>\n      <p style=\"margin: 0;\">\n        <strong>C.R:</strong> 704187840<br>\n        <strong>VAT:</strong> 301377763200003<br>\n        <strong>PHONE:</strong> 0555522138\n      </p>\n    </div>\n  </div>\n</div>\n\n<div class=\"invoice-title\">فاتورة ضريبية مبسطة</div>\n\n<!-- بيانات العميل -->\n<div style=\"display: flex; justify-content: space-between; align-items: flex-start; margin-top: 10px; font-size: 13px;\">\n  <div style=\"text-align: right; width: 50%; padding: 5px;\">\n    <div><strong>العميل:</strong> {{ doc.customer_name }}</div>\n    <div style=\"margin-top: 5px;\"><strong>الرقم الضريبي للعميل:</strong> {{ doc.tax_id or 'غير محدد' }}</div>\n  </div>\n  <div style=\"text-align: left; width: 50%; padding: 5px;\">\n    <div><strong>رقم الفاتورة:</strong> {{ doc.name }}</div>\n    <div style=\"margin-top: 5px;\"><strong>التاريخ:</strong> {{ doc.posting_date }}</div>\n  </div>\n</div>\n\n<!-- QR Code - يتم توليده تلقائياً بـ JavaScript -->\n<div class=\"qr-code\">\n  <div style=\"display: inline-block; border: 1px solid #ddd; padding: 10px; margin: 10px 0;\">\n    <div id=\"qr-code-container\">\n      <canvas id=\"qr-canvas\" style=\"max-width: 150px; max-height: 150px;\"></canvas>\n    </div>\n    <div style=\"font-size: 12px; margin-top: 5px;\">QR Code للفاتورة</div>\n  </div>\n</div>\n\n<!-- جدول الأصناف -->\n<table>\n    <thead>\n        <tr>\n            <th>م</th>\n            <th>رقم الهيكل</th>\n            <th>الصنف</th>\n            <th>اللون</th>\n            <th>الموديل</th>\n            <th>الكمية</th>\n            <th>السعر</th>\n            <th>الإجمالي قبل الضريبة</th>\n            <th>الضريبة 15%</th>\n            <th>الإجمالي بعد الضريبة</th>\n        </tr>\n    </thead>\n    <tbody>\n        {% for item in doc.items %}\n        <tr>\n            <td>{{ loop.index }}</td>\n            <td>{{ item.custom_chassis_no or item.custom_engine_no or '' }}</td>\n            <td>{{ item.item_name }}</td>\n            <td>{{ item.custom_car_color or '' }}</td>\n            <td>{{ item.custom_car_model or '' }}</td>\n            <td>{{ item.qty }}</td>\n            <td>{{ frappe.utils.fmt_money(item.rate, currency=doc.currency) }}</td>\n            <td>{{ frappe.utils.fmt_money(item.amount, currency=doc.currency) }}</td>\n            <td>{{ frappe.utils.fmt_money(item.tax_amount or 0, currency=doc.currency) }}</td>\n            <td>{{ frappe.utils.fmt_money(item.amount + (item.tax_amount or 0), currency=doc.currency) }}</td>\n        </tr>\n        {% endfor %}\n    </tbody>\n</table>\n\n<!-- الإجماليات -->\n<div style=\"font-size: 13px; margin-top: 30px; width: 100%; direction: rtl;\">\n  <div style=\"display: flex; justify-content: space-between;\">\n    <div style=\"width: 48%;\">\n      {% set right_items = [\n        (\"الإجمالي قبل الضريبة\", doc.net_total),\n        (\"ضريبة القيمة المضافة 15%\", doc.total_taxes_and_charges),\n        (\"الإجمالي بعد الضريبة\", doc.grand_total),\n        (\"قيمة الفاتورة الضريبية\", doc.grand_total)\n      ] %}\n      {% for label, value in right_items %}\n      <div style=\"margin-bottom: 6px;\">\n        <div style=\"border-bottom: 1px solid black; display: inline-block; padding-bottom: 4px;\">\n          <span>{{ label }} : {{ frappe.utils.fmt_money(value, currency=doc.currency) }}</span>\n        </div>\n      </div>\n      {% endfor %}\n    </div>\n    <div style=\"width: 48%;\">\n      {% set left_items = [\n        (\"رسوم إصدار لوحات\", doc.custom_plate_fees or 0),\n        (\"المدفوع\", doc.paid_amount or 0),\n        (\"المتبقي\", doc.outstanding_amount or 0),\n        (\"قيمة الفاتورة النهائي\", doc.grand_total)\n      ] %}\n      {% for label, value in left_items %}\n      <div style=\"margin-bottom: 6px;\">\n        <div style=\"border-bottom: 1px solid black; display: inline-block; padding-bottom: 4px;\">\n          <span>{{ label }} : {{ frappe.utils.fmt_money(value, currency=doc.currency) }}</span>\n        </div>\n      </div>\n      {% endfor %}\n    </div>\n  </div>\n</div>\n\n<!-- ذيل الصفحة -->\n<div class=\"footer\" style=\"font-size: 11px; text-align: center; margin-top: 20px; padding-top: 10px; direction: rtl;\">\n  <div style=\"display: flex; justify-content: space-between; margin-bottom: 10px;\">\n      <div style=\"width: 30%; text-align: right;\">\n          <strong>الموظف</strong><br>\n          ___________________\n      </div>\n      <div style=\"width: 30%; text-align: center;\"></div>\n      <div style=\"width: 30%; text-align: left;\">\n          <strong>توقيع العميل</strong><br>\n          ___________________\n      </div>\n  </div>\n  <div style=\"margin: 10px 0;\">\n      لقد استلمت السيارة / السيارات المذكورة أعلاه سليمة، وفعالة، وفي حالة جيدة وكاملة العدة واللوازم، وغير منقوصة، ولا يوجد بها أي عيب من العيوب.<br>\n      ولستُ مسؤولًا عن هذه السيارة / السيارات، مسؤولية كاملة من الناحية الأمنية، وهذا إقرار مني بذلك، وعليه أوقع.\n  </div>\n  <div style=\"font-weight: bold; margin: 8px 0;\">\n      ** أي فاتورة لا تحمل ختم وتوقيع صاحب المعرض تعتبر لاغية **\n  </div>\n  <div style=\"background-color: #eee; padding: 6px; font-weight: bold;\">\n      المملكة العربية السعودية - جدة\n  </div>\n</div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    generateSmartQRForPrint();\n});\n\nfunction generateSmartQRForPrint() {\n    try {\n        // محاولة استخدام QR المحفوظ أولاً\n        let qrData = '{{ doc.qr_zatca_data or \"\" }}';\n        \n        if (!qrData) {\n            // توليد QR جديد تلقائياً للطباعة\n            qrData = generateZATCADataForPrint();\n        }\n        \n        if (qrData) {\n            loadQRLibraryAndRender(qrData);\n        } else {\n            renderFallbackQR();\n        }\n    } catch (error) {\n        console.error('خطأ في النظام الذكي للـ QR:', error);\n        renderFallbackQR();\n    }\n}\n\nfunction generateZATCADataForPrint() {\n    try {\n        const companyName = 'مؤسسة السرعة الفاخرة للسيارات';\n        const taxId = '301377763200003';\n        const postingDate = '{{ doc.posting_date or frappe.utils.today() }}';\n        const postingTime = '{{ doc.posting_time or frappe.utils.nowtime() }}';\n        const grandTotal = '{{ doc.grand_total or 0 }}';\n        const taxAmount = '{{ doc.total_taxes_and_charges or 0 }}';\n        \n        return [companyName, taxId, postingDate + 'T' + postingTime, grandTotal, taxAmount].join('|');\n    } catch (error) {\n        console.error('خطأ في توليد بيانات ZATCA:', error);\n        return null;\n    }\n}\n\nfunction loadQRLibraryAndRender(qrData) {\n    // محاولة استخدام مكتبة QR إذا كانت محملة\n    if (typeof QRCode !== 'undefined') {\n        renderQRCode(qrData);\n        return;\n    }\n    \n    // تحميل مكتبة QR من CDN\n    const script = document.createElement('script');\n    script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js';\n    script.onload = function() {\n        renderQRCode(qrData);\n    };\n    script.onerror = function() {\n        renderFallbackQR();\n    };\n    document.head.appendChild(script);\n}\n\nfunction renderQRCode(qrData) {\n    try {\n        const canvas = document.getElementById('qr-canvas');\n        if (canvas && typeof QRCode !== 'undefined') {\n            // مسح المحتوى السابق\n            canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);\n            \n            // إنشاء QR جديد\n            new QRCode(canvas, {\n                text: qrData,\n                width: 150,\n                height: 150,\n                colorDark: '#000000',\n                colorLight: '#ffffff',\n                correctLevel: QRCode.CorrectLevel.M\n            });\n        } else {\n            renderFallbackQR();\n        }\n    } catch (error) {\n        console.error('خطأ في رسم QR:', error);\n        renderFallbackQR();\n    }\n}\n\nfunction renderFallbackQR() {\n    const container = document.getElementById('qr-code-container');\n    if (container) {\n        container.innerHTML = `\n            <div style=\"width: 150px; height: 150px; border: 2px dashed #666; \n                        display: flex; align-items: center; justify-content: center; \n                        background: #f9f9f9; flex-direction: column;\">\n                <div style=\"font-size: 16px; margin-bottom: 5px;\">📱</div>\n                <div style=\"font-size: 11px; text-align: center; color: #666;\">QR Code<br>للفاتورة</div>\n            </div>\n        `;\n    }\n}\n</script>\n", "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 10.0, "margin_right": 10.0, "margin_top": 15.0, "modified": "2025-06-30 18:13:59.278362", "module": "Accounts", "name": "فاتورة ضريبية - السرعة الفاخرة", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "No"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 14, "format_data": null, "html": "<div style='direction: rtl;'>فاتورة نقدية - مختصرة وسريعة</div>", "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-07-01 18:43:17.702704", "module": "Accounts", "name": "Sales Invoice Cash", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "No"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 14, "format_data": null, "html": "<div style='direction: rtl;'>فاتورة آجلة - تفصيلية</div>", "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-07-01 18:43:18.350536", "module": "Accounts", "name": "Sales Invoice Credit", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "No"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 14, "format_data": null, "html": "<div style='direction: rtl;'>فاتورة شراء نقدية</div>", "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-07-01 18:43:18.498773", "module": "Accounts", "name": "Purchase Invoice Cash", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "No"}, {"absolute_value": 0, "align_labels_right": 0, "css": null, "custom_format": 1, "default_print_language": null, "disabled": 0, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype": "Print Format", "font": null, "font_size": 14, "format_data": null, "html": "<div style='direction: rtl;'>فاتورة شراء آجلة</div>", "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-07-01 18:43:18.596978", "module": "Accounts", "name": "Purchase Invoice Credit", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_commands": null, "raw_printing": 0, "show_section_headings": 0, "standard": "No"}]