
frappe.ui.form.on('Purchase Invoice', {
    supplier: function(frm) {
        if (frm.doc.supplier) {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Purchase Invoice",
                    filters: { supplier: frm.doc.supplier },
                    fields: ["name"]
                },
                callback: function(r) {
                    let count = r.message.length;
                    frappe.call({
                        method: "frappe.client.get_list",
                        args: {
                            doctype: "Payment Entry",
                            filters: { party: frm.doc.supplier },
                            fields: ["name"]
                        },
                        callback: function(res) {
                            let pay_count = res.message.length;
                            frappe.msgprint({
                                title: "معلومات المورد",
                                indicator: "green",
                                message: `📦 <b>${frm.doc.supplier}</b><br>
                                    عدد فواتير الشراء: <b>${count}</b><br>
                                    عدد السندات المرتبطة: <b>${pay_count}</b><br>
                                    <br><a href='/app/purchase-invoice?supplier=${frm.doc.supplier}' target='_blank'>عرض الفواتير</a><br>
                                    <a href='/app/payment-entry?party=${frm.doc.supplier}' target='_blank'>عرض السندات</a>`
                            });
                        }
                    });
                }
            });
        }
    }
});
