# دليل الحلول السريعة - مشكلة عدم جلب البيانات

## التشخيص السريع

### 1. افتح أداة التشخيص:
```bash
# افتح في المتصفح
file:///home/<USER>/frappe-bench/frappevue_adminlte/debug_connection.html
```

### 2. تحقق من حالة الخدمات:
```bash
ps aux | grep frappe
# يجب أن ترى عدة عمليات frappe تعمل
```

### 3. اختبر الاتصال الأساسي:
```bash
curl -X GET "http://localhost:8000/api/resource/Customer" -H "Content-Type: application/json"
# يجب أن تحصل على خطأ مصادقة (وهذا طبيعي)
```

## الحلول حسب نوع المشكلة

### المشكلة 1: "No permission for Customer"

**التشخيص:**
- الخادم يعمل ولكن المستخدم لا يملك صلاحيات

**الحل السريع:**
```bash
# 1. دخول console
bench --site site1.local console

# 2. في Python console
from frappe.core.doctype.user.user import generate_keys

# إنشاء مستخدم جديد أو تحديث موجود
user_email = "<EMAIL>"  # غير هذا لبريدك
user = frappe.get_doc("User", user_email)

# إضافة أدوار
user.add_roles("Sales User", "Stock User", "System Manager")

# إنشاء API keys
generate_keys(user.name)
user.save()
frappe.db.commit()

print(f"API Key: {user.api_key}")
print(f"API Secret: {user.get_password('api_secret')}")
```

### المشكلة 2: "Authentication failed"

**التشخيص:**
- Token غير صحيح أو منتهي الصلاحية

**الحل السريع:**
```javascript
// في console المتصفح
localStorage.removeItem('frappUser');
// ثم سجل دخول مرة أخرى
```

### المشكلة 3: خطأ CORS

**التشخيص:**
- رسائل خطأ CORS في console المتصفح

**الحل السريع:**
```bash
# تحديث إعدادات CORS
echo '{
    "allow_cors": "*",
    "cors_headers": [
        "Authorization",
        "Content-Type",
        "Accept",
        "X-Requested-With"
    ],
    "db_name": "_d7fceab10a7d05d3",
    "db_password": "n4nxEfHxvB7wMERd",
    "db_type": "mariadb",
    "encryption_key": "go0NdfeMhLdGcijNxKN9sY4fOXr9aqNAX68pp27V14k=",
    "maintenance_mode": 0
}' > sites/site1.local/site_config.json

# إعادة تشغيل الخدمات
pkill -f frappe
bench start
```

### المشكلة 4: الخدمات لا تعمل

**التشخيص:**
```bash
ps aux | grep frappe
# لا توجد عمليات frappe
```

**الحل السريع:**
```bash
cd /home/<USER>/frappe-bench
bench start
```

## اختبار سريع للحل

### 1. اختبار تسجيل الدخول:
```javascript
// في console المتصفح (بعد فتح التطبيق)
const frappe = new utils.Frappe('http://localhost:8000');
frappe.login({usr: 'Administrator', pwd: 'admin'})
    .then(result => {
        console.log('Login result:', result);
        if (result.token) {
            localStorage.setItem('frappUser', JSON.stringify({
                token: result.token,
                userData: result
            }));
            console.log('Login successful!');
        }
    });
```

### 2. اختبار جلب البيانات:
```javascript
// بعد تسجيل الدخول الناجح
frappe.get_list('Customer', 'fields=["name","customer_name"]&limit_page_length=5')
    .then(result => {
        console.log('Customers:', result);
        if (result.status_code === 200) {
            console.log('Data fetching works!');
        }
    });
```

## إنشاء مستخدم اختبار سريع

```bash
# دخول console
bench --site site1.local console

# إنشاء مستخدم جديد
user = frappe.get_doc({
    "doctype": "User",
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User",
    "send_welcome_email": 0,
    "new_password": "test123"
})
user.insert()

# إضافة أدوار
user.add_roles("Sales User", "Stock User", "Item Manager", "Customer")

# إنشاء API keys
from frappe.core.doctype.user.user import generate_keys
generate_keys(user.name)
user.save()
frappe.db.commit()

print(f"User created: {user.email}")
print(f"Password: test123")
print(f"API Key: {user.api_key}")
```

## فحص سريع للبيانات

```bash
# دخول console
bench --site site1.local console

# فحص وجود عملاء
customers = frappe.get_list("Customer", limit=5)
print(f"Number of customers: {len(customers)}")

# إنشاء عميل تجريبي إذا لم يوجد
if len(customers) == 0:
    customer = frappe.get_doc({
        "doctype": "Customer",
        "customer_name": "Test Customer",
        "customer_type": "Individual"
    })
    customer.insert()
    frappe.db.commit()
    print("Test customer created")

# فحص وجود أصناف
items = frappe.get_list("Item", limit=5)
print(f"Number of items: {len(items)}")

# إنشاء صنف تجريبي إذا لم يوجد
if len(items) == 0:
    item = frappe.get_doc({
        "doctype": "Item",
        "item_code": "TEST-ITEM-001",
        "item_name": "Test Item",
        "item_group": "All Item Groups",
        "stock_uom": "Nos"
    })
    item.insert()
    frappe.db.commit()
    print("Test item created")
```

## تشغيل التطبيق

```bash
# تشغيل Frappe
cd /home/<USER>/frappe-bench
bench start

# في terminal آخر - تشغيل Vue app
cd /home/<USER>/frappe-bench/frappevue_adminlte
npm run serve
```

## روابط مفيدة للاختبار

- **Frappe Backend:** http://localhost:8000
- **Vue Frontend:** http://localhost:8080 (أو المنفذ المعروض)
- **أداة التشخيص:** file:///home/<USER>/frappe-bench/frappevue_adminlte/debug_connection.html

## إذا لم تعمل الحلول

1. **تحقق من logs:**
```bash
tail -f logs/web.log
tail -f logs/worker.log
```

2. **إعادة تشغيل كاملة:**
```bash
pkill -f frappe
pkill -f node
bench start
```

3. **تحديث التطبيقات:**
```bash
bench update
```

4. **إعادة بناء الأصول:**
```bash
bench build
```
