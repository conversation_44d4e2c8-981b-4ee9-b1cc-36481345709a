# دليل إعداد نظام العملاء - FrappeVue AdminLTE

## نظرة عامة

تم تحديث نظام إنشاء العملاء ليعمل بشكل صحيح مع ERPNext ويستخدم البيانات الفعلية من النظام.

## التحديثات المنجزة

### 1. إعداد البيانات الأساسية في ERPNext

تم إنشاء البيانات التالية في ERPNext:

#### Customer Groups (مجموعات العملاء):
- **All Customer Groups** (مجموعة رئيسية)
- **فرد** - للعملاء الأفراد
- **تجاري** - للعملاء التجاريين
- **حكومة** - للعملاء الحكوميين
- **غير ربحية** - للمؤسسات غير الربحية
- **Company** - للشركات (إنجليزي)

#### Territories (الأقاليم):
- **All Territories** (إقليم رئيسي)
- **Saudi Arabia** - المملكة العربية السعودية
- **Rest Of The World** - باقي أنحاء العالم
- **Yemen** - اليمن
- **باقي أنحاء العالم** (عربي)
- **جميع الأقاليم** (عربي)

### 2. تحديث واجهة إنشاء العميل

#### الحقول المحدثة:
- **اسم العميل** (مطلوب)
- **نوع العميل** (مطلوب): Individual/Company
- **المنطقة** (اختياري): يتم تحميلها من ERPNext
- **مجموعة العميل** (اختياري): يتم تحميلها من ERPNext
- **البريد الإلكتروني** (اختياري)
- **رقم الجوال** (اختياري)
- **الرقم الضريبي** (اختياري)
- **تفاصيل العميل** (اختياري)

#### الميزات الجديدة:
- تحميل البيانات تلقائياً من ERPNext عند النقر على الحقول
- استخدام القيم الإنجليزية الصحيحة من ERPNext
- معالجة أفضل للأخطاء
- دعم الحقول الاختيارية

### 3. نظام الترجمة

تم إنشاء نظام ترجمة شامل للاستخدام المستقبلي:

#### الملفات المضافة:
- `src/locales/ar.json` - ملف الترجمة العربية
- `src/utils/translation.js` - مساعد الترجمة

#### الدوال المتاحة:
```javascript
import { translate, translateCustomerType, translateTerritory } from '@/utils/translation'

// ترجمة عامة
translate('customer.customer_name') // "اسم العميل"

// ترجمة أنواع العملاء
translateCustomerType('Individual') // "فرد"

// ترجمة الأقاليم
translateTerritory('Saudi Arabia') // "المملكة العربية السعودية"
```

## كيفية الاستخدام

### 1. إنشاء عميل جديد

1. انتقل إلى صفحة إنشاء العميل
2. أدخل **اسم العميل** (مطلوب)
3. اختر **نوع العميل** (مطلوب)
4. اختر **المنطقة** من القائمة المنسدلة (اختياري)
5. اختر **مجموعة العميل** من القائمة المنسدلة (اختياري)
6. أدخل البيانات الإضافية حسب الحاجة
7. انقر على "إنشاء العميل"

### 2. القيم المقبولة

#### أنواع العملاء:
- `Individual` - فرد
- `Company` - شركة

#### مجموعات العملاء المتاحة:
- `فرد` - للعملاء الأفراد
- `تجاري` - للعملاء التجاريين
- `حكومة` - للعملاء الحكوميين
- `غير ربحية` - للمؤسسات غير الربحية
- `Company` - للشركات

#### الأقاليم المتاحة:
- `Saudi Arabia` - المملكة العربية السعودية
- `Rest Of The World` - باقي أنحاء العالم
- `Yemen` - اليمن

## استكشاف الأخطاء وإصلاحها

### مشكلة LinkValidationError

إذا ظهرت رسالة خطأ "لا يمكن أن تجد مجموعة العميل" أو "لا يمكن أن تجد الإقليم":

1. تأكد من أن البيانات الأساسية موجودة في ERPNext
2. استخدم القيم الصحيحة (العربية للمجموعات، الإنجليزية للأقاليم)
3. تأكد من أن الحقول الاختيارية فارغة إذا لم تكن مطلوبة

### إعادة إنشاء البيانات الأساسية

إذا كنت بحاجة لإعادة إنشاء البيانات الأساسية:

```bash
cd /home/<USER>/frappe-bench
bench --site site1.local console
```

ثم في console:
```python
exec(open('/home/<USER>/frappe-bench/frappevue_adminlte/setup_basic_data.py').read())
```

## التطوير المستقبلي

### خطة الترجمة

1. **المرحلة الحالية**: استخدام القيم الإنجليزية من ERPNext مباشرة
2. **المرحلة المستقبلية**: 
   - تطبيق نظام الترجمة في الواجهات
   - إنشاء ملفات ترجمة لجميع اللغات
   - ترجمة القيم المعروضة للمستخدم مع الاحتفاظ بالقيم الإنجليزية للإرسال

### تحسينات مقترحة

1. **التحقق من صحة البيانات**: إضافة تحقق أفضل من البيانات المدخلة
2. **التحميل التلقائي**: تحميل البيانات عند فتح الصفحة
3. **البحث والتصفية**: إضافة إمكانية البحث في القوائم المنسدلة
4. **الحفظ التلقائي**: حفظ البيانات تلقائياً أثناء الكتابة

## الملفات المحدثة

- `src/views/transactions/CreateCustomer.vue` - واجهة إنشاء العميل
- `src/locales/ar.json` - ملف الترجمة العربية
- `src/utils/translation.js` - مساعد الترجمة
- `setup_basic_data.py` - سكريبت إعداد البيانات الأساسية

## الاختبار

تم اختبار النظام بنجاح مع:
- ✅ إنشاء عميل بالحقول الإلزامية فقط
- ✅ إنشاء عميل مع territory و customer_group
- ✅ تحميل البيانات من ERPNext
- ✅ معالجة الأخطاء بشكل صحيح

---

**ملاحظة**: هذا النظام يستخدم القيم الإنجليزية من ERPNext كما هو مطلوب، مع إمكانية إضافة الترجمة في المستقبل دون تغيير البيانات الأساسية.
