<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص الاتصال مع Frappe</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>تشخيص الاتصال مع Frappe ERPNext</h1>

        <div class="test-section info">
            <h3>معلومات التكوين الحالي</h3>
            <p><strong>عنوان الخادم:</strong> <span id="server-url">http://localhost:8000</span></p>
            <p><strong>حالة تسجيل الدخول:</strong> <span id="login-status">غير محدد</span></p>
            <p><strong>Token:</strong> <span id="token-status">غير محدد</span></p>
        </div>

        <div class="test-section">
            <h3>اختبارات الاتصال</h3>
            <button onclick="testServerConnection()">اختبار الاتصال بالخادم</button>
            <button onclick="testAuthentication()">اختبار المصادقة</button>
            <button onclick="testCustomerAPI()">اختبار API العملاء</button>
            <button onclick="testItemAPI()">اختبار API الأصناف</button>
            <button onclick="clearStorage()">مسح البيانات المحفوظة</button>
        </div>

        <div class="test-section">
            <h3>نتائج الاختبارات</h3>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>تسجيل دخول سريع للاختبار</h3>
            <input type="text" id="username" placeholder="اسم المستخدم" style="margin: 5px; padding: 8px;">
            <input type="password" id="password" placeholder="كلمة المرور" style="margin: 5px; padding: 8px;">
            <button onclick="quickLogin()">تسجيل دخول</button>
        </div>
    </div>

    <script>
        const serverUrl = 'http://localhost:8000';

        // تحديث معلومات التكوين
        function updateConfigInfo() {
            document.getElementById('server-url').textContent = serverUrl;

            try {
                if (localStorage.frappUser) {
                    const userData = JSON.parse(localStorage.frappUser);
                    document.getElementById('login-status').textContent = 'مسجل دخول';
                    document.getElementById('token-status').textContent = userData.token ? 'موجود' : 'غير موجود';
                } else {
                    document.getElementById('login-status').textContent = 'غير مسجل دخول';
                    document.getElementById('token-status').textContent = 'غير موجود';
                }
            } catch (error) {
                document.getElementById('login-status').textContent = 'خطأ في البيانات';
                document.getElementById('token-status').textContent = 'خطأ';
            }
        }

        // إضافة نتيجة اختبار
        function addTestResult(title, success, message, details = null) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${success ? 'success' : 'error'}`;

            let html = `<h4>${title}</h4><p>${message}</p>`;
            if (details) {
                html += `<pre>${JSON.stringify(details, null, 2)}</pre>`;
            }

            resultDiv.innerHTML = html;
            resultsDiv.appendChild(resultDiv);
        }

        // اختبار الاتصال بالخادم
        async function testServerConnection() {
            try {
                const response = await fetch(`${serverUrl}/api/method/ping`);
                if (response.ok) {
                    addTestResult('اختبار الاتصال بالخادم', true, 'الخادم يعمل بشكل طبيعي');
                } else {
                    addTestResult('اختبار الاتصال بالخادم', false, `خطأ في الاتصال: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                addTestResult('اختبار الاتصال بالخادم', false, `فشل في الاتصال: ${error.message}`);
            }
        }

        // اختبار المصادقة
        async function testAuthentication() {
            try {
                if (!localStorage.frappUser) {
                    addTestResult('اختبار المصادقة', false, 'لا توجد بيانات مستخدم محفوظة. يرجى تسجيل الدخول أولاً.');
                    return;
                }

                const userData = JSON.parse(localStorage.frappUser);
                if (!userData.token) {
                    addTestResult('اختبار المصادقة', false, 'لا يوجد token محفوظ.');
                    return;
                }

                const response = await fetch(`${serverUrl}/api/method/frappe.auth.get_logged_user`, {
                    headers: {
                        'Authorization': `token ${userData.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addTestResult('اختبار المصادقة', true, 'المصادقة تعمل بشكل صحيح', data);
                } else {
                    const errorData = await response.json();
                    addTestResult('اختبار المصادقة', false, `فشل في المصادقة: ${response.status}`, errorData);
                }
            } catch (error) {
                addTestResult('اختبار المصادقة', false, `خطأ في اختبار المصادقة: ${error.message}`);
            }
        }

        // اختبار API العملاء
        async function testCustomerAPI() {
            try {
                if (!localStorage.frappUser) {
                    addTestResult('اختبار API العملاء', false, 'يرجى تسجيل الدخول أولاً.');
                    return;
                }

                const userData = JSON.parse(localStorage.frappUser);
                const response = await fetch(`${serverUrl}/api/resource/Customer?fields=["name","customer_name"]&limit_page_length=5`, {
                    headers: {
                        'Authorization': `token ${userData.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addTestResult('اختبار API العملاء', true, `تم جلب ${data.data ? data.data.length : 0} عميل بنجاح`, data);
                } else {
                    const errorData = await response.json();
                    addTestResult('اختبار API العملاء', false, `فشل في جلب العملاء: ${response.status}`, errorData);
                }
            } catch (error) {
                addTestResult('اختبار API العملاء', false, `خطأ في اختبار API العملاء: ${error.message}`);
            }
        }

        // اختبار API الأصناف
        async function testItemAPI() {
            try {
                if (!localStorage.frappUser) {
                    addTestResult('اختبار API الأصناف', false, 'يرجى تسجيل الدخول أولاً.');
                    return;
                }

                const userData = JSON.parse(localStorage.frappUser);
                const response = await fetch(`${serverUrl}/api/resource/Item?fields=["name","item_name","item_code"]&limit_page_length=5`, {
                    headers: {
                        'Authorization': `token ${userData.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addTestResult('اختبار API الأصناف', true, `تم جلب ${data.data ? data.data.length : 0} صنف بنجاح`, data);
                } else {
                    const errorData = await response.json();
                    addTestResult('اختبار API الأصناف', false, `فشل في جلب الأصناف: ${response.status}`, errorData);
                }
            } catch (error) {
                addTestResult('اختبار API الأصناف', false, `خطأ في اختبار API الأصناف: ${error.message}`);
            }
        }

        // تسجيل دخول سريع
        async function quickLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                addTestResult('تسجيل الدخول', false, 'يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            try {
                const response = await fetch(`${serverUrl}/api/method/frappeauth_app.authentication.login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        usr: username,
                        pwd: password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.message && data.message.status_code === 200) {
                        localStorage.setItem('frappUser', JSON.stringify({
                            token: data.message.token,
                            userData: data.message
                        }));
                        addTestResult('تسجيل الدخول', true, 'تم تسجيل الدخول بنجاح', data.message);
                        updateConfigInfo();
                    } else {
                        addTestResult('تسجيل الدخول', false, 'فشل في تسجيل الدخول', data);
                    }
                } else {
                    const errorData = await response.json();
                    addTestResult('تسجيل الدخول', false, `خطأ في تسجيل الدخول: ${response.status}`, errorData);
                }
            } catch (error) {
                addTestResult('تسجيل الدخول', false, `خطأ في تسجيل الدخول: ${error.message}`);
            }
        }

        // مسح البيانات المحفوظة
        function clearStorage() {
            localStorage.removeItem('frappUser');
            addTestResult('مسح البيانات', true, 'تم مسح البيانات المحفوظة');
            updateConfigInfo();
        }

        // تحديث المعلومات عند تحميل الصفحة
        window.onload = function () {
            updateConfigInfo();
        };
    </script>
</body>

</html>