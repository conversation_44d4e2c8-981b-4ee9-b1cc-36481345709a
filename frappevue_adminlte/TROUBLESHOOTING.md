# دليل استكشاف الأخطاء - FrappeVue AdminLTE

## المشكلة: عدم جلب البيانات (العملاء، الأصناف، الفواتير)

### الأسباب المحتملة:

1. **مشكلة في المصادقة (Authentication)**
2. **عدم وجود صلاحيات للمستخدم**
3. **مشكلة في إعدادات CORS**
4. **خطأ في تكوين الاتصال**

---

## خطوات التشخيص:

### 1. استخدام أداة التشخيص
افتح ملف `debug_connection.html` في المتصفح واتبع الخطوات التالية:

```bash
# افتح الملف في المتصفح
file:///home/<USER>/frappe-bench/frappevue_adminlte/debug_connection.html
```

### 2. التحقق من حالة الخادم
```bash
# تحقق من أن خدمات Frappe تعمل
ps aux | grep frappe

# تحقق من المنفذ 8000
curl -I http://localhost:8000
```

### 3. اختبار API مباشرة
```bash
# اختبار بدون مصادقة (يجب أن يعطي خطأ 401)
curl -X GET "http://localhost:8000/api/resource/Customer" -H "Content-Type: application/json"

# اختبار مع مصادقة (استبدل TOKEN بالـ token الفعلي)
curl -X GET "http://localhost:8000/api/resource/Customer" \
  -H "Content-Type: application/json" \
  -H "Authorization: token YOUR_API_KEY:YOUR_API_SECRET"
```

---

## الحلول:

### 1. إصلاح مشكلة المصادقة

#### أ. التحقق من بيانات تسجيل الدخول:
```javascript
// في console المتصفح
console.log(localStorage.frappUser);
```

#### ب. إعادة تسجيل الدخول:
```javascript
// مسح البيانات القديمة
localStorage.removeItem('frappUser');
// ثم سجل دخول مرة أخرى
```

### 2. التحقق من صلاحيات المستخدم

#### في ERPNext:
1. اذهب إلى **User Management > User**
2. ابحث عن المستخدم
3. تأكد من وجود الأدوار التالية:
   - `Sales User` (للعملاء والفواتير)
   - `Stock User` (للأصناف)
   - `System Manager` (للصلاحيات الكاملة)

#### إضافة صلاحيات عبر bench:
```bash
# إضافة دور للمستخدم
bench --site [site-name] add-user-role [username] "Sales User"
bench --site [site-name] add-user-role [username] "Stock User"
```

### 3. إصلاح إعدادات CORS

#### في ملف site_config.json:
```json
{
  "allow_cors": "*",
  "cors_headers": [
    "Authorization",
    "Content-Type",
    "Accept"
  ]
}
```

#### إعادة تشغيل الخادم:
```bash
bench restart
```

### 4. التحقق من إعدادات API

#### تفعيل API Keys للمستخدم:
```bash
# في bench console
bench --site [site-name] console

# في Python console
from frappe.core.doctype.user.user import generate_keys
user = frappe.get_doc('User', '<EMAIL>')
generate_keys(user.name)
frappe.db.commit()
```

---

## اختبار الحلول:

### 1. اختبار تسجيل الدخول:
```javascript
// في console المتصفح
const frappe = new utils.Frappe('http://localhost:8000');
frappe.login({usr: 'your-email', pwd: 'your-password'})
  .then(result => console.log('Login result:', result));
```

### 2. اختبار جلب البيانات:
```javascript
// بعد تسجيل الدخول الناجح
frappe.get_list('Customer', 'fields=["name","customer_name"]&limit_page_length=5')
  .then(result => console.log('Customers:', result));
```

---

## رسائل الخطأ الشائعة:

### 1. "No permission for Customer"
**الحل:** إضافة دور `Sales User` للمستخدم

### 2. "Authentication failed"
**الحل:** إعادة تسجيل الدخول أو تجديد API keys

### 3. "CORS error"
**الحل:** إضافة إعدادات CORS في site_config.json

### 4. "Connection refused"
**الحل:** التأكد من تشغيل خدمات Frappe

---

## ملفات مهمة للتحقق:

1. `frappevue_adminlte/appConfig.json` - إعدادات الاتصال
2. `frappevue_adminlte/src/utils.js` - دوال API
3. `sites/[site-name]/site_config.json` - إعدادات الموقع

---

## أوامر مفيدة:

```bash
# عرض حالة الخدمات
ps aux | grep frappe

# إعادة تشغيل الخدمات
bench restart

# عرض logs
bench logs

# دخول console
bench --site [site-name] console

# تحديث التطبيق
bench update

# إعادة بناء الأصول
bench build
```

---

## للحصول على مساعدة إضافية:

1. تحقق من logs في `logs/` directory
2. استخدم أداة التشخيص `debug_connection.html`
3. اختبر API مباشرة باستخدام curl أو Postman
4. تأكد من إعدادات المستخدم والصلاحيات في ERPNext
