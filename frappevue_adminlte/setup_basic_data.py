#!/usr/bin/env python3
"""
سكريبت لإعداد البيانات الأساسية في ERPNext
يجب تشغيله من داخل frappe bench console
"""

import frappe

def setup_customer_groups():
    """إنشاء Customer Groups الأساسية"""
    print("=== إنشاء Customer Groups ===")
    
    customer_groups = [
        {
            "customer_group_name": "All Customer Groups",
            "is_group": 1,
            "parent_customer_group": None
        },
        {
            "customer_group_name": "Individual",
            "is_group": 0,
            "parent_customer_group": "All Customer Groups"
        },
        {
            "customer_group_name": "Commercial",
            "is_group": 0,
            "parent_customer_group": "All Customer Groups"
        },
        {
            "customer_group_name": "Company",
            "is_group": 0,
            "parent_customer_group": "All Customer Groups"
        }
    ]
    
    for group_data in customer_groups:
        try:
            # التحقق من وجود المجموعة
            if frappe.db.exists("Customer Group", group_data["customer_group_name"]):
                print(f"✓ Customer Group '{group_data['customer_group_name']}' already exists")
                continue
                
            # إنشاء المجموعة
            customer_group = frappe.get_doc({
                "doctype": "Customer Group",
                **group_data
            })
            customer_group.insert()
            print(f"✓ Created Customer Group: {group_data['customer_group_name']}")
            
        except Exception as e:
            print(f"✗ Error creating Customer Group '{group_data['customer_group_name']}': {e}")
    
    frappe.db.commit()

def setup_territories():
    """إنشاء Territories الأساسية"""
    print("\n=== إنشاء Territories ===")
    
    territories = [
        {
            "territory_name": "All Territories",
            "is_group": 1,
            "parent_territory": None
        },
        {
            "territory_name": "Saudi Arabia",
            "is_group": 0,
            "parent_territory": "All Territories"
        },
        {
            "territory_name": "Rest Of The World",
            "is_group": 0,
            "parent_territory": "All Territories"
        }
    ]
    
    for territory_data in territories:
        try:
            # التحقق من وجود الإقليم
            if frappe.db.exists("Territory", territory_data["territory_name"]):
                print(f"✓ Territory '{territory_data['territory_name']}' already exists")
                continue
                
            # إنشاء الإقليم
            territory = frappe.get_doc({
                "doctype": "Territory",
                **territory_data
            })
            territory.insert()
            print(f"✓ Created Territory: {territory_data['territory_name']}")
            
        except Exception as e:
            print(f"✗ Error creating Territory '{territory_data['territory_name']}': {e}")
    
    frappe.db.commit()

def setup_sales_persons():
    """إنشاء Sales Persons الأساسية"""
    print("\n=== إنشاء Sales Persons ===")
    
    sales_persons = [
        {
            "sales_person_name": "Sales Team",
            "is_group": 1,
            "parent_sales_person": None
        }
    ]
    
    for sp_data in sales_persons:
        try:
            # التحقق من وجود مندوب المبيعات
            if frappe.db.exists("Sales Person", sp_data["sales_person_name"]):
                print(f"✓ Sales Person '{sp_data['sales_person_name']}' already exists")
                continue
                
            # إنشاء مندوب المبيعات
            sales_person = frappe.get_doc({
                "doctype": "Sales Person",
                **sp_data
            })
            sales_person.insert()
            print(f"✓ Created Sales Person: {sp_data['sales_person_name']}")
            
        except Exception as e:
            print(f"✗ Error creating Sales Person '{sp_data['sales_person_name']}': {e}")
    
    frappe.db.commit()

def verify_setup():
    """التحقق من إعداد البيانات"""
    print("\n=== التحقق من البيانات ===")
    
    # التحقق من Customer Groups
    customer_groups = frappe.get_list("Customer Group", fields=["name"])
    print(f"Customer Groups ({len(customer_groups)}):")
    for cg in customer_groups:
        print(f"  - {cg.name}")
    
    # التحقق من Territories
    territories = frappe.get_list("Territory", fields=["name"])
    print(f"\nTerritories ({len(territories)}):")
    for t in territories:
        print(f"  - {t.name}")
    
    # التحقق من Sales Persons
    sales_persons = frappe.get_list("Sales Person", fields=["name"])
    print(f"\nSales Persons ({len(sales_persons)}):")
    for sp in sales_persons:
        print(f"  - {sp.name}")

def test_customer_creation():
    """اختبار إنشاء عميل"""
    print("\n=== اختبار إنشاء عميل ===")
    
    try:
        # إنشاء عميل تجريبي
        customer_data = {
            "doctype": "Customer",
            "customer_name": "Test Customer Auto Setup",
            "customer_type": "Individual",
            "customer_group": "Individual",
            "territory": "Saudi Arabia"
        }
        
        customer = frappe.get_doc(customer_data)
        customer.insert()
        frappe.db.commit()
        
        print(f"✓ Test customer created successfully: {customer.name}")
        
        # حذف العميل التجريبي
        frappe.delete_doc("Customer", customer.name)
        frappe.db.commit()
        print("✓ Test customer deleted")
        
    except Exception as e:
        print(f"✗ Error creating test customer: {e}")

def main():
    """الدالة الرئيسية"""
    print("بدء إعداد البيانات الأساسية في ERPNext...")
    print("=" * 50)
    
    try:
        setup_customer_groups()
        setup_territories()
        setup_sales_persons()
        verify_setup()
        test_customer_creation()
        
        print("\n" + "=" * 50)
        print("✅ تم إعداد البيانات الأساسية بنجاح!")
        print("يمكنك الآن استخدام التطبيق لإنشاء العملاء.")
        
    except Exception as e:
        print(f"\n❌ خطأ في إعداد البيانات: {e}")
        frappe.db.rollback()

if __name__ == "__main__":
    main()
