import appConfig from '../appConfig';

export default {
    name: 'utils',
    Frappe: class Frappe {
        constructor(url) {
            this.url = url;
            this.token = '';
            this.headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            };
            // this.stack = <call stack>;
        }

        // التحقق من حالة تسجيل الدخول
        isLoggedIn() {
            try {
                if (!localStorage.frappUser) {
                    return false;
                }
                let userData = JSON.parse(localStorage.frappUser);
                return userData && userData.token;
            } catch (error) {
                console.error('Error checking login status:', error);
                return false;
            }
        }

        // تسجيل الخروج
        logout() {
            localStorage.removeItem('frappUser');
            window.location.href = '/login';
        }
        async login(body) {

            let res = await fetch(`${this.url}/api/method/${appConfig.frappe_custom_app}.authentication.login`, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify(body)
            })
            let auth = await res.json()
            // console.log('AUTH', auth)
            //     // confirm auth
            if (auth.message.status_code == 200) {
                let user = await fetch(`${this.url}/api/resource/User/${auth.message.user}`, {
                    method: 'GET',
                    headers: { 'Authorization': `token ${auth.message.token}` }
                })
                user = await user.json()
                return await { ...user, 'token': auth.message.token }
            } else {
                return auth.message;
            }
        }
        // get updated headers
        getHeader() {
            try {
                // التحقق من وجود المستخدم في localStorage
                if (!localStorage.frappUser) {
                    console.error('No user found in localStorage. Please login first.');
                    // إعادة توجيه للصفحة الرئيسية أو صفحة تسجيل الدخول
                    window.location.href = '/login';
                    return;
                }

                let userData = JSON.parse(localStorage.frappUser);
                if (!userData.token) {
                    console.error('No token found for user. Please login again.');
                    localStorage.removeItem('frappUser');
                    window.location.href = '/login';
                    return;
                }

                let token = userData.token;
                this.headers.Authorization = `token ${token}`;
                console.log('Authorization header set:', this.headers.Authorization);
            } catch (error) {
                console.error('Error getting user token:', error);
                localStorage.removeItem('frappUser');
                window.location.href = '/login';
            }
        }

        // handleResponse
        async handleResponse(res) {
            console.log('Handling response with status:', res.status);

            if (res.status == 200) {
                let data = await res.json();
                console.log('Response data:', data);

                // التعامل مع استجابات Frappe المختلفة
                if (data.message) {
                    return { 'status_code': res.status, 'data': data.message };
                } else if (data.data) {
                    return { 'status_code': res.status, 'data': data.data };
                } else {
                    return { 'status_code': res.status, 'data': data };
                }
            } else if (res.status == 404) {
                // not found
                console.warn('Resource not found:', res.statusText);
                return { 'status_code': res.status, 'text': res.statusText }
            } else if (res.status == 401 || res.status == 403) {
                // authentication/authorization errors
                let errorData;
                try {
                    errorData = await res.json();
                    console.error('Authentication error:', errorData);
                } catch (e) {
                    errorData = { message: res.statusText };
                }
                return { 'status_code': res.status, 'text': errorData._error_message || errorData.message || res.statusText, 'error': errorData }
            } else {
                // other errors
                let errorData;
                try {
                    errorData = await res.json();
                    console.error('API error:', errorData);
                } catch (e) {
                    errorData = { message: res.statusText };
                }
                return { 'status_code': res.status, 'text': errorData._error_message || errorData.message || res.statusText, 'error': errorData }
            }
        }

        // get_doc
        async get_doc(doctype, docname) {
            try {
                this.getHeader();
                let res = await fetch(`${this.url}/api/resource/${doctype}/${docname}`, {
                    method: 'GET',
                    headers: this.headers
                })
                return await this.handleResponse(res);
            } catch (e) {
                console.log(e)
            }
        }
        // update doc
        async update_doc(doctype, docname, body) {
            try {
                this.getHeader();
                let res = await fetch(`${this.url}/api/resource/${doctype}/${docname}`, {
                    method: 'PUT',
                    headers: this.headers,
                    body: JSON.stringify(body)
                })
                return await this.handleResponse(res);
            } catch (e) {
                console.log(e)
            }
        }
        // create doc
        async new_doc(doctype, body) {
            try {
                this.getHeader();

                // إضافة doctype إلى body
                const docData = {
                    doctype: doctype,
                    ...body
                };

                // إعداد headers محسنة لتجنب مشكلة EXPECTATION FAILED
                const headers = {
                    ...this.headers,
                    'Content-Type': 'application/json'
                };

                // إزالة headers التي قد تسبب مشاكل
                delete headers['Expect'];
                delete headers['Content-Length'];

                const requestBody = JSON.stringify({
                    doc: JSON.stringify(docData)
                });

                console.log('Sending request to:', `${this.url}/api/method/frappe.client.insert`);
                console.log('Request body:', requestBody);

                let res = await fetch(`${this.url}/api/method/frappe.client.insert`, {
                    method: 'POST',
                    headers: headers,
                    body: requestBody,
                    credentials: 'include' // إضافة credentials للتأكد من إرسال الكوكيز
                })

                console.log('Response status:', res.status);
                console.log('Response statusText:', res.statusText);

                return await this.handleResponse(res);
            } catch (e) {
                console.error('Error in new_doc:', e);
                return { 'status_code': 500, 'text': e.message }
            }
        }
        // DELETE doc
        async delete_doc(doctype, docname) {
            try {
                this.getHeader();
                let res = await fetch(`${this.url}/api/resource/${doctype}/${docname}`, {
                    method: 'DELETE',
                    headers: this.headers
                })
                return await this.handleResponse(res);
            } catch (e) {
                console.log(e)
            }
        }
        // get list
        async get_list(doctype, filters = null) {
            try {
                this.getHeader();

                // التحقق من وجود Authorization header
                if (!this.headers.Authorization) {
                    console.error('No authorization token found');
                    return { 'status_code': 401, 'text': 'Authentication required' };
                }

                let url = `${this.url}/api/resource/${doctype}`
                if (filters) {
                    url = url + '?' + filters;
                }

                console.log('Fetching data from:', url);
                console.log('Headers:', this.headers);

                let res = await fetch(url, {
                    method: 'GET',
                    headers: this.headers,
                    credentials: 'include' // إضافة credentials للتأكد من إرسال الكوكيز
                })

                console.log('Response status:', res.status);

                // التعامل مع أخطاء المصادقة
                if (res.status === 401 || res.status === 403) {
                    console.error('Authentication failed. Redirecting to login...');
                    localStorage.removeItem('frappUser');
                    window.location.href = '/login';
                    return { 'status_code': res.status, 'text': 'Authentication failed' };
                }

                return await this.handleResponse(res);
            } catch (e) {
                console.error('Error in get_list:', e);
                return { 'status_code': 500, 'text': e.message };
            }
        }

        // get pdf
        async getPDF(
            doctype, docname, format, Standard,
            no_letterhead, letterhead, lang
        ) {
            format = format ? format : 'Standard';
            no_letterhead = no_letterhead ? no_letterhead : 1;
            letterhead = letterhead ? letterhead : 'No Letterhead';
            lang = lang ? lang : 'en';

            let url = `${this.url}/api/method/frappe.utils.print_format.download_pdf?doctype=${doctype}&name=${docname}&format=${format}&no_letterhead=${no_letterhead}&letterhead=${letterhead}&settings=%7B%7D&_lang=${lang}`
            // process download
            this.getHeader();
            let res = await fetch(url, {
                method: 'GET',
                headers: this.headers
            })
            if (res.status == 200) {
                let blob = await res.blob();
                let file = await window.URL.createObjectURL(blob);
                window.location.assign(file);
            } else {
                this.$popIt.error('Error', res.statusText);
            }
        }
    }
}

// export default new Frappe()
