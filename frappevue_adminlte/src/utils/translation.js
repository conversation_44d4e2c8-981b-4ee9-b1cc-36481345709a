/**
 * مساعد الترجمة للتطبيق
 * يوفر دوال لترجمة النصوص والقيم من ERPNext إلى العربية
 */

import translations from '@/locales/ar.json'

/**
 * ترجمة نص باستخدام مفتاح الترجمة
 * @param {string} key - مفتاح الترجمة (مثل: 'customer.customer_name')
 * @param {object} params - معاملات للاستبدال في النص
 * @returns {string} النص المترجم
 */
export function translate(key, params = {}) {
  try {
    const keys = key.split('.')
    let value = translations
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return key // إرجاع المفتاح إذا لم توجد الترجمة
      }
    }
    
    if (typeof value === 'string') {
      // استبدال المعاملات في النص
      return value.replace(/\{(\w+)\}/g, (match, param) => {
        return params[param] || match
      })
    }
    
    return key
  } catch (error) {
    console.error('Translation error:', error)
    return key
  }
}

/**
 * ترجمة نوع العميل من الإنجليزية إلى العربية
 * @param {string} customerType - نوع العميل (Individual/Company)
 * @returns {string} النوع مترجم
 */
export function translateCustomerType(customerType) {
  return translate(`customer_types.${customerType}`) || customerType
}

/**
 * ترجمة مجموعة العميل
 * @param {string} customerGroup - مجموعة العميل
 * @returns {string} المجموعة مترجمة
 */
export function translateCustomerGroup(customerGroup) {
  return translate(`customer_groups.${customerGroup}`) || customerGroup
}

/**
 * ترجمة الإقليم/المنطقة
 * @param {string} territory - الإقليم
 * @returns {string} الإقليم مترجم
 */
export function translateTerritory(territory) {
  return translate(`territories.${territory}`) || territory
}

/**
 * الحصول على قائمة أنواع العملاء مترجمة
 * @returns {Array} قائمة بأنواع العملاء مع القيم الأصلية والمترجمة
 */
export function getCustomerTypes() {
  return [
    { value: 'Individual', label: translateCustomerType('Individual') },
    { value: 'Company', label: translateCustomerType('Company') }
  ]
}

/**
 * ترجمة رسالة الخطأ
 * @param {string} errorKey - مفتاح رسالة الخطأ
 * @param {object} params - معاملات إضافية
 * @returns {string} رسالة الخطأ مترجمة
 */
export function translateError(errorKey, params = {}) {
  return translate(`messages.error.${errorKey}`, params)
}

/**
 * ترجمة رسالة النجاح
 * @param {string} successKey - مفتاح رسالة النجاح
 * @param {object} params - معاملات إضافية
 * @returns {string} رسالة النجاح مترجمة
 */
export function translateSuccess(successKey, params = {}) {
  return translate(`messages.success.${successKey}`, params)
}

/**
 * ترجمة عنصر من قائمة ERPNext
 * يحاول العثور على الترجمة في ملف الترجمة، وإذا لم يجدها يعيد القيمة الأصلية
 * @param {string} doctype - نوع المستند (مثل: 'Customer Group', 'Territory')
 * @param {string} value - القيمة المراد ترجمتها
 * @returns {string} القيمة مترجمة أو الأصلية
 */
export function translateERPNextValue(doctype, value) {
  if (!value) return value
  
  const doctypeMap = {
    'Customer Group': 'customer_groups',
    'Territory': 'territories',
    'Customer Type': 'customer_types'
  }
  
  const translationKey = doctypeMap[doctype]
  if (translationKey) {
    return translate(`${translationKey}.${value}`) || value
  }
  
  return value
}

/**
 * إنشاء خيارات select مترجمة من قائمة ERPNext
 * @param {Array} items - قائمة العناصر من ERPNext
 * @param {string} doctype - نوع المستند للترجمة
 * @returns {Array} قائمة خيارات مع القيم الأصلية والتسميات المترجمة
 */
export function createTranslatedOptions(items, doctype) {
  if (!Array.isArray(items)) return []
  
  return items.map(item => ({
    value: item.name || item,
    label: translateERPNextValue(doctype, item.name || item)
  }))
}

/**
 * دالة مساعدة لاستخدامها في Vue components
 * @param {string} key - مفتاح الترجمة
 * @param {object} params - معاملات الترجمة
 * @returns {string} النص المترجم
 */
export function $t(key, params = {}) {
  return translate(key, params)
}

// تصدير الدوال كـ default للاستخدام المباشر
export default {
  translate,
  translateCustomerType,
  translateCustomerGroup,
  translateTerritory,
  getCustomerTypes,
  translateError,
  translateSuccess,
  translateERPNextValue,
  createTranslatedOptions,
  $t
}
