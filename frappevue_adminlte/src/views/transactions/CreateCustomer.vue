<template>
  <div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>إضافة عميل جديد</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><router-link to="/">الرئيسية</router-link></li>
              <li class="breadcrumb-item active">إضافة عميل</li>
            </ol>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-8 offset-md-2">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">بيانات العميل</h3>
              </div>
              
              <form @submit.prevent="createCustomer">
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="customer_name">اسم العميل *</label>
                        <input v-model="customer.customer_name" type="text" class="form-control" id="customer_name" required>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="customer_type">نوع العميل</label>
                        <select v-model="customer.customer_type" class="form-control" id="customer_type">
                          <option value="Individual">فرد</option>
                          <option value="Company">شركة</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="email_id">البريد الإلكتروني</label>
                        <input v-model="customer.email_id" type="email" class="form-control" id="email_id">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="mobile_no">رقم الجوال</label>
                        <input v-model="customer.mobile_no" type="tel" class="form-control" id="mobile_no">
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="tax_id">الرقم الضريبي</label>
                        <input v-model="customer.tax_id" type="text" class="form-control" id="tax_id">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="territory">المنطقة</label>
                        <select v-model="customer.territory" class="form-control" id="territory">
                          <option value="Saudi Arabia">المملكة العربية السعودية</option>
                          <option value="All Territories">جميع المناطق</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="customer_details">تفاصيل إضافية</label>
                    <textarea v-model="customer.customer_details" class="form-control" id="customer_details" rows="3"></textarea>
                  </div>

                  <!-- Address Section -->
                  <h4>العنوان</h4>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="address_line1">العنوان الأول</label>
                        <input v-model="customer.address_line1" type="text" class="form-control" id="address_line1">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="address_line2">العنوان الثاني</label>
                        <input v-model="customer.address_line2" type="text" class="form-control" id="address_line2">
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="city">المدينة</label>
                        <input v-model="customer.city" type="text" class="form-control" id="city">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="state">المنطقة/الولاية</label>
                        <input v-model="customer.state" type="text" class="form-control" id="state">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="pincode">الرمز البريدي</label>
                        <input v-model="customer.pincode" type="text" class="form-control" id="pincode">
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-footer">
                  <button type="submit" class="btn btn-primary" :disabled="loading">
                    <i class="fas fa-save"></i> 
                    {{ loading ? 'جاري الحفظ...' : 'حفظ العميل' }}
                  </button>
                  <button @click="goBack" type="button" class="btn btn-secondary ml-2">
                    <i class="fas fa-arrow-left"></i> رجوع
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'CreateCustomer',
  data() {
    return {
      loading: false,
      customer: {
        customer_name: '',
        customer_type: 'Individual',
        email_id: '',
        mobile_no: '',
        tax_id: '',
        territory: 'Saudi Arabia',
        customer_details: '',
        address_line1: '',
        address_line2: '',
        city: '',
        state: '',
        pincode: ''
      }
    }
  },
  methods: {
    async createCustomer() {
      this.loading = true
      try {
        // Create customer
        const customerData = {
          doctype: 'Customer',
          customer_name: this.customer.customer_name,
          customer_type: this.customer.customer_type,
          customer_details: this.customer.customer_details
        }

        // إضافة territory فقط إذا كان موجوداً ومحدداً
        if (this.customer.territory && this.customer.territory.trim() !== '') {
          customerData.territory = this.customer.territory;
        }

        console.log('Creating customer with data:', customerData);

        const customerRes = await this.$frappe.new_doc('Customer', customerData)
        
        if (customerRes.status_code === 200) {
          // If address data is provided, create address
          if (this.customer.address_line1 || this.customer.city) {
            const addressData = {
              doctype: 'Address',
              address_title: this.customer.customer_name,
              address_type: 'Billing',
              address_line1: this.customer.address_line1,
              address_line2: this.customer.address_line2,
              city: this.customer.city,
              state: this.customer.state,
              pincode: this.customer.pincode,
              country: 'Saudi Arabia',
              links: [{
                link_doctype: 'Customer',
                link_name: customerRes.data.name
              }]
            }

            await this.$frappe.new_doc('Address', addressData)
          }

          // If contact data is provided, create contact
          if (this.customer.email_id || this.customer.mobile_no) {
            const contactData = {
              doctype: 'Contact',
              first_name: this.customer.customer_name,
              email_id: this.customer.email_id,
              mobile_no: this.customer.mobile_no,
              links: [{
                link_doctype: 'Customer',
                link_name: customerRes.data.name
              }]
            }

            await this.$frappe.new_doc('Contact', contactData)
          }

          this.$popIt.success('نجح', 'تم إنشاء العميل بنجاح')
          this.goBack()
        } else {
          this.$popIt.error('خطأ', 'فشل في إنشاء العميل')
        }
      } catch (error) {
        console.error('Error creating customer:', error)
        this.$popIt.error('خطأ', 'حدث خطأ أثناء إنشاء العميل')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.form-control {
  text-align: right;
}
</style>
