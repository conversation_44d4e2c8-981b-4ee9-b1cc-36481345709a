<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إنشاء العميل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .form-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .result-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار إنشاء العميل - FrappeVue AdminLTE</h1>
        
        <div class="form-section">
            <h3>بيانات العميل</h3>
            
            <label for="customer_name">اسم العميل *</label>
            <input type="text" id="customer_name" placeholder="أدخل اسم العميل" value="عميل تجريبي">
            
            <label for="customer_type">نوع العميل *</label>
            <select id="customer_type">
                <option value="Individual">فرد</option>
                <option value="Company">شركة</option>
            </select>
            
            <label for="territory">الإقليم</label>
            <select id="territory">
                <option value="">-- اختر الإقليم --</option>
                <option value="Saudi Arabia">Saudi Arabia</option>
                <option value="جميع الأقاليم">جميع الأقاليم</option>
                <option value="Yemen">Yemen</option>
                <option value="باقي أنحاء العالم">باقي أنحاء العالم</option>
            </select>
            
            <label for="customer_details">تفاصيل العميل</label>
            <textarea id="customer_details" placeholder="تفاصيل إضافية عن العميل"></textarea>
            
            <button onclick="testLogin()" id="loginBtn">تسجيل دخول أولاً</button>
            <button onclick="createCustomer()" id="createBtn" disabled>إنشاء العميل</button>
            <button onclick="loadTerritories()">تحميل الأقاليم</button>
        </div>

        <div class="result-section">
            <h3>نتائج الاختبار</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script src="src/utils.js"></script>
    <script>
        const serverUrl = 'http://localhost:8000';
        let frappe = null;
        let isLoggedIn = false;

        // تهيئة Frappe utils
        function initFrappe() {
            frappe = new utils.Frappe(serverUrl);
        }

        // إضافة نتيجة اختبار
        function addTestResult(title, success, message, details = null) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result-section ${success ? 'success' : 'error'}`;
            
            let html = `<h4>${title}</h4><p>${message}</p>`;
            if (details) {
                html += `<pre>${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            resultDiv.innerHTML = html;
            resultsDiv.appendChild(resultDiv);
        }

        // تسجيل دخول تجريبي
        async function testLogin() {
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = 'جاري تسجيل الدخول...';

            try {
                if (!frappe) {
                    initFrappe();
                }

                const result = await frappe.login({
                    usr: 'Administrator',
                    pwd: 'admin'
                });

                if (result && result.status_code === 200) {
                    localStorage.setItem('frappUser', JSON.stringify({
                        token: result.token,
                        userData: result
                    }));
                    
                    addTestResult('تسجيل الدخول', true, 'تم تسجيل الدخول بنجاح', result);
                    isLoggedIn = true;
                    document.getElementById('createBtn').disabled = false;
                    loginBtn.textContent = 'تم تسجيل الدخول';
                    loginBtn.style.backgroundColor = '#28a745';
                } else {
                    addTestResult('تسجيل الدخول', false, 'فشل في تسجيل الدخول', result);
                    loginBtn.disabled = false;
                    loginBtn.textContent = 'تسجيل دخول أولاً';
                }
            } catch (error) {
                addTestResult('تسجيل الدخول', false, `خطأ في تسجيل الدخول: ${error.message}`);
                loginBtn.disabled = false;
                loginBtn.textContent = 'تسجيل دخول أولاً';
            }
        }

        // تحميل الأقاليم المتاحة
        async function loadTerritories() {
            try {
                if (!frappe) {
                    initFrappe();
                }

                const result = await frappe.get_list('Territory', 'fields=["name"]&limit_page_length=20');
                
                if (result && result.status_code === 200) {
                    const territorySelect = document.getElementById('territory');
                    territorySelect.innerHTML = '<option value="">-- اختر الإقليم --</option>';
                    
                    result.data.forEach(territory => {
                        const option = document.createElement('option');
                        option.value = territory.name;
                        option.textContent = territory.name;
                        territorySelect.appendChild(option);
                    });
                    
                    addTestResult('تحميل الأقاليم', true, `تم تحميل ${result.data.length} إقليم بنجاح`, result.data);
                } else {
                    addTestResult('تحميل الأقاليم', false, 'فشل في تحميل الأقاليم', result);
                }
            } catch (error) {
                addTestResult('تحميل الأقاليم', false, `خطأ في تحميل الأقاليم: ${error.message}`);
            }
        }

        // إنشاء العميل
        async function createCustomer() {
            if (!isLoggedIn) {
                addTestResult('إنشاء العميل', false, 'يرجى تسجيل الدخول أولاً');
                return;
            }

            const createBtn = document.getElementById('createBtn');
            createBtn.disabled = true;
            createBtn.textContent = 'جاري الإنشاء...';

            try {
                const customerName = document.getElementById('customer_name').value;
                const customerType = document.getElementById('customer_type').value;
                const territory = document.getElementById('territory').value;
                const customerDetails = document.getElementById('customer_details').value;

                if (!customerName.trim()) {
                    addTestResult('إنشاء العميل', false, 'اسم العميل مطلوب');
                    createBtn.disabled = false;
                    createBtn.textContent = 'إنشاء العميل';
                    return;
                }

                // إعداد بيانات العميل
                const customerData = {
                    customer_name: customerName,
                    customer_type: customerType,
                    customer_details: customerDetails
                };

                // إضافة territory فقط إذا كان محدداً
                if (territory && territory.trim() !== '') {
                    customerData.territory = territory;
                }

                console.log('Creating customer with data:', customerData);

                const result = await frappe.new_doc('Customer', customerData);

                if (result && result.status_code === 200) {
                    addTestResult('إنشاء العميل', true, `تم إنشاء العميل بنجاح: ${result.data.name}`, result.data);
                    
                    // مسح النموذج
                    document.getElementById('customer_name').value = '';
                    document.getElementById('customer_details').value = '';
                } else {
                    addTestResult('إنشاء العميل', false, 'فشل في إنشاء العميل', result);
                }
            } catch (error) {
                addTestResult('إنشاء العميل', false, `خطأ في إنشاء العميل: ${error.message}`);
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = 'إنشاء العميل';
            }
        }

        // تهيئة الصفحة
        window.onload = function() {
            initFrappe();
            
            // التحقق من حالة تسجيل الدخول
            if (localStorage.frappUser) {
                try {
                    const userData = JSON.parse(localStorage.frappUser);
                    if (userData.token) {
                        isLoggedIn = true;
                        document.getElementById('createBtn').disabled = false;
                        document.getElementById('loginBtn').textContent = 'مسجل دخول';
                        document.getElementById('loginBtn').style.backgroundColor = '#28a745';
                    }
                } catch (error) {
                    console.error('Error parsing user data:', error);
                }
            }
        };
    </script>
</body>
</html>
