# بنية النظام - FrappeVue AdminLTE مع ERPNext

## نظرة عامة على النظام

### 1. المكونات الأساسية:

```
┌─────────────────────────────────────────────────────────────┐
│                    FrappeVue AdminLTE                      │
│                   (Vue.js Frontend)                        │
│                  http://localhost:3000                     │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP API Calls
                      │ (Authentication + CRUD)
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                  Frappe Framework                          │
│                 http://localhost:8000                      │
│  ┌─────────────────┬─────────────────┬─────────────────┐   │
│  │  frappeauth_app │    ERPNext      │   Other Apps    │   │
│  │ (Authentication)│   (Business)    │                 │   │
│  └─────────────────┴─────────────────┴─────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                   MariaDB Database                         │
│                    (site1.local)                           │
└─────────────────────────────────────────────────────────────┘
```

### 2. تطبيق frappeauth_app:

**الغرض:** توفير آلية مصادقة مخصصة للتطبيقات الخارجية

**الملفات الرئيسية:**
- `apps/frappeauth_app/frappeauth_app/authentication.py` - دوال المصادقة
- `apps/frappeauth_app/frappeauth_app/hooks.py` - إعدادات التطبيق

**آلية العمل:**
1. يستقبل بيانات تسجيل الدخول (username/password)
2. يتحقق من صحة البيانات مع قاعدة البيانات
3. ينشئ API Keys للمستخدم إذا لم تكن موجودة
4. يرجع Token للاستخدام في الطلبات اللاحقة

### 3. FrappeVue AdminLTE:

**الغرض:** واجهة مستخدم حديثة لإدارة بيانات ERPNext

**البنية:**
```
frappevue_adminlte/
├── src/
│   ├── main.js              # نقطة البداية
│   ├── App.vue              # المكون الرئيسي
│   ├── router/index.js      # إعدادات التوجيه
│   ├── utils.js             # دوال API والمساعدة
│   ├── views/               # صفحات التطبيق
│   │   ├── authentication/  # صفحات تسجيل الدخول
│   │   ├── transactions/    # العمليات (فواتير، عملاء)
│   │   └── inventory/       # المخزون (أصناف، مستودعات)
│   └── components/          # مكونات قابلة للإعادة
├── appConfig.json           # إعدادات الاتصال
└── debug_connection.html    # أداة تشخيص الاتصال
```

## تدفق البيانات والمصادقة

### 1. تسجيل الدخول:
```javascript
// في Login.vue
async loginUser() {
    let user = await this.$frappe.login(this.auth)
    // يرسل POST إلى: /api/method/frappeauth_app.authentication.login
    
    if (user && !user.status_code) {
        // حفظ Token في localStorage
        localStorage.set('frappUser', {
            token: user.token,
            userData: user.data
        })
    }
}
```

### 2. جلب البيانات:
```javascript
// في utils.js
async get_list(doctype, filters = null) {
    this.getHeader(); // إضافة Authorization header
    
    let url = `${this.url}/api/resource/${doctype}`
    if (filters) {
        url = url + '?' + filters;
    }
    
    let res = await fetch(url, {
        method: 'GET',
        headers: this.headers, // يحتوي على Authorization: token API_KEY:API_SECRET
        credentials: 'include'
    })
    
    return await this.handleResponse(res);
}
```

### 3. إنشاء البيانات:
```javascript
// في utils.js
async new_doc(doctype, body) {
    const docData = {
        doctype: doctype,
        ...body
    };
    
    const requestBody = JSON.stringify({
        doc: JSON.stringify(docData)
    });
    
    let res = await fetch(`${this.url}/api/method/frappe.client.insert`, {
        method: 'POST',
        headers: this.headers,
        body: requestBody,
        credentials: 'include'
    })
}
```

## إعدادات النظام

### 1. ملف appConfig.json:
```json
{
    "domain": "http://localhost:8000",
    "authToken": "",
    "frappe_custom_app": "frappeauth_app"
}
```

### 2. إعدادات CORS في site_config.json:
```json
{
    "allow_cors": "*",
    "cors_headers": [
        "Authorization",
        "Content-Type", 
        "Accept",
        "X-Requested-With"
    ]
}
```

### 3. إعدادات Router (حماية الصفحات):
```javascript
router.beforeEach((to, from, next) => {
    if (!['/login', '/register'].includes(to.fullPath)) {
        if (!getToken()) {
            next('/login'); // إعادة توجيه لصفحة تسجيل الدخول
        }
    }
    next();
});
```

## المشاكل الشائعة والحلول

### 1. مشكلة "No permission for Customer":
**السبب:** المستخدم لا يملك صلاحيات للوصول للبيانات
**الحل:** 
- إضافة أدوار مناسبة للمستخدم في ERPNext
- التأكد من وجود Token صحيح

### 2. مشكلة "Authentication failed":
**السبب:** Token غير صحيح أو منتهي الصلاحية
**الحل:**
- مسح localStorage وإعادة تسجيل الدخول
- التحقق من API Keys في ERPNext

### 3. مشكلة CORS:
**السبب:** إعدادات CORS غير مكونة
**الحل:**
- إضافة إعدادات CORS في site_config.json
- إعادة تشغيل خدمات Frappe

## أدوات التشخيص

### 1. ملف debug_connection.html:
- اختبار الاتصال بالخادم
- اختبار المصادقة
- اختبار APIs مختلفة
- تسجيل دخول سريع للاختبار

### 2. Console المتصفح:
```javascript
// فحص حالة تسجيل الدخول
console.log(localStorage.frappUser);

// اختبار API مباشرة
this.$frappe.get_list('Customer', 'fields=["name","customer_name"]&limit_page_length=5')
    .then(result => console.log(result));
```

### 3. أوامر Terminal مفيدة:
```bash
# فحص حالة الخدمات
ps aux | grep frappe

# فحص logs
tail -f logs/web.log

# إعادة تشغيل الخدمات
bench restart

# دخول console
bench --site site1.local console
```

## نصائح للتطوير

### 1. تجنب تكرار الملفات:
- تحقق من وجود الملف قبل إنشائه
- استخدم أدوات البحث للعثور على الملفات المشابهة

### 2. إدارة الأخطاء:
- استخدم try-catch في جميع دوال API
- اعرض رسائل خطأ واضحة للمستخدم
- سجل الأخطاء في console للتشخيص

### 3. الأمان:
- لا تحفظ كلمات المرور في localStorage
- استخدم HTTPS في الإنتاج
- تحقق من صلاحيات المستخدم قبل عرض البيانات

### 4. الأداء:
- استخدم pagination للقوائم الطويلة
- cache البيانات المستخدمة بكثرة
- استخدم lazy loading للمكونات الكبيرة
