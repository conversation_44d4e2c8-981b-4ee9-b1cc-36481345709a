# Frappe Bench Repository Information

## Summary
This repository contains a Frappe framework installation with multiple custom applications focused on ERP functionality. The main components include the core Frappe framework, ERPNext, and several custom applications for car showroom management, WhatsApp integration, and UI customizations.

## Structure
- **apps/**: Contains all installed Frappe applications
- **sites/**: Contains site configurations and data
- **config/**: Server configuration files
- **migration_package/**: Tools for migrating customizations between instances
- **.zencoder/**: Documentation and metadata
- **archived/**: Archived versions of applications

## Language & Runtime
**Language**: Python, JavaScript
**Python Version**: 3.10.12
**Node.js Version**: >=18 (required by Frappe)
**Framework**: Frappe Framework

## Main Applications

### Frappe Core
**Configuration File**: apps/frappe/package.json
**Description**: Core framework providing the foundation for all other applications

#### Dependencies
**Main Dependencies**:
- Vue.js 3.3.0
- Socket.io 4.7.1
- jQuery 3.7.0
- Bootstrap 4.6.2
- Frappe Charts 2.0.0-rc26

#### Build & Installation
```bash
cd apps/frappe && node esbuild
```

### ERPNext
**Configuration File**: apps/erpnext/package.json
**Description**: Enterprise Resource Planning application built on Frappe

### ERPSmart Car
**Configuration File**: apps/erpsmart_car/requirements.txt
**Description**: Car showroom management application
**Dependencies**: Requires Frappe 15.0.0+

### WhatsApp Integration
**Configuration File**: apps/whatsapp/whatsapp/whatsapp/server/package.json
**Description**: WhatsApp messaging integration for the ERP system

#### Dependencies
**Main Dependencies**:
- whatsapp-web.js 1.21.0
- express 4.18.2
- qrcode 1.5.3
- axios 1.4.0

#### Usage
```bash
cd apps/whatsapp/whatsapp/whatsapp/server && node server.js
```

## Site Configuration
**Default Site**: site1.local
**Development Mode**: Enabled
**Maintenance Mode**: Enabled
**Web Server Port**: 8000
**Socket.io Port**: 9000

## Multi-language Support
The repository includes extensive Arabic language support with multiple Arabic script files for documentation, testing, and implementation guides. The system supports multilingual print formats for invoices and other documents.

## Custom Features
- Car showroom management system
- WhatsApp business integration
- Custom UI themes
- Multilingual print formats
- Saudi Phase 2 API integration (ZATCA)